import numpy as np
from scipy import signal
from scipy import stats # Needed for statistical distributions
import matplotlib.pyplot as plt

# --- 0. 辅助函数 ---
def db_to_linear(db_value):
    """将 dB 值转换为线性幅度（假设参考值为 1）"""
    return 10**(db_value / 20.0)

def linear_to_db(linear_value, epsilon=1e-12):
    """将线性幅度转换为 dB 值"""
    return 20 * np.log10(np.maximum(linear_value, epsilon)) # 避免 log(0)

def calculate_psd(signal_data, fs, nfft=1024):
    """计算信号的功率谱密度 (PSD)"""
    freqs, psd = signal.welch(signal_data, fs, nperseg=nfft, scaling='density')
    return freqs, psd

def scale_signal_rms(signal_data, target_rms):
    """将信号缩放至指定的目标 RMS 值"""
    current_rms = np.sqrt(np.mean(signal_data**2))
    if current_rms < 1e-15: # 避免除以零
        return signal_data # 如果信号几乎为零，不进行缩放
    scale_factor = target_rms / current_rms
    return signal_data * scale_factor

# --- 1. 连续谱模拟 (基本不变, 但可能需要调整以匹配新公式中的 S_conti) ---
# (复用之前的 target_continuous_spectrum_level 和 simulate_continuous_noise)
def target_continuous_spectrum_level(freq, f0, sl0, a1, a2, f_H):
    # ... (代码同前) ...
    freq = np.asarray(freq)
    sl = np.zeros_like(freq, dtype=float)
    epsilon = 1e-9
    mask1 = (freq > epsilon) & (freq < f0)
    sl[mask1] = sl0 - a1 * np.log10(f0 / freq[mask1])
    mask2 = np.isclose(freq, f0)
    sl[mask2] = sl0
    mask3 = (freq > f0) & (freq <= f_H)
    sl[mask3] = sl0 + a2 * np.log10(freq[mask3] / f0)
    # mask_outside = (freq <= epsilon) | (freq > f_H)
    # sl[mask_outside] = sl0 - 500 # 大幅衰减 dB 值
    return sl

def simulate_continuous_noise(t, fs, f0, sl0, a1, a2, f_H, filter_order=1025):
    # ... (代码同前, 返回 s_conti(t)) ...
    num_samples = len(t)
    nyquist = fs / 2.0
    num_freq_points = filter_order * 2
    # -- 准备 fir2 输入 --
    # 定义用于 fir2 设计的频率点。包含 0、f0、奈奎斯特频率。
    # 在 f0 周围使用倍频程步长，确保点覆盖所需范围。
    num_octaves_down = 11  # f0 以下的倍频程数
    num_octaves_up = 11    # f0 以上的倍频程数
    f_nyquist = nyquist    # 奈奎斯特频率

    freq_pts = [1.0]
    # f0 以下的频率
    for i in range(num_octaves_down, 0, -1):
        f = f0 / (2**i)
        # 确保不会低于最小实用频率（如 1 Hz）
        if f >= 1.0:
            freq_pts.append(f)
    freq_pts.append(f0)

    # f0 以上的频率
    for i in range(1, num_octaves_up + 1):
        f = f0 * (2**i)
        # 确保不超过f_H
        if f < f_H:
            freq_pts.append(f)
    freq_pts.append(f_H)

    freq_pts = np.array(sorted(list(set(freq_pts))))  # 去重并排序
    print(f"  使用 {len(freq_pts)} 个频率点进行 fir2 设计")
    print(f"  频率点: {freq_pts}")

    freq_points_hz = freq_pts
    # freq_points_hz = np.linspace(0, nyquist, num_freq_points)
    sl_db = target_continuous_spectrum_level(freq_points_hz, f0, sl0, a1, a2, f_H)

    # 表格展示sl_db vs. freq_points_hz
    print("  连续谱目标频谱函数:")
    print("    f (Hz) | SL (dB)")
    for f, sl in zip(freq_points_hz, sl_db):
        print(f"    {f:.2f} Hz | {sl:.2f} dB")

    # 注意：这里的 sl0 是 dB 级。 FIR 滤波器设计基于线性幅度。
    # 我们需要将 dB 转换为线性幅度，并可能需要归一化或后续缩放。
    # target_magnitude = 10**(sl_db / 20.0) # 绝对幅度？
    # 简化处理：先按形状设计，后续再根据目标 SL0 缩放整个 S(t) 或各分量。
    # 这里先生成具有正确形状但幅度任意的信号。
    target_magnitude_shape = 10**(sl_db / 20.0)
    target_magnitude_shape /= np.max(target_magnitude_shape) + 1e-9 # 按形状归一化

    normalized_freq_points = freq_points_hz / nyquist
    normalized_freq_points[0] = 0.0
    normalized_freq_points[-1] = 1.0

    # # 绘制目标频率响应曲线
    # plt.figure(figsize=(10, 6))
    # plt.plot(freq_points_hz, linear_to_db(target_magnitude_shape), 'b-')
    # plt.grid(True)
    # plt.xlabel('频率 (Hz)')
    # plt.ylabel('幅度 (dB)')
    # plt.title('目标频率响应')
    # plt.show()

    if filter_order % 2 == 0:
        filter_order += 1

    try:
        fir_coeffs = signal.firwin2(filter_order, normalized_freq_points, target_magnitude_shape, window='hann')
    except ValueError as e:
        print(f"连续谱 FIR 滤波器设计错误: {e}")
        return np.zeros_like(t)


    white_noise = np.random.randn(num_samples)
    s_conti = signal.lfilter(fir_coeffs, 1.0, white_noise)

    # 初始幅度缩放：使 RMS 为 1，便于后续相对缩放
    s_conti = scale_signal_rms(s_conti, 1.0)
    return s_conti


# --- 2. 机械线谱模拟 (S_mac(t)) ---
def simulate_machinery_noise(t, fs, params_mac):
    """
    根据 Table 1 模拟机械线谱噪声 S_mac(t)。

    Args:
        t (np.ndarray): 时间向量。
        fs (float): 采样频率 (Hz)。
        params_mac (dict): 包含机械噪声参数的字典:
            'num_sources_range': (min, max) 噪声源数量范围 (默认 2-6)
            'f_fund_range': (min, max) 每个源的基频范围 (Hz) (默认 3-10 Hz)
            'num_harmonics_per_source': 生成谐波指数的数量 (用于 ncx2)
            'ncx2_df': 非中心卡方分布的自由度 (默认 v=6)
            'ncx2_nc': 非中心卡方分布的非中心参数 (默认 lambda=32)
            'amp_chisq_df': 幅度卡方分布的自由度 (默认 v=16)

    Returns:
        np.ndarray: 时域机械噪声信号 S_mac(t)。
    """
    s_mac = np.zeros_like(t)
    num_sources = np.random.randint(params_mac.get('num_sources_range', (2, 7))[0],
                                    params_mac.get('num_sources_range', (2, 7))[1]) # randint upper bound is exclusive

    print(f"  模拟 {num_sources} 个机械噪声源...")

    for _ in range(num_sources):
        # 1. 确定基频
        f_fund = np.random.uniform(*params_mac.get('f_fund_range', (3, 10)))

        # 2. 确定谐波指数 (ζ) - 使用非中心卡方分布
        #    生成一组谐波指数候选值
        num_candidates = params_mac.get('num_harmonics_per_source', 50) # 每个源尝试生成多少个谐波
        zeta_candidates = np.round(stats.ncx2.rvs(df=params_mac.get('ncx2_df', 6),
                                                  nc=params_mac.get('ncx2_nc', 32),
                                                  size=num_candidates)).astype(int)
        zeta_candidates = np.unique(zeta_candidates[zeta_candidates > 0]) # 去重并取正

        # 3. 计算谐波频率和幅度
        for zeta in zeta_candidates:
            freq_k = zeta * f_fund
            # 检查频率是否在有效范围内 (0 到 Nyquist)
            if 0 < freq_k < fs / 2.0:
                # 幅度 A_k: 假设 A_k^2 ~ ChiSq(v), 则 A_k ~ sqrt(ChiSq(v))
                # 幅度也可能随谐波次数衰减，这里暂不考虑显式衰减，依赖 ChiSq 分布
                amp_k = np.sqrt(stats.chi2.rvs(df=params_mac.get('amp_chisq_df', 16)))
                # 随机相位
                phase_k = np.random.uniform(0, 2 * np.pi)
                # 添加正弦分量
                s_mac += amp_k * np.cos(2 * np.pi * freq_k * t + phase_k)

    # 初始幅度缩放：使 RMS 为 1，便于后续相对缩放
    s_mac = scale_signal_rms(s_mac, 1.0)
    return s_mac


# --- 3. 螺旋桨叶片速率线谱模拟 (S_prop(t)) ---
def simulate_propeller_noise(t, fs, n_blades, shaft_speed_rps, params_prop):
    """
    根据 Table 1 模拟螺旋桨叶片速率线谱噪声 S_prop(t)。

    Args:
        t (np.ndarray): 时间向量。
        fs (float): 采样频率 (Hz)。
        n_blades (int): 螺旋桨叶片数。
        shaft_speed_rps (float): 螺旋桨转速 (Hz)。
        params_prop (dict): 包含螺旋桨噪声参数的字典:
            'num_harmonics_candidates': 生成谐波指数的候选数量 (用于 Rayleigh)
            'rayleigh_scale': 瑞利分布的尺度参数 (sigma, sqrt(sigma^2=8))
            'amp_chisq_df': 幅度卡方分布的自由度 (默认 v=2)

    Returns:
        np.ndarray: 时域螺旋桨噪声信号 S_prop(t)。
    """
    s_prop = np.zeros_like(t)
    f_blade = n_blades * shaft_speed_rps # 叶片基频

    if f_blade <= 0 or f_blade >= fs / 2.0:
        print(f"  警告: 叶片频率 {f_blade:.2f} Hz 不可用。跳过螺旋桨线谱。")
        return s_prop

    # 1. 确定谐波指数 (ζ) - 使用舍入瑞利分布
    num_candidates = params_prop.get('num_harmonics_candidates', 100)
    rayleigh_scale = params_prop.get('rayleigh_scale', np.sqrt(8)) # sigma = sqrt(sigma^2)
    zeta_candidates = np.round(stats.rayleigh.rvs(scale=rayleigh_scale, size=num_candidates)).astype(int)
    zeta_values = np.unique(zeta_candidates[zeta_candidates > 0]) # 取正、去重

    print(f"  模拟螺旋桨线谱，基频 {f_blade:.2f} Hz, 使用 {len(zeta_values)} 个谐波...")

    # 2. 计算谐波频率和幅度
    for zeta in zeta_values:
        freq_k = zeta * f_blade
        # 检查频率
        if 0 < freq_k < fs / 2.0:
            # 幅度 A_k ~ sqrt(ChiSq(v))
            amp_k = np.sqrt(stats.chi2.rvs(df=params_prop.get('amp_chisq_df', 2)))
            # 随机相位
            phase_k = np.random.uniform(0, 2 * np.pi)
            # 添加分量
            s_prop += amp_k * np.cos(2 * np.pi * freq_k * t + phase_k)

    # 初始幅度缩放：使 RMS 为 1
    s_prop = scale_signal_rms(s_prop, 1.0)
    return s_prop


# --- 4. 调制线谱模拟 (S_modu(t)) ---
def simulate_modulation_noise(t, fs, n_blades, shaft_speed_rps, params_modu):
    """
    根据 Table 1 模拟调制线谱噪声 S_modu(t)。
    注意：这个 S_modu(t) 用于 [1 + S_modu(t)]，其幅度应相对较小。

    Args:
        t (np.ndarray): 时间向量。
        fs (float): 采样频率 (Hz)。
        n_blades (int): 螺旋桨叶片数 (可能用于幅度调整)。
        shaft_speed_rps (float): 螺旋桨转速 (Hz)。
        params_modu (dict): 包含调制噪声参数的字典:
            'num_harmonics_range': (min, max) 谐波数范围 (默认 8-16)
            'amp_chisq_df': 幅度卡方分布的自由度 (默认 v=2)
            'target_rms': S_modu 的目标 RMS 值 (控制调制深度, 默认 0.1)

    Returns:
        np.ndarray: 时域调制噪声信号 S_modu(t)。
    """
    s_modu = np.zeros_like(t)
    f_shaft = shaft_speed_rps # 调制基频 = 轴频

    if f_shaft <= 0 or f_shaft >= fs / 2.0:
        print(f"  警告: 轴频 {f_shaft:.2f} Hz 不可用。跳过调制线谱。")
        return s_modu

    # 1. 确定谐波数
    num_harmonics = np.random.randint(params_modu.get('num_harmonics_range', (8, 17))[0],
                                      params_modu.get('num_harmonics_range', (8, 17))[1])

    print(f"  模拟调制线谱，基频 {f_shaft:.2f} Hz, 使用 {num_harmonics} 个谐波...")

    # 2. 计算谐波频率和幅度
    for zeta in range(1, num_harmonics + 1):
        freq_k = zeta * f_shaft
        # 检查频率
        if 0 < freq_k < fs / 2.0:
            # 幅度 A_k ~ sqrt(ChiSq(v))
            # 论文提到在叶片频率整数倍处幅度更大，这里简化处理
            amp_k = np.sqrt(stats.chi2.rvs(df=params_modu.get('amp_chisq_df', 2)))
            # 随机相位
            phase_k = np.random.uniform(0, 2 * np.pi)
            # 添加分量
            s_modu += amp_k * np.cos(2 * np.pi * freq_k * t + phase_k)

    # 幅度缩放：将 s_modu 调整到所需的目标 RMS (控制调制强度)
    target_rms = params_modu.get('target_rms', 0.1) # 默认 RMS 为 0.1
    s_modu = scale_signal_rms(s_modu, target_rms)

    print(f"  调制信号 S_modu RMS: {np.sqrt(np.mean(s_modu**2)):.4f}")
    return s_modu


# --- 5. 主模拟函数 ---
def simulate_radiated_noise_detailed(
    # Continuous Spectrum Params
    f0, sl0_ref_db, a1, a2, # sl0_ref_db 是连续谱峰值的目标参考 dB 级
    # Propeller Params
    n_blades, shaft_speed_rpm, # 输入改为 RPM
    # Simulation Params
    fs=44100, duration=5.0,
    # Scaling Params (Relative dB levels compared to sl0_ref_db)
    mac_level_offset_db = -5.0,  # 机械噪声 RMS 相对连续谱峰值的 dB 偏移
    prop_level_offset_db = 0.0,  # 螺旋桨噪声 RMS 相对连续谱峰值的 dB 偏移
    # Component Parameters (using dicts for clarity)
    params_mac = {},
    params_prop = {},
    params_modu = {},
    # Optional Continuous Spectrum Params
    f_H=None,
    fir_filter_order=2049
    ):
    """
    根据公式(11)和Table 1详细模拟水下辐射噪声信号 S(t)。

    Args:
        f0, sl0_ref_db, a1, a2: 连续谱参数 (sl0_ref_db 是目标峰值dB)
        n_blades, shaft_speed_rpm: 螺旋桨参数 (转速单位 RPM)
        fs, duration: 模拟参数
        mac_level_offset_db: 机械噪声平均功率相对于连续谱峰值功率的dB偏移 (近似用RMS)
        prop_level_offset_db: 螺旋桨噪声平均功率相对于连续谱峰值功率的dB偏移 (近似用RMS)
        params_mac, params_prop, params_modu: 各分量的详细统计参数 (见各自函数)
        f_H, fir_filter_order: 连续谱滤波器参数

    Returns:
        tuple: (S, t, fs, component_signals)
            S (np.ndarray): 合成的辐射噪声时域信号。
            t (np.ndarray): 时间向量。
            fs (float): 采样频率。
            component_signals (dict): 包含各分量信号的字典。
    """
    num_samples = int(fs * duration)
    t = np.linspace(0, duration, num_samples, endpoint=False)
    shaft_speed_rps = shaft_speed_rpm / 60.0 # 转换为 Hz

    # --- 设定默认参数 (如果未提供) ---
    params_mac.setdefault('num_sources_range', (2, 7))
    params_mac.setdefault('f_fund_range', (3, 10))
    params_mac.setdefault('num_harmonics_per_source', 50)
    params_mac.setdefault('ncx2_df', 6)
    params_mac.setdefault('ncx2_nc', 32)
    params_mac.setdefault('amp_chisq_df', 16)

    params_prop.setdefault('num_harmonics_candidates', 100)
    params_prop.setdefault('rayleigh_scale', np.sqrt(8))
    params_prop.setdefault('amp_chisq_df', 2)

    params_modu.setdefault('num_harmonics_range', (8, 17))
    params_modu.setdefault('amp_chisq_df', 2)
    params_modu.setdefault('target_rms', 0.1) # 控制调制深度

    # Set default f_H
    if f_H is None:
        f_H = fs / 2.0

    # --- 1. 模拟各分量 (初始 RMS=1 或目标 RMS) ---
    print("模拟连续谱 S_conti(t)...")
    # 注意：sl0_ref_db 仅作为参考，这里模拟出的 s_conti RMS 为 1
    s_conti = simulate_continuous_noise(t, fs, f0, sl0_ref_db, a1, a2, f_H, filter_order=fir_filter_order)

    print("模拟机械线谱 S_mac(t)...")
    s_mac = simulate_machinery_noise(t, fs, params_mac)

    print("模拟螺旋桨线谱 S_prop(t)...")
    s_prop = simulate_propeller_noise(t, fs, n_blades, shaft_speed_rps, params_prop)

    print("模拟调制线谱 S_modu(t)...")
    s_modu = simulate_modulation_noise(t, fs, n_blades, shaft_speed_rps, params_modu)

    # --- 2. 相对幅度缩放 ---
    # 目标：调整 s_conti, s_mac, s_prop 的 RMS，使其功率谱密度 (PSD) 的相对水平
    #       大致符合预期 (基于 sl0_ref_db 和 offset)。
    # 这是一个简化方法，精确匹配需要迭代或更复杂的校准。

    # a) 估算连续谱在峰值 f0 处的 PSD 值 (作为参考)
    #    我们不能直接从 sl0_ref_db 设置 PSD，因为模拟的 s_conti RMS=1。
    #    先计算 RMS=1 的 s_conti 的 PSD 峰值。
    freqs_conti, psd_conti = calculate_psd(s_conti, fs, nfft=fir_filter_order-1) # Use filter order for NFFT
    peak_psd_conti = np.max(psd_conti) # 近似峰值 PSD (线性)
    if peak_psd_conti < 1e-15: peak_psd_conti = 1e-15 # 避免零
    #print(f"  初始 S_conti 峰值 PSD (线性): {peak_psd_conti:.2e}")

    # b) 计算各分量所需的目标 RMS，以匹配相对 dB 偏移
    #    目标线性功率 = 参考功率 * 10^(dB_offset / 10)
    #    目标 RMS = sqrt(参考 RMS^2 * 10^(dB_offset / 10))
    #    这里假设 RMS^2 与 PSD 峰值成正比 (粗略假设)
    #    并且我们以 s_conti (RMS=1) 作为基准，其“等效”峰值功率为 peak_psd_conti

    # 目标连续谱 RMS (使其峰值 PSD "代表" sl0_ref_db - 这是最难精确的部分)
    # 简化：我们先保持 s_conti RMS=1，并以此为基础缩放其他分量。
    # 或者，设定一个目标总信号 RMS？
    # 让我们尝试根据 dB 偏移设置 mac 和 prop 相对于 conti 的 RMS。

    target_rms_conti = 1.0 # 固定连续谱 RMS 为 1 作为参考

    # 目标机械噪声 RMS
    target_rms_mac = target_rms_conti * 10**(mac_level_offset_db / 20.0)
    s_mac_scaled = scale_signal_rms(s_mac, target_rms_mac)
    print(f"  缩放 S_mac 至 RMS: {np.sqrt(np.mean(s_mac_scaled**2)):.4f} (目标 RMS: {target_rms_mac:.4f})")


    # 目标螺旋桨噪声 RMS
    target_rms_prop = target_rms_conti * 10**(prop_level_offset_db / 20.0)
    s_prop_scaled = scale_signal_rms(s_prop, target_rms_prop)
    print(f"  缩放 S_prop 至 RMS: {np.sqrt(np.mean(s_prop_scaled**2)):.4f} (目标 RMS: {target_rms_prop:.4f})")

    # s_modu 已在生成时设置了目标 RMS
    s_modu_scaled = s_modu

    # s_conti 保持 RMS=1
    s_conti_scaled = s_conti

    # --- 3. 合成信号 (公式 11) ---
    print("合成最终信号 S(t)...")
    S = (1 + s_modu_scaled) * s_conti_scaled + s_mac_scaled + s_prop_scaled

    # --- 4. (可选) 最终整体缩放 ---
    #    如果需要使最终信号的整体功率谱达到某个绝对水平 (如峰值在 sl0_ref_db 附近)
    #    需要计算 S 的 PSD，找到峰值，然后缩放 S。
    # freqs_S, psd_S = calculate_psd(S, fs, nfft=2048)
    # current_peak_db = linear_to_db(np.max(psd_S)) # 当前峰值 dB (相对某个任意参考)
    # desired_peak_db = sl0_ref_db # 目标峰值 dB
    # scale_db = desired_peak_db - current_peak_db
    # scale_linear = 10**(scale_db / 20.0)
    # S_final = S * scale_linear
    # print(f"  最终信号整体缩放因子: {scale_linear:.4f}")
    # S = S_final # 应用最终缩放

    print("模拟完成。")

    component_signals = {
        'conti': s_conti_scaled,
        'mac': s_mac_scaled,
        'prop': s_prop_scaled,
        'modu': s_modu_scaled
    }

    return S, t, fs, component_signals

# --- 示例用法 ---
if __name__ == "__main__":
    # --- 输入参数 ---
    # 连续谱
    param_f0 = 249      # 峰值频率 (Hz) - 根据 Fig 4 调整
    param_sl0_ref = 130.16 # 目标峰值声级 (dB) - 根据 Fig 4
    param_a1 = 3        # 低频上升斜率 (dB/Octave) - 估计值
    param_a2 = -4.236        # 高频下降斜率 (dB/Octave) - 估计值

    # 螺旋桨
    param_n_blades = 7        # 叶片数 (示例)
    param_shaft_speed_rpm = 95 # 轴转速 (RPM) - 对应 Fig 3 调制谱基频 N/60 = 1.59 Hz

    # 模拟设置
    param_fs = 8192      # 采样率 (Hz) - 降低以便快速测试，需 >= 2*最高频率
    param_duration = 2.0    # 时长 (秒)

    # 相对声级设置 (相对于连续谱峰值 sl0_ref)
    param_mac_offset = -10 # 机械噪声 RMS 比连续谱峰值低 10 dB
    param_prop_offset = -5  # 螺旋桨噪声 RMS 比连续谱峰值低 5 dB

    # 各分量详细参数 (可省略使用默认值)
    custom_params_mac = {
        'num_sources_range': (3, 6), # 3-5 个源
        'f_fund_range': (5, 9),     # 基频 5-9 Hz
        'amp_chisq_df': 10,         # 降低幅度方差
    }
    custom_params_prop = {
        'rayleigh_scale': np.sqrt(6), # 调整谐波分布
        'amp_chisq_df': 1.5,        # 调整幅度分布
    }
    custom_params_modu = {
        'num_harmonics_range': (10, 15), # 10-14 个谐波
        'target_rms': 0.15,            # 调制深度稍大
    }

    # --- 运行模拟 ---
    S, t, fs_out, components = simulate_radiated_noise_detailed(
        f0=param_f0, sl0_ref_db=param_sl0_ref, a1=param_a1, a2=param_a2,
        n_blades=param_n_blades, shaft_speed_rpm=param_shaft_speed_rpm,
        fs=param_fs, duration=param_duration,
        mac_level_offset_db=param_mac_offset,
        prop_level_offset_db=param_prop_offset,
        params_mac=custom_params_mac,
        params_prop=custom_params_prop,
        params_modu=custom_params_modu,
        fir_filter_order=1025 # 滤波器阶数
    )

    # --- 分析与可视化 ---
    print(f"生成 {len(S)} 个样本 ({param_duration}s @ {fs_out} Hz)")

    plt.figure(figsize=(12, 10))

    # 1. 绘制合成信号时域波形 (前 1 秒)
    plt.subplot(3, 1, 1)
    samples_1sec = int(fs_out * 1.0)
    plt.plot(t[:samples_1sec], S[:samples_1sec])
    plt.title(f"合成辐射噪声 S(t) (前 1 秒), RMS={np.sqrt(np.mean(S**2)):.3f}")
    plt.xlabel("时间 (s)")
    plt.ylabel("幅度 (相对)")
    plt.grid(True)

    # 2. 绘制合成信号功率谱密度 (PSD) - 对数频率轴
    plt.subplot(3, 1, 2)
    nfft = 2048
    freqs, psd = calculate_psd(S, fs_out, nfft=nfft)
    psd_db = 10 * np.log10(psd) # dB scale
    # 基于 sl0_ref 进行近似校准 (将计算得到的 PSD 峰值对齐到 sl0_ref)
    psd_peak_db_calc = np.max(psd_db)
    db_calibration_offset = param_sl0_ref - psd_peak_db_calc
    psd_db_calibrated = psd_db + db_calibration_offset

    plt.semilogx(freqs, psd_db_calibrated) # 使用对数频率轴更符合水声习惯
    plt.title("合成信号功率谱密度 (PSD - 近似校准至目标峰值)")
    plt.xlabel("频率 (Hz)")
    plt.ylabel("PSD (dB / Hz, 任意参考)")
    plt.grid(True, which='both') # 对数轴用 'both'
    plt.xlim(1, fs_out / 2)
    plt.ylim(param_sl0_ref - 50, param_sl0_ref + 10) # 根据 sl0_ref 设置范围

    # 绘制预期线谱位置
    shaft_rps = param_shaft_speed_rpm / 60.0
    blade_freq = param_n_blades * shaft_rps
    for k in range(1, 11): # 绘制前10个叶片谐波
         line_f = k*blade_freq
         if line_f < fs_out/2:
              plt.axvline(line_f, color='r', linestyle='--', alpha=0.5, lw=0.8, label='叶频谐波' if k==1 else None)
    if blade_freq < fs_out/2: plt.legend(fontsize=8)

    # 3. 绘制各分量 PSD (对比)
    plt.subplot(3, 1, 3)
    styles = {'conti': '-', 'mac': '--', 'prop': ':', 'modu': '-.'}
    colors = {'conti': 'blue', 'mac': 'green', 'prop': 'red', 'modu': 'purple'}
    for name, comp_signal in components.items():
         if np.any(comp_signal): # 检查信号是否全零
            freqs_comp, psd_comp = calculate_psd(comp_signal, fs_out, nfft=nfft)
            psd_comp_db = 10 * np.log10(psd_comp) + db_calibration_offset # 应用相同校准偏移
            plt.semilogx(freqs_comp, psd_comp_db, label=f'{name} component', linestyle=styles[name], color=colors[name], alpha=0.8)

    plt.title("各分量功率谱密度 (PSD - 近似校准)")
    plt.xlabel("频率 (Hz)")
    plt.ylabel("PSD (dB / Hz)")
    plt.grid(True, which='both')
    plt.legend(fontsize=8)
    plt.xlim(1, fs_out / 2)
    plt.ylim(param_sl0_ref - 70, param_sl0_ref + 10)

    plt.tight_layout()
    plt.show()

    # 可选: 播放声音
    # import sounddevice as sd
    # print("播放声音...")
    # S_norm = S / np.max(np.abs(S)) * 0.8 # 归一化防止削波
    # sd.play(S_norm, int(fs_out))
    # sd.wait()
    # print("播放结束.")