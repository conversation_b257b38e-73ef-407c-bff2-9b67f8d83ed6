import numpy as np
from scipy import signal
import matplotlib.pyplot as plt

# Helper function to define the continuous spectrum shape (Equation 2)
# Note: Slope parameters a1, a2 are in dB/Octave
def target_continuous_spectrum_level(freq, f0, sl0, a1, a2, f_H):
    """
    Calculates the target Sound Level (SL) in dB at a given frequency.
    Based on Equation (2).

    Args:
        freq (float or np.ndarray): Frequency/frequencies in Hz.
        f0 (float): Peak frequency in Hz.
        sl0 (float): Peak sound level in dB.
        a1 (float): Upward slope below f0 in dB/Octave.
        a2 (float): Downward slope above f0 in dB/Octave.
        f_H (float): High-frequency limit for the spectrum model.

    Returns:
        float or np.ndarray: Sound level(s) in dB.
    """
    freq = np.asarray(freq)
    sl = np.zeros_like(freq, dtype=float)

    # Ensure f is positive for log calculation
    # Use small epsilon or handle f=0 appropriately
    epsilon = 1e-9 
    
    # Below f0
    mask1 = (freq > epsilon) & (freq < f0)
    sl[mask1] = sl0 - a1 * np.log10(f0 / freq[mask1]) # SL = SL0 - a1 * log10(f0/f) -> SL0 + a1*log10(f/f0)

    # At f0
    mask2 = np.isclose(freq, f0)
    sl[mask2] = sl0

    # Above f0 up to f_H
    mask3 = (freq > f0) & (freq <= f_H)
    sl[mask3] = sl0 + a2 * np.log10(freq[mask3] / f0) # SL = SL0 + a2 * log10(f/f0)

    # Very low frequency (e.g., below where slope applies) or above f_H - set to a low value or extrapolate?
    # For simplicity, let's assume it drops sharply or is negligible outside [epsilon, f_H]
    # Or potentially set lowest value based on slope a1 down to a minimum freq
    # Or set highest value based on slope a2 up to fs/2?
    # For now, set to very low dB outside the defined range [epsilon, f_H]
    mask_outside = (freq <= epsilon) | (freq > f_H)
    sl[mask_outside] = -200 # Effectively zero in linear scale

    return sl

# 1. Simulate Continuous Spectrum Noise (g_X(t))
def simulate_continuous_noise(t, fs, f0, sl0, a1, a2, f_H, filter_order=1025):
    """
    Simulates the continuous spectrum noise component g_X(t).

    Args:
        t (np.ndarray): Time vector.
        fs (float): Sampling frequency in Hz.
        f0, sl0, a1, a2: Parameters for the target continuous spectrum (Eq. 2).
        f_H (float): High-frequency limit for the spectrum model.
        filter_order (int): Order of the FIR filter. Should be odd.

    Returns:
        np.ndarray: Time-domain continuous noise signal g_X(t).
    """
    num_samples = len(t)
    nyquist = fs / 2.0

    # --- Define target spectrum for FIR filter design ---
    # Need frequencies from 0 to nyquist for firwin2
    # Create a reasonable number of frequency points, denser around f0?
    # Let's use a linear spacing for simplicity first.
    # firwin2 requires frequencies normalized to nyquist
    # TODO: 这里频率点的分布不是按照fo向两侧倍频程取样的
    num_freq_points = filter_order * 2 # More points for better resolution
    freq_points_hz = np.linspace(0, nyquist, num_freq_points)
    
    # Get target SL in dB
    sl_db = target_continuous_spectrum_level(freq_points_hz, f0, sl0, a1, a2, f_H)

    # Convert SL (dB) to linear magnitude for the filter design
    # Target Magnitude = 10^(SL/20). Normalize?
    # firwin2 designs based on amplitude response.
    # The absolute scaling needs care. Let's focus on the shape first.
    target_magnitude = 10**(sl_db / 20.0)
    
    # Normalize magnitude
    # TODO: 幅度需要归一化

    # target_magnitude /= np.max(target_magnitude) # Or use SL0 as reference?

    # Handle 0 Hz and Nyquist frequency carefully if needed
    target_magnitude[freq_points_hz == 0] = 0 # Often helps stability

    # --- Design FIR filter using frequency sampling (firwin2) ---
    # firwin2 needs normalized frequencies (0 to 1, where 1=nyquist)
    normalized_freq_points = freq_points_hz / nyquist
    
    # Ensure filter order is odd for Type I filter (zero-phase possible if centered)
    if filter_order % 2 == 0:
        filter_order += 1
        
    try:
        fir_coeffs = signal.firwin2(filter_order, normalized_freq_points, target_magnitude)
    except ValueError as e:
        print(f"Error designing FIR filter: {e}")
        print("Frequency points:", normalized_freq_points)
        print("Magnitudes:", target_magnitude)
        # Add fallbacks or adjustments if needed
        # Maybe reduce order, ensure monotonicity if required by algorithm version?
        # Let's try ensuring magnitude is non-negative
        target_magnitude[target_magnitude < 0] = 0
        try:
             fir_coeffs = signal.firwin2(filter_order, normalized_freq_points, target_magnitude)
        except Exception as final_e:
             print(f"FIR Filter design failed definitively: {final_e}")
             return np.zeros_like(t) # Return silence on failure


    # --- Generate white noise and filter it ---
    white_noise = np.random.randn(num_samples)
    # TODO：确认一下这个函数的参数
    g_X = signal.lfilter(fir_coeffs, 1.0, white_noise)

    # Optional: Scale g_X to have a certain RMS or power if needed
    # Scaling is complex to match dB levels exactly without reference pressure.
    # Let's return the shaped noise. Its spectrum *shape* should match.

    return g_X


# 2. Simulate Line Spectrum Noise (g_L(t))
def simulate_line_spectrum(t, fs, n_blades, shaft_speed_rps, 
                           num_harmonics=5, line_dB_offset=15, 
                           continuous_spectrum_params=None):
    """
    Simulates the line spectrum noise component g_L(t).

    Args:
        t (np.ndarray): Time vector.
        fs (float): Sampling frequency in Hz.
        n_blades (int): Number of propeller blades.
        shaft_speed_rps (float): Propeller shaft speed in revolutions per second (Hz).
        num_harmonics (int): Number of blade frequency harmonics to include.
        line_dB_offset (float): dB level of lines above continuous spectrum at same freq.
        continuous_spectrum_params (dict): Parameters {f0, sl0, a1, a2, f_H} to find
                                           the continuous level for amplitude reference. 
                                           If None, use arbitrary amplitude scaling.

    Returns:
        np.ndarray: Time-domain line spectrum signal g_L(t).
    """
    blade_freq = n_blades * shaft_speed_rps
    g_L = np.zeros_like(t)

    if blade_freq <= 0 or blade_freq >= fs / 2:
         print(f"Warning: Blade frequency {blade_freq:.2f} Hz is zero or too high for fs={fs}. Skipping line spectrum.")
         return g_L

    for k in range(1, num_harmonics + 1):
        freq_k = k * blade_freq
        
        # Ensure frequency is below Nyquist
        if freq_k >= fs / 2.0:
            break # Stop adding harmonics if they exceed Nyquist

        # Determine amplitude A_k
        # Based on text: Line spectrum is 10-25 dB above continuous spectrum
        if continuous_spectrum_params:
            sl_continuous_at_fk = target_continuous_spectrum_level(freq_k, **continuous_spectrum_params)
            sl_line_k = sl_continuous_at_fk + line_dB_offset
            # Convert dB SL to linear amplitude - needs reference, tricky.
            # Simplification: Assume amplitude is proportional to 10^(SL/20)
            # Relative amplitude might be sufficient. Let's scale arbitrarily for now.
            # Assume continuous spectrum peak (sl0) corresponds roughly to an amplitude of 1.
            # Adjust this scaling factor based on observations or requirements.
            amplitude_k = 10**(sl_line_k / 20.0) / 10**(continuous_spectrum_params['sl0'] / 20.0) # Relative amplitude scaling
            # Add small safety factor in case sl0 is very low
            amplitude_k = amplitude_k * 0.5 # Example scaling factor - ADJUST AS NEEDED
        else:
            # Arbitrary amplitude if continuous spectrum params not provided
            # Decay amplitude for higher harmonics?
            amplitude_k = 0.1 / k # Simple decaying amplitude

        # Random phase
        phase_k = np.random.rand() * 2 * np.pi

        # Add sinusoid component
        g_L += amplitude_k * np.sin(2 * np.pi * freq_k * t + phase_k)

    return g_L


# 3. Simulate Modulation Signal (a(t))
def simulate_modulation_signal(t, fs, n_blades, shaft_speed_rps, 
                               N_shaft_harmonics=3, M_blade_harmonics=3,
                               A1_mod=0.05, A2_mod=0.1, p_decay=0.1, q_decay=0.1):
    """
    Simulates the modulation signal a(t) based on Equation (4).

    Args:
        t (np.ndarray): Time vector.
        fs (float): Sampling frequency in Hz.
        n_blades (int): Number of propeller blades.
        shaft_speed_rps (float): Propeller shaft speed in revolutions per second (Hz).
        N_shaft_harmonics (int): Number of shaft harmonics (k for f1).
        M_blade_harmonics (int): Number of blade harmonics (i for f2).
        A1_mod (float): Amplitude factor for shaft freq components.
        A2_mod (float): Amplitude factor for blade freq components.
        p_decay (float): Decay factor for shaft freq components.
        q_decay (float): Decay factor for blade freq components.

    Returns:
        np.ndarray: Time-domain modulation signal a(t).
    """
    f_shaft = shaft_speed_rps # f1 in Eq (4)
    f_blade = n_blades * shaft_speed_rps # f2 in Eq (4)
    a_t = np.zeros_like(t)

    # --- Shaft Frequency Harmonics Term ---
    if f_shaft > 0 and f_shaft < fs / 2:
        for k in range(1, N_shaft_harmonics + 1):
            freq_k = k * f_shaft
            if freq_k >= fs / 2.0:
                break
            # Amplitude A1e * exp(-pk) - Using constant A1_mod for A1e for simplicity
            amplitude_k = A1_mod * np.exp(-p_decay * k)
            phase_k = np.random.rand() * 2 * np.pi # phi_1
            a_t += amplitude_k * np.sin(2 * np.pi * freq_k * t + phase_k)

    # --- Blade Frequency Harmonics Term ---
    if f_blade > 0 and f_blade < fs / 2:
         for i in range(1, M_blade_harmonics + 1):
            freq_i = i * f_blade
            if freq_i >= fs / 2.0:
                break
            # Amplitude A2i * exp(-qi) - Using constant A2_mod for A2i for simplicity
            amplitude_i = A2_mod * np.exp(-q_decay * i)
            phase_i = np.random.rand() * 2 * np.pi # phi_2
            a_t += amplitude_i * np.sin(2 * np.pi * freq_i * t + phase_i)

    # Ensure amplitude of a(t) is reasonable (e.g., mostly < 1)
    # print(f"Modulation signal peak: {np.max(np.abs(a_t))}") 

    return a_t


# --- Main Simulation Function ---
def simulate_radiated_noise(
    # Continuous Spectrum Params (Eq 2)
    f0, sl0, a1, a2, 
    # Propeller Params
    n_blades, shaft_speed_rps,
    # Simulation Params
    fs=44100, duration=5.0,
    # Optional Continuous Spectrum Params
    f_H=None, # If None, set based on fs
    fir_filter_order=2049,
    # Optional Line Spectrum Params
    line_num_harmonics=4,
    line_dB_offset=18.0,
    # Optional Modulation Params
    mod_N_shaft_harmonics=3,
    mod_M_blade_harmonics=3,
    mod_A1=0.05, mod_A2=0.1, 
    mod_p=0.1, mod_q=0.1,
    # Control flags
    include_line_spectrum=True,
    include_modulation=True
    ):
    """
    Simulates the underwater radiated noise signal S(t) based on the paper's model.

    Args:
        f0 (float): Peak frequency for continuous spectrum (Hz).
        sl0 (float): Peak sound level for continuous spectrum (dB).
        a1 (float): Upward slope below f0 (dB/Octave).
        a2 (float): Downward slope above f0 (dB/Octave).
        n_blades (int): Number of propeller blades.
        shaft_speed_rps (float): Propeller shaft speed (revs per second, Hz).
        fs (float): Sampling frequency (Hz). Default: 44100.
        duration (float): Signal duration (seconds). Default: 5.0.
        f_H (float, optional): High-frequency limit for continuous spectrum model (Hz). 
                               Defaults to fs/2.5 (leaving some room before Nyquist).
        fir_filter_order (int, optional): Order for the FIR filter. Default: 2049.
        line_num_harmonics (int, optional): Number of blade harmonics for line spectrum. Default: 4.
        line_dB_offset (float, optional): dB offset for line spectrum above continuous. Default: 18.0.
        mod_N_shaft_harmonics (int, optional): Number of shaft harmonics for modulation. Default: 3.
        mod_M_blade_harmonics (int, optional): Number of blade harmonics for modulation. Default: 3.
        mod_A1 (float, optional): Amplitude factor for shaft modulation terms. Default: 0.05.
        mod_A2 (float, optional): Amplitude factor for blade modulation terms. Default: 0.1.
        mod_p (float, optional): Decay factor for shaft modulation terms. Default: 0.1.
        mod_q (float, optional): Decay factor for blade modulation terms. Default: 0.1.
        include_line_spectrum (bool, optional): Whether to include g_L(t). Default: True.
        include_modulation (bool, optional): Whether to include a(t) modulation. Default: True.


    Returns:
        tuple: (S, t, fs)
            S (np.ndarray): The simulated radiated noise time-domain signal.
            t (np.ndarray): The time vector.
            fs (float): The sampling frequency used.
    """
    num_samples = int(fs * duration)
    t = np.linspace(0, duration, num_samples, endpoint=False)

    # Set default f_H if not provided
    if f_H is None:
        f_H = fs / 2.5 # Example: limit spectrum model well below Nyquist

    # Store continuous spectrum parameters for line spectrum calculation
    continuous_spectrum_params = {'f0': f0, 'sl0': sl0, 'a1': a1, 'a2': a2, 'f_H': f_H}

    # --- Simulate Components ---
    print("Simulating continuous spectrum g_X(t)...")
    g_X = simulate_continuous_noise(t, fs, f0, sl0, a1, a2, f_H, filter_order=fir_filter_order)

    if include_line_spectrum:
        print("Simulating line spectrum g_L(t)...")
        g_L = simulate_line_spectrum(t, fs, n_blades, shaft_speed_rps, 
                                     num_harmonics=line_num_harmonics, 
                                     line_dB_offset=line_dB_offset,
                                     continuous_spectrum_params=continuous_spectrum_params)
    else:
        g_L = np.zeros_like(t)

    if include_modulation:
        print("Simulating modulation signal a(t)...")
        a = simulate_modulation_signal(t, fs, n_blades, shaft_speed_rps,
                                      N_shaft_harmonics=mod_N_shaft_harmonics, 
                                      M_blade_harmonics=mod_M_blade_harmonics,
                                      A1_mod=mod_A1, A2_mod=mod_A2, 
                                      p_decay=mod_p, q_decay=mod_q)
    else:
        a = np.zeros_like(t)

    # --- Combine Components (Equation 1) ---
    print("Combining components...")
    S = (1 + a) * g_X + g_L

    print("Simulation complete.")
    return S, t, fs

# --- Example Usage ---
if __name__ == "__main__":
    # Example Parameters (adjust these based on a specific scenario)
    
    # Continuous Spectrum (Example values)
    param_f0 = 250     # Peak frequency (Hz)
    param_sl0 = 130    # Peak level (dB re 1uPa or arbitrary reference)
    param_a1 = 3       # Upward slope (dB/Octave) -> low freq slope
    param_a2 = -4       # Downward slope (dB/Octave) -> high freq slope (-6 dB/Oct)
    
    # Propeller (Example values)
    param_n_blades = 4      # Number of blades
    param_shaft_speed_rps = 4.08 # Shaft speed in Hz (e.g., 180 RPM / 60)
    
    # Simulation settings
    param_fs = 8000     # Sampling frequency (Hz) - Use lower fs for faster testing
    param_duration = 5.0   # Duration (seconds)

    # Run simulation
    S, t, fs_out = simulate_radiated_noise(
        f0=param_f0, sl0=param_sl0, a1=param_a1, a2=param_a2,
        n_blades=param_n_blades, shaft_speed_rps=param_shaft_speed_rps,
        fs=param_fs, duration=param_duration,
        # Optional: Adjust f_H, line/mod params if needed
        # f_H = 3000, 
        line_dB_offset=20, # Make lines more prominent
        # mod_A1=0.1, mod_A2=0.15
    )

    # --- Basic Analysis/Visualization ---
    print(f"Generated {len(S)} samples ({param_duration}s at {fs_out} Hz)")
    
    # 1. Plot time series (first second)
    plt.figure(figsize=(12, 8))
    
    plt.subplot(3, 1, 1)
    samples_1sec = int(fs_out * 1.0)
    plt.plot(t[:samples_1sec], S[:samples_1sec])
    plt.title("Simulated Radiated Noise S(t) (First Second)")
    plt.xlabel("Time (s)")
    plt.ylabel("Amplitude")
    plt.grid(True)

    # 2. Plot Power Spectral Density (PSD)
    plt.subplot(3, 1, 2)
    nfft = 2048 # Adjust FFT length as needed
    freqs, psd = signal.welch(S, fs_out, nperseg=nfft, scaling='density')
    psd_db = 10 * np.log10(psd + 1e-12) # Convert to dB, add epsilon for log(0)
    
    plt.semilogy(freqs, psd) # Log scale for frequency often useful
    plt.title("Power Spectral Density (Welch's Method)")
    plt.xlabel("Frequency (Hz)")
    plt.ylabel("PSD (Linear Scale)")
    # plt.xlim(1, fs_out / 2) # Focus on relevant frequency range
    plt.grid(True)

    # Plot PSD in dB
    plt.subplot(3, 1, 3)
    plt.plot(freqs, psd_db) 
    plt.title("Power Spectral Density (dB Scale)")
    plt.xlabel("Frequency (Hz)")
    plt.ylabel("PSD (dB arbitrary ref)")
    plt.ylim(np.max(psd_db)-80, np.max(psd_db)+5) # Adjust y-lim to see details
    # Plot expected line frequencies
    blade_freq = param_n_blades * param_shaft_speed_rps
    for k in range(1, 6):
         line_f = k*blade_freq
         if line_f < fs_out/2:
              plt.axvline(line_f, color='r', linestyle='--', alpha=0.7, label=f'Line {k*blade_freq:.1f} Hz' if k==1 else None)
    if blade_freq < fs_out/2: plt.legend()
    plt.grid(True)


    plt.tight_layout()
    plt.show()

    # Optional: Listen to the sound (if you have sounddevice/pyaudio)
    # import sounddevice as sd
    # print("Playing sound...")
    # # Normalize signal to avoid clipping
    # S_norm = S / np.max(np.abs(S)) * 0.8 
    # sd.play(S_norm, fs_out)
    # sd.wait()
    # print("Playback finished.")