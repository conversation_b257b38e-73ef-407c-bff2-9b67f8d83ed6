# -*- coding: utf-8 -*-
"""
仿真主控制器

协调各个专门控制器的工作，管理整体仿真流程，为主窗口提供高级接口。
"""

from PyQt5.QtCore import QObject, pyqtSignal

from src.core.controllers.ship_noise_controller import ShipNoiseController
from src.core.controllers.ambient_noise_controller import AmbientNoiseController
from src.core.controllers.propagation_controller import PropagationController
from src.core.controllers.integrated_controller import IntegratedController


class SimulationController(QObject):
    """
    仿真主控制器

    协调各个专门控制器的工作，管理整体仿真流程，为主窗口提供高级接口。
    """

    # 信号定义
    simulation_started = pyqtSignal(str)  # 仿真开始信号，参数为模块名称
    simulation_progress = pyqtSignal(str, int)  # 仿真进度信号，参数为模块名称和进度百分比
    simulation_completed = pyqtSignal(str)  # 仿真完成信号，参数为模块名称
    simulation_error = pyqtSignal(str, str)  # 仿真错误信号，参数为模块名称和错误信息

    def __init__(self, data_manager):
        """
        初始化仿真主控制器

        Args:
            data_manager: 数据管理器实例
        """
        super().__init__()

        # 保存数据管理器引用
        self.data_manager = data_manager

        # 创建专门控制器
        self.ship_noise_controller = ShipNoiseController(self.data_manager)
        self.ambient_noise_controller = AmbientNoiseController(self.data_manager)
        self.propagation_controller = PropagationController(self.data_manager)
        self.integrated_controller = IntegratedController(self.data_manager)

        # 连接船舶辐射噪声控制器的信号
        self.ship_noise_controller.simulation_started.connect(
            lambda: self.simulation_started.emit('ship_noise'))
        self.ship_noise_controller.simulation_progress.connect(
            lambda progress: self.simulation_progress.emit('ship_noise', progress))
        self.ship_noise_controller.simulation_completed.connect(
            lambda: self.simulation_completed.emit('ship_noise'))
        self.ship_noise_controller.simulation_error.connect(
            lambda error: self.simulation_error.emit('ship_noise', error))

        # 连接海洋环境噪声控制器的信号
        self.ambient_noise_controller.simulation_started.connect(
            lambda: self.simulation_started.emit('ambient_noise'))
        self.ambient_noise_controller.simulation_progress.connect(
            lambda progress: self.simulation_progress.emit('ambient_noise', progress))
        self.ambient_noise_controller.simulation_completed.connect(
            lambda: self.simulation_completed.emit('ambient_noise'))
        self.ambient_noise_controller.simulation_error.connect(
            lambda error: self.simulation_error.emit('ambient_noise', error))

        # 连接声传播控制器的信号
        self.propagation_controller.simulation_started.connect(
            lambda module: self.simulation_started.emit(module))
        self.propagation_controller.simulation_progress.connect(
            lambda module, progress: self.simulation_progress.emit(module, progress))
        self.propagation_controller.simulation_completed.connect(
            lambda module: self.simulation_completed.emit(module))
        self.propagation_controller.simulation_error.connect(
            lambda module, error: self.simulation_error.emit(module, error))

        # 连接综合仿真控制器的信号
        self.integrated_controller.simulation_started.connect(
            lambda module: self.simulation_started.emit(module))
        self.integrated_controller.simulation_progress.connect(
            lambda module, progress: self.simulation_progress.emit(module, progress))
        self.integrated_controller.simulation_completed.connect(
            lambda module: self.simulation_completed.emit(module))
        self.integrated_controller.simulation_error.connect(
            lambda module, error: self.simulation_error.emit(module, error))

    def get_data_manager(self):
        """
        获取数据管理器实例

        Returns:
            SimulationDataManager: 数据管理器实例
        """
        return self.data_manager

    def simulate_ship_noise(self):
        """
        执行船舶辐射噪声仿真
        """
        # 检查是否已经在运行
        if self.ship_noise_controller.is_simulation_running():
            return

        # 启动仿真线程
        self.ship_noise_controller.start()

    def cancel_ship_noise_simulation(self):
        """
        取消船舶辐射噪声仿真
        """
        self.ship_noise_controller.cancel()

    def simulate_ambient_noise(self):
        """
        执行海洋环境噪声仿真
        """
        # 检查是否已经在运行
        if self.ambient_noise_controller.is_simulation_running():
            return

        # 启动仿真线程
        self.ambient_noise_controller.start()

    def cancel_ambient_noise_simulation(self):
        """
        取消海洋环境噪声仿真
        """
        self.ambient_noise_controller.cancel()

    def simulate_propagation(self):
        """
        执行声传播仿真
        """
        # 检查是否已经在运行
        if self.propagation_controller.is_simulation_running():
            return

        # 启动仿真线程
        self.propagation_controller.start()

    def cancel_propagation_simulation(self):
        """
        取消声传播仿真
        """
        self.propagation_controller.cancel()

    def simulate_integrated(self, computation_type='channel_data_analysis'):
        """
        执行综合仿真

        Args:
            computation_type (str): 计算类型，如'channel_data_analysis', 'cross_spectral_density'等
        """
        # 检查是否已经在运行
        if self.integrated_controller.is_simulation_running():
            return

        # 更新计算类型
        self.data_manager.update_parameter('integrated', 'computation_type', computation_type)

        # 启动仿真线程
        self.integrated_controller.start()

    def cancel_integrated_simulation(self):
        """
        取消综合仿真
        """
        self.integrated_controller.cancel()

    def cancel_simulation(self, module):
        """
        取消指定模块的仿真

        Args:
            module (str): 模块名称，如'ship_noise', 'ambient_noise', 'propagation', 'integrated'
        """
        if module == 'ship_noise':
            self.cancel_ship_noise_simulation()
        elif module == 'ambient_noise':
            self.cancel_ambient_noise_simulation()
        elif module == 'propagation':
            self.cancel_propagation_simulation()
        elif module == 'integrated':
            self.cancel_integrated_simulation()
        else:
            print(f"未知的模块名称: {module}")

    def load_channel_data(self, channel_data_dir):
        """
        加载信道数据（非线程方式）

        Args:
            channel_data_dir (str): 信道数据目录

        Returns:
            bool: 是否成功加载数据
        """
        return self.integrated_controller.load_channel_data(channel_data_dir)

    # 项目管理相关方法已移除，直接使用ProjectManager
