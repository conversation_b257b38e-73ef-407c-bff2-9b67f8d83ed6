# 船舶辐射噪声仿真数据流文档

本文档详细描述了船舶辐射噪声仿真过程中的数据流向，作为其他模块开发的参考模板。

## 1. 数据流概述

船舶辐射噪声仿真的数据流可以分为以下几个阶段：

1. **参数设置阶段**：用户在UI中设置参数，参数从UI流向数据管理器
2. **仿真执行阶段**：控制器从数据管理器获取参数，执行仿真，将结果存储到数据管理器
3. **结果显示阶段**：视图更新器从数据管理器获取结果，更新视图
4. **项目保存阶段**：项目管理器从数据管理器获取参数和结果，保存到文件

## 2. 详细数据流

### 2.1 参数设置阶段

```
用户 -> ShipNoiseTab -> SimulationUIManager -> SimulationDataManager
```

详细步骤：

1. 用户在 `ShipNoiseTab` 中设置以下参数：
   - 连续谱参数：峰值频率、峰值电平、上升斜率、下降斜率
   - 线谱参数：频率列表、电平差值列表
   - 高级参数：持续时间、滤波器阶数
   - 注意：采样率作为全局参数，通过系统参数设置进行配置

2. 用户点击"开始仿真"按钮，触发 `on_simulate_clicked()` 方法

3. `ShipNoiseTab` 调用 `validate_line_spectrum_table()` 验证参数

4. `ShipNoiseTab` 调用 `get_current_parameters()` 获取参数字典：
   ```python
   params = {
       'f0': peak_freq_value,
       'sl0': peak_level_value,
       'a1': rise_slope_value,
       'a2': fall_slope_value,
       'line_frequencies': line_freq_list,
       'line_levels_diff': line_level_list,
       'include_line_spectrum': include_line_value,
       'duration': duration_value,
       'filter_order': filter_order_value
       # 注意：fs作为全局参数，不包含在模块参数中
   }
   ```

5. `ShipNoiseTab` 发送 `simulation_requested` 信号，携带参数字典

6. `SimulationUIManager` 接收信号，调用：
   ```python
   data_manager.set_parameters('ship_noise', params)
   ```

7. `SimulationUIManager` 调用：
   ```python
   simulation_controller.simulate_ship_noise()
   ```

### 2.2 仿真执行阶段

```
SimulationController -> ShipNoiseController -> ShipRadiated -> SimulationDataManager
```

详细步骤：

1. `SimulationController` 调用 `ship_noise_controller.start()`

2. `ShipNoiseController` 在后台线程中执行以下步骤：

   a. 发送仿真开始信号：
      ```python
      self.simulation_started.emit()
      ```

   b. 从数据管理器获取参数：
      ```python
      params = self.data_manager.get_parameters('ship_noise')
      ```

   c. 提取参数：
      ```python
      # 从模块参数中获取
      f0 = params.get('f0', 250)
      sl0 = params.get('sl0', 130.0)
      a1 = params.get('a1', 3.0)
      a2 = params.get('a2', -4.2)
      line_frequencies = params.get('line_frequencies', [])
      line_levels_diff = params.get('line_levels_diff', [])
      include_line_spectrum = params.get('include_line_spectrum', True)
      duration = params.get('duration', 5.0)
      filter_order = params.get('filter_order', 16385)

      # 从全局参数中获取
      fs = self.data_manager.get_global_param('fs')
      ```

   d. 创建时间数组：
      ```python
      time_data = np.arange(0, duration, 1/fs)
      ```

   e. 生成连续谱信号：
      ```python
      continuous_signal = generate_continuous_spectrum(time_data, f0, sl0, a1, a2, fs)
      ```

   f. 生成线谱信号（如果启用）：
      ```python
      if include_line_spectrum and line_frequencies and line_levels_diff:
          line_signal = generate_line_spectrum(time_data, line_frequencies, line_levels_diff, sl0, fs)
      else:
          line_signal = np.zeros_like(time_data)
      ```

   g. 合成总信号：
      ```python
      total_signal = continuous_signal + line_signal
      ```

   h. 计算频谱：
      ```python
      # 连续谱频谱
      continuous_freqs, continuous_psd = calculate_psd(continuous_signal, fs)
      continuous_psd_db = 10 * np.log10(continuous_psd)

      # 线谱频谱
      line_freqs, line_psd = calculate_psd(line_signal, fs)
      line_psd_db = 10 * np.log10(line_psd)

      # 总信号频谱
      total_freqs, total_psd = calculate_psd(total_signal, fs)
      total_psd_db = 10 * np.log10(total_psd)
      ```

   i. 将结果存储到数据管理器：
      ```python
      results = {
          'time_data': time_data,
          'continuous_signal': continuous_signal,
          'continuous_freqs': continuous_freqs,
          'continuous_psd_db': continuous_psd_db,
          'line_signal': line_signal,
          'line_freqs': line_freqs,
          'line_psd_db': line_psd_db,
          'total_signal': total_signal,
          'total_freqs': total_freqs,
          'total_psd_db': total_psd_db
      }
      self.data_manager.set_results('ship_noise', results)
      ```

   j. 发送仿真完成信号：
      ```python
      self.simulation_completed.emit()
      ```

### 2.3 结果显示阶段

```
SimulationDataManager -> ShipNoiseViewUpdater -> ShipNoiseView
```

详细步骤：

1. `SimulationDataManager` 在 `set_results()` 方法中发送 `results_changed` 信号：
   ```python
   self.results_changed.emit('ship_noise')
   ```

2. `ShipNoiseViewUpdater` 监听 `results_changed` 信号，当 `module == 'ship_noise'` 时调用 `update_view()`

3. `ShipNoiseViewUpdater` 从数据管理器获取结果：
   ```python
   results = self.data_manager.get_results('ship_noise')
   ```

4. `ShipNoiseViewUpdater` 检查结果是否包含必要的数据，如果包含则更新视图：

   a. 更新连续谱视图：
      ```python
      self.view.update_continuous_spectrum(
          time_data=time_data,
          time_signal=results['continuous_signal'],
          freq_data=results['continuous_freqs'],
          psd_data=results['continuous_psd_db']
      )
      ```

   b. 更新线谱视图：
      ```python
      self.view.update_line_spectrum(
          time_data=time_data,
          time_signal=results['line_signal'],
          freq_data=results['line_freqs'],
          psd_data=results['line_psd_db']
      )
      ```

   c. 更新总信号视图：
      ```python
      self.view.update_radiated_noise(
          time_data=time_data,
          time_signal=results['total_signal'],
          freq_data=results['total_freqs'],
          psd_data=results['total_psd_db']
      )
      ```

5. `ShipNoiseView` 更新图表，显示时域信号和频谱图

### 2.4 项目保存阶段

```
用户 -> ProjectUIManager -> SimulationController -> ProjectManager -> SimulationDataManager -> 文件
```

详细步骤：

1. 用户点击"保存项目"按钮，触发 `ProjectUIManager.on_save_project()`

2. `ProjectUIManager` 调用 `main_window.sync_ui_parameters_to_data_manager()` 确保最新参数已同步到数据管理器

3. `ProjectUIManager` 检查项目是否已保存，如果已保存则获取文件路径：
   ```python
   file_path = simulation_controller.get_project_file_path()
   ```

4. `ProjectUIManager` 调用：
   ```python
   simulation_controller.save_project(file_path)
   ```

5. `SimulationController` 调用：
   ```python
   project_manager.save_project(file_path)
   ```

6. `ProjectManager` 从数据管理器获取所有参数和结果：
   ```python
   all_parameters = self.data_manager.get_all_parameters()
   all_results = self.data_manager.get_all_results()
   ```

7. `ProjectManager` 处理结果数据，将 NumPy 数组转换为可序列化的格式

8. `ProjectManager` 将数据序列化为 JSON 格式并保存到文件

9. `ProjectManager` 更新项目文件路径和修改状态：
   ```python
   self._project_file_path = file_path
   self._saved_state = self._create_state_snapshot().copy()
   self._is_modified = False
   ```

10. `ProjectManager` 发送信号：
    ```python
    self.project_saved.emit(file_path)
    self.modification_state_changed.emit(False)
    ```

## 3. 数据结构

### 3.1 参数数据结构

船舶辐射噪声仿真的参数数据结构如下：

```python
{
    'f0': 250,                  # 连续谱峰值频率 (Hz)
    'sl0': 130.0,               # 连续谱峰值电平 (dB)
    'a1': 3.0,                  # 连续谱上升斜率 (dB/倍频程)
    'a2': -4.2,                 # 连续谱下降斜率 (dB/倍频程)
    'line_frequencies': [16, 32, 48, 65, 350, 800],  # 线谱频率列表 (Hz)
    'line_levels_diff': [25.0, 23.0, 21.0, 19.0, 18.0, 18.0],  # 线谱电平差值列表 (dB)
    'include_line_spectrum': True,  # 是否包含线谱
    'duration': 5.0,            # 信号持续时间 (s)
    'filter_order': 16385       # 滤波器阶数
}
```

全局参数存储在数据管理器的`_global_params`字典中：

```python
{
    'fs': 44100                 # 采样率 (Hz)
}
```

### 3.2 结果数据结构

船舶辐射噪声仿真的结果数据结构如下：

```python
{
    'time_data': ndarray,       # 时间数组 (s)
    'continuous_signal': ndarray,  # 连续谱时域信号
    'continuous_freqs': ndarray,   # 连续谱频率数组 (Hz)
    'continuous_psd_db': ndarray,  # 连续谱功率谱密度 (dB/Hz)
    'line_signal': ndarray,     # 线谱时域信号
    'line_freqs': ndarray,      # 线谱频率数组 (Hz)
    'line_psd_db': ndarray,     # 线谱功率谱密度 (dB/Hz)
    'total_signal': ndarray,    # 总信号时域信号
    'total_freqs': ndarray,     # 总信号频率数组 (Hz)
    'total_psd_db': ndarray     # 总信号功率谱密度 (dB/Hz)
}
```

## 4. 开发其他模块的参考

开发其他模块时，应参考船舶辐射噪声仿真的数据流设计，遵循以下原则：

1. **参数设置**：
   - 在控制面板标签页中提供模块特定参数设置界面，包括模块特定的持续时间设置
   - 通过系统参数设置对话框设置全局参数（如采样率）
   - 验证参数有效性
   - 通过信号将参数传递给 UI 管理器
   - UI 管理器将参数存储到数据管理器

2. **仿真执行**：
   - 在后台线程中执行仿真
   - 从数据管理器获取模块特定参数
   - 从数据管理器获取全局参数（如采样率、持续时间）
   - 执行仿真计算
   - 将结果存储到数据管理器
   - 发送仿真进度和完成信号

3. **结果显示**：
   - 创建专门的视图更新器
   - 监听数据管理器的结果变更信号
   - 从数据管理器获取结果
   - 更新视图

4. **项目保存**：
   - 确保参数和结果可序列化
   - 确保全局参数也被保存和加载
   - 使用项目管理器保存和加载项目

## 5. 结论

船舶辐射噪声仿真的数据流设计清晰、模块化，各个组件职责明确，便于维护和扩展。其他模块的开发应参考这一设计，确保系统的一致性和可维护性。
