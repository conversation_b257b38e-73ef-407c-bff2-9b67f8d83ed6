# -*- coding: utf-8 -*-
"""
船舶辐射噪声控制器

负责执行船舶辐射噪声仿真任务，在后台线程中运行以保持UI响应性。
"""

import numpy as np
from PyQt5.QtCore import QThread, pyqtSignal

from src.models.noise_sources.ship_radiated import ShipRadiatedNoise


class ShipNoiseController(QThread):
    """
    船舶辐射噪声控制器

    负责执行船舶辐射噪声仿真任务，在后台线程中运行以保持UI响应性。
    """

    # 信号定义
    simulation_started = pyqtSignal()  # 仿真开始信号
    simulation_progress = pyqtSignal(int)  # 仿真进度信号，参数为进度百分比
    simulation_completed = pyqtSignal()  # 仿真完成信号
    simulation_error = pyqtSignal(str)  # 仿真错误信号，参数为错误信息

    def __init__(self, data_manager):
        """
        初始化船舶辐射噪声控制器

        Args:
            data_manager: 数据管理器实例
        """
        super().__init__()

        # 保存数据管理器引用
        self.data_manager = data_manager

        # 创建船舶辐射噪声模型实例
        self.ship_model = ShipRadiatedNoise()

        # 仿真状态
        self.is_running = False
        self.is_cancelled = False

    def run(self):
        """
        线程执行函数，执行船舶辐射噪声仿真
        """
        try:
            # 设置运行状态
            self.is_running = True
            self.is_cancelled = False

            # 设置仿真状态并发送仿真开始信号
            self.data_manager.set_module_simulating('ship_noise', True)
            self.simulation_started.emit()
            self.simulation_progress.emit(0)

            # 从数据管理器获取参数
            params = self.data_manager.get_parameters('ship_noise')

            # 设置连续谱参数
            self.ship_model.set_continuous_params(
                f0=params.get('f0', 250.0),
                sl0=params.get('sl0', 130.0),
                a1_oct=params.get('a1', 3.0),
                a2_oct=params.get('a2', -4.2)
            )

            # 获取全局参数
            fs = self.data_manager.get_global_param('fs')

            # 获取模块参数
            duration = params.get('duration', 5.0)  # 从模块参数获取持续时间
            filter_order = params.get('filter_order', 16385)  # 默认值为16385

            # 设置仿真参数
            self.ship_model.set_simulation_params(
                fs=fs,
                duration=duration,
                filter_order=filter_order
            )

            # 发送进度信号
            self.simulation_progress.emit(20)

            # 检查是否取消
            if self.is_cancelled:
                return

            # 如果启用了线谱并有线谱参数
            include_line = params.get('include_line_spectrum', False)
            if include_line and 'line_frequencies' in params and 'line_levels_diff' in params:
                # 确保线谱参数是一维数组
                frequencies = np.array(params['line_frequencies'], dtype=float)
                levels_diff = np.array(params['line_levels_diff'], dtype=float)

                if len(frequencies) > 0:
                    self.ship_model.set_line_params(
                        frequencies=frequencies,
                        levels_diff=levels_diff
                    )

            # 设置调制谱参数
            self.ship_model.set_modulation_params(
                f1=params.get('mod_f1', 8.0),
                f2=params.get('mod_f2', 32.0),
                A1=params.get('mod_A1', 0.05),
                A2=params.get('mod_A2', 0.3),
                p=params.get('mod_p', 0.1),
                q=params.get('mod_q', 0.1),
                N=params.get('mod_N', 5),
                M=params.get('mod_M', 5)
            )

            # 发送进度信号
            self.simulation_progress.emit(40)

            # 检查是否取消
            if self.is_cancelled:
                return

            # 生成时间数组
            n_samples = int(duration * fs)
            t = np.linspace(0, duration, n_samples, endpoint=False)

            # 生成连续谱信号
            continuous_signal, _ = self.ship_model.simulate_continuous_spectrum()

            # 计算连续谱信号的功率谱密度
            cont_freqs, _, cont_psd_db = self.ship_model.calculate_psd(continuous_signal)

            # 发送进度信号
            self.simulation_progress.emit(60)

            # 检查是否取消
            if self.is_cancelled:
                return

            # 准备结果字典，用于批量设置结果
            # 只保留必要的数据，移除不需要的PSD线性值
            # 将大型数组转换为float32类型以节省内存
            results = {
                'time_data': np.array(t, dtype=np.float32),
                'continuous_signal': np.array(continuous_signal, dtype=np.float32),
                'continuous_freqs': np.array(cont_freqs, dtype=np.float32),
                'continuous_psd_db': np.array(cont_psd_db, dtype=np.float32),

                # 添加元数据，记录生成参数
                'metadata': {
                    'fs': fs,
                    'duration': duration,
                    'filter_order': filter_order,
                    'generation_time': np.datetime64('now').astype(str)  # 使用NumPy的datetime64，可以序列化
                }
            }

            # 如果启用了线谱
            if include_line:
                # 生成线谱信号
                line_signal = self.ship_model.simulate_line_spectrum()

                # 计算线谱信号的功率谱密度
                line_freqs, _, line_psd_db = self.ship_model.calculate_psd(line_signal)

                # 添加线谱结果到结果字典，只保留必要的数据
                # 将大型数组转换为float32类型以节省内存
                results['line_signal'] = np.array(line_signal, dtype=np.float32)
                results['line_freqs'] = np.array(line_freqs, dtype=np.float32)
                results['line_psd_db'] = np.array(line_psd_db, dtype=np.float32)
            else:
                # 如果没有启用线谱，不添加线谱相关的结果
                # 这样视图更新器将会重置线谱图表
                pass

            # 检查是否启用调制谱
            include_modulation = params.get('include_modulation', False)

            # 如果启用了调制谱
            if include_modulation:
                # 生成调制谱信号
                modulation_signal = self.ship_model.simulate_modulation_spectrum()

                # 计算调制谱信号的功率谱密度 (仅用于调试，不展示在UI中)
                # 注意：我们不展示调制谱的功率谱密度，而是展示调制前后的连续谱对比
                # mod_freqs, mod_psd, mod_psd_db = self.ship_model.calculate_psd(modulation_signal)

                # 生成调制后的连续谱信号 [1 + a(t)] * g_x(t)
                modulated_continuous_signal = (1 + modulation_signal) * continuous_signal

                # 计算调制后连续谱信号的功率谱密度
                _, _, modulated_cont_psd_db = self.ship_model.calculate_psd(modulated_continuous_signal)

                # 添加调制谱结果到结果字典，只保留必要的数据
                # 将大型数组转换为float32类型以节省内存
                results['modulation_signal'] = np.array(modulation_signal, dtype=np.float32)
                results['modulated_continuous_signal'] = np.array(modulated_continuous_signal, dtype=np.float32)
                results['modulated_continuous_psd_db'] = np.array(modulated_cont_psd_db, dtype=np.float32)
            else:
                # 如果没有启用调制谱，不添加调制谱相关的结果
                # 这样视图更新器将会重置调制谱图表，而不是显示原始连续谱数据
                pass

            # 生成总信号（连续谱+线谱+调制谱）
            total_signal = self.ship_model.simulate_radiated_noise(
                include_line=include_line,
                include_modulation=include_modulation
            )

            # 发送进度信号
            self.simulation_progress.emit(80)

            # 检查是否取消
            if self.is_cancelled:
                return

            # 计算总信号的功率谱密度
            total_freqs, _, total_psd_db = self.ship_model.calculate_psd(total_signal)

            # 添加总信号结果到结果字典，只保留必要的数据
            # 将大型数组转换为float32类型以节省内存
            results['total_signal'] = np.array(total_signal, dtype=np.float32)
            results['total_freqs'] = np.array(total_freqs, dtype=np.float32)
            results['total_psd_db'] = np.array(total_psd_db, dtype=np.float32)

            # 批量设置所有结果，减少锁操作次数
            self.data_manager.set_results('ship_noise', results)

            # 发送进度信号
            self.simulation_progress.emit(100)

            # 发送仿真完成信号
            self.simulation_completed.emit()

        except Exception as e:
            # 发送错误信号
            self.simulation_error.emit(str(e))
        finally:
            # 重置运行状态和仿真状态
            self.is_running = False
            self.data_manager.set_module_simulating('ship_noise', False)

    def cancel(self):
        """
        取消仿真
        """
        if self.is_running:
            self.is_cancelled = True

    def is_simulation_running(self):
        """
        检查仿真是否正在运行

        Returns:
            bool: 仿真是否正在运行
        """
        return self.is_running
