# -*- coding: utf-8 -*-
"""
声传播控制器

负责执行声传播仿真，在后台线程中运行以保持UI响应性。
"""

import numpy as np
from PyQt5.QtCore import QThread, pyqtSignal

from src.models.propagation.propagation_model import PropagationModel


class PropagationController(QThread):
    """
    声传播控制器

    负责执行声传播仿真，在后台线程中运行以保持UI响应性。
    """

    # 信号定义
    simulation_started = pyqtSignal(str)  # 仿真开始信号，参数为模块名称
    simulation_progress = pyqtSignal(str, int)  # 仿真进度信号，参数为模块名称和进度百分比
    simulation_completed = pyqtSignal(str)  # 仿真完成信号，参数为模块名称
    simulation_error = pyqtSignal(str, str)  # 仿真错误信号，参数为模块名称和错误信息

    def __init__(self, data_manager):
        """
        初始化声传播控制器

        Args:
            data_manager: 数据管理器实例
        """
        super().__init__()

        # 保存数据管理器引用
        self.data_manager = data_manager

        # 创建声传播模型实例
        self.model = PropagationModel()

        # 仿真状态
        self.is_running = False
        self.is_cancelled = False

    def run(self):
        """
        线程执行函数，执行声传播仿真
        """
        try:
            # 设置运行状态
            self.is_running = True
            self.is_cancelled = False

            # 设置仿真状态并发送仿真开始信号
            self.data_manager.set_module_simulating('propagation', True)
            self.simulation_started.emit('propagation')
            self.simulation_progress.emit('propagation', 0)

            # 从数据管理器获取参数
            params = self.data_manager.get_parameters('propagation')

            # 检查参数
            if not params:
                self.simulation_error.emit('propagation', "参数为空")
                self.is_running = False
                return

            # 根据计算类型执行不同的计算
            computation_type = params.get('computation_type', 'environment')

            if computation_type == 'environment':
                # 计算环境
                self._compute_environment(params)
            elif computation_type == 'rays':
                # 计算声线
                self._compute_rays(params)
            elif computation_type == 'eigenrays':
                # 计算本征声线
                self._compute_eigenrays(params)
            elif computation_type == 'arrivals':
                # 计算到达结构
                self._compute_arrivals(params)
            elif computation_type == 'transmission_loss':
                # 计算传播损失
                self._compute_transmission_loss(params)
            elif computation_type == 'broadband':
                # 宽带计算（多频率）
                self._compute_broadband(params)
            elif computation_type == 'channel_preparation':
                # 信道数据制备
                self._prepare_channel_data(params)
            else:
                self.simulation_error.emit('propagation', f"未知的计算类型: {computation_type}")
                self.is_running = False
                return

            # 发送仿真完成信号
            self.simulation_progress.emit('propagation', 100)
            self.simulation_completed.emit('propagation')

        except Exception as e:
            # 发送错误信号
            self.simulation_error.emit('propagation', str(e))
        finally:
            # 重置运行状态和仿真状态
            self.is_running = False
            self.data_manager.set_module_simulating('propagation', False)

    def _create_environment_params(self, params):
        """
        创建环境参数

        Args:
            params (dict): 原始参数字典

        Returns:
            dict: 环境参数字典
        """
        # 复制参数，避免修改原始参数
        env_params = params.copy()

        # 处理接收器距离和深度参数（用于传播损失计算）
        if params.get('computation_type') == 'transmission_loss':
            # 获取接收器距离范围
            if 'range_grid' in params:
                range_grid = params.get('range_grid', {})
                range_start = range_grid.get('start', 0)
                range_end = range_grid.get('end', 1000)
                range_step = range_grid.get('step', 10)
                rx_range = np.arange(range_start, range_end + range_step/2, range_step)
                env_params['rx_range'] = rx_range

            # 获取接收器深度范围
            if 'depth_grid' in params:
                depth_grid = params.get('depth_grid', {})
                depth_start = depth_grid.get('start', 0)
                depth_end = depth_grid.get('end', 100)
                depth_step = depth_grid.get('step', 5)
                rx_depth = np.arange(depth_start, depth_end + depth_step/2, depth_step)
                env_params['rx_depth'] = rx_depth

        return env_params

    def _compute_environment(self, params):
        """
        计算环境

        Args:
            params (dict): 参数字典
        """
        # 发送进度信号
        self.simulation_progress.emit('propagation', 10)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 创建环境参数
        env_params = self._create_environment_params(params)

        # 创建环境
        env = self.model.create_environment(env_params)

        # 发送进度信号
        self.simulation_progress.emit('propagation', 80)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 获取当前结果
        current_results = self.data_manager.get_results('propagation')

        # 更新结果字典，只更新环境相关的结果
        current_results['environment'] = env

        # 将更新后的结果存储到数据管理器
        self.data_manager.set_results('propagation', current_results)

    def _compute_rays(self, params):
        """
        计算声线

        Args:
            params (dict): 参数字典
        """
        # 发送进度信号
        self.simulation_progress.emit('propagation', 10)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 创建环境参数
        env_params = self._create_environment_params(params)

        # 创建环境
        env = self.model.create_environment(env_params)

        # 发送进度信号
        self.simulation_progress.emit('propagation', 30)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 获取调试模式参数
        debug_mode = params.get('debug_mode', False)

        # 计算声线
        rays = self.model.compute_rays(env, debug=debug_mode)

        # 发送进度信号
        self.simulation_progress.emit('propagation', 80)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 获取当前结果
        current_results = self.data_manager.get_results('propagation')

        # 更新结果字典，只更新环境和声线相关的结果
        current_results['environment'] = env
        current_results['rays'] = rays

        # 将更新后的结果存储到数据管理器
        self.data_manager.set_results('propagation', current_results)

    def _compute_eigenrays(self, params):
        """
        计算本征声线

        Args:
            params (dict): 参数字典
        """
        # 发送进度信号
        self.simulation_progress.emit('propagation', 10)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 创建环境参数
        env_params = self._create_environment_params(params)

        # 创建环境
        env = self.model.create_environment(env_params)

        # 发送进度信号
        self.simulation_progress.emit('propagation', 30)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 获取调试模式参数
        debug_mode = params.get('debug_mode', False)

        # 计算本征声线
        eigenrays = self.model.compute_eigenrays(env, debug=debug_mode)

        # 发送进度信号
        self.simulation_progress.emit('propagation', 80)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 获取当前结果
        current_results = self.data_manager.get_results('propagation')

        # 更新结果字典，只更新环境和本征声线相关的结果
        current_results['environment'] = env
        current_results['eigenrays'] = eigenrays

        # 将更新后的结果存储到数据管理器
        self.data_manager.set_results('propagation', current_results)

    def _compute_arrivals(self, params):
        """
        计算到达结构

        Args:
            params (dict): 参数字典
        """
        # 发送进度信号
        self.simulation_progress.emit('propagation', 10)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 创建环境参数
        env_params = self._create_environment_params(params)

        # 创建环境
        env = self.model.create_environment(env_params)

        # 发送进度信号
        self.simulation_progress.emit('propagation', 30)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 获取调试模式参数
        debug_mode = params.get('debug_mode', False)

        # 计算到达结构
        arrivals = self.model.compute_arrivals(env, debug=debug_mode)

        # 发送进度信号
        self.simulation_progress.emit('propagation', 80)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 获取当前结果
        current_results = self.data_manager.get_results('propagation')

        # 更新结果字典，只更新环境和到达结构相关的结果
        current_results['environment'] = env
        current_results['arrivals'] = arrivals

        # 将更新后的结果存储到数据管理器
        self.data_manager.set_results('propagation', current_results)

    def _compute_transmission_loss(self, params):
        """
        计算传播损失

        Args:
            params (dict): 参数字典
        """
        # 发送进度信号
        self.simulation_progress.emit('propagation', 10)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 创建环境参数
        env_params = self._create_environment_params(params)

        # 创建环境
        env = self.model.create_environment(env_params)

        # 发送进度信号
        self.simulation_progress.emit('propagation', 30)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 获取计算模式和调试模式参数
        mode = params.get('tl_mode', 'coherent')
        debug_mode = params.get('debug_mode', False)

        # 计算传播损失
        transmission_loss = self.model.compute_transmission_loss(env, mode=mode, debug=debug_mode)

        # 发送进度信号
        self.simulation_progress.emit('propagation', 80)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 获取当前结果
        current_results = self.data_manager.get_results('propagation')

        # 更新结果字典，只更新环境和传播损失相关的结果
        current_results['environment'] = env
        current_results['transmission_loss'] = transmission_loss

        # 将更新后的结果存储到数据管理器
        self.data_manager.set_results('propagation', current_results)

    def _compute_broadband(self, params):
        """
        宽带计算（多频率）

        Args:
            params (dict): 参数字典
        """
        # 发送进度信号
        self.simulation_progress.emit('propagation', 10)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 获取频率参数
        freq_mode = params.get('freq_mode', 'range')

        if freq_mode == 'range':
            # 频率范围
            freq_start = params.get('freq_start', 100)
            freq_end = params.get('freq_end', 1000)
            freq_step = params.get('freq_step', 100)
            frequencies = np.arange(freq_start, freq_end + freq_step/2, freq_step)
        else:
            # 自定义频率列表
            freq_list_str = params.get('freq_list', '100, 200, 500, 1000, 2000')
            frequencies = [float(f.strip()) for f in freq_list_str.split(',')]

        # 获取并行计算参数和调试模式参数
        # 如果参数中没有max_workers，则使用全局参数中的cpu_cores作为默认值
        max_workers = params.get('max_workers', self.data_manager.get_global_param('cpu_cores'))
        debug_mode = params.get('debug_mode', False)

        # 发送进度信号
        self.simulation_progress.emit('propagation', 20)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 创建环境参数
        env_params = self._create_environment_params(params)

        # 定义进度回调函数
        def update_progress(progress):
            # 将进度映射到20%-80%范围
            overall_progress = 20 + int(progress * 60)
            self.simulation_progress.emit('propagation', overall_progress)

            # 检查是否取消
            if self.is_cancelled:
                raise Exception("计算已取消")

        # 并行计算多个频率的到达结构
        try:
            arrivals_dict = self.model.parallel_compute_arrivals(
                frequencies=frequencies,
                env_params=env_params,
                max_workers=max_workers,
                progress_callback=update_progress,
                debug=debug_mode
            )
        except Exception as e:
            if str(e) == "计算已取消":
                return
            raise e

        # 发送进度信号
        self.simulation_progress.emit('propagation', 90)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 获取当前结果
        current_results = self.data_manager.get_results('propagation')

        # 更新结果字典，只更新宽带计算相关的结果
        current_results['broadband_frequencies'] = frequencies
        current_results['broadband_arrivals'] = arrivals_dict

        # 将更新后的结果存储到数据管理器
        self.data_manager.set_results('propagation', current_results)

    def _prepare_channel_data(self, params):
        """
        信道数据制备

        Args:
            params (dict): 参数字典
        """
        import os
        import json
        import datetime

        # 发送进度信号
        self.simulation_progress.emit('propagation', 10)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 获取阵元位置数据
        array_elements_data = params.get('array_elements_data', [])
        if not array_elements_data:
            self.simulation_error.emit('propagation', "未设置阵元位置数据")
            return

        # 获取频率参数
        freq_mode = params.get('freq_mode', 'range')

        if freq_mode == 'range':
            # 频率范围
            freq_start = params.get('freq_start', 500)
            freq_end = params.get('freq_end', 2500)
            freq_step = params.get('freq_step', 500)
            frequencies = np.arange(freq_start, freq_end + freq_step/2, freq_step)
        else:
            # 自定义频率列表
            if isinstance(params.get('freq_list'), str):
                freq_list_str = params.get('freq_list', '500 1000 1500 2000 2500')
                frequencies = [float(f.strip()) for f in freq_list_str.split() if f.strip()]
            else:
                frequencies = params.get('freq_list', [500, 1000, 1500, 2000, 2500])

        # 获取输出目录
        output_dir = params.get('output_dir', os.path.join(os.getcwd(), "channel_data"))

        # 创建输出目录（如果不存在）
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 创建时间戳文件夹
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_folder = os.path.join(output_dir, f"channel_data_{timestamp}")
        os.makedirs(output_folder)

        # 获取调试模式参数
        debug_mode = params.get('debug_mode', False)

        # 发送进度信号
        self.simulation_progress.emit('propagation', 20)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 创建环境参数的基础模板
        env_params_template = self._create_environment_params(params)

        # 准备元数据
        env_metadata = {
            'bottom_soundspeed': params.get('bottom_soundspeed'),
            'bottom_density': params.get('bottom_density'),
            'bottom_absorption': params.get('bottom_absorption'),
            'bottom_roughness': params.get('bottom_roughness'),
            'tx_depth': params.get('tx_depth'),
            'min_angle': params.get('min_angle'),
            'max_angle': params.get('max_angle')
        }

        # 添加声速信息
        if params.get('ssp_type') == 'constant':
            env_metadata['soundspeed'] = params.get('soundspeed')
            env_metadata['ssp_type'] = 'constant'
        else:
            env_metadata['ssp_type'] = 'depth_dependent'
            env_metadata['ssp_data'] = params.get('ssp_data', [])
            env_metadata['soundspeed_interp'] = params.get('soundspeed_interp')

        # 添加水深信息
        if params.get('depth_type') == 'flat':
            env_metadata['depth'] = params.get('depth')
            env_metadata['depth_type'] = 'flat'
        else:
            env_metadata['depth_type'] = 'range_dependent'
            env_metadata['bathymetry_data'] = params.get('bathymetry_data', [])
            env_metadata['depth_interp'] = params.get('depth_interp')

        # 添加声源指向性信息
        if params.get('tx_directivity_enabled', False):
            env_metadata['tx_directivity_enabled'] = True
            env_metadata['directivity_data'] = params.get('directivity_data', [])
        else:
            env_metadata['tx_directivity_enabled'] = False

        metadata = {
            'environment': env_metadata,
            'array_elements': array_elements_data,
            'frequencies': frequencies.tolist() if isinstance(frequencies, np.ndarray) else frequencies,
            'generation_time': timestamp
        }

        # 保存元数据
        with open(os.path.join(output_folder, 'meta.json'), 'w') as f:
            json.dump(metadata, f, indent=4)

        # 获取并行计算参数
        max_workers = params.get('max_workers', self.data_manager.get_global_param('cpu_cores'))

        # 定义进度回调函数
        def update_progress(progress):
            # 将进度映射到20%-90%范围
            overall_progress = 20 + int(progress * 70)
            self.simulation_progress.emit('propagation', overall_progress)

            # 检查是否取消
            if self.is_cancelled:
                raise Exception("计算已取消")

        # 提取阵元位置数据
        rx_ranges = [pos[0] for pos in array_elements_data]
        rx_depths = [pos[1] for pos in array_elements_data]

        # 检查是否为水平或垂直线列阵
        is_horizontal = len(set(rx_depths)) == 1
        is_vertical = len(set(rx_ranges)) == 1

        # 根据阵列类型设置接收器位置
        if is_horizontal:
            # 水平线列阵，深度固定，距离变化
            env_params_template['rx_depth'] = rx_depths[0]  # 所有阵元深度相同
            env_params_template['rx_range'] = np.array(rx_ranges)  # 距离数组
        elif is_vertical:
            # 垂直线列阵，距离固定，深度变化
            env_params_template['rx_range'] = rx_ranges[0]  # 所有阵元距离相同
            env_params_template['rx_depth'] = np.array(rx_depths)  # 深度数组

        # 使用并行计算多个频率的到达结构
        try:
            # 使用model的parallel_compute_arrivals函数进行并行计算
            arrivals_dict = self.model.parallel_compute_arrivals(
                frequencies=frequencies,
                env_params=env_params_template,
                max_workers=max_workers,
                progress_callback=update_progress,
                debug=debug_mode
            )

            # 保存每个频率的结果
            for freq, arrivals in arrivals_dict.items():
                # 保存为CSV文件（一个频率一个文件，包含所有接收器的数据）
                csv_filename = f"fc_{int(freq)}Hz.csv"
                arrivals.to_csv(os.path.join(output_folder, csv_filename), index=False)

        except Exception as e:
            if str(e) == "计算已取消":
                return
            print(f"并行计算到达结构时出错: {str(e)}")
            raise e

        # 发送进度信号
        self.simulation_progress.emit('propagation', 90)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 获取当前结果
        current_results = self.data_manager.get_results('propagation')

        # 更新结果字典，添加信道数据制备相关的结果
        current_results['channel_preparation'] = {
            'output_folder': output_folder,
            'frequencies': frequencies.tolist() if isinstance(frequencies, np.ndarray) else frequencies,
            'array_elements': array_elements_data,
            'timestamp': timestamp
        }

        # 将更新后的结果存储到数据管理器
        self.data_manager.set_results('propagation', current_results)

    def cancel(self):
        """
        取消仿真
        """
        if self.is_running:
            self.is_cancelled = True

    def is_simulation_running(self):
        """
        检查仿真是否正在运行

        Returns:
            bool: 仿真是否正在运行
        """
        return self.is_running
