现在我们开始综合仿真模块的阶段二的开发任务。但是这次我们把阶段二的开发任务也拆成两部分执行，我们先执行叠加海洋环境噪声之前的部分，即模拟宽带源信号通过多阵元水声信道后的接收信号。我们的处理方法采用频域子带处理但暂不使用 Overlap-Save 分块卷积方法（因为预计我们正常情况单次FFT的点数在一百万及一百万以内，后续如有需要再进行优化）。
首先，我们在阶段一中，收集了信道数据并进行了初步的分析，也就是我们找到了 t_global_first 和 t_global_last 两个构建冲击响应的关键时间。
现在在阶段二的参数设置中，我们允许用户选择使用全部的到达声线构建冲击响应，或者设置一个用户感兴趣的截断时间 t_user_max ，当然这个截断时间要大于 t_global_first 。这个截断时间表示的是用户只对到达信息中，小于等于这个时间到达的声线感兴趣，这对所有频率和阵元的到达信息同时生效。
这样，如果用户选择的是使用全部的到达声线，那么我们的最大相对时间 t_prime_max = t_global_last - t_global_first 。
如果用户选择的是设置一个用户感兴趣的截断时间 t_user_max，那么我们需要进一步筛选有效路径中的最大到达时间 t_global_last_eff ，从而我们的最大相对时间 t_prime_max = t_global_last_eff - t_global_first 。
从而，我们就能够计算得出等效冲击响应长度 N_h = int(np.ceil(t_prime_max * Fs)) + 1 。

有了我们构建冲击响应的最大相对时间 t_prime_max 后，从为了后续分析接收信号的统计稳定性考虑，我们可以要求源信号的长度至少为 t_prime_max + 1 秒，这样我们可以截取出约1秒长的时间用于计算功率谱密度同时获得1Hz的频率分辨率。设置一些余量，我们可以建议用户源信号的时长为至少为 ceil(t_prime_max + 1) 秒。
实际上为了更好的统计稳定性，我们可以建议源信号的时长为 ceil(t_prime_max + 5) 秒。从而在UI上我们可以把这两个时长都告知用户，对用户进行引导。

综合仿真模块使用船舶辐射噪声模块模拟的噪声信号，因此我们可以对数据管理器中的保存的船舶辐射噪声结果中的 metadata 的 duration 进行访问，如果时长小于我们建议的最低时长，系统可以给出警告“当前源信号时长可能不足以模拟出接近稳态噪声通过信道的效果”，然后给出建议并询问用户是否要继续执行仿真。
紧接着，我们来确定FFT参数，N_conv_len = N_s + N_h - 1, N_FFT_single = scipy.fft.next_fast_len(N_conv_len) 其中N_s是源信号的数组长度。

然后，我们为每个阵元 j 构建并缓存其传递函数 H_j_fft (f) (长度 N_FFT_single)：
定义子带： 根据我们信道数据中的 N 个频率点 f_k 和源信号的 Nyquist 频率，合理划分子带 (f_center, f_start, f_end)。如果提供的频率列表是等间隔的，那么频带也是等间隔划分，以各 f_k 作为子带的 f_center，只需要注意头部和尾部的子带不要覆盖到0和nyquist之外。如果频率列表不是等间隔的，那么以相邻频率点的中点来确定子带边界。如果只提供了1个频率 f_k 则无需划分子带。
特别注意，子带是半开半闭区间。
对于每个阵元 j：
    创建一个全零复数数组 H_j_fft (长度 N_FFT_single)。
    对于每个子带 (中心频率 f_center_subband)：
    使用N个计算频率点中对应 f_center_subband 的那个频率 f_k_actual。
    使用阵元 j 在 f_k_actual 下的到达数据 (A_m(f_k_actual, j), t_m(f_k_actual, j))。
    计算该子带的代表性传递函数值（基于相对延迟 t_prime_m，即实际到达时间减去 t_first_global）：
    H_subband_val_j = Σ_m A_m(f_k_actual, j) * exp(-i * 2π * f_k_actual * t_prime_m(f_k_actual, j))
    (注意：这里的 t_prime_m 是相对于 t_first_global 的，并且可能经过了截断筛选)。
    将 H_subband_val_j 赋值给 H_j_fft(f) 中所有属于该子带的频率点 f。
    0、Nyquist以及负频率点则继续保持值0。
    缓存这个 H_j_fft(f)。

执行阶段：
    将源信号 s(t) (长度 N_s) 补零到 N_FFT_single，得到 s_padded。计算 S_fft = FFT(s_padded)。
    从缓存加载阵元 j 的传递函数 H_j_fft (长度也必须是 N_FFT_single)。
    R_j_fft = S_fft * H_j_fft。
    r_j_padded = IFFT(R_j_fft)。
    取有效部分：r_prime_j(t') = r_j_padded[0 : N_conv_len]。
后处理阶段 (对每个阵元的 r_prime_j(t') 操作)：
    a. 时间轴对齐：
        每个阵元的接收信号 r_prime_j(t') 的时间轴起点是 t=0（相对时间）。要得到物理时间正确的信号，需要将时间轴平移 t_first_global。
        最终的接收信号 r_j(t) 的时间向量从 t_first_global 开始。
        r_j(t) 就是 r_prime_j(t' = t - t_first_global)。 (在绘图或保存时调整时间标签即可)。

至此，我们就成功模拟出了各个阵元的接收信号（没有叠加海洋环境噪声的，叠加海洋环境噪声作为一个选项提供给用户，且还需要其他处理，这部分开发暂不涉及）。
那么在UI上，我们需要在VIEW中规划一个选项卡用于展示各阵元的接收信号，如果船舶辐射噪声模块的展示方式，采用上方时域波形，下方功率谱密度的展示方式。但是这里的是域波形展示需要注意时间轴标签的处理，然后暂不计算和绘制功率谱密度（因为还有待后续处理）。用户同样需要能够通过在control tab中选择阵元查看对应的图。




问题：
1. 现在实现的建议时长是一次性的，没办法做到随着UI CONTROL TAB中的截断时间设置的变动而更新。
2. model 的 _divide_subbands 函数的实现对于子带的划分不统一，我们需要明确标准，第一个和最后一个子带是否一定要覆盖到0和Nyquist点？还是根据用户提供的频率列表，如果按照某一个逻辑划分的子带无法覆盖到0和Nyquist，那么小于第一个子带的起点的频率以及大于最后一个子带的终点的频率我们都不管了？例如说，第一个子带的中心频率是200，而终点是300，我们按这个逻辑推出子带的起点应该是100，从而对于0-100的频带我们就不管了，后续在构建传递函数时也直接赋值0。但如果只有一个频率点，那么默认在整个0-Nyquist都使用这个频率计算出的传递函数值。
3. 对第2点进行一个补充。我们需要明确的标准，如果频率列表是等间隔的，那么中心频率就是确实就是整个子带的中间值（第一个和最后一个子带待标准确定），而如果频率列表不是等间隔的，如200 400 800 2000，那么我们的f_center还是子带中心吗？如果我们确定标准，使得f_center就是f_k_actual，那么在构建传递函数时就不需要频繁计算“# 找到最接近中心频率的实际频率
                f_k_actual = min(self.frequencies, key=lambda x: abs(x - f_center))”
4. controller中处理信道数据时，有这么一段代码“                # 使用rx_range_ndx和rx_depth_ndx作为阵元ID
                if 'rx_range_ndx' in arrivals_df.columns and 'rx_depth_ndx' in arrivals_df.columns:
                    # 创建阵元ID列
                    arrivals_df['receiver_id'] = arrivals_df.apply(
                        lambda row: int(row['rx_depth_ndx']), axis=1
                    )”，这是不是只考虑了垂直线列阵的情况？我们制备信道数据时是允许了垂直或水平的线列阵。
5. 在model的process_signal函数中，我看到计算源信号的频谱使用了“        # 计算源信号的FFT
        S_fft = np.fft.rfft(s_padded)”rfft，这是不是会导致输出的数组的长度和我们预期的N_fft不同？如果是的话，而且你觉得对性能开销影响不大，我们可以先改成使用fft。而且这里怎么使用的是np的方法而不是scipy的方法？要不要改成scipy的方法以确保一致性？
6. 其实在实际执行的这部分，我不是很确定我们的使用的数量关系（数组长度以及有效部分的长度）是否是正确的，我们的目标是和时域卷积等效，也就是说把时域的源信号s(t)和冲击响应（数组长度为冲击响应时长 * Fs）进行卷积并得到完整数组（也就是fftconvolve，mode = full）
7. 现在的实现中，我们无法通过在UI的Control tab中选择阵元来查看对应的信号，而是只默认展示了阵元#0的信号。