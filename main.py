#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
水声噪声仿真系统入口文件

启动应用程序并显示主窗口
"""

import sys
import platform
import matplotlib
from PyQt5.QtWidgets import QApplication

# 导入主窗口
from src.ui.main.main_window import MainWindow


def configure_matplotlib():
    """
    配置Matplotlib以支持中文显示
    """
    # 设置后端
    matplotlib.use("Qt5Agg")

    # 根据操作系统设置中文字体
    system = platform.system()
    if system == "Windows":
        # Windows系统使用微软雅黑或宋体
        font_family = ["Microsoft YaHei", "SimSun", "SimHei"]
    elif system == "Darwin":  # macOS
        # macOS系统使用苹方或华文黑体
        font_family = ["PingFang SC", "STHeiti"]
    else:  # Linux等其他系统
        # Linux系统使用文泉驿等开源字体
        font_family = ["WenQuanYi Micro Hei", "Noto Sans CJK SC"]

    # 配置Matplotlib字体
    matplotlib.rcParams["font.family"] = "sans-serif"
    matplotlib.rcParams["font.sans-serif"] = font_family
    matplotlib.rcParams["axes.unicode_minus"] = False  # 正确显示负号


def main():
    """
    应用程序入口函数
    """
    # 配置Matplotlib以支持中文显示
    configure_matplotlib()

    # 创建应用程序实例
    app = QApplication(sys.argv)

    # 创建主窗口
    window = MainWindow()

    # 显示主窗口
    window.show()

    # 运行应用程序事件循环
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
