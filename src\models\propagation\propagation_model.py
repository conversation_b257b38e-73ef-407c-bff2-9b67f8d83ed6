# -*- coding: utf-8 -*-
"""
声传播模型

封装与arlpy.uwapm的交互，提供声传播计算功能
"""

import os
import uuid
import numpy as np
import pandas as pd
import concurrent.futures
import arlpy.uwapm as pm

# 定义常量
LINEAR = 'linear'
SPLINE = 'spline'
CURVILINEAR = 'curvilinear'


class PropagationModel:
    """
    声传播模型类

    封装与arlpy.uwapm的交互，提供声传播计算功能
    """

    def __init__(self):
        """初始化声传播模型"""
        pass

    def create_environment(self, params):
        """
        根据参数创建声学环境

        Args:
            params (dict): 环境参数字典

        Returns:
            dict: 声学环境字典
        """
        # 提取基本参数
        frequency = params.get('frequency', 1000)  # 默认频率1000Hz

        # 提取声源和接收器参数
        tx_depth = params.get('tx_depth', 50)  # 默认发射深度50m
        rx_depth = params.get('rx_depth', 20)  # 默认接收深度20m
        rx_range = params.get('rx_range', 1000)  # 默认接收距离1000m

        # 提取声速参数
        ssp_type = params.get('ssp_type', 'constant')  # 默认常数声速
        if ssp_type == 'constant':
            soundspeed = params.get('soundspeed', 1500)  # 默认声速1500m/s
        else:
            # 深度相关声速剖面
            ssp_data = params.get('ssp_data', [[0, 1500], [100, 1500]])
            soundspeed = np.array(ssp_data)

        # 声速插值方法
        soundspeed_interp = params.get('soundspeed_interp', SPLINE)

        # 提取水深参数
        depth_type = params.get('depth_type', 'flat')  # 默认平坦水深
        if depth_type == 'flat':
            # 使用标准参数名depth
            depth = params.get('depth', 100)  # 默认水深100m
        else:
            # 距离相关水深
            bathymetry_data = params.get('bathymetry_data', [[0, 100], [1000, 100]])
            depth = np.array(bathymetry_data)

        # 水深插值方法
        depth_interp = params.get('depth_interp', LINEAR)

        # 提取海底参数
        bottom_soundspeed = params.get('bottom_soundspeed', 1600)  # 默认海底声速1600m/s
        bottom_density = params.get('bottom_density', 1600)  # 默认海底密度1600kg/m³
        bottom_absorption = params.get('bottom_absorption', 0.1)  # 默认海底吸收系数0.1dB/wavelength
        bottom_roughness = params.get('bottom_roughness', 0)  # 默认海底粗糙度0m

        # 提取声源指向性参数
        tx_directivity_enabled = params.get('tx_directivity_enabled', False)
        if tx_directivity_enabled:
            directivity_data = params.get('directivity_data', [])
            tx_directionality = np.array(directivity_data) if directivity_data else None
        else:
            tx_directionality = None

        # 提取角度参数
        min_angle = params.get('min_angle', -80)  # 默认最小角度-80度
        max_angle = params.get('max_angle', 80)  # 默认最大角度80度

        # 声束数量
        nbeams = params.get('nbeams', 0)  # 默认为0，表示自动设置

        # 创建环境
        env = pm.create_env2d(
            frequency=frequency,
            soundspeed=soundspeed,
            soundspeed_interp=soundspeed_interp,
            depth=depth,
            surface_interp=depth_interp,
            tx_depth=tx_depth,
            tx_directionality=tx_directionality,
            rx_depth=rx_depth,
            rx_range=rx_range,
            bottom_soundspeed=bottom_soundspeed,
            bottom_density=bottom_density,
            bottom_absorption=bottom_absorption,
            bottom_roughness=bottom_roughness,
            min_angle=min_angle,
            max_angle=max_angle,
            nbeams=nbeams
        )

        return env

    def compute_rays(self, env, debug=False):
        """
        计算声线

        Args:
            env (dict): 声学环境
            debug (bool): 是否输出调试信息

        Returns:
            pandas.DataFrame: 声线数据
        """
        # 创建唯一的临时文件名基础
        fname_base = f"bellhop_rays_{uuid.uuid4().hex}"

        try:
            # 计算声线
            rays = pm.compute_rays(env, debug=debug, fname_base=fname_base)
            return rays
        finally:
            # 清理临时文件
            if not debug:
                self._cleanup_temp_files(fname_base)

    def compute_eigenrays(self, env, debug=False):
        """
        计算本征声线

        Args:
            env (dict): 声学环境
            debug (bool): 是否输出调试信息

        Returns:
            pandas.DataFrame: 本征声线数据
        """
        # 创建唯一的临时文件名基础
        fname_base = f"bellhop_eigenrays_{uuid.uuid4().hex}"

        try:
            # 计算本征声线
            eigenrays = pm.compute_eigenrays(env, debug=debug, fname_base=fname_base)
            return eigenrays
        finally:
            # 清理临时文件
            if not debug:
                self._cleanup_temp_files(fname_base)

    def compute_arrivals(self, env, debug=False):
        """
        计算到达结构

        Args:
            env (dict): 声学环境
            debug (bool): 是否输出调试信息

        Returns:
            pandas.DataFrame: 到达结构数据
        """
        # 创建唯一的临时文件名基础
        fname_base = f"bellhop_arrivals_{uuid.uuid4().hex}"

        try:
            # 计算到达结构
            arrivals = pm.compute_arrivals(env, debug=debug, fname_base=fname_base)
            return arrivals
        finally:
            # 清理临时文件
            if not debug:
                self._cleanup_temp_files(fname_base)

    def compute_transmission_loss(self, env, mode='coherent', debug=False):
        """
        计算传播损失

        Args:
            env (dict): 声学环境
            mode (str): 计算模式，可选值为'coherent', 'incoherent', 'semicoherent'
            debug (bool): 是否输出调试信息

        Returns:
            pandas.DataFrame: 传播损失数据
        """
        # 创建唯一的临时文件名基础
        fname_base = f"bellhop_tl_{uuid.uuid4().hex}"

        try:
            # 确定计算模式
            if mode == 'coherent':
                calc_mode = pm.coherent
            elif mode == 'incoherent':
                calc_mode = pm.incoherent
            elif mode == 'semicoherent':
                calc_mode = pm.semicoherent
            else:
                raise ValueError(f"无效的计算模式: {mode}")

            # 计算传播损失
            tloss = pm.compute_transmission_loss(env, mode=calc_mode, debug=debug, fname_base=fname_base)
            return tloss
        finally:
            # 清理临时文件
            if not debug:
                self._cleanup_temp_files(fname_base)

    def compute_single_frequency(self, freq, env_params, debug=False):
        """
        计算单个频率的到达结构

        Args:
            freq (float): 频率 (Hz)
            env_params (dict): 环境参数
            debug (bool): 是否输出调试信息

        Returns:
            pandas.DataFrame: 到达结构数据
        """
        # 创建唯一的临时文件名基础
        fname_base = f"bellhop_freq_{freq}Hz_{uuid.uuid4().hex}"

        try:
            # 创建环境
            env_copy = env_params.copy()
            env_copy['frequency'] = freq
            env = self.create_environment(env_copy)

            # 计算到达结构
            arrivals = pm.compute_arrivals(env, debug=debug, fname_base=fname_base)

            # 添加频率信息
            arrivals['frequency'] = freq

            return arrivals
        except Exception as e:
            print(f"计算频率 {freq} Hz 时出错: {str(e)}")
            return None
        finally:
            # 清理临时文件
            if not debug:
                self._cleanup_temp_files(fname_base)

    def parallel_compute_arrivals(self, frequencies, env_params, max_workers=None, progress_callback=None, debug=False):
        """
        并行计算多个频率的到达结构

        Args:
            frequencies (list): 频率列表 (Hz)
            env_params (dict): 环境参数
            max_workers (int): 最大并行进程数，None表示使用CPU核心数
            progress_callback (callable): 进度回调函数，接收一个0-1之间的浮点数表示进度
            debug (bool): 是否输出调试信息

        Returns:
            dict: 键为频率，值为对应的到达结构DataFrame
        """
        results = {}
        total_tasks = len(frequencies)
        completed_tasks = 0

        # 使用ProcessPoolExecutor进行并行计算，打印出运算耗时
        with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有计算任务
            future_to_freq = {
                executor.submit(self.compute_single_frequency, freq, env_params, debug): freq
                for freq in frequencies
            }

            # 收集结果
            for future in concurrent.futures.as_completed(future_to_freq):
                freq = future_to_freq[future]
                try:
                    arrivals = future.result()
                    if arrivals is not None:
                        results[freq] = arrivals

                    # 更新进度
                    completed_tasks += 1
                    if progress_callback:
                        progress_callback(completed_tasks / total_tasks)

                except Exception as e:
                    print(f"处理频率 {freq} Hz 的结果时出错: {str(e)}")

                    # 即使出错也更新进度
                    completed_tasks += 1
                    if progress_callback:
                        progress_callback(completed_tasks / total_tasks)

        return results

    def _cleanup_temp_files(self, fname_base):
        """
        清理临时文件

        Args:
            fname_base (str): 文件名基础
        """
        # Bellhop可能生成的文件扩展名
        extensions = ['.env', '.bty', '.ssp', '.ati', '.sbp', '.prt', '.log', '.arr', '.ray', '.shd']

        # 尝试删除每个文件
        for ext in extensions:
            try:
                os.unlink(fname_base + ext)
            except:
                pass  # 忽略删除失败的错误
