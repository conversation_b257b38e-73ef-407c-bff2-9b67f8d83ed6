@startuml 4-1-系统分层架构图
!define RECTANGLE class

package "表示层 (Presentation Layer)" {
    RECTANGLE MainWindow
    RECTANGLE ControlPanels
    RECTANGLE ViewAreas
    RECTANGLE Dialogs
}

package "业务逻辑层 (Business Logic Layer)" {
    RECTANGLE SimulationController
    RECTANGLE ProjectManager
    RECTANGLE ViewUpdaters
    RECTANGLE UIManagers
}

package "模型层 (Model Layer)" {
    RECTANGLE ShipRadiatedNoise
    RECTANGLE OceanAmbientNoise
    RECTANGLE PropagationModel
    RECTANGLE IntegratedSimulation
}

package "数据层 (Data Layer)" {
    RECTANGLE SimulationDataManager
    RECTANGLE SignalExportManager
    RECTANGLE FieldCalculator
}

package "基础设施层 (Infrastructure Layer)" {
    RECTANGLE BELLHOP
    RECTANGLE Matplotlib
    RECTANGLE NumPy_SciPy
    RECTANGLE PyQt5
}

MainWindow --> SimulationController
ControlPanels --> ViewUpdaters
ViewAreas --> ViewUpdaters
SimulationController --> ShipRadiatedNoise
SimulationController --> OceanAmbientNoise
SimulationController --> PropagationModel
SimulationController --> IntegratedSimulation
ProjectManager --> SimulationDataManager
ShipRadiatedNoise --> SimulationDataManager
OceanAmbientNoise --> SimulationDataManager
PropagationModel --> BELLHOP
SimulationDataManager --> NumPy_SciPy
ViewAreas --> Matplotlib

@enduml

@startuml 4-2-设计类图
!theme plain
hide empty members
'skinparam linetype ortho

' ===== UI Layer =====
class MainWindow {
  main_menu
  view_panel
  control_panel
  --
  initializeUI()
  handleMenuActions()
  updateDisplay()
  connectSignals()
  showFileDialog()
  displaySaveSuccessMessage()
}

class SimulationPanel {
  input_controls
  result_display
  --
  setupControls()
  validateInput()
  displayResults()
  handleUserEvents()
  updateFromDataManager()
}

class ShipNoisePanel {
  ship_param_inputs
  spectrum_displays
  propeller_config_widget
  --
  setupShipParameterInputs()
  displaySpectrumResults()
  onSimulateClicked()
  updateDisplayResults()
}

class OceanNoisePanel {
  curve_editor
  wenz_display
  point_selection_widget
  --
  setupCurveEditor()
  handlePointSelection()
  displayWenzCurve()
}

class PropagationPanel {
  environment_inputs
  ray_display
  sound_speed_widget
  --
  setupEnvironmentInputs()
  displayRayPaths()
  showTransmissionLoss()
  onGenerateChannelData()
  displayCompletionStatus()
}

class IntegratedSimPanel {
  array_config
  beamforming_display
  element_selection_widget
  --
  setupArrayConfiguration()
  displayDirectivityPattern()
  showCrossSpectralDensity()
  onGenerateSignals()
  onCalculateCharacteristics()
  displayCharacteristicsResults()
}

' ===== Controller Layer =====
class SimulationController {
  data_manager
  calculation_service
  is_running
  --
  validateParameters()
  executeSimulation()
  processResults()
  updateDataManager()
  handleSimulationProgress()
}

class ProjectController {
  current_project_path
  modification_state
  --
  createNewProject()
  saveProject()
  loadProject()
  trackModifications()
  startProjectSave()
  saveParametersAndSignals()
  saveCompleted()
}

class ShipNoiseController {
  --
  calculateContinuousSpectrum()
  calculateLineSpectrum()
  calculateModulationSpectrum()
  startSimulation()
  simulationCompleted()
}

class OceanNoiseController {
  --
  processUserCurvePoints()
  designFIRFilter()
  generateFilteredNoise()
}

class PropagationController {
  --
  computeEnvironmentModel()
  calculateRayPaths()
  computeTransmissionLoss()
  prepareChannelData()
  startChannelDataGeneration()
  outputFilesToDirectory()
  generationCompleted()
}

class IntegratedController {
  --
  processArraySignals()
  calculateDirectivity()
  computeCrossSpectralDensity()
  performBeamforming()
  loadChannelData()
  startSignalGeneration()
  startCharacteristicsCalculation()
  signalGenerationCompleted()
  characteristicsCalculationCompleted()
}

class ExportController {
  export_formats
  data_manager
  --
  validateExportParams()
  exportWAVFiles()
  exportNPZFiles()
  exportMATFiles()
  generateMetadata()
}

' ===== Data Management Layer =====
class DataManager {
  global_parameters
  module_parameters
  simulation_results
  simulation_states
  --
  setParameters(module, params)
  getParameters(module)
  setResults(module, results)
  getResults(module)
  setGlobalParam(key, value)
  getGlobalParam(key)
  notifyParametersChanged(module)
  notifyResultsChanged(module)
}

' ===== Data Transfer Object Layer =====
class SimulationData {
  module_name
  timestamp
  parameters
  results
  --
  getParameters()
  setParameters()
  getResults()
  setResults()
}

'class ChannelData {
 ' array_geometry
  'frequency_list
  'arrival_structure
  'impulse_responses
  '--
  'getChannelResponse(frequency)
  'interpolateResponse(new_frequency)
'}

' ===== Calculation Service Layer =====
class CalculationService {
  --
  executeCalculation(parameters)
  validateInputs(parameters)
  processResults(raw_results)
}

class ShipNoiseCalculationService {
  --
  generateSignal(ship_params, continuous_params, line_params, modulation_params)
  calculateSpectrum(signal_data)
}

class OceanNoiseCalculationService {
  --
  generateNoise(curve_points, filter_params)
  designFIRFilter(frequency_response)
}

class PropagationCalculationService {
  --
  computeEnvironment(sound_speed_profile, bathymetry_data)
  generateChannelData(array_geometry, source_position)
  calculateTransmissionLoss(frequency, range_data)
}

class IntegratedCalculationService {
  --
  simulateArraySignals(source_signal, channel_data, ambient_noise)
  calculateDirectivity(array_signals, scan_angles)
  computeCrossSpectralDensity(element_pair, signal_data)
}

' ===== Inheritance Relationships =====
SimulationPanel <|-up- ShipNoisePanel
SimulationPanel <|-up- OceanNoisePanel
SimulationPanel <|-up- PropagationPanel
SimulationPanel <|-up- IntegratedSimPanel

SimulationController <|-right- ShipNoiseController
SimulationController <|-right- OceanNoiseController
SimulationController <|-- PropagationController
SimulationController <|-- IntegratedController

CalculationService <|-- ShipNoiseCalculationService
CalculationService <|-right- OceanNoiseCalculationService
CalculationService <|-- PropagationCalculationService
CalculationService <|-- IntegratedCalculationService
'SimulationData <|-- ChannelData

' ===== Core Architecture Relationships =====
MainWindow *-- SimulationPanel
MainWindow --> ProjectController : project operations
MainWindow --> ExportController : export operations

SimulationPanel --> SimulationController : delegates to
SimulationController --> DataManager : gets/stores data
SimulationController --> CalculationService : calls methods

ProjectController --> DataManager : accesses
ExportController --> DataManager : accesses

' ===== Data Flow Relationships =====
DataManager --> SimulationPanel : notifies changes
SimulationController -right-> SimulationData : creates/uses

@enduml

@startuml 4-3-船舶噪声仿真时序图
participant "用户界面" as UI
participant "仿真UI管理器" as SUM
participant "仿真控制器" as SC
participant "船舶噪声控制器" as SNC
participant "船舶噪声模型" as SNM
participant "数据管理器" as DM
participant "视图更新器" as VU

UI -> SUM: 点击开始仿真
SUM -> SC: 启动船舶噪声仿真
SC -> SNC: 创建并启动控制器线程
activate SNC

SNC -> DM: 获取仿真参数
DM --> SNC: 返回参数数据

SNC -> SNM: 创建模型实例
SNC -> SNM: 设置连续谱参数
SNC -> SNM: 设置线谱参数
SNC -> SNM: 设置调制谱参数

SNC -> SNM: 执行连续谱仿真
activate SNM
SNM --> SNC: 返回连续谱信号
deactivate SNM

SNC -> SNM: 执行线谱仿真
activate SNM
SNM --> SNC: 返回线谱信号
deactivate SNM

SNC -> SNM: 执行调制谱仿真
activate SNM
SNM --> SNC: 返回调制谱信号
deactivate SNM

SNC -> SNM: 计算功率谱密度
activate SNM
SNM --> SNC: 返回频谱数据
deactivate SNM

SNC -> DM: 保存仿真结果
SNC -> SC: 发送仿真完成信号
deactivate SNC

SC -> VU: 触发视图更新
VU -> DM: 获取最新结果
DM --> VU: 返回结果数据
VU -> UI: 更新图表显示

@enduml

@startuml 4-4-数据管理器状态图
[*] --> 初始化
初始化 : 创建数据结构
初始化 : 初始化锁机制
初始化 --> 就绪

就绪 --> 参数设置 : set_parameter()
参数设置 : 验证参数
参数设置 : 更新数据
参数设置 : 发送变更信号
参数设置 --> 就绪

就绪 --> 结果存储 : set_result()
结果存储 : 存储计算结果
结果存储 : 发送变更信号
结果存储 --> 就绪

就绪 --> 数据查询 : get_parameter()/get_result()
数据查询 : 返回请求数据
数据查询 --> 就绪

就绪 --> 项目保存 : save_project()
项目保存 : 序列化数据
项目保存 : 写入文件
项目保存 --> 就绪

就绪 --> 项目加载 : load_project()
项目加载 : 读取文件
项目加载 : 反序列化数据
项目加载 : 发送变更信号
项目加载 --> 就绪

@enduml

@startuml 4-4-海洋环境噪声仿真核心算法设计流程图
start

:获取用户定义的频谱特性;
note right: 支持点选和文件导入

:验证频谱数据完整性;

if (数据验证通过?) then (是)
    :频谱曲线插值处理;
    note right: 生成连续频谱曲线

    :设计频域滤波器;
    note right: 基于目标频谱特性

    :生成随机噪声源;
    note right: 满足统计特性要求

    :执行频域滤波;
    note right: 实现目标频谱塑形

    :频谱校准处理;
    note right: 确保频谱精度

    :输出噪声信号;

else (否)
    :输出错误信息;
endif

stop
@enduml

@startuml 4-5-综合仿真活动图
start

:获取仿真参数;
:验证参数完整性;

if (参数验证通过?) then (是)
    :准备信道数据;

    fork
        :计算声传播信道;
    fork again
        :准备源信号;
    end fork

    :设置阵列几何;
    :设置接收器位置;

    repeat
        :选择频率点;
        :计算该频率的传播损失;
        :获取声线到达时间-幅度;
        :构建信道响应;
    repeat while (还有频率点?)

    :合成宽带信道数据;
    :应用信道效应到源信号;
    :计算阵列接收信号;
    :保存仿真结果;
    :发送完成信号;

else (否)
    :报告参数错误;
endif

stop
@enduml

@startuml 4-7-延迟求和波束成形算法设计流程图
start

:获取阵列信号数据;
:获取阵元几何配置;
:获取扫描角度范围;

:确定参考坐标系;
note right: 支持多种参考位置选择

:计算阵元相对位置;

partition "波束形成处理" {
    :计算几何时延;
    note right: 基于阵列几何和扫描方向

    :选择时延补偿策略;
    note right: 频域/时域补偿方法

    :执行信号时延对齐;

    :应用阵元加权;
    note right: 支持多种窗函数

    :信号相干合成;

    :计算方向响应;
}

:生成波束方向图;
:输出指向性结果;

stop
@enduml

@startuml 4-8-阵列信号生成算法设计流程图
start

:获取多源输入数据;
note right: 源信号、信道数据、阵列配置

:数据预处理与验证;
note right: 时间对齐、格式统一

:信号频域分解;
note right: 子带划分策略

partition "子带处理循环" {
    :选择频率子带;

    :提取子带信号;
    note right: 窗函数处理

    :获取对应频率信道响应;
    note right: 频率插值匹配

    :频域卷积计算;
    note right: 优化的FFT乘法

    :相位一致性处理;
    note right: 保持物理正确性
}

:子带信号相干合成;
note right: 时间轴对齐

if (包含背景噪声?) then (是)
    :噪声信号叠加;
    note right: 独立噪声源处理
endif

:多阵元信号输出;
note right: 物理时间轴统一

stop
@enduml

@startuml 4-9-系统组件图
package "用户界面层" {
    component [主窗口] as MainWin
    component [控制面板] as ControlPanel
    component [视图区域] as ViewArea
    component [对话框] as Dialogs
}

package "业务逻辑层" {
    component [仿真控制器] as SimController
    component [项目管理器] as ProjManager
    component [视图更新器] as ViewUpdater
    component [UI管理器] as UIManager
}

package "模型计算层" {
    component [船舶噪声模型] as ShipModel
    component [环境噪声模型] as AmbientModel
    component [声传播模型] as PropModel
    component [综合仿真模型] as IntegratedModel
}

package "数据管理层" {
    component [数据管理器] as DataManager
    component [信号导出器] as SignalExporter
}

package "基础设施层" {
    component [BELLHOP引擎] as BELLHOP
    component [科学计算库] as SciLib
    component [可视化库] as VisLib
    component [GUI框架] as GUIFramework
}

MainWin --> SimController
ControlPanel --> UIManager
ViewArea --> ViewUpdater
SimController --> ShipModel
SimController --> AmbientModel
SimController --> PropModel
SimController --> IntegratedModel
ProjManager --> DataManager
ShipModel --> DataManager
AmbientModel --> DataManager
PropModel --> BELLHOP
ViewUpdater --> DataManager
ViewArea --> VisLib
MainWin --> GUIFramework
ShipModel --> SciLib
AmbientModel --> SciLib

interface "数据访问接口" as DataInterface
interface "仿真控制接口" as SimInterface
interface "视图更新接口" as ViewInterface

DataManager - DataInterface
SimController - SimInterface
ViewUpdater - ViewInterface

@enduml


' ========== 顺序图1：船舶辐射噪声仿真操作 ==========
@startuml 4-10-船舶辐射噪声仿真操作顺序图
'!theme plain
skinparam sequenceMessageAlign center
participant "shipNoisePanel:\nShipNoisePanel" as shipPanel
participant "shipController:\nShipNoiseController" as shipController
participant "dataManager:\nDataManager" as dataManager
participant "shipCalcService:\nShipNoiseCalculationService" as shipCalcService

[o-> shipPanel : onSimulateClicked()
activate shipPanel
shipPanel ->> shipController : simulationRequested()  //signal
note right : Asynchronous signal to start simulation\n(Non-blocking UI)
note over shipController : QThread execution begins\nin background
activate shipController
shipController -> dataManager : getParameters()
activate dataManager
dataManager -->> shipController : parameters
deactivate dataManager
shipController -> shipCalcService : generateSignal()
activate shipCalcService
note right shipCalcService : Calculate continuous spectrum,\nline spectrum, modulation spectrum\nand synthesize final signal
shipCalcService -->> shipController : simulation results
deactivate shipCalcService
shipController -> dataManager : setResults()
activate dataManager
dataManager -->> shipPanel : resultsChanged()  //signal
note right : Asynchronous signal notification\n(Observer pattern)
shipPanel -> shipPanel : updateDisplayResults()
activate shipPanel
dataManager -->> shipController
deactivate shipController

deactivate dataManager
shipPanel -->> shipPanel
deactivate shipPanel
@enduml


' ========== 顺序图2：生成信道数据操作 ==========
@startuml 4-11-生成信道数据操作顺序图
'!theme plain
skinparam sequenceMessageAlign center

participant "propagationPanel:\nPropagationPanel" as propPanel
participant "propController:\nPropagationController" as propController
participant "dataManager:\nDataManager" as dataManager
participant "propCalcService:\nPropagationCalculationService" as propCalcService

[o-> propPanel : onGenerateChannelData()
activate propPanel
propPanel ->> propController : generationRequested()  //signal
note right : Asynchronous signal to start generation\n(Non-blocking UI)
note over propController : QThread execution begins\nin background
activate propController
propController -> dataManager : getParameters()
activate dataManager
dataManager -->> propController : parameters
deactivate dataManager
propController -> propCalcService : generateChannelData()
activate propCalcService
note right propCalcService : Parallel calculation of\nray arrivals at different\nfrequencies for all\nreceiver positions
propCalcService -->> propController : channel data
deactivate propCalcService
propController -> propController : outputFilesToDirectory()
activate propController
propController -->> propController
deactivate propController
propController ->> propPanel : generationCompleted()  //signal
note right : Asynchronous completion notification
deactivate propController
propPanel -> propPanel : displayCompletionStatus()
activate propPanel
propPanel -->> propPanel
deactivate propPanel

@enduml


' ========== 顺序图3：综合声场仿真操作 ==========
@startuml 4-12-综合声场仿真操作顺序图
'!theme plain
skinparam sequenceMessageAlign center

participant "integratedPanel:\nIntegratedSimPanel" as integratedPanel
participant "integratedController:\nIntegratedController" as integratedController
participant "dataManager:\nDataManager" as dataManager
participant "integratedCalcService:\nIntegratedCalculationService" as integratedCalcService

[o-> integratedPanel : onGenerateSignals()
activate integratedPanel
integratedPanel ->> integratedController : simulationRequested('signalGeneration')  //signal
note right : Asynchronous signal to start simulation\n(Non-blocking UI)
note over integratedController : QThread execution begins\nin background
activate integratedController
integratedController -> dataManager : getResults('channel_data')
activate dataManager
dataManager -->> integratedController : channel data
deactivate dataManager
integratedController -> dataManager : getResults('ship_noise')
activate dataManager
dataManager -->> integratedController : ship noise signal
deactivate dataManager
integratedController -> dataManager : getResults('ambient_noise')
activate dataManager
dataManager -->> integratedController : ocean noise signal
deactivate dataManager
integratedController -> integratedCalcService : simulateArraySignals()
activate integratedCalcService
note right integratedCalcService : Generate array element\nsignals based on channel\ndata and source signals
integratedCalcService -->> integratedController : array signals
deactivate integratedCalcService
integratedController -> dataManager : setResults()
activate dataManager
dataManager ->> integratedPanel : resultsChanged()  //signal
note right : Asynchronous signal notification\n(Observer pattern)
integratedPanel -> integratedPanel : updateDisplayResults()
activate integratedPanel
dataManager -->> integratedController
deactivate integratedController

deactivate dataManager
integratedPanel -->> integratedPanel
deactivate integratedPanel

@enduml


' ========== 顺序图4：项目保存操作 ==========
@startuml 4-13-项目保存操作顺序图
'!theme plain
skinparam sequenceMessageAlign center

participant "mainWindow:\nMainWindow" as mainWindow
participant "projectController:\nProjectController" as projectController
participant "dataManager:\nDataManager" as dataManager

[o-> mainWindow : onSaveProject()
activate mainWindow
mainWindow -> mainWindow : showFileDialog()
activate mainWindow
mainWindow -->> mainWindow : file path
deactivate mainWindow
mainWindow -> projectController : startProjectSave('file_path')
activate projectController
projectController -> dataManager : getParameters()
activate dataManager
dataManager -->> projectController : parameters data
deactivate dataManager
projectController -> dataManager : getResults('ship_noise')
activate dataManager
dataManager -->> projectController : ship noise signal
deactivate dataManager
projectController -> dataManager : getResults('ambient_noise')
activate dataManager
dataManager -->> projectController : ocean noise signal
deactivate dataManager
projectController -> projectController : saveParametersAndSignals()
activate projectController
note right projectController : Save project data using\nJSON+NPZ format
projectController -->> projectController
deactivate projectController
projectController -->> mainWindow
deactivate projectController
mainWindow -> mainWindow : displaySaveSuccessMessage()
activate mainWindow
mainWindow -->> mainWindow
deactivate mainWindow

@enduml


' ========== 以下为第五章实现阶段的详细算法流程图 ==========

@startuml 海洋环境噪声仿真详细实现流程图
start

:获取用户定义的频谱特性;
note right: 频率-功率谱密度点对

:验证频谱数据有效性;

if (数据验证通过?) then (是)
    :进行三次样条插值;
    note right: 生成完整的频谱曲线

    :设计FIR滤波器;
    note right: 基于目标频谱特性\n滤波器阶数：16385

    :生成高斯白噪声;
    note right: 均值为0，方差为1

    :应用FIR滤波器;
    note right: 时域卷积或频域乘法

    :计算滤波后信号的功率谱密度;

    :计算谱级校准因子;
    note right: K_power = 目标峰值PSD / 实测峰值PSD

    :应用校准因子;
    note right: 信号缩放：sqrt(K_power)

    :保存最终噪声信号;

else (否)
    :报告数据错误;
endif

stop
@enduml

@startuml 延迟求和波束成形详细实现流程图
start

:获取阵列信号片段;
:获取阵元位置信息;
:获取扫描角度列表;

:确定参考位置;
note right: 'center', 'first', 'last'\n或具体坐标

:计算相对位置;
note right: 各阵元相对于参考位置

partition "对每个扫描角度" {
    :计算扫描方向向量;
    note right: direction = [cos(θ), sin(θ)]

    :计算各阵元几何时延;
    note right: τ_k = (p_k · u_scan) / c

    :选择时延补偿方法;

    if (补偿方法?) then (相位补偿)
        :计算FFT;
        :计算相位移动;
        note right: exp(-j*2π*(-delays)*freqs)
        :频域相位补偿;
        :逆FFT回时域;
    elseif (整数延迟)
        :计算补偿时延样本数;
        note right: round((-delay) * fs)
        :整数样本移位;
    elseif (分数延迟)
        :计算补偿时延样本数;
        note right: (-delay) * fs
        :线性插值实现分数延迟;
    endif

    :应用加权函数;
    note right: uniform, hanning,\nhamming, blackman

    :加权求和;
    note right: beamformed_signal = Σ(w_k * delayed_signal_k)

    :计算波束输出功率;
    note right: mean(beamformed_signal²)
}

:归一化波束方向图;
:返回角度和波束方向图;

stop
@enduml

@startuml 阵列信号生成详细实现流程图
start

:加载源信号数据;
:加载信道数据;
:加载阵列几何配置;

:初始化信号处理参数;
note right: 采样率、信号长度、FFT长度

:预处理源信号;
note right: 补零、窗函数处理

partition "频域卷积处理" {
    :计算源信号FFT;

    repeat
        :选择当前阵元;
        :加载对应信道响应;
        :计算信道响应FFT;
        :执行频域乘法;
        note right: R_fft = S_fft * H_fft
        :计算逆FFT;
        :提取有效信号段;
        :时间轴对齐;
        :保存阵元接收信号;
    repeat while (还有阵元?)
}

if (包含背景噪声?) then (是)
    :生成背景噪声;
    :叠加到接收信号;
endif

:计算信号统计特性;
:保存阵列信号结果;

stop
@enduml