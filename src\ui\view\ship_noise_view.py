# -*- coding: utf-8 -*-
"""
船舶辐射噪声视图

用于显示船舶辐射噪声的频谱图等
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QTabWidget
from matplotlib.figure import Figure
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
import numpy as np


class ShipNoiseView(QWidget):
    """
    船舶辐射噪声视图

    用于显示船舶辐射噪声的频谱图等
    """

    def __init__(self, parent=None):
        """
        初始化船舶辐射噪声视图

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 创建布局
        layout = QVBoxLayout(self)

        # 创建选项卡用于切换不同图表
        self.tabs = QTabWidget()

        # 创建连续谱图表
        self.continuous_canvas = FigureCanvas(Figure(figsize=(8, 8)))
        self.continuous_fig = self.continuous_canvas.figure

        # 创建连续谱工具栏
        self.continuous_toolbar = NavigationToolbar(self.continuous_canvas, self)

        # 连续谱时域图
        self.continuous_time_ax = self.continuous_fig.add_subplot(211)
        self.continuous_time_ax.set_title('船舶辐射噪声连续谱 - 时域信号')
        self.continuous_time_ax.set_xlabel('时间 (s)')
        self.continuous_time_ax.set_ylabel('幅度')
        self.continuous_time_ax.set_xlim(0, 1.0)  # 显示1秒的时域信号
        self.continuous_time_ax.grid(True)

        # 连续谱频域图
        self.continuous_freq_ax = self.continuous_fig.add_subplot(212)
        self.continuous_freq_ax.set_title('船舶辐射噪声连续谱 - 功率谱密度')
        self.continuous_freq_ax.set_xlabel('频率 (Hz)')
        self.continuous_freq_ax.set_ylabel('功率谱密度 (dB)')
        self.continuous_freq_ax.set_xlim(0, 2500)  # x轴范围0-2500Hz
        self.continuous_freq_ax.grid(True)

        self.continuous_fig.tight_layout()

        # 创建线谱图表
        self.line_canvas = FigureCanvas(Figure(figsize=(8, 8)))
        self.line_fig = self.line_canvas.figure

        # 创建线谱工具栏
        self.line_toolbar = NavigationToolbar(self.line_canvas, self)

        # 线谱时域图
        self.line_time_ax = self.line_fig.add_subplot(211)
        self.line_time_ax.set_title('船舶辐射噪声线谱 - 时域信号')
        self.line_time_ax.set_xlabel('时间 (s)')
        self.line_time_ax.set_ylabel('幅度')
        self.line_time_ax.set_xlim(0, 1.0)  # 显示1秒的时域信号
        self.line_time_ax.grid(True)

        # 线谱频域图
        self.line_freq_ax = self.line_fig.add_subplot(212)
        self.line_freq_ax.set_title('船舶辐射噪声线谱 - 功率谱密度')
        self.line_freq_ax.set_xlabel('频率 (Hz)')
        self.line_freq_ax.set_ylabel('功率谱密度 (dB)')
        self.line_freq_ax.set_xlim(0, 2500)  # x轴范围0-2500Hz
        self.line_freq_ax.grid(True)

        self.line_fig.tight_layout()

        # 创建辐射噪声图表
        self.radiated_canvas = FigureCanvas(Figure(figsize=(8, 8)))
        self.radiated_fig = self.radiated_canvas.figure

        # 创建辐射噪声工具栏
        self.radiated_toolbar = NavigationToolbar(self.radiated_canvas, self)

        # 辐射噪声时域图
        self.radiated_time_ax = self.radiated_fig.add_subplot(211)
        self.radiated_time_ax.set_title('船舶辐射噪声 - 时域信号')
        self.radiated_time_ax.set_xlabel('时间 (s)')
        self.radiated_time_ax.set_ylabel('幅度')
        self.radiated_time_ax.set_xlim(0, 1.0)  # 显示1秒的时域信号
        self.radiated_time_ax.grid(True)

        # 辐射噪声频域图
        self.radiated_freq_ax = self.radiated_fig.add_subplot(212)
        self.radiated_freq_ax.set_title('船舶辐射噪声 - 功率谱密度')
        self.radiated_freq_ax.set_xlabel('频率 (Hz)')
        self.radiated_freq_ax.set_ylabel('功率谱密度 (dB)')
        self.radiated_freq_ax.set_xlim(0, 2500)  # x轴范围0-2500Hz
        self.radiated_freq_ax.grid(True)

        self.radiated_fig.tight_layout()

        # 创建调制谱图表
        self.modulation_canvas = FigureCanvas(Figure(figsize=(8, 8)))
        self.modulation_fig = self.modulation_canvas.figure

        # 创建调制谱工具栏
        self.modulation_toolbar = NavigationToolbar(self.modulation_canvas, self)

        # 调制谱时域图
        self.modulation_time_ax = self.modulation_fig.add_subplot(211)
        self.modulation_time_ax.set_title('船舶辐射噪声调制谱 - 时域信号')
        self.modulation_time_ax.set_xlabel('时间 (s)')
        self.modulation_time_ax.set_ylabel('幅度')
        self.modulation_time_ax.set_xlim(0, 1.0)  # 显示1秒的时域信号
        self.modulation_time_ax.grid(True)

        # 调制前后连续谱对比图
        self.modulation_freq_ax = self.modulation_fig.add_subplot(212)
        self.modulation_freq_ax.set_title('调制前后连续谱对比 - 功率谱密度')
        self.modulation_freq_ax.set_xlabel('频率 (Hz)')
        self.modulation_freq_ax.set_ylabel('功率谱密度 (dB)')
        self.modulation_freq_ax.set_xlim(0, 2500)  # x轴范围0-2500Hz，与连续谱保持一致
        self.modulation_freq_ax.grid(True)

        self.modulation_fig.tight_layout()

        # 为每个图表创建容器和布局
        self.continuous_container = QWidget()
        continuous_layout = QVBoxLayout(self.continuous_container)
        continuous_layout.addWidget(self.continuous_toolbar)
        continuous_layout.addWidget(self.continuous_canvas)
        continuous_layout.setContentsMargins(0, 0, 0, 0)
        continuous_layout.setSpacing(0)

        self.line_container = QWidget()
        line_layout = QVBoxLayout(self.line_container)
        line_layout.addWidget(self.line_toolbar)
        line_layout.addWidget(self.line_canvas)
        line_layout.setContentsMargins(0, 0, 0, 0)
        line_layout.setSpacing(0)

        self.modulation_container = QWidget()
        modulation_layout = QVBoxLayout(self.modulation_container)
        modulation_layout.addWidget(self.modulation_toolbar)
        modulation_layout.addWidget(self.modulation_canvas)
        modulation_layout.setContentsMargins(0, 0, 0, 0)
        modulation_layout.setSpacing(0)

        self.radiated_container = QWidget()
        radiated_layout = QVBoxLayout(self.radiated_container)
        radiated_layout.addWidget(self.radiated_toolbar)
        radiated_layout.addWidget(self.radiated_canvas)
        radiated_layout.setContentsMargins(0, 0, 0, 0)
        radiated_layout.setSpacing(0)

        # 添加到选项卡
        self.tabs.addTab(self.continuous_container, "连续谱")
        self.tabs.addTab(self.line_container, "线谱")
        self.tabs.addTab(self.modulation_container, "调制谱")
        self.tabs.addTab(self.radiated_container, "辐射噪声")

        # 添加到布局
        layout.addWidget(self.tabs)

    def update_continuous_spectrum(self, time_data, time_signal, freq_data, psd_data):
        """
        更新连续谱图表

        Args:
            time_data: 时间数组
            time_signal: 时域信号
            freq_data: 频率数组
            psd_data: 功率谱密度数组 (dB/Hz)
        """
        # 更新时域图
        self.continuous_time_ax.clear()
        self.continuous_time_ax.plot(time_data, time_signal, 'b-')
        self.continuous_time_ax.set_title('船舶辐射噪声连续谱 - 时域信号')
        self.continuous_time_ax.set_xlabel('时间 (s)')
        self.continuous_time_ax.set_ylabel('幅度')
        self.continuous_time_ax.set_xlim(0, 1.0)  # 显示1秒的时域信号
        self.continuous_time_ax.grid(True)

        # 更新频域图
        self.continuous_freq_ax.clear()
        # 将小于0的值设为0
        psd_data_positive = np.maximum(psd_data, 0)
        self.continuous_freq_ax.plot(freq_data, psd_data_positive, 'b-')
        self.continuous_freq_ax.set_title('船舶辐射噪声连续谱 - 功率谱密度')
        self.continuous_freq_ax.set_xlabel('频率 (Hz)')
        self.continuous_freq_ax.set_ylabel('功率谱密度 (dB)')
        self.continuous_freq_ax.set_xlim(0, 2500)  # x轴范围0-2500Hz
        self.continuous_freq_ax.grid(True)

        self.continuous_fig.tight_layout()
        self.continuous_canvas.draw()

    def update_line_spectrum(self, time_data, time_signal, freq_data, psd_data):
        """
        更新线谱图表

        Args:
            time_data: 时间数组
            time_signal: 时域信号
            freq_data: 频率数组
            psd_data: 功率谱密度数组 (dB/Hz)
        """
        # 更新时域图
        self.line_time_ax.clear()
        self.line_time_ax.plot(time_data, time_signal, 'r-')
        self.line_time_ax.set_title('船舶辐射噪声线谱 - 时域信号')
        self.line_time_ax.set_xlabel('时间 (s)')
        self.line_time_ax.set_ylabel('幅度')
        self.line_time_ax.set_xlim(0, 1.0)  # 显示1秒的时域信号
        self.line_time_ax.grid(True)

        # 更新频域图
        self.line_freq_ax.clear()
        # 将小于0的值设为0
        psd_data_positive = np.maximum(psd_data, 0)
        self.line_freq_ax.plot(freq_data, psd_data_positive, 'r-')
        self.line_freq_ax.set_title('船舶辐射噪声线谱 - 功率谱密度')
        self.line_freq_ax.set_xlabel('频率 (Hz)')
        self.line_freq_ax.set_ylabel('功率谱密度 (dB)')
        self.line_freq_ax.set_xlim(0, 2500)  # x轴范围0-2500Hz
        self.line_freq_ax.grid(True)

        self.line_fig.tight_layout()
        self.line_canvas.draw()

    def update_radiated_noise(self, time_data, time_signal, freq_data, psd_data):
        """
        更新辐射噪声图表

        Args:
            time_data: 时间数组
            time_signal: 时域信号
            freq_data: 频率数组
            psd_data: 功率谱密度数组 (dB/Hz)
        """
        # 更新时域图
        self.radiated_time_ax.clear()
        self.radiated_time_ax.plot(time_data, time_signal, 'g-')
        self.radiated_time_ax.set_title('船舶辐射噪声 - 时域信号')
        self.radiated_time_ax.set_xlabel('时间 (s)')
        self.radiated_time_ax.set_ylabel('幅度')
        self.radiated_time_ax.set_xlim(0, 1.0)  # 显示1秒的时域信号
        self.radiated_time_ax.grid(True)

        # 更新频域图
        self.radiated_freq_ax.clear()
        # 将小于0的值设为0
        psd_data_positive = np.maximum(psd_data, 0)
        self.radiated_freq_ax.plot(freq_data, psd_data_positive, 'g-')
        self.radiated_freq_ax.set_title('船舶辐射噪声 - 功率谱密度')
        self.radiated_freq_ax.set_xlabel('频率 (Hz)')
        self.radiated_freq_ax.set_ylabel('功率谱密度 (dB)')
        self.radiated_freq_ax.set_xlim(0, 2500)  # x轴范围0-2500Hz
        self.radiated_freq_ax.grid(True)

        self.radiated_fig.tight_layout()
        self.radiated_canvas.draw()

    def update_modulation_spectrum(self, time_data, time_signal, freq_data, original_psd_data, modulated_psd_data):
        """
        更新调制谱图表

        Args:
            time_data: 时间数组
            time_signal: 调制谱时域信号
            freq_data: 频率数组
            original_psd_data: 原始连续谱功率谱密度数组 (dB/Hz)
            modulated_psd_data: 调制后连续谱功率谱密度数组 (dB/Hz)
        """
        # 更新时域图 - 显示调制谱信号
        self.modulation_time_ax.clear()
        self.modulation_time_ax.plot(time_data, time_signal, 'm-')
        self.modulation_time_ax.set_title('船舶辐射噪声调制谱 - 时域信号')
        self.modulation_time_ax.set_xlabel('时间 (s)')
        self.modulation_time_ax.set_ylabel('幅度')
        self.modulation_time_ax.set_xlim(0, 1.0)  # 显示1秒的时域信号
        self.modulation_time_ax.grid(True)

        # 更新频域图 - 显示调制前后的连续谱对比
        self.modulation_freq_ax.clear()
        # 将小于0的值设为0
        original_psd_data_positive = np.maximum(original_psd_data, 0)
        modulated_psd_data_positive = np.maximum(modulated_psd_data, 0)

        # 绘制原始连续谱和调制后连续谱
        self.modulation_freq_ax.plot(freq_data, original_psd_data_positive, 'b-', label='原始连续谱')
        self.modulation_freq_ax.plot(freq_data, modulated_psd_data_positive, 'r-', label='调制后连续谱')

        self.modulation_freq_ax.set_title('调制前后连续谱对比 - 功率谱密度')
        self.modulation_freq_ax.set_xlabel('频率 (Hz)')
        self.modulation_freq_ax.set_ylabel('功率谱密度 (dB)')
        self.modulation_freq_ax.set_xlim(0, 2500)  # x轴范围0-2500Hz，与连续谱保持一致
        self.modulation_freq_ax.legend()
        self.modulation_freq_ax.grid(True)

        self.modulation_fig.tight_layout()
        self.modulation_canvas.draw()
