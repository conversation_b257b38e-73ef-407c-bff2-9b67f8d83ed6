# -*- coding: utf-8 -*-
"""
船舶工况参数输入对话框

用于输入船舶工况参数，计算连续谱参数
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QDoubleSpinBox, QPushButton, QGroupBox,
    QDialogButtonBox, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
import math

from src.models.noise_sources.ship_parameters import calculate_spectrum_params, convert_spectrum_params_to_ui_params


class ShipConditionDialog(QDialog):
    """
    船舶工况参数输入对话框

    用于输入船舶工况参数，计算连续谱参数
    """

    # 自定义信号
    params_calculated = pyqtSignal(dict)  # 参数计算完成信号，参数为计算结果字典

    def __init__(self, parent=None):
        """
        初始化船舶工况参数输入对话框

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 设置对话框属性
        self.setWindowTitle("船舶工况参数输入")
        self.setMinimumWidth(400)
        self.setMinimumHeight(400)

        # 创建UI
        self.init_ui()

        # 计算结果
        self.calculation_result = None

    def init_ui(self):
        """
        初始化UI
        """
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建参数输入组
        params_group = QGroupBox("工况参数输入")
        params_layout = QFormLayout()

        # 螺旋桨直径
        self.D_spin = QDoubleSpinBox()
        self.D_spin.setRange(0.1, 10.0)
        self.D_spin.setValue(1.600)
        self.D_spin.setSingleStep(0.1)
        self.D_spin.setDecimals(3)
        self.D_spin.setSuffix(" m")
        params_layout.addRow("螺旋桨直径 (D):", self.D_spin)

        # 螺旋桨进速系数
        self.J_P_spin = QDoubleSpinBox()
        self.J_P_spin.setRange(0.1, 2.0)
        self.J_P_spin.setValue(0.470)
        self.J_P_spin.setSingleStep(0.01)
        self.J_P_spin.setDecimals(3)
        params_layout.addRow("螺旋桨进速系数 (J_P):", self.J_P_spin)

        # 伴流分数
        self.omega_spin = QDoubleSpinBox()
        self.omega_spin.setRange(0.0, 0.9)
        self.omega_spin.setValue(0.210)
        self.omega_spin.setSingleStep(0.01)
        self.omega_spin.setDecimals(3)
        params_layout.addRow("伴流分数 (ω):", self.omega_spin)

        # 空泡评价数
        self.sigma_i_spin = QDoubleSpinBox()
        self.sigma_i_spin.setRange(0.01, 10.0)
        self.sigma_i_spin.setValue(3.600)
        self.sigma_i_spin.setSingleStep(0.01)
        self.sigma_i_spin.setDecimals(3)
        params_layout.addRow("空泡评价数 (σ_i):", self.sigma_i_spin)

        # 航行深度
        self.H_spin = QDoubleSpinBox()
        self.H_spin.setRange(0.1, 1000.0)
        self.H_spin.setValue(30.000)
        self.H_spin.setSingleStep(1.0)
        self.H_spin.setDecimals(3)
        self.H_spin.setSuffix(" m")
        params_layout.addRow("航行深度 (H):", self.H_spin)

        # 航速
        self.v_op_kn_spin = QDoubleSpinBox()
        self.v_op_kn_spin.setRange(0.1, 50.0)
        self.v_op_kn_spin.setValue(7.500)
        self.v_op_kn_spin.setSingleStep(0.1)
        self.v_op_kn_spin.setDecimals(3)
        self.v_op_kn_spin.setSuffix(" knots")
        params_layout.addRow("航速 (v_op):", self.v_op_kn_spin)

        # 螺旋桨转速
        self.N_op_rpm_spin = QDoubleSpinBox()
        self.N_op_rpm_spin.setRange(1.0, 1000.0)
        self.N_op_rpm_spin.setValue(245.000)
        self.N_op_rpm_spin.setSingleStep(1.0)
        self.N_op_rpm_spin.setDecimals(3)
        self.N_op_rpm_spin.setSuffix(" r/min")
        params_layout.addRow("螺旋桨转速 (N_op):", self.N_op_rpm_spin)

        params_group.setLayout(params_layout)
        main_layout.addWidget(params_group)

        # 创建结果预览组
        result_group = QGroupBox("计算结果预览")
        result_layout = QFormLayout()

        # 空化状态
        self.cavitation_state_label = QLabel("未计算")
        result_layout.addRow("空化状态:", self.cavitation_state_label)

        # 峰值频率
        self.f0_label = QLabel("未计算")
        result_layout.addRow("峰值频率 (f_0):", self.f0_label)

        # 峰值声源级
        self.sl0_label = QLabel("未计算")
        result_layout.addRow("峰值声源级 (SL_0):", self.sl0_label)

        # 上升斜率
        self.rise_slope_label = QLabel("未计算")
        result_layout.addRow("上升斜率 (dB/Oct):", self.rise_slope_label)

        # 下降斜率
        self.fall_slope_label = QLabel("未计算")
        result_layout.addRow("下降斜率 (dB/Oct):", self.fall_slope_label)

        # 螺旋桨叶尖线速度
        self.tip_speed_label = QLabel("未计算")
        result_layout.addRow("叶尖线速度:", self.tip_speed_label)

        result_group.setLayout(result_layout)
        main_layout.addWidget(result_group)

        # 创建按钮组
        button_layout = QHBoxLayout()

        # 计算按钮
        self.calculate_button = QPushButton("计算")
        self.calculate_button.clicked.connect(self.calculate_params)
        button_layout.addWidget(self.calculate_button)

        # 对话框按钮
        self.button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        self.button_box.button(QDialogButtonBox.Ok).setEnabled(False)  # 初始禁用确定按钮
        button_layout.addWidget(self.button_box)

        main_layout.addLayout(button_layout)

    def calculate_params(self):
        """
        计算连续谱参数
        """
        try:
            # 获取输入参数
            D = self.D_spin.value()
            J_P = self.J_P_spin.value()
            omega = self.omega_spin.value()
            sigma_i_input = self.sigma_i_spin.value()
            H = self.H_spin.value()
            v_op_kn = self.v_op_kn_spin.value()
            N_op_rpm = self.N_op_rpm_spin.value()

            # 计算连续谱参数
            self.calculation_result = calculate_spectrum_params(
                D, J_P, omega, sigma_i_input, H, v_op_kn, N_op_rpm
            )

            # 计算叶尖线速度
            D = self.D_spin.value()
            N_op_rpm = self.N_op_rpm_spin.value()
            U_tip = math.pi * D * (N_op_rpm / 60.0)

            # 更新结果预览
            self.cavitation_state_label.setText(self.get_cavitation_state_text(self.calculation_result['cavitation_state']))
            self.f0_label.setText(f"{self.calculation_result['f_0']:.2f} Hz")
            self.sl0_label.setText(f"{self.calculation_result['SL_0']:.2f} dB")
            self.rise_slope_label.setText(f"{self.calculation_result['q_rise_dB_oct']:.2f} dB/Oct")
            self.fall_slope_label.setText(f"{self.calculation_result['q_fall2_dB_oct']:.2f} dB/Oct")
            self.tip_speed_label.setText(f"{U_tip:.2f} m/s")

            # 启用确定按钮
            self.button_box.button(QDialogButtonBox.Ok).setEnabled(True)

            # 显示空化状态相关信息
            self.show_cavitation_info(self.calculation_result)

        except Exception as e:
            QMessageBox.warning(self, "计算错误", f"计算连续谱参数时出错: {str(e)}")

    def get_cavitation_state_text(self, state):
        """
        获取空化状态的中文文本

        Args:
            state (str): 空化状态英文文本

        Returns:
            str: 空化状态中文文本
        """
        state_map = {
            "Non-cavitating": "无空泡",
            "Developing": "空泡演变区",
            "Saturated": "空泡饱和"
        }
        return state_map.get(state, state)

    def show_cavitation_info(self, result):
        """
        显示空化状态相关信息

        Args:
            result (dict): 计算结果字典
        """
        v_op_kn = self.v_op_kn_spin.value()
        v_SI_kn = result['v_SI_kn']
        v_SY_kn = result['v_SY_kn']

        info = f"当前航速: {v_op_kn:.2f} knots\n"
        info += f"空泡初生航速: {v_SI_kn:.2f} knots\n"
        info += f"空泡饱和航速: {v_SY_kn:.2f} knots\n\n"

        if result['cavitation_state'] == "Non-cavitating":
            info += f"当前航速低于空泡初生航速，处于无空泡状态。"
        elif result['cavitation_state'] == "Developing":
            info += f"当前航速介于空泡初生航速和空泡饱和航速之间，处于空泡演变区状态。"
        else:  # Saturated
            info += f"当前航速高于空泡饱和航速，处于空泡饱和状态。"

        QMessageBox.information(self, "空化状态信息", info)

    def accept(self):
        """
        确定按钮点击事件处理
        """
        if self.calculation_result:
            # 转换为UI参数格式
            ui_params = convert_spectrum_params_to_ui_params(self.calculation_result)

            # 发送参数计算完成信号
            self.params_calculated.emit(ui_params)

            # 调用父类的accept方法
            super().accept()
        else:
            QMessageBox.warning(self, "未计算", "请先计算连续谱参数")

    def get_result(self):
        """
        获取计算结果

        Returns:
            dict: 计算结果字典，如果未计算则返回None
        """
        if self.calculation_result:
            return convert_spectrum_params_to_ui_params(self.calculation_result)
        return None
