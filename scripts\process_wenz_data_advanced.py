import csv
import json
import os
import numpy as np
from collections import defaultdict
from scipy import interpolate
import time

# 基本配置
INPUT_FILE = os.path.join('data', 'datasets.csv')
OUTPUT_FILE = os.path.join('data', 'wenz_curves.json')
DECIMAL_PLACES = 4  # 保留4位小数
NUM_INTERPOLATION_POINTS = 1000  # 插值点数

# 读取CSV文件
def read_csv_data(file_path):
    """
    从CSV文件中读取Wenz曲线数据

    Args:
        file_path: CSV文件路径

    Returns:
        包含所有曲线数据的字典，格式为 {curve_name: [(freq1, power1), (freq2, power2), ...]}
    """
    print(f"读取CSV数据: {file_path}")
    data = defaultdict(list)

    with open(file_path, 'r') as f:
        reader = csv.reader(f)
        headers = next(reader)  # 第一行是曲线名称
        _ = next(reader)  # 第二行是X和Y标签

        # 处理每一行数据
        for row in reader:
            if not any(row):  # 跳过空行
                continue

            # 每两列为一组数据，对应一条曲线的一个点
            for i in range(0, len(row), 2):
                if i+1 < len(row) and row[i] and row[i+1]:  # 确保X和Y值都存在
                    try:
                        curve_name = headers[i]
                        x = float(row[i])    # 频率
                        y = float(row[i+1])  # 功率谱级
                        data[curve_name].append((x, y))
                    except (ValueError, IndexError) as e:
                        print(f"警告: 处理数据时出错 - {e}, 行: {row}, 列: {i}")
                        pass  # 跳过无法转换为浮点数的数据

    print(f"读取了 {len(data)} 条曲线")
    return data

# 处理数据：按频率排序并去除重复点，保留指定位数小数
def process_data(data, decimal_places=DECIMAL_PLACES):
    """
    处理原始数据：按频率排序、去除重复点、保留指定位数小数

    Args:
        data: 原始数据字典
        decimal_places: 小数位数

    Returns:
        处理后的数据字典，格式为 {curve_name: {"frequencies": [...], "power_levels": [...]}}
    """
    print(f"处理数据，保留 {decimal_places} 位小数...")
    processed_data = {}

    for curve_name, points in data.items():
        # 按频率排序
        sorted_points = sorted(points, key=lambda p: p[0])

        # 去除重复的频率点（保留第一个）并保留指定位数小数
        unique_points = []
        seen_freqs = set()

        for freq, power in sorted_points:
            # 使用近似值比较，避免浮点精度问题
            freq_rounded = round(freq, decimal_places)
            if freq_rounded not in seen_freqs:
                # 保留指定位数小数
                unique_points.append((round(freq, decimal_places), round(power, decimal_places)))
                seen_freqs.add(freq_rounded)

        # 将点列表转换为字典格式
        processed_data[curve_name] = {
            "frequencies": [p[0] for p in unique_points],
            "power_levels": [p[1] for p in unique_points]
        }

    return processed_data

# 识别曲线中的转折点
def identify_turning_points(freqs, powers, threshold=0.5):
    """
    识别曲线中的转折点

    Args:
        freqs: 频率数组
        powers: 功率谱级数组
        threshold: 斜率变化阈值，越大识别的转折点越少

    Returns:
        转折点的索引数组
    """
    if len(freqs) < 3:
        return []

    # 计算相邻点之间的斜率
    log_freqs = np.log10(freqs)  # 在对数频率上计算斜率
    slopes = np.diff(powers) / np.diff(log_freqs)

    # 计算斜率的变化率
    slope_changes = np.abs(np.diff(slopes))

    # 计算斜率变化的标准差，用于自适应阈值
    std_slope_change = np.std(slope_changes)
    adaptive_threshold = threshold * std_slope_change

    # 找出斜率变化超过阈值的点
    turning_indices = np.where(slope_changes > adaptive_threshold)[0] + 1

    # 确保起点和终点也被包含
    if 0 not in turning_indices:
        turning_indices = np.append([0], turning_indices)
    if len(freqs) - 1 not in turning_indices:
        turning_indices = np.append(turning_indices, [len(freqs) - 1])

    # 按索引排序
    turning_indices = np.sort(turning_indices)

    return turning_indices

# 分段插值函数
def piecewise_interpolation(freqs, powers, turning_indices=None, num_points=NUM_INTERPOLATION_POINTS, decimal_places=DECIMAL_PLACES):
    """
    分段插值，在转折点处分段

    Args:
        freqs: 频率数组
        powers: 功率谱级数组
        turning_indices: 转折点索引，如果为None则自动识别
        num_points: 插值后的总点数
        decimal_places: 小数位数

    Returns:
        插值后的频率数组和功率谱级数组
    """
    if len(freqs) < 2:
        return np.array(freqs), np.array(powers)

    # 如果没有提供转折点，则自动识别
    if turning_indices is None or len(turning_indices) == 0:
        turning_indices = identify_turning_points(freqs, powers)

    # 确保起点和终点被包含
    if 0 not in turning_indices:
        turning_indices = np.append([0], turning_indices)
    if len(freqs) - 1 not in turning_indices:
        turning_indices = np.append(turning_indices, [len(freqs) - 1])

    # 按索引排序
    turning_indices = np.sort(turning_indices)

    # 创建输出数组
    log_freqs = np.log10(freqs)
    result_log_freqs = []
    result_powers = []

    # 对每段分别进行插值
    for i in range(len(turning_indices) - 1):
        start_idx = turning_indices[i]
        end_idx = turning_indices[i + 1]

        # 确保至少包含两个点
        if end_idx <= start_idx:
            continue

        # 当前段的数据
        segment_log_freqs = log_freqs[start_idx:end_idx + 1]  # 包含结束点
        segment_powers = powers[start_idx:end_idx + 1]

        # 为当前段生成插值点数，根据段长度按比例分配
        segment_length = end_idx - start_idx
        segment_points = max(2, int(num_points * segment_length / len(freqs)))

        # 生成新的对数频率点
        new_log_freqs = np.linspace(segment_log_freqs[0], segment_log_freqs[-1], segment_points)

        # 根据段的长度选择插值方法
        if len(segment_log_freqs) > 3:
            # 使用三次样条插值
            try:
                interp_func = interpolate.interp1d(
                    segment_log_freqs, segment_powers,
                    kind='cubic', bounds_error=False,
                    fill_value="extrapolate"
                )
                new_powers = interp_func(new_log_freqs)
            except Exception as e:
                print(f"警告: 三次样条插值失败，使用线性插值 - {e}")
                # 如果三次样条插值失败，回退到线性插值
                interp_func = interpolate.interp1d(
                    segment_log_freqs, segment_powers,
                    kind='linear', bounds_error=False,
                    fill_value="extrapolate"
                )
                new_powers = interp_func(new_log_freqs)
        else:
            # 点数不足时使用线性插值
            interp_func = interpolate.interp1d(
                segment_log_freqs, segment_powers,
                kind='linear', bounds_error=False,
                fill_value="extrapolate"
            )
            new_powers = interp_func(new_log_freqs)

        # 添加到结果中
        result_log_freqs.extend(new_log_freqs)
        result_powers.extend(new_powers)

    # 转换回原始频率并保留指定位数小数
    result_freqs = 10 ** np.array(result_log_freqs)
    result_freqs = np.round(result_freqs, decimal_places)
    result_powers = np.round(result_powers, decimal_places)

    # 去除可能的重复点
    unique_indices = []
    seen_freqs = set()
    for i, freq in enumerate(result_freqs):
        if freq not in seen_freqs:
            unique_indices.append(i)
            seen_freqs.add(freq)

    result_freqs = result_freqs[unique_indices]
    result_powers = result_powers[unique_indices]

    return result_freqs, result_powers

# 处理带状区域的边界曲线
def process_band_areas(processed_data):
    """
    处理带状区域的边界曲线，确保上下边界曲线在频率范围上的一致性

    Args:
        processed_data: 处理后的数据字典

    Returns:
        处理后的数据字典
    """
    print("处理带状区域的边界曲线...")

    # 定义带状区域
    band_areas = [
        {
            'name': 'low-frequency-very-shallow-wind',
            'top': 'low-frequency-very-shallow-wind-top',
            'bottom': 'low-frequency-very-shallow-wind-bottom'
        },
        {
            'name': 'usual-traffic-noise-shallow',
            'top': 'usual-traffic-noise-shallow-top',
            'bottom': 'usual-traffic-noise-shallow-bottom'
        },
        {
            'name': 'usual-traffic-noise-deep',
            'top': 'usual-traffic-noise-deep-top',
            'bottom': 'usual-traffic-noise-deep-bottom'
        }
    ]

    # 处理每个带状区域
    for band in band_areas:
        top_name = band['top']
        bottom_name = band['bottom']

        # 确保两条曲线都存在
        if top_name not in processed_data or bottom_name not in processed_data:
            print(f"警告: 带状区域 {band['name']} 的边界曲线不完整，跳过处理")
            continue

        top_data = processed_data[top_name]
        bottom_data = processed_data[bottom_name]

        # 获取上下边界曲线的频率范围
        top_freqs = np.array(top_data['frequencies'])
        bottom_freqs = np.array(bottom_data['frequencies'])

        # 找出需要添加的频率点
        top_min_freq = min(top_freqs)
        top_max_freq = max(top_freqs)
        bottom_min_freq = min(bottom_freqs)
        bottom_max_freq = max(bottom_freqs)

        # 确保下边界曲线包含上边界曲线的频率范围
        if bottom_min_freq > top_min_freq:
            print(f"为 {bottom_name} 添加低频点 {top_min_freq}")
            # 在下边界曲线中添加上边界曲线的最小频率点
            # 使用线性插值估计功率值
            bottom_powers = np.array(bottom_data['power_levels'])

            # 确保数组长度一致
            if len(bottom_freqs) == len(bottom_powers):
                power_at_top_min = np.interp(
                    np.log10(top_min_freq),
                    np.log10(bottom_freqs),
                    bottom_powers,
                    left=bottom_powers[0],
                    right=bottom_powers[-1]
                )

                # 添加新点
                processed_data[bottom_name]['frequencies'].append(top_min_freq)
                processed_data[bottom_name]['power_levels'].append(round(power_at_top_min, DECIMAL_PLACES))

                # 重新排序
                indices = np.argsort(processed_data[bottom_name]['frequencies'])
                processed_data[bottom_name]['frequencies'] = [processed_data[bottom_name]['frequencies'][i] for i in indices]
                processed_data[bottom_name]['power_levels'] = [processed_data[bottom_name]['power_levels'][i] for i in indices]
            else:
                print(f"警告: {bottom_name} 的频率和功率数组长度不一致，跳过添加低频点")

        # 重新获取频率数组（可能已经更新）
        bottom_freqs = np.array(processed_data[bottom_name]['frequencies'])
        bottom_powers = np.array(processed_data[bottom_name]['power_levels'])

        if bottom_max_freq < top_max_freq:
            print(f"为 {bottom_name} 添加高频点 {top_max_freq}")
            # 在下边界曲线中添加上边界曲线的最大频率点

            # 确保数组长度一致
            if len(bottom_freqs) == len(bottom_powers):
                power_at_top_max = np.interp(
                    np.log10(top_max_freq),
                    np.log10(bottom_freqs),
                    bottom_powers,
                    left=bottom_powers[0],
                    right=bottom_powers[-1]
                )

                # 添加新点
                processed_data[bottom_name]['frequencies'].append(top_max_freq)
                processed_data[bottom_name]['power_levels'].append(round(power_at_top_max, DECIMAL_PLACES))

                # 重新排序
                indices = np.argsort(processed_data[bottom_name]['frequencies'])
                processed_data[bottom_name]['frequencies'] = [processed_data[bottom_name]['frequencies'][i] for i in indices]
                processed_data[bottom_name]['power_levels'] = [processed_data[bottom_name]['power_levels'][i] for i in indices]
            else:
                print(f"警告: {bottom_name} 的频率和功率数组长度不一致，跳过添加高频点")

    return processed_data

# 对所有曲线进行插值处理
def interpolate_all_curves(processed_data, num_points=NUM_INTERPOLATION_POINTS):
    """
    对所有曲线进行插值处理

    Args:
        processed_data: 处理后的数据字典
        num_points: 插值点数

    Returns:
        插值后的数据字典
    """
    print(f"对所有曲线进行插值处理，每条曲线生成约 {num_points} 个点...")
    interpolated_data = {}

    for curve_name, curve_data in processed_data.items():
        print(f"处理曲线: {curve_name}")
        freqs = np.array(curve_data['frequencies'])
        powers = np.array(curve_data['power_levels'])

        # 识别转折点
        turning_indices = identify_turning_points(freqs, powers)

        # 使用分段插值
        interp_freqs, interp_powers = piecewise_interpolation(
            freqs, powers, turning_indices, num_points
        )

        # 保存插值结果
        interpolated_data[curve_name] = {
            'frequencies': interp_freqs.tolist(),
            'power_levels': interp_powers.tolist()
        }

    return interpolated_data

# 预处理带状区域数据，为可视化做准备
def preprocess_band_areas_for_visualization(interpolated_data, num_points=900):
    """
    预处理带状区域数据，为可视化做准备
    为每个带状区域创建共同的频率点和对应的功率谱级值，避免在可视化时再进行插值

    Args:
        interpolated_data: 插值后的数据字典
        num_points: 共同频率点的数量

    Returns:
        包含带状区域预处理数据的字典
    """
    print("预处理带状区域数据，为可视化做准备...")
    band_areas_data = {}

    # 定义带状区域
    band_areas = [
        {
            'name': 'low-frequency-very-shallow-wind',
            'top': 'low-frequency-very-shallow-wind-top',
            'bottom': 'low-frequency-very-shallow-wind-bottom'
        },
        {
            'name': 'usual-traffic-noise-shallow',
            'top': 'usual-traffic-noise-shallow-top',
            'bottom': 'usual-traffic-noise-shallow-bottom'
        },
        {
            'name': 'usual-traffic-noise-deep',
            'top': 'usual-traffic-noise-deep-top',
            'bottom': 'usual-traffic-noise-deep-bottom'
        }
    ]

    # 处理每个带状区域
    for band in band_areas:
        band_name = band['name']
        top_name = band['top']
        bottom_name = band['bottom']

        # 确保两条曲线都存在
        if top_name not in interpolated_data or bottom_name not in interpolated_data:
            print(f"警告: 带状区域 {band_name} 的边界曲线不完整，跳过处理")
            continue

        top_data = interpolated_data[top_name]
        bottom_data = interpolated_data[bottom_name]

        # 获取上下边界曲线的频率范围
        top_freqs = np.array(top_data['frequencies'])
        top_powers = np.array(top_data['power_levels'])
        bottom_freqs = np.array(bottom_data['frequencies'])
        bottom_powers = np.array(bottom_data['power_levels'])

        # 确定共同的频率范围
        min_freq = max(min(top_freqs), min(bottom_freqs))
        max_freq = min(max(top_freqs), max(bottom_freqs))

        # 创建共同的频率点（对数均匀分布）
        common_freqs = np.logspace(np.log10(min_freq), np.log10(max_freq), num_points)

        # 在共同频率点上插值上下边界曲线
        top_interp = np.interp(np.log10(common_freqs), np.log10(top_freqs), top_powers)
        bottom_interp = np.interp(np.log10(common_freqs), np.log10(bottom_freqs), bottom_powers)

        # 保存预处理结果
        band_areas_data[band_name] = {
            'common_frequencies': common_freqs.tolist(),
            'top_powers': top_interp.tolist(),
            'bottom_powers': bottom_interp.tolist()
        }

        print(f"带状区域 {band_name} 预处理完成，共 {num_points} 个点")

    return band_areas_data

# 可视化相关函数已移除，专注于数据处理

# 分析数据并生成统计信息
def analyze_data(data):
    """
    分析数据并生成统计信息

    Args:
        data: 处理后的数据字典

    Returns:
        包含统计信息的字典
    """
    print("生成数据统计信息...")
    stats = {}

    for curve_name, curve_data in data.items():
        freqs = np.array(curve_data["frequencies"])
        powers = np.array(curve_data["power_levels"])

        stats[curve_name] = {
            "point_count": len(freqs),
            "freq_min": float(np.min(freqs)),
            "freq_max": float(np.max(freqs)),
            "power_min": float(np.min(powers)),
            "power_max": float(np.max(powers)),
            "freq_range": float(np.max(freqs) - np.min(freqs)),
            "power_range": float(np.max(powers) - np.min(powers))
        }

    return stats

# 主函数
def main():
    """
    主函数
    """
    start_time = time.time()
    print(f"开始处理Wenz曲线数据，时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

    # 确保输出目录存在
    os.makedirs(os.path.dirname(OUTPUT_FILE), exist_ok=True)

    # 读取CSV数据
    raw_data = read_csv_data(INPUT_FILE)

    # 处理数据
    processed_data = process_data(raw_data)

    # 处理带状区域的边界曲线
    processed_data = process_band_areas(processed_data)

    # 对所有曲线进行插值处理
    interpolated_data = interpolate_all_curves(processed_data)

    # 生成统计信息
    original_stats = analyze_data(processed_data)
    interpolated_stats = analyze_data(interpolated_data)

    # 预处理带状区域数据，为可视化做准备
    band_areas_data = preprocess_band_areas_for_visualization(interpolated_data)

    # 创建结果数据结构
    result = {
        "metadata": {
            "description": "Wenz curves data with interpolation",
            "units": {
                "frequency": "Hz",
                "power_level": "dB re 1 μPa^2/Hz"
            },
            "processing": {
                "decimal_places": DECIMAL_PLACES,
                "interpolation_points": NUM_INTERPOLATION_POINTS,
                "processing_time": time.time() - start_time
            },
            "stats": {
                "original": original_stats,
                "interpolated": interpolated_stats
            }
        },
        "curves": {},
        "band_areas": band_areas_data  # 添加带状区域预处理数据
    }

    # 合并原始数据和插值数据
    for curve_name in processed_data.keys():
        result["curves"][curve_name] = {
            "original": processed_data[curve_name],
            "interpolated": interpolated_data[curve_name]
        }

    # 保存为JSON文件
    print(f"保存数据到 {OUTPUT_FILE}...")
    with open(OUTPUT_FILE, 'w') as f:
        json.dump(result, f, indent=2)

    print(f"数据处理完成，用时 {time.time() - start_time:.2f} 秒")

    # 打印一些统计信息
    print("\n曲线统计信息:")
    for curve_name, stats in original_stats.items():
        print(f"{curve_name}: 原始 {stats['point_count']} 点, 插值后 {interpolated_stats[curve_name]['point_count']} 点")

    print("处理完成！")

if __name__ == "__main__":
    main()
