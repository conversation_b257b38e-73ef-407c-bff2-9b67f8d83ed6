# -*- coding: utf-8 -*-
"""
测试调制谱模拟功能

测试船舶辐射噪声模型中的调制谱模拟功能
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import numpy as np
import matplotlib.pyplot as plt
import matplotlib as mpl
from src.models.noise_sources.ship_radiated import ShipRadiatedNoise

# 设置中文字体支持
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号
except:
    print("警告: 无法设置中文字体，图形中的中文可能无法正确显示")


def test_modulation_spectrum():
    """测试调制谱模型"""
    print("=" * 50)
    print("测试船舶辐射噪声调制谱模型")
    print("=" * 50)

    # 创建船舶辐射噪声模型实例
    ship_model = ShipRadiatedNoise(name="测试船舶辐射噪声")

    # 设置调制谱参数
    ship_model.set_modulation_params(
        f1=8.0,      # 轴频 (Hz)
        f2=32.0,      # 叶频 (Hz)
        A1=0.05,      # 轴频分量振幅
        A2=0.3,       # 叶频分量振幅
        p=0.1,        # 轴频衰减系数
        q=0.1,        # 叶频衰减系数
        N=10,         # 轴频谐波数量
        M=5           # 叶频谐波数量
    )

    # 设置仿真参数
    ship_model.set_simulation_params(
        fs=10000,
        duration=5.0,
        filter_order=257
    )

    # 生成调制谱信号
    modulation_signal = ship_model.simulate_modulation_spectrum()

    # 计算功率谱密度
    freqs, psd, psd_db = ship_model.calculate_psd(modulation_signal)

    # 打印信息
    print(f"生成的信号长度: {len(modulation_signal)}")
    print(f"采样频率: {ship_model.fs} Hz")
    print(f"信号持续时间: {ship_model.duration} 秒")
    print(f"轴频: {ship_model.mod_f1} Hz")
    print(f"叶频: {ship_model.mod_f2} Hz")
    print(f"叶片数: {round(ship_model.mod_f2 / ship_model.mod_f1)}")
    print(f"轴频分量振幅: {ship_model.mod_A1}")
    print(f"叶频分量振幅: {ship_model.mod_A2}")
    print(f"轴频衰减系数: {ship_model.mod_p}")
    print(f"叶频衰减系数: {ship_model.mod_q}")
    print(f"轴频谐波数量: {ship_model.mod_N}")
    print(f"叶频谐波数量: {ship_model.mod_M}")

    # 绘制结果
    plt.figure(figsize=(12, 12))

    # 绘制时域信号
    plt.subplot(2, 1, 1)
    t = np.linspace(0, ship_model.duration, len(modulation_signal), endpoint=False)
    # 计算显示1秒所需的点数
    points_per_second = int(ship_model.fs)
    plt.plot(t[:points_per_second], modulation_signal[:points_per_second], 'r-', label='调制谱信号')
    plt.title('船舶辐射噪声调制谱 - 时域信号')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.xlim(0, 1.0)  # 显示前1秒
    plt.grid(True)
    plt.legend()

    # 绘制频域信号
    plt.subplot(2, 1, 2)
    # 只显示0-500Hz的频谱
    mask = freqs <= 500
    plt.plot(freqs[mask], psd_db[mask], 'b-', label='调制谱PSD')
    plt.title('船舶辐射噪声调制谱 - 频域信号')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('功率谱密度 (dB/Hz)')
    plt.grid(True)
    plt.legend()

    # 标记轴频谐波
    for k in range(1, ship_model.mod_N + 1):
        f = k * ship_model.mod_f1
        if f <= 500:  # 只标记500Hz以内的谐波
            # 检查是否与叶频谐波重叠
            blade_count = round(ship_model.mod_f2 / ship_model.mod_f1)
            is_overlap = (k % blade_count == 0)
            
            if not is_overlap:
                plt.axvline(x=f, color='r', linestyle='--', alpha=0.3)
                plt.text(f, np.max(psd_db[mask]) - 10, f"{k}f1", 
                         horizontalalignment='center', color='r')

    # 标记叶频谐波
    for i in range(1, ship_model.mod_M + 1):
        f = i * ship_model.mod_f2
        if f <= 500:  # 只标记500Hz以内的谐波
            plt.axvline(x=f, color='g', linestyle='--', alpha=0.3)
            plt.text(f, np.max(psd_db[mask]) - 5, f"{i}f2", 
                     horizontalalignment='center', color='g')

    plt.tight_layout()
    plt.savefig('modulation_spectrum_test.png')
    plt.close()

    print("调制谱测试完成，结果已保存为 modulation_spectrum_test.png")
    
    # 测试完整的船舶辐射噪声（包含调制谱）
    test_full_radiated_noise_with_modulation(ship_model)
    
    return ship_model


def test_full_radiated_noise_with_modulation(ship_model):
    """测试包含调制谱的完整船舶辐射噪声"""
    print("\n" + "=" * 50)
    print("测试包含调制谱的完整船舶辐射噪声")
    print("=" * 50)
    
    # 设置连续谱参数
    ship_model.set_continuous_params(
        f0=250.0,
        sl0=130.0,
        a1_oct=3.0,
        a2_oct=-4.2
    )
    
    # 设置线谱参数
    line_frequencies = np.array([16.0, 32.0, 48.0, 65.0, 350.0, 800.0])
    line_levels_diff = np.array([25.0, 23.0, 21.0, 19.0, 18.0, 18.0])
    ship_model.set_line_params(line_frequencies, line_levels_diff)
    
    # 生成完整的船舶辐射噪声（包含调制谱）
    total_signal = ship_model.simulate_radiated_noise(include_line=True, include_modulation=True)
    
    # 计算功率谱密度
    freqs, psd, psd_db = ship_model.calculate_psd(total_signal)
    
    # 绘制结果
    plt.figure(figsize=(12, 12))
    
    # 绘制时域信号
    plt.subplot(2, 1, 1)
    t = np.linspace(0, ship_model.duration, len(total_signal), endpoint=False)
    # 计算显示1秒所需的点数
    points_per_second = int(ship_model.fs)
    plt.plot(t[:points_per_second], total_signal[:points_per_second], 'r-', label='完整船舶辐射噪声')
    plt.title('包含调制谱的船舶辐射噪声 - 时域信号')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.xlim(0, 1.0)  # 显示前1秒
    plt.grid(True)
    plt.legend()
    
    # 绘制频域信号
    plt.subplot(2, 1, 2)
    # 只显示0-1000Hz的频谱
    mask = freqs <= 1000
    plt.plot(freqs[mask], psd_db[mask], 'b-', label='船舶辐射噪声PSD')
    plt.title('包含调制谱的船舶辐射噪声 - 频域信号')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('功率谱密度 (dB/Hz)')
    plt.grid(True)
    plt.legend()
    
    # 标记线谱频率
    for i, f in enumerate(line_frequencies):
        if f <= 1000:  # 只标记1000Hz以内的线谱
            plt.axvline(x=f, color='m', linestyle='--', alpha=0.3)
            plt.text(f, np.max(psd_db[mask]) - 15, f"{f}Hz", 
                     horizontalalignment='center', color='m')
    
    plt.tight_layout()
    plt.savefig('full_radiated_noise_with_modulation_test.png')
    plt.close()
    
    print("包含调制谱的完整船舶辐射噪声测试完成，结果已保存为 full_radiated_noise_with_modulation_test.png")


if __name__ == "__main__":
    test_modulation_spectrum()
