3   面向复杂水声环境的噪声仿真系统的分析
面向复杂水声环境的噪声仿真系统要求
3.1 面向复杂水声环境的噪声仿真系统的需求描述
面向复杂水声环境的噪声仿真系统的主要功能应该包括：配置船舶辐射噪声的相关参数，包括连续谱参数、线谱参数与调制谱参数；配置海洋环境噪声参数；配置海洋传播声场的相关参数，包括海底地形、海水声速剖面等多样的海洋环境参数；基于相关物理模型生成船舶辐射噪声和海洋环境噪声并可视化；基于Bellhop模型对海洋信道进行建模，模拟声波在海洋信道中的传播，生成噪声场的若干典型特性，包括谱级、指向性与互功率谱，并对结果进行可视化。
3.1.1 功能需求分析
面向复杂水声环境的噪声仿真系统旨在为水声领域的研究人员提供一个全面、灵活且用户友好的仿真平台。系统致力于模拟复杂海洋声学环境下的主要噪声的特性，并对声波在信道中的传播过程进行建模，最终能够综合评估特定水声环境下的声场分布及信号特性，具体的功能需求分析如下所述：
1）船舶辐射噪声仿真
船舶辐射噪声仿真功能允许用户通过设置连续谱噪声参数、线谱噪声参数以及调制谱噪声参数对这三种噪声进行分别模拟，进而综合生成船舶的辐射噪声。此外，用户能够通过目标船舶工况参数来对连续谱参数进行估计计算。在仿真完成后，模拟出的噪声信号的时域波形以及频谱将展示在界面的视图区域。
2）海洋环境噪声仿真
海洋环境噪声仿真支持基于经典的Wenz曲线对海洋环境噪声进行模拟，用户应能够在可交互的图形界面上直观定义目标噪声的功率谱密度。在仿真完成后，模拟的海洋环境噪声信号的时域波形和频谱会被展示于界面左侧的视图区域。
3）声传播环境仿真
用户能够定义包括海底地形、海水声速剖面等多样的海洋环境参数，系统利用成熟的海洋声传播模拟模型Bellhop计算声波在特定环境中的传播路径，生成传播损失以及到达时间-幅度序列等结果，并可视化展示在系统界面的视图区域。用户还可以定义阵列和频率列表，制备海洋信道建模所需的数据。
4）综合声场仿真
用户在定义了噪声源和传播环境后，系统能够模拟声信号通过复杂的海洋信道传播至接收阵列的过程，并可叠加海洋环境噪声作为背景噪声，生成各阵元接收到的混合信号。基于这些信号，系统还提供如波束形成、空间相关性等信号处理与分析功能，以揭示声场特性，同时这些结果也将以可视化的方式展示给用户。
5）项目文件管理与信号数据导出
该功能使得用户可以新建、保存和加载仿真项目，方便工作的延续。同时，系统支持将仿真生成的时域信号导出为标准格式文件，便于用户进行二次开发或与其他工具链集成。
3.1.2 仿真系统活动图分析
活动图是一种用于呈现系统或软件中业务流程与操作步骤的图形化工具。它通过直观、可视的方式展现各活动之间的执行顺序、并行处理以及条件判断等逻辑关系。在本系统中，活动图能够反映出不同业务之间的关联。
图3-1展示了系统的典型业务流程，用户可以加载已有的项目文件来继续完成之前的工作，选择和查看噪声信号与信道数据，如若数据完备则可以进一步设置综合声场仿真的参数以进行仿真，如果存在缺失或不满意则可以进行对应的噪声仿真或声传播环境仿真来生成噪声信号和信道数据。所有的仿真结果都将以可视化的方式展现给用户。在用户完成仿真后，可以选择保存项目文件或者导出信号数据。

图 3 1  面向复杂水声环境的噪声仿真系统活动图
3.2 面向复杂水声环境的噪声仿真系统的分析模型
接下来，本文将从三个维度分别入手建立相应的模型对面向复杂水声环境的噪声仿真系统进行分析。综合面向对象的相关理论和技术，三个维度的分析模型分别为功能模型、静态模型、动态模型。
3.2.1 面向复杂水声环境的噪声仿真系统的功能模型
系统的功能模型主要通过用例图来描述，用例图能够清晰地展现系统的功能边界、主要参与者以及系统提供的核心功能。整体用例图如图3-2所示，系统边界内包含了四个主要用例：噪声信号仿真、声传播环境仿真、综合声场仿真、项目文件管理与信号数据导出。这些用例涵盖了系统的核心功能。

图 3 2  面向复杂水声环境噪声仿真系统整体用例图
1）噪声信号仿真
在本系统中，用户可以对船舶辐射噪声和海洋环境噪声进行单独的信号仿真，因其功能相似，故在本文中将其合并为噪声信号仿真一个单独的用例图，其用例图如图3-3所示，包含三个核心用例。

图 3 3  噪声信号仿真用例图
（1）配置噪声参数
用户可以设置船舶辐射噪声或海洋环境噪声的各项参数，详细的用例描述如表3-1所示。
表 3-1  配置噪声参数用例表
用例	说明
前置条件	系统已启动
主要事件流	a.用户进入船舶辐射噪声或海洋环境噪声的仿真界面。
b.用户配置相应的参数（A1）。
c.系统验证参数有效性（E1）。
d.系统保存参数配置。
异常事件流	E1：参数验证失败。
其它事件流	a．A1：用户配置相应的参数：
若为船舶辐射噪声，则设置连续谱、线谱和调制谱参数，可选择通过工况参数估计连续谱参数。
若为海洋环境噪声，则在Wenz曲线图上定义频率-谱级点，调整点的频率和谱级参数。

（2）执行仿真计算
在完成噪声参数的配置后，用户可以执行仿真计算，该用例的详细描述如表3-2所示。
表 3-2  执行仿真计算用例表
用例	说明
前置条件	已完成噪声参数配置
主要事件流	a.用户点击开始仿真按钮。
b.系统读取已配置的参数。
c.系统基于相应算法生成噪声信号（E1）。
d.系统计算信号的功率谱密度。
e.系统保存仿真结果数据。
异常事件流	E1：仿真计算失败。

（3）查看仿真结果
系统正确完成噪声信号仿真后，其结果将会更新到图形界面以供用户查看，用例的描述如表3-3所示。
表 3-3  查看仿真结果用例表
用例	说明
前置条件	已完成仿真计算，系统中存在仿真结果数据
主要事件流	a.系统在视图区域显示时域波形图和频谱图。
b.用户可以缩放、平移图表进行详细查看。

2）声传播环境仿真
用户除了可以对船舶辐射噪声和海洋环境噪声两种噪声信号进行独立仿真，还可以通过设置声传播环境的多样参数来对声传播环境进行仿真。在完成对声传播环境的探索后，用户可以基于所设置的声传播环境制备信道建模所需的数据，如图3-4所示。

图 3 4  声传播环境仿真用例图
（1）配置声传播环境参数
用户可以配置声传播环境参数用于定义海洋环境条件，例如声速剖面等，其具体用例描述见表3-4。
表 3-4  配置声传播环境参数用例表
用例	说明
前置条件	系统已启动，用户已进入声传播环境仿真界面
主要事件流	a.用户设置海洋环境参数（水深、声速剖面、海底参数等）。
b.用户设置声源深度和接收器位置参数。
c.系统验证参数的合理性（E1）。
d.系统保存环境参数配置。
异常事件流	E1：参数验证失败。

（2）执行声传播计算
在用户完成声传播环境的参数配置并无误后，可以执行生传播计算，系统将调用Bellhop模型返回一系列的声传播计算结果，具体描述如表3-5所示。
表 3-5  执行声传播计算用例表
用例	说明
前置条件	用户已完成声传播环境参数的配置
主要事件流	a.用户启动声传播计算。
b.系统调用Bellhop模型进行计算（E1）。
c.系统生成声线轨迹、声线到达时间-幅度序列和传播损失数据。
d.系统保存计算结果。
异常事件流	E1：调用Bellhop计算过程出现错误。

（3）查看声传播计算结果
当系统完成了声传播计算并保存了计算结果后，系统将以可视化的方式展示声线图、声线到达结构图表和传播损失图，用户可以缩放、平移图表以及细致浏览表格数据。
（4）配置阵列与频率参数
当用户完成了声传播环境参数配置和相关调试后，用户可以设置接收阵列的几何配置以及将要执行计算的频率列表，这些参数是为制备信道数据准备的。
（5）制备信道数据
在用户完成了阵列和频率列表的设置后，系统将其与声传播环境参数结合使用，并行计算各个阵元位置在不同频率下的声线到达时间-幅度序列，并保存到指定目录供后续综合仿真使用。
3）综合声场仿真
在用户完成至少船舶辐射噪声仿真一种噪声信号仿真以及完成了信道数据的制备后，用户可以依托于这些已有的数据进行综合声场的仿真。综合声场仿真能利用用户准备好的信道数据对海洋信道进行建模，以支持更精确的宽带信号的波形预测，生成各阵元的接受信号，并进一步对信号进行处理以得到噪声场的特性，如图3-5所示。

图 3 5  综合声场仿真用例图
（1）选择信道数据
用户选择声传播计算生成的信道数据目录，系统验证数据有效性并加载信道数据，显示基本信息供用户确认。
（2）生成阵元接收信号
在用户选择了信道数据后，系统就可以加载并利用这些数据对水声信道进行建模，进而可以模拟源信号在建模的水声信道中的传播，从而生成阵元接收信号，该用例的具体描述如表3-6所示。
表 3-6  生成阵元接收信号用例表
用例	说明
前置条件	用户已选择有效的信道数据，已完成噪声仿真
主要事件流	a.系统读取源信号数据。
b.系统利用信道数据对水声信道进行建模并模拟源信号的传播。
c.系统为每个阵元生成接收信号（A1）。
d.系统保存生成的阵元接收信号。
其它事件流	a．A1：用户选择是否叠加海洋环境噪声：
若选择叠加，则系统为每个阵元独立生成环境噪声并叠加到接收信号中。
若不选择叠加，则系统不进行环境噪声的生成和叠加。

（3）进行阵列信号处理
在完成了阵元接收信号的模拟后，用户可以进行信号处理与分析，以进一步探索声场特性，该用例的具体描述如表3-7所示。
表 3-7  进行阵列信号处理用例表
用例	说明
前置条件	已生成阵元接收信号
主要事件流	a.用户选择分析类型（频谱分析、指向性分析、互功率谱分析等）（A1）。
b.用户设置分析参数。
c.系统执行相应的信号处理算法。
d.系统保存结果。
其它事件流	a．A1：用户选择分析类型：
若选择频谱分析，则系统计算各阵元信号的功率谱密度。
若选择指向性分析，则系统使用延迟求和波束形成计算指向性。
若选择互功率谱分析，则系统计算阵元间的互功率谱和相干函数。

（4）查看声场特性
系统以多种可视化方式展示声场分析结果，包括时域波形、频谱、指向性图、互功率谱和相干函数图，用户可以选择不同阵元和频率进行对比分析。
4）项目文件管理与信号数据导出
用户在完成仿真后，有保存项目文件以便于下次继续在此基础上工作的需求，或有需要将仿真得到的噪声的时域信号导出，项目文件管理与信号数据导出功能可以满足用户的这些需求，如图3-6所示。

图 3 6  项目文件管理与信号数据导出用例图
（1）管理项目文件
管理项目文件是用户管理工作进度的重要功能，其用例描述如表3-8所示。
表 3-8  管理项目文件用例表
用例	说明
前置条件	系统已启动
主要事件流	a.用户选择项目操作类型（A1）。
b.系统执行相应的项目操作（E1）。
c.系统更新界面状态以反映项目变化。
异常事件流	E1：文件操作失败。
其它事件流	a．	A1：若前项目有未保存修改，则系统询问用户是否保存当前修改：

（2）打开项目文件
用户选择已有的项目文件，系统加载并恢复所有参数和结果数据，更新界面显示到之前的工作状态。
（3）保存项目文件
系统收集当前所有参数和结果数据，将其序列化保存到用户指定的文件路径，便于后续工作的延续。
（4）导出信号时域数据
用户可以将仿真得到的时域信号数据以标准格式导出，方便用户以满足其需求的方式进行处理或与其他工具链集成，该用例的描述如表3-9所示。
表 3-9  导出信号时域数据用例表
用例	说明
前置条件	系统中存在可导出的信号数据
主要事件流	a.用户选择要导出的具体信号。
b.用户选择导出格式以及导出路径。
c.系统执行信号导出操作。

3.2.2 面向复杂水声环境的噪声仿真系统的静态模型
静态模型通过类图来描述系统中的核心概念及其相互关系，反映问题空间中的主要实体和它们之间的结构关系。在面向复杂水声环境的噪声仿真系统中，经过对系统功能需求的分析，对每个类的描述如下：
1）仿真项目类
仿真项目作为整个仿真工作的容器和管理单元，包含项目名称等基本标识信息。该类统一管理用户使用仿真系统过程中涉及的所有参数和结果。
2）参数类型
（1）噪声参数类
作为抽象基类，定义了所有噪声源的共同参数特征，包括采样率和信号持续时间等基础参数。
（2）船舶噪声参数类
继承自噪声参数类，专门描述船舶辐射噪声的特有参数，包括连续谱参数、线谱参数和调制谱参数，分别对应船舶噪声的不同物理成分。
（3）环境噪声参数类
同样继承自噪声参数类，用于描述海洋环境噪声的参数，主要包含频谱特性等用于描述环境噪声的关键属性。
（4）声传播参类
描述声波传播计算所需的配置信息，包含环境参数和阵列配置，为声传播建模提供必要的输入条件。
3）数据结果类型
（1）噪声信号类
表示噪声仿真的输出结果，包含时域波形和频谱数据，是噪声参数经过仿真计算后产生的具体信号数据。
（2）信道数据类
代表声传播计算的结果，虽然未在类图中绘制出具体属性，但概念上包含了声波在特定环境中的传播特性信息，主要用于综合仿真的信道建模。
（3）声场结果类
表示综合声场分析的最终输出，包含阵列信号、指向性和互功率谱等声场特征参数，是系统最终向用户提供的分析结果。
用于描述系统的静态类型的分析类图如图3-7所示。

图 3 7  系统分析类图
3.2.3 面向复杂水声环境的噪声仿真系统的动态模型
动态模型通过活动图来描述系统中各个业务流程的执行逻辑，反映用户与系统交互过程中的操作步骤、条件判断和并发处理等动态行为。本节将分别绘制对应的活动图来展现各个业务流程的具体执行过程。
1）船舶辐射噪声仿真
船舶辐射噪声仿真是系统的核心功能之一，用户通过配置连续谱、线谱和调制谱参数来对船舶辐射噪声信号进行仿真，其业务流程如图3-8所示。
用户进行船舶辐射噪声仿真时，首先需要确定连续谱参数的设置方式：直接输入连续谱参数或根据工况估算连续谱参数。若选择直接输入，用户需要输入连续谱参数；若选择工况估算，用户需要提供船舶工况信息，仿真系统根据物理模型自动计算连续谱参数。在连续谱配置完成后，用户可以选择是否包含线谱和调制谱。若包含线谱，需要设置线谱参数；若包含调制谱，需要设置调制谱参数。用户完成所有参数配置后启动仿真计算，仿真系统生成船舶辐射噪声信号，计算信号特性，并展示仿真结果供用户查看和分析。

图 3 8  船舶辐射噪声仿真活动图
2）海洋环境噪声仿真
海洋环境噪声仿真基于经典的Wenz曲线进行建模，允许用户通过交互式图形界面直观地定义目标噪声的功率谱密度特性，该业务流程如图3-9所示。

用户进行海洋环境噪声仿真时，仿真系统首先显示Wenz曲线背景图，用户开始定义噪声谱级曲线。用户可以选择通过图形直接交互定义或通过输入方式定义频率-谱级控制点。完成噪声谱级曲线定义后，用户启动噪声仿真计算。仿真系统生成环境噪声信号，计算信号特性，并展示仿真结果供用户查看和分析。

图 3 9  海洋环境噪声仿真活动图
3）声传播环境仿真
声传播环境仿真用于建模声波在复杂海洋环境中的传播特性，为后续的综合声场分析提供信道数据支撑，业务流程如图3-10所示。
用户进行声传播环境仿真时，首先配置声传播环境参数并启动声传播计算，仿真系统调用声传播模型进行计算并可视化展示计算结果，用户查看和分析结果。在完成声传播环境探索后，用户可以选择是否制备信道数据。若选择制备信道数据，用户只需要设置接收阵列几何与计算频率列表，无需重新配置环境参数，然后启动信道数据制备，仿真系统基于已有的环境配置调用声传播模型进行计算并保存信道数据到目标目录。

图 3 10  声传播环境仿真活动图
4）综合声场仿真
综合声场仿真是系统的最高层次功能，整合前述各模块的结果进行综合分析，业务流程如图3-11所示。
用户进行综合声场仿真时，首先选择信道数据目录，仿真系统加载信道信息并显示信道基本信息。然后用户可以配置噪声信号并启动信号仿真，仿真系统进行信道建模并模拟噪声信号传播，展示各阵元时域信号。最后，用户可以进一步进行信号分析，仿真系统将进行阵列信号处理并可视化展示分析结果，从而用户可以查看信号处理的结果。

图 3 10  综合声场仿真活动图
3.3 面向复杂水声环境的噪声仿真系统的非功能型需求分析
为确保面向复杂水声环境的噪声仿真系统不仅功能完善，还能提供良好的用户体验并具备实际应用价值，除功能性需求外，系统还需满足以下两点关键的非功能性需求。
首先，在性能方面，系统必须展现出高效的处理能力和即时响应特性。对于核心的声学仿真计算，尤其是处理复杂场景或大规模数据集（如多频点信道建模、多阵元信号综合）时，系统应能在用户可接受的时间范围内完成计算任务，避免过长的等待时间。同时，用户界面的响应速度至关重要，用户在进行参数输入、功能选择、视图切换等操作时，系统应能提供流畅无延迟的交互反馈，不应因后台计算任务而影响前台操作的顺畅性。
其次，考虑到系统未来可能的维护与发展，系统在设计初期即应关注其可维护性与可扩展性。这意味着系统的内部逻辑结构应具备高度的清晰性和组织性，便于开发人员理解、修改现有功能或排查潜在问题。同时，系统应具备良好的扩展潜力，以便在未来能够相对容易地集成新的噪声模型、声传播算法或增加新的分析功能模块，以适应水声领域不断发展的研究需求。
3.4 本章小结
本章从需求分析的视角对水声噪声仿真系统进行了全面梳理，明确了系统应当具备的核心功能和性能指标。通过功能性需求和非功能性需求的识别与分类，建立了系统的需求框架。在此基础上，进行用例分析以建模系统的功能模型，刻画了用户与系统的交互场景，并以分析类图对系统的静态模型进行了建模，最后通过动态模型建模描述了主要业务流程的执行逻辑。下一章将在需求分析的基础上，开展系统的架构设计和详细设计工作。