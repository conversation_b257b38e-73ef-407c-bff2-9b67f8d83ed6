# -*- coding: utf-8 -*-
"""
信号导出管理器

负责管理和执行时域信号的导出功能，支持多种格式和导出选项。
"""

import json
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Any
from PyQt5.QtCore import QObject, pyqtSignal


class SignalExportManager(QObject):
    """
    信号导出管理器

    负责管理和执行时域信号的导出功能
    """

    # 导出进度信号
    export_progress = pyqtSignal(int)  # 进度百分比
    export_completed = pyqtSignal(str)  # 导出完成，参数为导出路径
    export_error = pyqtSignal(str)  # 导出错误，参数为错误信息

    def __init__(self, data_manager):
        """
        初始化信号导出管理器

        Args:
            data_manager: 数据管理器实例
        """
        super().__init__()
        self.data_manager = data_manager

        # 支持的导出格式
        self.supported_formats = {
            'wav': 'WAV音频文件 (*.wav)',
            'npz': 'NumPy压缩文件 (*.npz)',
            'mat': 'MATLAB文件 (*.mat)'
        }

    def get_available_signals(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        获取可导出的信号列表

        Returns:
            Dict: 按模块分类的可导出信号信息
                {
                    'ship_noise': [
                        {
                            'name': '总信号',
                            'key': 'total_signal',
                            'description': '船舶辐射噪声总信号',
                            'metadata': {...}
                        },
                        ...
                    ],
                    'ambient_noise': [...],
                    'integrated': [...]
                }
        """
        available_signals = {}

        # 检查船舶辐射噪声信号
        try:
            ship_results = self.data_manager.get_results('ship_noise')
            if ship_results:
                ship_signals = []

                # 总信号
                if 'total_signal' in ship_results:
                    ship_signals.append({
                        'name': '总信号',
                        'key': 'total_signal',
                        'description': '船舶辐射噪声总信号（连续谱+线谱+调制谱）',
                        'metadata': self._extract_metadata(ship_results, 'total_signal')
                    })

                # 连续谱信号
                if 'continuous_signal' in ship_results:
                    ship_signals.append({
                        'name': '连续谱信号',
                        'key': 'continuous_signal',
                        'description': '船舶辐射噪声连续谱信号',
                        'metadata': self._extract_metadata(ship_results, 'continuous_signal')
                    })

                # 线谱信号
                if 'line_signal' in ship_results:
                    ship_signals.append({
                        'name': '线谱信号',
                        'key': 'line_signal',
                        'description': '船舶辐射噪声线谱信号',
                        'metadata': self._extract_metadata(ship_results, 'line_signal')
                    })

                # 调制谱信号
                if 'modulation_signal' in ship_results:
                    ship_signals.append({
                        'name': '调制谱信号',
                        'key': 'modulation_signal',
                        'description': '船舶辐射噪声调制谱信号',
                        'metadata': self._extract_metadata(ship_results, 'modulation_signal')
                    })

                if ship_signals:
                    available_signals['ship_noise'] = ship_signals

        except Exception as e:
            print(f"检查船舶辐射噪声信号时出错: {e}")

        # 检查海洋环境噪声信号
        try:
            ambient_results = self.data_manager.get_results('ambient_noise')
            if ambient_results and 'ambient_signal' in ambient_results:
                available_signals['ambient_noise'] = [{
                    'name': '环境噪声信号',
                    'key': 'ambient_signal',
                    'description': '海洋环境噪声信号',
                    'metadata': self._extract_metadata(ambient_results, 'ambient_signal')
                }]
        except Exception as e:
            print(f"检查海洋环境噪声信号时出错: {e}")

        # 检查综合仿真信号
        try:
            integrated_results = self.data_manager.get_results('integrated')
            if integrated_results:
                integrated_signals = []

                # 检查信号处理结果中的接收信号
                signal_processing = integrated_results.get('signal_processing', {})

                # 检查是否有接收信号数据
                if 'received_signals' in signal_processing:
                    received_signals = signal_processing['received_signals']

                    # 检查是否有原始信号（未叠加噪声）
                    has_original_signals = False
                    has_noise_added_signals = False

                    # 检查第一个阵元的数据结构来判断可用的信号类型
                    if received_signals:
                        first_element_data = next(iter(received_signals.values()))
                        if 'original_signal' in first_element_data:
                            has_original_signals = True
                        if 'signal' in first_element_data:
                            # 检查是否已叠加噪声
                            ambient_noise_added = signal_processing.get('ambient_noise_added', False)
                            if ambient_noise_added:
                                has_noise_added_signals = True
                            else:
                                # 如果没有叠加噪声，signal就是原始信号
                                if not has_original_signals:
                                    has_original_signals = True

                    # 添加原始信号（未叠加噪声）
                    if has_original_signals:
                        integrated_signals.append({
                            'name': '各阵元接收信号（无噪声）',
                            'key': 'received_signals_original',
                            'description': '综合仿真各阵元接收信号（未叠加海洋环境噪声）',
                            'metadata': self._extract_integrated_metadata(integrated_results, 'received_signals', use_original=True),
                            'is_multi_channel': True
                        })

                    # 添加叠加噪声后的信号
                    if has_noise_added_signals:
                        integrated_signals.append({
                            'name': '各阵元接收信号（有噪声）',
                            'key': 'received_signals_with_noise',
                            'description': '综合仿真各阵元接收信号（已叠加海洋环境噪声）',
                            'metadata': self._extract_integrated_metadata(integrated_results, 'received_signals', use_original=False),
                            'is_multi_channel': True
                        })

                if integrated_signals:
                    available_signals['integrated'] = integrated_signals

        except Exception as e:
            print(f"检查综合仿真信号时出错: {e}")

        return available_signals

    def _extract_metadata(self, results: Dict, signal_key: str) -> Dict[str, Any]:
        """
        提取信号的元数据信息

        Args:
            results: 模块结果字典
            signal_key: 信号键名

        Returns:
            Dict: 元数据信息
        """
        metadata = {}

        # 尝试从结果中提取元数据
        if 'metadata' in results:
            metadata.update(results['metadata'])

        # 尝试从信号处理结果中提取元数据（针对综合仿真）
        if 'signal_processing' in results:
            signal_processing = results['signal_processing']
            if 'source_signal' in signal_processing and 'metadata' in signal_processing['source_signal']:
                metadata.update(signal_processing['source_signal']['metadata'])

        # 添加信号特定信息
        if signal_key in results:
            signal_data = results[signal_key]
            if isinstance(signal_data, np.ndarray):
                metadata['signal_length'] = len(signal_data)
                metadata['signal_shape'] = signal_data.shape
                metadata['signal_dtype'] = str(signal_data.dtype)

        return metadata

    def _extract_integrated_metadata(self, results: Dict, signal_key: str, use_original: bool = False) -> Dict[str, Any]:
        """
        提取综合仿真信号的元数据信息

        Args:
            results: 综合仿真结果字典
            signal_key: 信号键名
            use_original: 是否使用原始信号（未叠加噪声）

        Returns:
            Dict: 元数据信息
        """
        metadata = {}

        # 从信号处理结果中提取元数据
        signal_processing = results.get('signal_processing', {})

        # 获取源信号元数据
        if 'source_signal' in signal_processing and 'metadata' in signal_processing['source_signal']:
            source_metadata = signal_processing['source_signal']['metadata']
            metadata.update(source_metadata)

        # 获取信号数据 - 现在received_signals是字典结构
        if signal_key in signal_processing:
            received_signals = signal_processing[signal_key]
            if isinstance(received_signals, dict) and received_signals:
                # 获取第一个阵元的数据来分析信号结构
                first_element_data = next(iter(received_signals.values()))

                # 根据use_original参数选择信号类型
                if use_original and 'original_signal' in first_element_data:
                    sample_signal = first_element_data['original_signal']
                    signal_type = 'original'
                elif 'signal' in first_element_data:
                    sample_signal = first_element_data['signal']
                    signal_type = 'current'
                else:
                    sample_signal = None
                    signal_type = 'unknown'

                if sample_signal is not None:
                    # 计算多阵元信号的总形状
                    num_elements = len(received_signals)
                    signal_length = len(sample_signal)

                    metadata['signal_length'] = signal_length
                    metadata['signal_shape'] = (num_elements, signal_length)
                    metadata['signal_dtype'] = str(sample_signal.dtype)
                    metadata['num_channels'] = num_elements
                    metadata['signal_type'] = signal_type

                    # 添加阵元信息
                    metadata['element_ids'] = list(received_signals.keys())

        # 添加时间信息
        if 't_first_global' in signal_processing:
            metadata['t_first_global'] = signal_processing['t_first_global']
        if 't_last_global' in signal_processing:
            metadata['t_last_global'] = signal_processing['t_last_global']
        if 't_last_global_eff' in signal_processing:
            metadata['t_last_global_eff'] = signal_processing['t_last_global_eff']

        # 添加噪声信息
        if 'ambient_noise_added' in signal_processing:
            metadata['ambient_noise_added'] = signal_processing['ambient_noise_added']

        # 添加信号类型说明
        metadata['use_original_signal'] = use_original

        return metadata

    def _convert_received_signals_to_array(self, received_signals: Dict, use_original: bool = False) -> np.ndarray:
        """
        将字典结构的接收信号转换为二维数组

        Args:
            received_signals: 接收信号字典 {element_id: {'signal': [...], 'original_signal': [...], ...}}
            use_original: 是否使用原始信号（未叠加噪声）

        Returns:
            np.ndarray: 二维数组，形状为 (num_elements, num_samples)
        """
        if not received_signals:
            raise ValueError("接收信号数据为空")

        # 获取阵元ID列表并排序，确保输出顺序一致（按阵元ID升序）
        element_ids = sorted(received_signals.keys())

        # 收集所有信号数据
        signals = []
        for element_id in element_ids:
            element_data = received_signals[element_id]

            # 根据use_original参数选择信号类型
            if use_original and 'original_signal' in element_data:
                signal = element_data['original_signal']
            elif 'signal' in element_data:
                signal = element_data['signal']
            else:
                raise ValueError(f"阵元 {element_id} 缺少信号数据")

            signals.append(signal)

        # 检查所有信号长度是否一致
        signal_lengths = [len(sig) for sig in signals]
        if len(set(signal_lengths)) > 1:
            print(f"警告：不同阵元的信号长度不一致: {signal_lengths}")
            # 使用最短长度
            min_length = min(signal_lengths)
            signals = [sig[:min_length] for sig in signals]
            print(f"已截断到最短长度: {min_length}")

        # 转换为二维数组
        signal_array = np.array(signals, dtype=np.float32)

        print(f"转换接收信号为数组，形状: {signal_array.shape}, 使用原始信号: {use_original}")

        return signal_array

    def _export_multiple_files(self, signal_data: np.ndarray, base_export_path: str,
                              metadata: Dict[str, Any], format_type: str,
                              options: Dict[str, Any] = None) -> bool:
        """
        导出多个文件，每个阵元一个文件

        Args:
            signal_data: 信号数据 (num_elements, num_samples)
            base_export_path: 基础导出路径
            metadata: 元数据
            format_type: 导出格式
            options: 导出选项

        Returns:
            bool: 是否全部导出成功
        """
        try:
            # 获取阵元ID列表，确保与信号数据的顺序对应
            element_ids = metadata.get('element_ids', list(range(signal_data.shape[0])))

            # 验证阵元ID数量与信号数据维度匹配
            if len(element_ids) != signal_data.shape[0]:
                print(f"警告：阵元ID数量({len(element_ids)})与信号数据维度({signal_data.shape[0]})不匹配")
                element_ids = list(range(signal_data.shape[0]))

            # 解析基础路径
            import os
            base_dir = os.path.dirname(base_export_path)
            base_name = os.path.splitext(os.path.basename(base_export_path))[0]

            # 计算归一化因子
            normalize_mode = options.get('normalize', 'none') if options else 'none'
            normalization_factors = self._calculate_normalization_factors(signal_data, normalize_mode)

            exported_files = []

            for i, element_id in enumerate(element_ids):
                # 生成文件名
                element_filename = f"{base_name}_element_{element_id}.{format_type}"
                element_path = os.path.join(base_dir, element_filename)

                # 获取单个阵元的信号
                element_signal = signal_data[i:i+1, :]  # 保持二维形状

                # 创建单个阵元的元数据
                element_metadata = metadata.copy()
                element_metadata.update({
                    'element_id': element_id,
                    'element_index': i,
                    'signal_shape': element_signal.shape,
                    'num_channels': 1,
                    'normalization_factor': normalization_factors[i],
                    'normalize_mode': normalize_mode
                })

                # 创建单个阵元的选项
                element_options = options.copy() if options else {}
                element_options['_single_element_mode'] = True
                element_options['_normalization_factor'] = normalization_factors[i]

                # 导出单个文件
                if format_type == 'wav':
                    success = self._export_wav(element_signal, element_path, element_metadata, element_options)
                elif format_type == 'npz':
                    success = self._export_npz(element_signal, element_path, element_metadata, element_options)
                elif format_type == 'mat':
                    success = self._export_mat(element_signal, element_path, element_metadata, element_options)
                else:
                    raise ValueError(f"不支持的导出格式: {format_type}")

                if success:
                    exported_files.append(element_path)
                    print(f"已导出阵元 {element_id}: {element_path}")
                else:
                    print(f"导出阵元 {element_id} 失败: {element_path}")
                    return False

            print(f"成功导出 {len(exported_files)} 个阵元文件")
            return True

        except Exception as e:
            print(f"多文件导出失败: {e}")
            return False

    def _calculate_normalization_factors(self, signal_data: np.ndarray, normalize_mode: str) -> List[float]:
        """
        计算归一化因子

        Args:
            signal_data: 信号数据 (num_elements, num_samples)
            normalize_mode: 归一化模式 ('none', 'global')

        Returns:
            List[float]: 每个阵元的归一化因子
        """
        num_elements = signal_data.shape[0]

        if normalize_mode == 'none':
            # 无归一化
            return [1.0] * num_elements
        elif normalize_mode == 'global':
            # 全局归一化：所有阵元使用相同因子
            global_max = np.max(np.abs(signal_data))
            factor = 1.0 / global_max if global_max > 0 else 1.0
            return [factor] * num_elements
        else:
            raise ValueError(f"不支持的归一化模式: {normalize_mode}")

    def get_supported_formats(self) -> Dict[str, str]:
        """
        获取支持的导出格式

        Returns:
            Dict: 格式代码到描述的映射
        """
        return self.supported_formats.copy()

    def export_signal(self, module_name: str, signal_key: str, export_path: str,
                     format_type: str, options: Dict[str, Any] = None) -> bool:
        """
        导出单个信号

        Args:
            module_name: 模块名称 ('ship_noise', 'ambient_noise', 'integrated')
            signal_key: 信号键名
            export_path: 导出路径
            format_type: 导出格式 ('wav', 'npz', 'mat')
            options: 导出选项
                - selected_channels: 选择的通道列表（用于多通道信号）
                - normalize: 归一化方式 ('none', 'global', 'individual')
                - bit_depth: 位深度（用于WAV格式）
                - export_mode: 导出模式 ('single_file', 'multiple_files') - 仅用于多阵元信号
                - base_filename: 基础文件名（用于多文件模式）

        Returns:
            bool: 是否导出成功
        """
        try:
            self.export_progress.emit(10)

            # 获取信号数据
            results = self.data_manager.get_results(module_name)
            if not results:
                raise ValueError(f"未找到模块结果: {module_name}")

            # 根据模块类型获取信号数据
            if module_name == 'integrated':
                # 综合仿真模块的信号在signal_processing中
                signal_processing = results.get('signal_processing', {})

                # 处理特殊的信号键名
                if signal_key == 'received_signals_original':
                    # 原始信号（未叠加噪声）
                    if 'received_signals' not in signal_processing:
                        raise ValueError(f"未找到接收信号数据")
                    received_signals = signal_processing['received_signals']
                    signal_data = self._convert_received_signals_to_array(received_signals, use_original=True)
                    metadata = self._extract_integrated_metadata(results, 'received_signals', use_original=True)
                elif signal_key == 'received_signals_with_noise':
                    # 叠加噪声后的信号
                    if 'received_signals' not in signal_processing:
                        raise ValueError(f"未找到接收信号数据")
                    received_signals = signal_processing['received_signals']
                    signal_data = self._convert_received_signals_to_array(received_signals, use_original=False)
                    metadata = self._extract_integrated_metadata(results, 'received_signals', use_original=False)
                else:
                    # 其他信号类型（保持原有逻辑）
                    if signal_key not in signal_processing:
                        raise ValueError(f"未找到信号: {module_name}.{signal_key}")
                    signal_data = signal_processing[signal_key]
                    metadata = self._extract_integrated_metadata(results, signal_key)
            else:
                # 其他模块的信号直接在results中
                if signal_key not in results:
                    raise ValueError(f"未找到信号: {module_name}.{signal_key}")
                signal_data = results[signal_key]
                metadata = self._extract_metadata(results, signal_key)

            self.export_progress.emit(30)

            # 检查是否为多阵元信号且需要多文件导出
            is_multi_element = (isinstance(signal_data, np.ndarray) and signal_data.ndim == 2 and
                              'element_ids' in metadata)
            export_mode = options.get('export_mode', 'single_file') if options else 'single_file'

            if is_multi_element and export_mode == 'multiple_files':
                # 多文件导出模式
                success = self._export_multiple_files(signal_data, export_path, metadata, format_type, options)
            else:
                # 单文件导出模式（导出所有阵元）
                self.export_progress.emit(50)

                # 根据格式导出
                if format_type == 'wav':
                    success = self._export_wav(signal_data, export_path, metadata, options)
                elif format_type == 'npz':
                    success = self._export_npz(signal_data, export_path, metadata, options)
                elif format_type == 'mat':
                    success = self._export_mat(signal_data, export_path, metadata, options)
                else:
                    raise ValueError(f"不支持的导出格式: {format_type}")

            self.export_progress.emit(90)

            if success:
                self.export_progress.emit(100)
                self.export_completed.emit(export_path)
                return True
            else:
                raise RuntimeError("导出失败")

        except Exception as e:
            error_msg = f"导出信号失败: {str(e)}"
            self.export_error.emit(error_msg)
            return False

    def _export_wav(self, signal_data: np.ndarray, export_path: str,
                   metadata: Dict[str, Any], options: Dict[str, Any] = None) -> bool:
        """
        导出WAV格式

        Args:
            signal_data: 信号数据
            export_path: 导出路径
            metadata: 元数据
            options: 导出选项

        Returns:
            bool: 是否成功
        """
        try:
            from scipy.io import wavfile

            # 获取采样率
            fs = metadata.get('fs', 44100)

            # 处理信号数据
            if signal_data.ndim == 1:
                # 单通道信号
                audio_data = signal_data.copy()
            elif signal_data.ndim == 2:
                # 多通道信号，转置为 (samples, channels) 格式
                audio_data = signal_data.T
            else:
                raise ValueError("不支持的信号维度")

            # 归一化处理和缩放因子记录
            normalization_factor = 1.0
            original_max_val = np.max(np.abs(audio_data))

            # 检查是否为单阵元模式（从多文件导出调用）
            if options and options.get('_single_element_mode', False):
                # 使用预计算的归一化因子
                normalization_factor = options.get('_normalization_factor', 1.0)
                if normalization_factor != 1.0:
                    audio_data = audio_data * normalization_factor
                    print(f"单阵元信号已归一化，归一化因子: {normalization_factor:.6f}")
            else:
                # 传统归一化方式（向后兼容）
                normalize_option = options.get('normalize', True) if options else True
                if normalize_option and normalize_option != 'none':
                    if original_max_val > 0:
                        normalization_factor = 1.0 / original_max_val
                        audio_data = audio_data * normalization_factor
                        print(f"信号已归一化，原始最大值: {original_max_val:.6f}, 归一化因子: {normalization_factor:.6f}")

            # 转换数据类型
            bit_depth = options.get('bit_depth', 16) if options else 16
            scaling_factor = 1.0

            if bit_depth == 16:
                scaling_factor = 32767.0
                audio_data = (audio_data * scaling_factor).astype(np.int16)
            elif bit_depth == 32:
                scaling_factor = 2147483647.0
                audio_data = (audio_data * scaling_factor).astype(np.int32)
            else:
                # 保持float32格式，但限制在[-1, 1]范围内
                audio_data = np.clip(audio_data, -1.0, 1.0).astype(np.float32)

            # 写入WAV文件
            wavfile.write(export_path, fs, audio_data)

            # 准备增强的元数据
            enhanced_metadata = metadata.copy()

            # 基础导出信息
            export_info = {
                'export_format': 'wav',
                'bit_depth': bit_depth,
                'original_max_amplitude': float(original_max_val),
                'normalization_factor': float(normalization_factor),
                'scaling_factor': float(scaling_factor),
                'total_scaling': float(normalization_factor * scaling_factor),
                'recovery_formula': f"original_signal = wav_signal / {normalization_factor * scaling_factor}"
            }

            # 添加归一化模式信息
            if options and options.get('_single_element_mode', False):
                # 单阵元模式（从多文件导出调用）
                export_info.update({
                    'normalized': True,
                    'normalization_mode': 'single_element',
                    'element_id': metadata.get('element_id', 'unknown'),
                    'element_index': metadata.get('element_index', 'unknown')
                })
            else:
                # 多阵元模式
                normalize_option = options.get('normalize', True) if options else True
                if normalize_option == 'none':
                    export_info['normalized'] = False
                    export_info['normalization_mode'] = 'none'
                elif normalize_option == 'global':
                    export_info['normalized'] = True
                    export_info['normalization_mode'] = 'global'
                    export_info['note'] = '所有阵元使用相同归一化因子，保持相对幅度关系'
                else:
                    # 向后兼容：布尔值
                    export_info['normalized'] = bool(normalize_option)
                    export_info['normalization_mode'] = 'legacy'

                # 添加阵元顺序信息
                if 'element_ids' in metadata:
                    export_info['channel_mapping'] = {
                        f'channel_{i}': f'element_{element_id}'
                        for i, element_id in enumerate(metadata['element_ids'])
                    }

            enhanced_metadata.update(export_info)

            # 生成元数据文件
            metadata_path = export_path.replace('.wav', '_metadata.json')
            self._save_metadata(metadata_path, enhanced_metadata, signal_data.shape)

            return True

        except Exception as e:
            print(f"导出WAV文件失败: {e}")
            return False

    def _export_npz(self, signal_data: np.ndarray, export_path: str,
                   metadata: Dict[str, Any], options: Dict[str, Any] = None) -> bool:
        """
        导出NPZ格式

        Args:
            signal_data: 信号数据
            export_path: 导出路径
            metadata: 元数据
            options: 导出选项

        Returns:
            bool: 是否成功
        """
        try:
            # 处理归一化
            export_signal_data = signal_data.copy()
            normalization_factor = 1.0
            original_max_val = np.max(np.abs(signal_data))

            # 检查是否为单阵元模式（从多文件导出调用）
            if options and options.get('_single_element_mode', False):
                # 使用预计算的归一化因子
                normalization_factor = options.get('_normalization_factor', 1.0)
                if normalization_factor != 1.0:
                    export_signal_data = export_signal_data * normalization_factor
                    print(f"NPZ导出：单阵元信号已归一化，归一化因子: {normalization_factor:.6f}")
            else:
                # 传统归一化方式（向后兼容）
                normalize_option = options.get('normalize', False) if options else False
                if normalize_option and normalize_option != 'none':
                    if original_max_val > 0:
                        normalization_factor = 1.0 / original_max_val
                        export_signal_data = export_signal_data * normalization_factor
                        print(f"NPZ导出：信号已归一化，原始最大值: {original_max_val:.6f}")

            # 准备保存的数据
            save_data = {
                'signal_data': export_signal_data,
                'metadata': metadata
            }

            # 添加时间轴数据
            fs = metadata.get('fs', 44100)
            num_samples = len(signal_data) if signal_data.ndim == 1 else signal_data.shape[1]

            # 对于综合仿真信号，使用实际的起始时间
            if 't_first_global' in metadata:
                start_time = metadata['t_first_global']
                duration = num_samples / fs
                time_data = np.linspace(start_time, start_time + duration, num_samples)
            else:
                # 其他信号从0开始
                duration = num_samples / fs
                time_data = np.linspace(0, duration, num_samples)

            save_data['time_data'] = time_data

            # 添加归一化信息
            save_data['export_info'] = {
                'format': 'npz',
                'normalized': options.get('normalize', False) if options else False,
                'original_max_amplitude': float(original_max_val),
                'normalization_factor': float(normalization_factor),
                'recovery_formula': f"original_signal = exported_signal / {normalization_factor}"
            }

            # 保存为NPZ文件
            np.savez_compressed(export_path, **save_data)

            return True

        except Exception as e:
            print(f"导出NPZ文件失败: {e}")
            return False

    def _export_mat(self, signal_data: np.ndarray, export_path: str,
                   metadata: Dict[str, Any], options: Dict[str, Any] = None) -> bool:
        """
        导出MAT格式

        Args:
            signal_data: 信号数据
            export_path: 导出路径
            metadata: 元数据
            options: 导出选项

        Returns:
            bool: 是否成功
        """
        try:
            from scipy.io import savemat

            # 处理归一化
            export_signal_data = signal_data.copy()
            normalization_factor = 1.0
            original_max_val = np.max(np.abs(signal_data))

            # 检查是否为单阵元模式（从多文件导出调用）
            if options and options.get('_single_element_mode', False):
                # 使用预计算的归一化因子
                normalization_factor = options.get('_normalization_factor', 1.0)
                if normalization_factor != 1.0:
                    export_signal_data = export_signal_data * normalization_factor
                    print(f"MAT导出：单阵元信号已归一化，归一化因子: {normalization_factor:.6f}")
            else:
                # 传统归一化方式（向后兼容）
                normalize_option = options.get('normalize', False) if options else False
                if normalize_option and normalize_option != 'none':
                    if original_max_val > 0:
                        normalization_factor = 1.0 / original_max_val
                        export_signal_data = export_signal_data * normalization_factor
                        print(f"MAT导出：信号已归一化，原始最大值: {original_max_val:.6f}")

            # 准备保存的数据
            fs = metadata.get('fs', 44100)
            num_samples = len(signal_data) if signal_data.ndim == 1 else signal_data.shape[1]

            # 对于综合仿真信号，使用实际的起始时间
            if 't_first_global' in metadata:
                start_time = metadata['t_first_global']
                duration = num_samples / fs
                time_data = np.linspace(start_time, start_time + duration, num_samples)
            else:
                # 其他信号从0开始
                duration = num_samples / fs
                time_data = np.linspace(0, duration, num_samples)

            save_data = {
                'signal_data': export_signal_data,
                'time_data': time_data,
                'fs': fs,
                'duration': duration,
                # 归一化信息
                'export_normalized': options.get('normalize', False) if options else False,
                'original_max_amplitude': float(original_max_val),
                'normalization_factor': float(normalization_factor),
                'recovery_note': f"To recover original signal: multiply by {1.0/normalization_factor}"
            }

            # 添加其他元数据
            for key, value in metadata.items():
                if isinstance(value, (int, float, str, bool, np.ndarray)):
                    save_data[f'metadata_{key}'] = value

            # 保存为MAT文件
            savemat(export_path, save_data)

            return True

        except Exception as e:
            print(f"导出MAT文件失败: {e}")
            return False

    def _save_metadata(self, metadata_path: str, metadata: Dict[str, Any],
                      signal_shape: Tuple) -> None:
        """
        保存元数据到JSON文件

        Args:
            metadata_path: 元数据文件路径
            metadata: 元数据字典
            signal_shape: 信号形状
        """
        try:
            # 准备元数据
            export_metadata = {
                'export_time': datetime.now().isoformat(),
                'signal_shape': signal_shape,
                'original_metadata': {}
            }

            # 复制原始元数据，确保可序列化
            for key, value in metadata.items():
                if isinstance(value, (int, float, str, bool, list, dict)):
                    export_metadata['original_metadata'][key] = value
                elif isinstance(value, np.ndarray):
                    export_metadata['original_metadata'][key] = value.tolist()
                else:
                    export_metadata['original_metadata'][key] = str(value)

            # 保存到JSON文件
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(export_metadata, f, indent=2, ensure_ascii=False)

        except Exception as e:
            print(f"保存元数据失败: {e}")
