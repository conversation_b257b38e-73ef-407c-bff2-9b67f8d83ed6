# 海洋环境噪声模拟模块设计文档

## 1. 模块概述

海洋环境噪声模拟模块是水声噪声仿真系统的重要组成部分，用于模拟和生成海洋环境中的背景噪声。本模块基于Wenz曲线模型，通过直观的图形界面允许用户自定义海洋环境噪声的功率谱密度曲线，并生成相应的时域噪声信号。

## 2. 设计目标

1. 提供基于Wenz曲线的海洋环境噪声模拟功能
2. 实现直观的图形化界面，允许用户通过在Wenz曲线图上选点来定义噪声谱
3. 支持实时预览和调整噪声谱形状
4. 生成符合用户定义谱特性的时域噪声信号

## 3. 功能需求

### 3.1 基本功能

1. 显示标准Wenz曲线图作为参考背景
2. 允许用户在图上通过点击选择点来定义自定义噪声谱
3. 实时绘制用户定义的噪声谱曲线
4. 支持编辑已添加的点（移动、删除）
5. 提供数值输入方式，允许精确设置特定频率点的噪声级：
   - 在选中点后通过输入框精确编辑其频率和噪声级值
   - 通过表格形式查看和编辑所有点的数据
   - 提供直接通过数值添加新点的功能
6. 显示当前鼠标位置对应的频率和噪声级
7. 生成符合定义谱特性的时域噪声信号
8. 计算并显示生成信号的功率谱密度
9. 支持保存和加载用户定义的噪声谱

### 3.2 高级功能

1. 支持平滑插值，使用户定义的曲线更加自然
2. 提供预设的典型海洋环境噪声谱：
   - 包含若干基于不同海况、水深、交通密度等因素组合定义的典型噪声谱
   - 每个预设包含一组完整的(频率, 谱级)点集
   - 用户选择预设后，系统加载对应的点集并重新绘制曲线
   - 预设数据存储在外部文件或代码中，便于维护和扩展

## 4. 技术方案

### 4.1 Wenz曲线背景实现

不同于使用图片背景的方式，本模块将直接使用Matplotlib绘制Wenz曲线作为背景。这种方法的优势在于：

1. 完美对齐坐标轴，特别是对数频率轴
2. 支持缩放和平移时保持清晰度和对齐
3. 避免图片分辨率和缩放问题
4. 便于动态调整显示范围

具体实现步骤：

1. 数据准备：使用数字化工具从标准Wenz曲线图中提取关键曲线的坐标数据点，包括：

   - 不同海况/风速的风生噪声曲线
   - 交通噪声上下边界
   - 湍流压力波动范围
   - 热噪声曲线
   - 噪声总体上下限

2. 绘制实现：

   - 使用Matplotlib的对数X轴和线性Y轴
   - 使用 `plot()`函数绘制各条曲线
   - 使用 `fill_between()`函数绘制带状区域
   - 添加适当的图例和标签
   - 设置X轴范围为1-10000Hz，这是我们关注的频率范围

3. 数据存储：

   - 将数字化后的Wenz曲线数据保存到data目录中
   - 使用结构化格式（如JSON或CSV）便于加载和使用

### 4.2 用户交互设计

用户交互设计遵循左侧View区域（画布）和右侧控制Tab的布局，提供丰富的交互功能：

1. 画布交互工具：

   - 支持Matplotlib原生的缩放和平移工具
   - 实现鼠标滚轮缩放功能，以鼠标指针位置为中心进行区域缩放
   - 提供一键还原视图的功能
   - 在画布右上角实时显示鼠标指针位置对应的频率和噪声级（精确到1-2位小数）

2. 点操作模式：

   - 添加模式：点击画布添加新的频率-噪声级点
   - 调整模式：选择已有点进行调整，被选中的点变色（如从红色变为绿色）
   - 删除模式：点击已有点进行删除

3. 点的精确调整：

   - 在调整模式下，使用键盘方向键对选中点进行微调
   - 调整时实时更新点的频率-噪声级值显示
   - 调整后自动更新用户曲线
   - 当一个点被选中时，在控制面板提供对应的频率和噪声级输入框（如`QDoubleSpinBox`），允许用户直接输入数值进行精确调整

4. 曲线实时渲染：

   - 当点数为0时，只显示Wenz曲线背景
   - 当点数为1时，显示单个点但不绘制曲线
   - 当点数≥2时，绘制用户曲线（按频率排序连接各点）
   - 每次点操作（添加、调整、删除）后实时更新曲线

5. 数据查看功能：

   - 在控制面板提供"查看数据"按钮
   - 点击后弹出新窗口，以表格形式展示所有点的频率-噪声级值
   - 表格按频率排序，数值精确到1-2位小数
   - 表格单元格可编辑，允许用户直接修改频率和噪声级值
   - 修改后自动更新曲线显示

### 4.3 频谱外插功能

为确保噪声谱覆盖完整的关注频率范围（1-10000Hz），系统提供自动外插功能：

1. 外插触发条件：

   - 用户完成初步选点后，系统检查是否覆盖1-10000Hz范围
   - 如果用户选择的最小频率点（精度处理后）不等于1.0Hz，则需要向低频外插
   - 如果用户选择的最大频率点（精度处理后）不等于10000Hz，则需要向高频外插

2. 外插算法：

   - 基于Wenz曲线的频段划分：1-20Hz, 20-500Hz, 500-20000Hz
   - 每个频段对应不同的斜率：如-9dB/Oct, -3dB/Oct, -5dB/Oct
   - 根据用户选择的频率点范围，确定外插的起始频段
   - 以倍频程为间隔，使用对应频段的斜率进行外插

3. 外插示例：

   - 如用户选择的最小频率为400Hz，系统将：
     - 以-3dB/Oct斜率从400Hz向20Hz进行外插
     - 再以-9dB/Oct斜率从20Hz向1Hz进行外插
   - 外插点使用不同颜色（如蓝色）与用户选择的点（如红色）区分
   - 用户可以继续调整所有点（包括外插点）

### 4.4 噪声信号生成

基于用户定义的噪声谱（包括外插后的完整频谱），生成时域噪声信号的步骤：

1. 频谱处理：

   - 将用户定义的点（包括外插点）按频率排序
   - 将dB值转换为线性幅度值
   - 确保频率点覆盖完整的1-10000Hz范围
   - 添加0Hz和Nyquist频率点以满足滤波器设计要求

2. 信号生成：

   - 生成高斯白噪声
   - 使用firwin2等函数直接设计FIR滤波器，传入频率点和线性幅度值
   - 对白噪声进行滤波
   - 进行谱级校正，确保生成信号的PSD符合目标谱

3. 验证：

   - 计算生成信号的功率谱密度
   - 与用户定义的目标谱进行比较，确保符合要求
   - 显示对比结果，便于用户评估

## 5. 模块架构

### 5.1 类结构设计

```
OceanAmbientNoise
├── 属性
│   ├── user_defined_points: List[(freq, level)]  # 用户定义的频率-电平点
│   ├── extrapolated_points: List[(freq, level)]  # 系统外插的频率-电平点
│   ├── interpolated_spectrum: Dict{freq: level}  # 插值后的完整频谱
│   ├── sampling_rate: float                      # 采样率
│   ├── duration: float                           # 信号持续时间
│   ├── generated_signal: ndarray                 # 生成的时域信号
│   ├── frequency_bands: Dict                     # 频段划分定义
│   └── slope_factors: Dict                       # 各频段斜率因子
│
├── 方法
│   ├── add_point(freq, level)                    # 添加一个用户定义点
│   ├── remove_point(index)                       # 删除指定索引的点
│   ├── update_point(index, freq, level)          # 更新指定索引的点
│   ├── clear_points()                            # 清除所有点
│   ├── extrapolate_spectrum()                    # 执行频谱外插
│   ├── convert_slope_to_factor(slope_db_oct)     # 将dB/Oct斜率转换为斜率因子
│   ├── prepare_filter_parameters()               # 准备滤波器设计所需的频率和幅度参数
│   ├── generate_signal()                         # 生成时域噪声信号
│   ├── calculate_psd()                           # 计算生成信号的PSD
│   ├── save_spectrum(file_path)                  # 保存用户定义的频谱
│   └── load_spectrum(file_path)                  # 加载用户定义的频谱
```

### 5.2 UI组件设计

```
AmbientNoiseView (QWidget)
├── 组件
│   ├── MatplotlibCanvas                          # Wenz曲线和用户曲线的绘图区域
│   │   ├── NavigationToolbar                     # Matplotlib导航工具栏（缩放、平移等）
│   │   └── CoordinateDisplay (QLabel)            # 右上角坐标显示
│   └── StatusBar (QLabel)                        # 状态信息显示
│
├── 信号
│   ├── point_added(freq, level)                  # 添加了新点
│   ├── point_selected(index)                     # 选中了已有点
│   ├── point_updated(index, freq, level)         # 更新了点的位置
│   └── point_removed(index)                      # 删除了点
│
├── 槽函数
│   ├── on_mouse_click(event)                     # 处理鼠标点击事件
│   ├── on_mouse_move(event)                      # 处理鼠标移动事件
│   ├── on_key_press(event)                       # 处理键盘事件（方向键调整点）
│   ├── update_user_curve()                       # 更新用户曲线显示
│   ├── update_extrapolated_curve()               # 更新外插曲线显示
│   ├── set_mode(mode)                            # 设置操作模式（添加/调整/删除）
│   └── reset_view()                              # 重置视图到初始状态

OceanAmbientNoiseTab (QWidget)
├── 组件
│   ├── ModeButtonGroup (QButtonGroup)            # 模式选择按钮组
│   │   ├── AddPointButton (QRadioButton)         # 添加点模式按钮
│   │   ├── AdjustPointButton (QRadioButton)      # 调整点模式按钮
│   │   └── DeletePointButton (QRadioButton)      # 删除点模式按钮
│   ├── OperationButtons (QHBoxLayout)            # 操作按钮组
│   │   ├── ClearButton                           # 清除所有点按钮
│   │   ├── ViewDataButton                        # 查看数据按钮
│   │   ├── ExtrapolateButton                     # 执行外插按钮
│   │   └── FinishButton                          # 完成绘制按钮
│   ├── SelectedPointEditor (QGroupBox)           # 选中点编辑器
│   │   ├── FrequencyInput (QDoubleSpinBox)       # 频率输入框
│   │   └── LevelInput (QDoubleSpinBox)           # 噪声级输入框
│   ├── PointAdder (QGroupBox)                    # 精确添加点功能组
│   │   ├── FrequencyInput (QDoubleSpinBox)       # 频率输入框
│   │   ├── LevelInput (QDoubleSpinBox)           # 噪声级输入框
│   │   └── AddButton (QPushButton)               # 添加按钮
│   ├── ParametersGroup (QGroupBox)               # 参数设置组
│   │   ├── SamplingRateInput                     # 采样率输入
│   │   └── DurationInput                         # 持续时间输入
│   ├── PresetSelector (QComboBox)                # 预设噪声谱选择器
│   └── SimulateButton                            # 开始仿真按钮
│
├── 信号
│   ├── simulation_requested                      # 请求开始仿真
│   └── parameters_changed                        # 参数已更改
│
├── 槽函数
│   ├── on_mode_changed(mode)                     # 处理模式变更
│   ├── on_clear()                                # 清除所有点
│   ├── on_view_data()                            # 显示数据表格
│   ├── on_extrapolate()                          # 执行外插
│   ├── on_finish()                               # 完成绘制
│   ├── on_point_added(freq, level)               # 处理添加点事件
│   ├── on_point_selected(index)                  # 处理选中点事件
│   ├── on_point_updated(index, freq, level)      # 处理更新点事件
│   ├── on_point_removed(index)                   # 处理删除点事件
│   └── on_preset_selected(index)                 # 处理预设选择

PointDataDialog (QDialog)
├── 组件
│   ├── DataTable (QTableWidget)                  # 点数据表格（单元格可编辑）
│   │   ├── FrequencyColumn                       # 频率列
│   │   ├── LevelColumn                           # 噪声级列
│   │   └── TypeColumn                            # 点类型列（用户/外插）
│   ├── ApplyButton                               # 应用修改按钮
│   └── CloseButton                               # 关闭按钮
│
├── 方法
│   ├── update_data(user_points, extrapolated_points)  # 更新表格数据
│   ├── format_value(value, precision)            # 格式化数值显示
│   ├── on_cell_changed(row, column)              # 处理单元格编辑事件
│   └── apply_changes()                           # 应用表格中的修改到模型
```

### 5.3 与系统集成

本模块将通过以下方式与系统其他部分集成：

1. 数据管理：

   - 用户定义的噪声谱点、外插点和生成的信号将存储在SimulationDataManager中
   - 数据管理器分别存储参数和结果，便于序列化和项目保存/加载
   - 参数数据结构设计如下：
     ```python
     {
         'ambient_noise': {
             'user_defined_points': [(freq1, level1), (freq2, level2), ...],  # 用户选择的点
             'extrapolated_points': [(freq1, level1), (freq2, level2), ...],  # 系统外插的点
             'sampling_rate': float,                                           # 采样率
             'duration': float                                                 # 持续时间
         }
     }
     ```
   - 结果数据结构设计如下：
     ```python
     {
         'ambient_noise': {
             'time_data': ndarray,                                             # 时间数组
             'ambient_signal': ndarray,                                        # 生成的时域信号
             'ambient_freqs': ndarray,                                         # 频率数组
             'ambient_psd_db': ndarray                                         # 功率谱密度(dB)
         }
     }
     ```

2. 控制流程：

   - 创建专门的AmbientNoiseController，继承自QThread
   - 控制器负责在后台线程中执行噪声信号生成
   - 通过信号机制与UI线程通信
   - 控制器主要功能：
     - 从数据管理器获取用户定义的点和外插点
     - 执行频谱插值和信号生成
     - 计算PSD并存储结果
     - 发送仿真完成信号

3. 视图更新：

   - 创建AmbientNoiseViewUpdater，负责更新结果显示
   - 监听数据管理器的结果变更信号，自动更新视图
   - 视图更新器主要功能：
     - 从数据管理器获取仿真结果
     - 更新时域信号图和PSD图
     - 确保视图与数据的一致性

4. 用户界面集成：

   - AmbientNoiseView作为ViewArea的一个子视图集成到主界面
   - OceanAmbientNoiseTab作为ControlPanel的一个标签页集成到主界面
   - 遵循系统的整体布局和交互模式
   - 确保与其他模块（如船舶辐射噪声、声传播）的视觉和交互一致性

## 6. 实现计划

### 6.1 开发阶段

1. **准备阶段**：

   - 数字化Wenz曲线图，提取关键曲线数据
   - 将数据保存到data目录中的结构化文件
   - 设计数据结构和类接口

2. **核心功能实现**：

   - 实现OceanAmbientNoise类的基本功能
   - 实现频谱外插算法
   - 实现噪声信号生成算法（基于FIR滤波）

3. **视图组件实现**：

   - 实现AmbientNoiseView类
   - 实现Matplotlib绘图和交互功能
   - 实现点的添加、选择、调整和删除功能
   - 实现键盘方向键微调功能
   - 实现鼠标滚轮缩放功能

4. **控制面板实现**：

   - 实现OceanAmbientNoiseTab界面
   - 实现模式切换按钮组
   - 实现PointDataDialog表格显示
   - 连接UI事件和核心功能

5. **系统集成**：

   - 实现AmbientNoiseController
   - 实现AmbientNoiseViewUpdater
   - 与数据管理器集成
   - 与项目保存/加载功能集成

6. **测试与优化**：

   - 单元测试各组件功能
   - 测试外插算法的准确性
   - 测试噪声信号生成的频谱特性
   - 集成测试与系统其他模块的交互
   - 性能优化和用户体验改进

### 6.2 关键技术点

1. **Wenz曲线数据提取与绘制**：

   - 使用WebPlotDigitizer等工具从标准Wenz曲线图中提取数据点
   - 确保数据点足够密集，特别是在曲线变化较大的区域
   - 正确设置Matplotlib的对数X轴和线性Y轴
   - 确保绘制的曲线与原始Wenz曲线图视觉一致

2. **交互绘图实现**：

   - 使用Matplotlib的事件处理机制捕获用户交互
   - 实现鼠标滚轮缩放功能，需要自定义Matplotlib的事件处理
   - 实现键盘方向键微调功能，需要捕获键盘事件并精确控制点的移动
   - **性能优化:** 使用Matplotlib的**Blitting**技术，通过缓存静态背景（Wenz曲线、坐标轴等），并在交互时仅重绘动态元素（用户曲线、外插曲线、选中点等），显著提高性能
   - 对高频事件（如鼠标移动时的坐标显示）使用**防抖(Debounce)或节流(Throttle)**技术，限制更新频率，避免不必要的计算开销
   - **键盘事件处理:** 通过`canvas.setFocusPolicy(Qt.StrongFocus)`设置Matplotlib画布的焦点策略，使其能接收键盘事件；在进入"调整点模式"并选中点后，调用`canvas.setFocus()`主动将键盘焦点赋予画布

3. **频谱外插算法**：

   - 实现斜率值（dB/Oct）到斜率因子m的转换
   - 根据频段划分（1-20Hz, 20-500Hz, 500-20000Hz）应用不同的斜率
   - 以倍频程为间隔进行外插，确保覆盖1-10000Hz范围
   - 处理外插点与用户点的无缝衔接

4. **噪声信号生成**：

   - 使用firwin2等函数直接设计FIR滤波器，无需手动插值
   - 正确处理dB到线性幅度的转换
   - 添加适当的边界频率点（0Hz和Nyquist频率）
   - 确保生成信号的PSD与目标谱的一致性
   - 优化大信号生成的性能

5. **UI交互逻辑**：

   - 实现三种操作模式（添加/调整/删除）的状态管理
   - **点的选择机制:** 在调整或删除模式下，基于屏幕像素距离计算点击位置与所有可见点的距离；当最近点的距离小于预设阈值（如10像素）时，该点被视为选中
   - **视觉反馈:** 选中的点通过明显的视觉变化（如颜色从红色变为绿色、标记大小增大）提供反馈；状态栏同时显示当前选中点的频率和噪声级信息
   - 实现数据表格的动态更新和格式化显示
   - 处理用户操作的边界情况和异常情况
   - **View-Model/Controller交互:** View (`AmbientNoiseView`) 负责捕获底层用户交互事件并发出高级语义信号；Controller (`OceanAmbientNoiseTab`) 响应这些信号，调用Model (`OceanAmbientNoise`) 的方法更新数据状态；Model的状态变化通过信号机制或Controller的协调更新View的显示

## 7. 总结

海洋环境噪声模拟模块通过直观的图形界面和基于Wenz曲线的参考背景，为用户提供了灵活定义海洋环境噪声特性的能力。该模块采用直接绘制而非图片背景的方式实现Wenz曲线显示，解决了坐标对齐和缩放问题。

本设计的主要特点和优势包括：

1. **直观的交互方式**：用户可以直接在Wenz曲线图上点击选择点，通过添加、调整和删除操作定义自己的噪声谱。支持键盘方向键微调和鼠标滚轮缩放，提高操作精度和便捷性。

2. **智能的频谱外插**：系统能够自动检测用户选择的频率范围，并根据Wenz曲线的频段特性进行智能外插，确保噪声谱覆盖完整的关注频率范围（1-10000Hz）。

3. **灵活的操作模式**：提供添加、调整和删除三种操作模式，用户可以根据需要自由切换，实现对噪声谱的精确定义。

4. **实时的视觉反馈**：系统实时显示用户曲线和外插曲线，提供直观的视觉反馈，帮助用户理解和调整噪声谱。

5. **精确的数据查看**：通过表格形式展示所有点的频率和噪声级值，支持按频率排序，便于用户精确查看和分析数据。

6. **高质量的信号生成**：基于用户定义的噪声谱，使用FIR滤波器设计和谱级校正，生成符合要求的时域噪声信号，确保信号的频谱特性与目标谱一致。

7. **无缝的系统集成**：模块设计充分考虑了与水声噪声仿真系统其他部分的集成，遵循系统的整体架构和数据流设计，确保协同工作的一致性和高效性。

通过这种设计，用户可以在权威的Wenz曲线参照下，方便直观地创建符合特定需求的环境噪声模型。该模块不仅提供了灵活的噪声谱定义功能，还通过智能外插和高质量信号生成，确保生成的噪声信号能够准确反映用户定义的噪声特性，为水声噪声仿真系统提供可靠的环境噪声输入。
