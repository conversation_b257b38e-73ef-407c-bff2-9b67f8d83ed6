#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
并行计算多个频率的声传播信息，使用arlpy的uwapm模块调用Bellhop模型。
"""

import os
import time
import uuid
import argparse
import numpy as np
import pandas as pd
import concurrent.futures
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from pathlib import Path
import arlpy.uwapm as pm

# 设置中文字体
def set_chinese_font():
    """
    设置matplotlib使用中文字体
    """
    try:
        # 查找系统中的中文字体
        chinese_fonts = [f.name for f in fm.fontManager.ttflist
                        if 'SimHei' in f.name or 'Microsoft YaHei' in f.name
                        or 'SimSun' in f.name or 'FangSong' in f.name]

        if chinese_fonts:
            # 使用找到的第一个中文字体
            plt.rcParams['font.sans-serif'] = [chinese_fonts[0]] + plt.rcParams['font.sans-serif']
            print(f"使用中文字体: {chinese_fonts[0]}")
        else:
            # 如果没有找到中文字体，尝试使用一些常见的中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'FangSong'] + plt.rcParams['font.sans-serif']
            print("尝试使用默认中文字体")

        # 解决负号显示问题
        plt.rcParams['axes.unicode_minus'] = False
    except Exception as e:
        print(f"设置中文字体时出错: {e}")
        print("将使用默认字体，中文可能无法正确显示")

def compute_single_frequency(freq, env_params=None, debug=False):
    """
    计算单个频率的声传播信息

    参数:
        freq: 频率 (Hz)
        env_params: 环境参数字典，如果为None则使用默认环境
        debug: 是否输出调试信息

    返回:
        包含arrivals信息的DataFrame
    """
    # 创建一个唯一的临时文件名基础，避免并行计算时的文件冲突
    fname_base = f"bellhop_temp_{freq}Hz_{uuid.uuid4().hex}"

    # 创建环境或使用提供的环境参数
    if env_params is None:
        env = pm.create_env2d(frequency=freq)
    else:
        env = env_params.copy()
        env['frequency'] = freq

    # 计算arrivals
    start_time = time.time()
    try:
        arrivals = pm.compute_arrivals(env, debug=debug, fname_base=fname_base)
        # 添加频率信息到结果中
        arrivals['frequency'] = freq

        if debug:
            elapsed = time.time() - start_time
            print(f"计算频率 {freq} Hz 完成，耗时: {elapsed:.2f}秒")

        return arrivals
    except Exception as e:
        print(f"计算频率 {freq} Hz 时出错: {str(e)}")
        return None
    finally:
        # 清理可能残留的临时文件
        # 如果为Debug模式则保留
        if not debug:
            for ext in ['.env', '.bty', '.ssp', '.ati', '.sbp', '.prt', '.log', '.arr', '.ray', '.shd']:
                try:
                    os.unlink(fname_base + ext)
                except:
                    pass

def parallel_compute_arrivals(frequencies, env_params=None, max_workers=None, debug=False):
    """
    并行计算多个频率的声传播信息

    参数:
        frequencies: 频率列表 (Hz)
        env_params: 环境参数字典
        max_workers: 最大并行进程数，None表示使用CPU核心数
        debug: 是否输出调试信息

    返回:
        字典，键为频率，值为对应的arrivals DataFrame
    """
    results = {}

    print(f"开始并行计算 {len(frequencies)} 个频率的声传播信息...")
    start_time = time.time()

    # 使用ProcessPoolExecutor进行并行计算
    with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有计算任务
        future_to_freq = {
            executor.submit(compute_single_frequency, freq, env_params, debug): freq
            for freq in frequencies
        }

        # 收集结果
        for future in concurrent.futures.as_completed(future_to_freq):
            freq = future_to_freq[future]
            try:
                arrivals = future.result()
                if arrivals is not None:
                    results[freq] = arrivals
                    print(f"频率 {freq} Hz 计算完成，已完成: {len(results)}/{len(frequencies)}")
            except Exception as e:
                print(f"处理频率 {freq} Hz 的结果时出错: {str(e)}")

    total_time = time.time() - start_time
    print(f"所有计算完成，总耗时: {total_time:.2f}秒")

    return results

def plot_channel_transfer_function(ctf_results, output_dir="results", base_filename="bellhop_arrivals"):
    """
    绘制信道传递函数的幅度和相位

    参数:
        ctf_results: 信道传递函数结果字典
        output_dir: 输出目录
        base_filename: 基础文件名
    """
    if not ctf_results:
        return

    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    frequencies = ctf_results['frequencies']

    # 绘制幅度
    plt.figure(figsize=(10, 6))
    plt.subplot(2, 1, 1)
    plt.plot(frequencies, ctf_results['H_all_magnitude'], 'b-', label='所有到达声线')
    plt.plot(frequencies, ctf_results['H_filtered_magnitude'], 'r--', label='反弹次数≤1的声线')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('幅度')
    plt.title('信道传递函数幅度')
    plt.grid(True)
    plt.legend()

    # 绘制相位
    plt.subplot(2, 1, 2)
    plt.plot(frequencies, ctf_results['H_all_phase'], 'b-', label='所有到达声线')
    plt.plot(frequencies, ctf_results['H_filtered_phase'], 'r--', label='反弹次数≤1的声线')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('相位 (弧度)')
    plt.title('信道传递函数相位')
    plt.grid(True)
    plt.legend()

    plt.tight_layout()

    # 保存图表
    plot_filepath = output_path / f"{base_filename}_channel_transfer_function.png"
    plt.savefig(plot_filepath, dpi=300)
    plt.close()
    print(f"已保存信道传递函数图表到 {plot_filepath}")

def process_channel_transfer_function(results):
    """
    处理arrivals信息，计算信道传递函数及其幅度和相位

    参数:
        results: 计算结果字典，键为频率，值为arrivals DataFrame

    返回:
        字典，包含以下键值对：
        - 'frequencies': 频率数组
        - 'H_all': 所有到达声线的信道传递函数
        - 'H_filtered': 只考虑海底和海面反弹次数都小于等于1的到达声线的信道传递函数
        - 'H_all_magnitude': H_all的幅度
        - 'H_all_phase': H_all的相位（弧度）
        - 'H_filtered_magnitude': H_filtered的幅度
        - 'H_filtered_phase': H_filtered的相位（弧度）
    """
    if not results:
        return None

    # 提取频率并排序
    frequencies = np.array(sorted(results.keys()))

    # 初始化信道传递函数数组
    H_all = np.zeros(len(frequencies), dtype=complex)
    H_filtered = np.zeros(len(frequencies), dtype=complex)

    # 计算每个频率的信道传递函数
    for i, freq in enumerate(frequencies):
        arrivals = results[freq]

        # 1. 所有到达声线的信道传递函数
        H_all[i] = np.sum(arrivals['arrival_amplitude'])

        # 2. 只考虑海底和海面反弹次数都小于等于1的到达声线的信道传递函数
        filtered_arrivals = arrivals[(arrivals['surface_bounces'] <= 1) & (arrivals['bottom_bounces'] <= 1)]
        H_filtered[i] = np.sum(filtered_arrivals['arrival_amplitude'])

    # 3. 计算幅度和相位
    H_all_magnitude = np.abs(H_all)
    H_all_phase = np.angle(H_all)
    H_filtered_magnitude = np.abs(H_filtered)
    H_filtered_phase = np.angle(H_filtered)

    return {
        'frequencies': frequencies,
        'H_all': H_all,
        'H_filtered': H_filtered,
        'H_all_magnitude': H_all_magnitude,
        'H_all_phase': H_all_phase,
        'H_filtered_magnitude': H_filtered_magnitude,
        'H_filtered_phase': H_filtered_phase
    }

def save_results(results, output_dir="results", base_filename="bellhop_arrivals"):
    """
    保存计算结果

    参数:
        results: 计算结果字典，键为频率，值为arrivals DataFrame
        output_dir: 输出目录
        base_filename: 基础文件名
    """
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    # 保存每个频率的结果
    for freq, arrivals in results.items():
        filename = f"{base_filename}_{freq}Hz.csv"
        filepath = output_path / filename
        arrivals.to_csv(filepath)
        print(f"已保存频率 {freq} Hz 的结果到 {filepath}")

    # 合并所有结果并保存
    if results:
        all_arrivals = pd.concat(list(results.values()))
        all_filepath = output_path / f"{base_filename}_all.csv"
        all_arrivals.to_csv(all_filepath)
        print(f"已保存所有频率的合并结果到 {all_filepath}")

        # 处理信道传递函数
        ctf_results = process_channel_transfer_function(results)
        if ctf_results:
            # 保存信道传递函数结果
            ctf_data = pd.DataFrame({
                'frequency': ctf_results['frequencies'],
                'H_all_real': np.real(ctf_results['H_all']),
                'H_all_imag': np.imag(ctf_results['H_all']),
                'H_filtered_real': np.real(ctf_results['H_filtered']),
                'H_filtered_imag': np.imag(ctf_results['H_filtered']),
                'H_all_magnitude': ctf_results['H_all_magnitude'],
                'H_all_phase': ctf_results['H_all_phase'],
                'H_filtered_magnitude': ctf_results['H_filtered_magnitude'],
                'H_filtered_phase': ctf_results['H_filtered_phase']
            })
            ctf_filepath = output_path / f"{base_filename}_channel_transfer_function.csv"
            ctf_data.to_csv(ctf_filepath, index=False)
            print(f"已保存信道传递函数结果到 {ctf_filepath}")

            # 绘制信道传递函数图表
            plot_channel_transfer_function(ctf_results, output_dir, base_filename)

def create_default_env_params():
    """创建默认的环境参数"""
    return {
        'depth': 100,                      # 水深 (m)
        'soundspeed': 1500,                # 声速 (m/s)
        'tx_depth': 50,                    # 发射深度 (m)
        'rx_depth': np.linspace(0, 100, 101),  # 接收深度范围 (m)
        'rx_range': np.linspace(100, 5000, 50),  # 接收距离范围 (m)
        'bottom_soundspeed': 1600,         # 海底声速 (m/s)
        'bottom_density': 1800,            # 海底密度 (kg/m^3)
        'bottom_absorption': 0.5,          # 海底吸收系数 (dB/wavelength)
        'min_angle': -80,                  # 最小发射角度 (度)
        'max_angle': 80,                   # 最大发射角度 (度)
        'nbeams': 0                        # 光线数量 (0表示自动)
    }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='并行计算多个频率的声传播信息')
    parser.add_argument('--freq_start', type=float, default=100, help='起始频率 (Hz)')
    parser.add_argument('--freq_end', type=float, default=1000, help='结束频率 (Hz)')
    parser.add_argument('--freq_step', type=float, default=100, help='频率步长 (Hz)')
    parser.add_argument('--freq_list', type=str, help='频率列表，以逗号分隔，优先于范围参数')
    parser.add_argument('--max_workers', type=int, help='最大并行进程数')
    parser.add_argument('--output_dir', type=str, default='results', help='输出目录')
    parser.add_argument('--debug', action='store_true', help='输出调试信息')
    args = parser.parse_args()

    set_chinese_font()

    # 确定要计算的频率列表
    if args.freq_list:
        frequencies = [float(f) for f in args.freq_list.split(',')]
    else:
        frequencies = np.arange(args.freq_start, args.freq_end + args.freq_step/2, args.freq_step)

    print(f"将计算以下频率的声传播信息: {frequencies} Hz")

    # 创建环境参数
    # env_params = create_default_env_params()

    # 将列表转换为NumPy数组
    ssp = np.array([
        [0.00, 1548.52],
        [200.00, 1530.29],
        [250.00, 1526.69],
        [400.00, 1517.78],
        [600.00, 1509.49],
        [800.00, 1504.30],
        [1000.00, 1501.38],
        [1200.00, 1500.14],
        [1400.00, 1500.12],
        [1600.00, 1501.02],
        [1800.00, 1502.57],
        [2000.00, 1504.62],
        [2200.00, 1507.02],
        [2400.00, 1509.69],
        [2600.00, 1512.55],
        [2800.00, 1515.56],
        [3000.00, 1518.67],
        [3200.00, 1521.85],
        [3400.00, 1525.10],
        [3600.00, 1528.38],
        [3800.00, 1531.70],
        [4000.00, 1535.04]
    ])
    env = pm.create_env2d(
        depth = 4000,
        max_angle = 90,
        min_angle = -90,
        rx_range = 20000,
        rx_depth = 200,
        tx_depth = 60,
        soundspeed = ssp
    )

    # 并行计算
    results = parallel_compute_arrivals(
        frequencies,
        env_params=env,
        max_workers=args.max_workers,
        debug=args.debug
    )

    # 保存结果
    if results:
        save_results(results, output_dir=args.output_dir)
    else:
        print("没有成功计算的结果可保存")

if __name__ == "__main__":
    main()
