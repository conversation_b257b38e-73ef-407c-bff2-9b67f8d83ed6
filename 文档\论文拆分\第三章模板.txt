3   XXX系统的分析
XXXXXXXXXXXXXXX（章导引段）。
3.1 XXX系统的需求描述
该章论述XXX系统的分析建模。篇幅要求：8到12页，最好为10页。
该章应首先描述XXX系统的功能性需求，主要描述业务逻辑。描述业务逻辑时，一方面应给出较详细的文字描述，另一方面推荐使用UML中的活动图，因为它不仅支持并行行为描述，而且提供泳道（swim lane）或格（grid）划分机制以清晰反映机构各部门在业务流程中的职责。描述业务逻辑的活动图的数量要求；由于是全局性描述，一幅活动图即可。
接下来，该章应为XXX系统建立分析模型，包括功能模型、静态模型、动态模型。三种模型的描述手段各不相同，但都由文字段和典型UML图组成。注意：文字段始终是必需的，否则学位论文的易读性将大受影响。
注意！本章禁止使用“模块”字眼，因为它是设计阶段的产出。
3.1.1 XXX
……
3.1.2 XXX
……
3.2 XXX系统的分析模型
在对XXX系统进行分析时，应从三个维度分别入手建立相应的模型。综合面向对象的相关理论和技术，三个维度的分析模型分别为功能模型、静态模型、动态模型。
3.2.1 XXX系统的功能模型
关于分析阶段的功能模型，推荐使用UML中的用例图。注意：不要使用上下文图，因为它是结构化分析的描述手段。与此同时，应给出若干关键用例的内容描述。功能模型的篇幅要求：4至6页足矣！虽然用例的内容描述没有标准书写格式，但是Alistair Cockburn提出了一种很实用的书写格式，推荐使用。
关于用例图的注意事项，请对照图3-1中的用例图示例。
1）用例图必须清晰地体现出用例、参与者和系统边界等三要素，尤其是系统边界不可或缺。而且，系统边界内顶端居中应有系统名称（示例图有此瑕疵）。
2）所有的用例名都必须置于各自的椭圆图符内。
3）参与者（即稻草人图符，亦称角色）与用例（即椭圆图符）之间的连线是不带任何箭头的实线段，并且实线旁没有任何语义标注字眼。
4）用例（即椭圆图符）之间的“包含”关系应使用带开放箭头的虚线段，箭头指向子用例，并且虚线旁应使用置于书名号内的“包含”字眼进行语义标注。
5）用例之间最好不要使用“扩展”关系。除非确有把握，应在存在“扩展”关系的用例之间使用带开放箭头的虚线段，箭头指向基用例（这一点显著异于“包含”关系），并且虚线旁应使用置于书名号内的“扩展”字眼进行语义标注。
6）参与者只能与海级用例（即第一级用例）连接，禁止与任何子用例（统称为鱼级用例）或扩展用例连接。
7）按角色或功能给出各个分解用例图时，其中的参与者名、海级用例名必须与整体用例图中的相应名称一致，系统边界内顶端的系统名称或与整体用例图的一致，或者对应子系统名称。
8）整体用例图中最好不出现子用例或扩展用例，给出所有参与者、所有海级用例、所有参与者与海级用例之间的连接即可。
 
图3 1  用例图示例
需要特别强调的是：与Alistair Cockburn所提出的用例书写格式不同，某些文献所给出的用例描述采用结构较复杂的表格且往往很难规范成三线表，不妨将其处理成图。用例图的数量要求：至少3幅，至多5幅。
……
3.2.2 XXX系统的静态模型
关于分析阶段的静态模型，推荐使用UML中的类图。在构建分析类图时，应尽量给出对象、类、类间关系的抽象过程。必要时，应给出针对问题描述某个片段的类抽象过程描述。注意：分析阶段的类图只能包括问题空间的类和类间关系。
关于类图的注意事项，请对照图3-2中的类图示例。
1）每个类框中均必须有类名。
2）每个类框中可以列出特别重要的属性、操作，亦可以完全没有。如果没有属性与操作，那么类框不需要分为三段，一段即可。如果没有属性但有操作，那么类框必须分为三段，中间的属性段空白即可。如果有属性但无操作，那么类框分为两段即可。
3）类之间的继承关系必须用带大空心箭头的实线表示，大空心箭头指向父类，实线旁不需要任何语义标注。
4）类之间的关联关系必须用实线表示。若需强调单向关联则应带开放箭头，且开放箭头指向被参考或被使用的目标类。
5）类之间的关联关系应有语义标注，即应有关联名或关联端名或重数等。
6）类之间的聚合关系必须用带空心菱形的实线表示，空心菱形靠近组合类，组元类的实线端可以有重数标注。
7）任何类框不应孤悬，即不应与任何其他类均无任何关系。
8）分析类图中，类名、属性名、操作名、关联名、关联端名均应尽量使用中文。
9）类图中可以随意添加注释，所有注释必须置于花括号内。注释可放在专门的注释图符（即右上折角的矩形）内，或者类框跟前，或者类框的属性格子/操作格子里面。
类图不存在数量要求问题，因为任何系统的问题空间类图都只有一个。当然，如果系统非常庞大，导致单个问题空间类图无法全面表达系统的静态模型，此时有必要进行更高层次的类抽象。
还有一个问题需要强调：类图中不要滥用依赖，即带开放箭头的虚线，如图3-3所示。虽然UML的类图能描述各种语义依赖，但是通常应使依赖减至最小程度，因为依赖往往导致棘手的涟漪效应。
如果非要使用类间依赖，那么必须注意如下要求：
1）类间依赖关系必须使用带开放箭头的虚线，且开放箭头指向依赖类。
2）类之间的依赖关系必须有带有书名号“《》”的语义标注，且语义标注关键词必须选自UML规定的依赖语义词典，不能自行命名。
 
图3 2  类图示例
 
图3 3  类图中依赖的示例
……
3.2.3 XXX系统的动态模型
关于分析阶段的动态模型，推荐使用UML中的活动图。活动图适合描述问题空间中多个对象之间的协作与依赖，反映的业务过程包括顺序、分支、循环、并发等执行逻辑。活动图描述的篇幅要求：4页足矣！活动图的数量要求：至少3幅，至多5幅。
关于活动图的注意事项，请对照图3-4、图3-5中的活动图示例。
 
图3 4  活动图示例
 
图3 5  带“泳道”的活动图示例
1）“分叉”（fork）与“汇合”（join）图符均为实心同步棒，必须配对使用。两个同步棒之间存在的多个（至少两个）动作分支流之间必须是并发/并行语义。
2）“决策”（decision）与“合并”（merge）图符均为空心菱形，必须配对使用。
3）“决策”菱形图符之后的所有出流上都必须标注置于西文中括号内的警戒条件，菱形图符旁或内均不需要标注任何条件。
4）任何动作节点均不允许直接有多个入流和/或多个出流，因为语义不明确。应首先使用“合并”菱形图符（对应于条件选择语义）或“汇合”同步棒图符（对应于并发并行语义）进行收敛，然后再连接到后继动作节点。
5）表示GUI的动作选择语义时，只能使用“决策”菱形图符。
6）当强调活动图中不同动作节点究竟由谁执行时应绘制带“泳道”的活动图。但是，“泳道”之间的分割线是不带任何装饰的实线。
7）活动图中所有连线均必须使用带开放箭头的实线。
8）活动图的“开始”与“结束”节点必须使用各自的图符，即实心圆与牛眼图符。
9）活动图周围最好不要画任何边框。
……
3.3 XXX系统的非功能性需求分析
XXX系统的非功能性需求往往也非常重要，甚至左右设计阶段的努力方向，因此其分析绝非可有可无。一般地，非功能性需求涉及各种ility。相应地，不需要使用UML中的有关图进行分析，使用文字段进行分析足矣。
注意：一旦论文包括了XXX系统的的非功能需求分析，那么在设计和测试环节均必须有所呼应。实际上，在设计XXX系统的软件体系结构时，往往必须考虑某些非功能需求，因此有必要彰显这种呼应。另外，如果测试环节需要突出XXX系统的某些性能，往往也会与某些非功能性需求呼应。
建议用编号格式描述不同非功能性需求。
……
3.4 本章小结
