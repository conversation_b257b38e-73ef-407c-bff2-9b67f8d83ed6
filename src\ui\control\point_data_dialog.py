# -*- coding: utf-8 -*-
"""
点数据对话框

用于表格形式查看和编辑频率-噪声级点数据
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QGroupBox, QFormLayout, QDoubleSpinBox, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal


class PointDataDialog(QDialog):
    """
    点数据对话框

    用于表格形式查看和编辑频率-噪声级点数据
    """

    # 自定义信号
    data_changed = pyqtSignal(list, list)  # 数据变更信号，参数为用户点列表和外插点列表

    def __init__(self, parent=None):
        """
        初始化点数据对话框

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 保存主窗口引用
        self.main_window = parent

        # 设置窗口属性
        self.setWindowTitle("频率-噪声级点数据")
        self.setMinimumSize(500, 400)

        # 初始化数据
        self.user_points = []
        self.extrapolated_points = []

        # 创建UI
        self.init_ui()

    def init_ui(self):
        """
        初始化UI
        """
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["频率 (Hz)", "噪声级 (dB)", "类型"])

        # 设置表格属性
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.EditKeyPressed)
        self.table.cellChanged.connect(self.on_cell_changed)

        # 创建添加点组
        add_point_group = QGroupBox("添加新点")
        add_point_layout = QFormLayout()

        # 频率输入框
        self.add_freq_input = QDoubleSpinBox()
        self.add_freq_input.setRange(0.01, 100000)
        self.add_freq_input.setDecimals(2)
        self.add_freq_input.setSuffix(" Hz")
        self.add_freq_input.setValue(100)  # 默认值
        add_point_layout.addRow("频率:", self.add_freq_input)

        # 噪声级输入框
        self.add_level_input = QDoubleSpinBox()
        self.add_level_input.setRange(0, 200)
        self.add_level_input.setDecimals(2)
        self.add_level_input.setSuffix(" dB")
        self.add_level_input.setValue(60)  # 默认值
        add_point_layout.addRow("噪声级:", self.add_level_input)

        # 添加点按钮
        self.add_point_button = QPushButton("添加点")
        self.add_point_button.clicked.connect(self.add_point)
        add_point_layout.addRow("", self.add_point_button)

        # 设置添加点组布局
        add_point_group.setLayout(add_point_layout)

        # 创建表格操作按钮布局
        table_button_layout = QHBoxLayout()

        # 删除选中点按钮
        self.delete_button = QPushButton("删除选中点")
        self.delete_button.clicked.connect(self.delete_selected_point)
        table_button_layout.addWidget(self.delete_button)

        # 创建底部按钮布局
        bottom_button_layout = QHBoxLayout()

        # 创建应用按钮
        self.apply_button = QPushButton("应用")
        self.apply_button.clicked.connect(self.apply_changes)

        # 创建关闭按钮
        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.close)

        # 添加按钮到布局
        bottom_button_layout.addStretch()
        bottom_button_layout.addWidget(self.apply_button)
        bottom_button_layout.addWidget(self.close_button)

        # 添加到主布局
        main_layout.addWidget(self.table)
        main_layout.addLayout(table_button_layout)
        main_layout.addWidget(add_point_group)
        main_layout.addLayout(bottom_button_layout)

    def update_data(self, user_points, extrapolated_points):
        """
        更新表格数据

        Args:
            user_points (list): 用户定义的点列表，每个元素为(freq, level)元组
            extrapolated_points (list): 外插的点列表，每个元素为(freq, level)元组
        """
        # 保存数据
        self.user_points = user_points.copy()
        self.extrapolated_points = extrapolated_points.copy()

        # 断开信号连接，避免触发cellChanged信号
        self.table.cellChanged.disconnect(self.on_cell_changed)

        # 清空表格
        self.table.setRowCount(0)

        # 合并所有点
        all_points = []
        for freq, level in user_points:
            all_points.append((freq, level, "用户定义"))

        for freq, level in extrapolated_points:
            all_points.append((freq, level, "系统外插"))

        # 按频率排序
        all_points.sort(key=lambda x: x[0])

        # 填充表格
        self.table.setRowCount(len(all_points))

        for i, (freq, level, point_type) in enumerate(all_points):
            # 频率
            freq_item = QTableWidgetItem(self.format_value(freq, 2))
            freq_item.setData(Qt.UserRole, freq)
            self.table.setItem(i, 0, freq_item)

            # 噪声级
            level_item = QTableWidgetItem(self.format_value(level, 2))
            level_item.setData(Qt.UserRole, level)
            self.table.setItem(i, 1, level_item)

            # 类型
            type_item = QTableWidgetItem(point_type)
            type_item.setFlags(type_item.flags() & ~Qt.ItemIsEditable)  # 设置为不可编辑
            if point_type == "系统外插":
                type_item.setBackground(Qt.lightGray)
            self.table.setItem(i, 2, type_item)

        # 重新连接信号
        self.table.cellChanged.connect(self.on_cell_changed)

    def format_value(self, value, precision=2):
        """
        格式化数值显示

        Args:
            value (float): 要格式化的数值
            precision (int): 小数位数

        Returns:
            str: 格式化后的字符串
        """
        return f"{value:.{precision}f}"

    def on_cell_changed(self, row, column):
        """
        单元格编辑事件处理

        Args:
            row (int): 行索引
            column (int): 列索引
        """
        # 只处理频率和噪声级列
        if column > 1:
            return

        # 获取类型
        point_type = self.table.item(row, 2).text()

        # 获取编辑后的值
        try:
            new_value = float(self.table.item(row, column).text())

            # 更新单元格显示
            self.table.item(row, column).setText(self.format_value(new_value, 2))
            self.table.item(row, column).setData(Qt.UserRole, new_value)

            # 更新状态栏
            if column == 0:
                self.main_window.statusBar().showMessage(f"已更新频率: {new_value:.2f} Hz")
            else:
                self.main_window.statusBar().showMessage(f"已更新噪声级: {new_value:.2f} dB")

            # 注意：这里不再立即排序，而是等用户点击"应用"按钮时才排序
        except ValueError:
            # 恢复原始值
            if point_type == "用户定义":
                points = self.user_points
            else:
                points = self.extrapolated_points

            # 查找对应的点
            for freq, level in points:
                if (column == 0 and self.table.item(row, 1).data(Qt.UserRole) == level) or \
                   (column == 1 and self.table.item(row, 0).data(Qt.UserRole) == freq):
                    if column == 0:
                        self.table.item(row, column).setText(self.format_value(freq, 2))
                        self.table.item(row, column).setData(Qt.UserRole, freq)
                    else:
                        self.table.item(row, column).setText(self.format_value(level, 2))
                        self.table.item(row, column).setData(Qt.UserRole, level)
                    break

            # 更新状态栏
            self.main_window.statusBar().showMessage("输入无效，请输入数字")

    def add_point(self):
        """
        添加新点
        """
        # 获取频率和噪声级
        freq = self.add_freq_input.value()
        level = self.add_level_input.value()

        # 检查频率是否已存在
        for row in range(self.table.rowCount()):
            if self.table.item(row, 2).text() == "用户定义":
                existing_freq = self.table.item(row, 0).data(Qt.UserRole)
                if abs(existing_freq - freq) < 0.01:  # 允许0.01的误差
                    QMessageBox.warning(self, "添加失败", f"频率 {freq} Hz 已存在，请使用不同的频率")
                    return

        # 断开信号连接，避免触发cellChanged信号
        self.table.cellChanged.disconnect(self.on_cell_changed)

        # 添加新行
        row = self.table.rowCount()
        self.table.insertRow(row)

        # 频率
        freq_item = QTableWidgetItem(self.format_value(freq, 2))
        freq_item.setData(Qt.UserRole, freq)
        self.table.setItem(row, 0, freq_item)

        # 噪声级
        level_item = QTableWidgetItem(self.format_value(level, 2))
        level_item.setData(Qt.UserRole, level)
        self.table.setItem(row, 1, level_item)

        # 类型
        type_item = QTableWidgetItem("用户定义")
        type_item.setFlags(type_item.flags() & ~Qt.ItemIsEditable)  # 设置为不可编辑
        self.table.setItem(row, 2, type_item)

        # 重新连接信号
        self.table.cellChanged.connect(self.on_cell_changed)

        # 更新状态栏
        self.main_window.statusBar().showMessage(f"已添加点: 频率={freq:.2f}Hz, 噪声级={level:.2f}dB")

    def delete_selected_point(self):
        """
        删除选中的点
        """
        # 获取选中的行
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())

        # 如果没有选中行，提示用户
        if not selected_rows:
            QMessageBox.information(self, "删除点", "请先选择要删除的点")
            return

        # 检查是否选中了系统外插点
        for row in selected_rows:
            if self.table.item(row, 2).text() == "系统外插":
                QMessageBox.warning(self, "删除失败", "不能删除系统外插点")
                return

        # 从后向前删除行，避免索引变化
        for row in sorted(selected_rows, reverse=True):
            self.table.removeRow(row)

        # 更新状态栏
        self.main_window.statusBar().showMessage(f"已删除 {len(selected_rows)} 个点")

    def apply_changes(self):
        """
        应用表格中的修改到模型
        """
        # 收集用户定义点和外插点
        new_user_points = []
        new_extrapolated_points = []

        for row in range(self.table.rowCount()):
            freq = self.table.item(row, 0).data(Qt.UserRole)
            level = self.table.item(row, 1).data(Qt.UserRole)
            point_type = self.table.item(row, 2).text()

            if point_type == "用户定义":
                new_user_points.append((freq, level))
            else:
                new_extrapolated_points.append((freq, level))

        # 按频率排序
        new_user_points.sort(key=lambda x: x[0])
        new_extrapolated_points.sort(key=lambda x: x[0])

        # 发送数据变更信号
        self.data_changed.emit(new_user_points, new_extrapolated_points)

        # 更新状态栏
        self.main_window.statusBar().showMessage("已应用修改")

        # 关闭对话框
        self.accept()