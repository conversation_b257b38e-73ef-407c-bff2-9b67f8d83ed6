# -*- coding: utf-8 -*-
"""
阵元位置编辑对话框

用于编辑阵元位置数据，支持垂直或水平线列阵
阵元位置必须满足以下条件之一：
1. 所有阵元具有相同的距离（垂直线列阵）
2. 所有阵元具有相同的深度（水平线列阵）
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout,
    QLabel, QTableWidget, QTableWidgetItem, QHeaderView,
    QPushButton, QFileDialog, QMessageBox
)
from PyQt5.QtCore import Qt
import numpy as np


class ArrayElementsDialog(QDialog):
    """
    阵元位置编辑对话框

    用于编辑阵元位置数据，支持垂直或水平线列阵
    """

    def __init__(self, parent=None, elements_data=None):
        """
        初始化阵元位置编辑对话框

        Args:
            parent: 父窗口
            elements_data: 初始阵元位置数据，格式为[[距离1, 深度1], [距离2, 深度2], ...]
        """
        super().__init__(parent)

        # 保存初始数据
        self.elements_data = elements_data if elements_data is not None else []

        # 设置对话框属性
        self.setWindowTitle("编辑阵元位置")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)

        # 创建UI
        self.init_ui()

        # 加载初始数据
        self.load_data()

    def init_ui(self):
        """
        初始化UI
        """
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 添加说明标签
        info_label = QLabel("请输入阵元位置数据，支持水平线列阵（相同深度）或垂直线列阵（相同距离）")
        info_label.setWordWrap(True)
        main_layout.addWidget(info_label)

        # 创建表格
        self.elements_table = QTableWidget(0, 2)
        self.elements_table.setHorizontalHeaderLabels(["距离 (m)", "深度 (m)"])
        self.elements_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        main_layout.addWidget(self.elements_table)

        # 按钮组
        buttons_layout = QHBoxLayout()

        # 添加行按钮
        self.add_row_button = QPushButton("添加行")
        self.add_row_button.clicked.connect(self.add_row)
        buttons_layout.addWidget(self.add_row_button)

        # 删除行按钮
        self.remove_row_button = QPushButton("删除行")
        self.remove_row_button.clicked.connect(self.remove_row)
        buttons_layout.addWidget(self.remove_row_button)

        # 导入按钮
        self.import_button = QPushButton("导入...")
        self.import_button.clicked.connect(self.import_data)
        buttons_layout.addWidget(self.import_button)

        # 导出按钮
        self.export_button = QPushButton("导出...")
        self.export_button.clicked.connect(self.export_data)
        buttons_layout.addWidget(self.export_button)

        main_layout.addLayout(buttons_layout)

        # 对话框按钮
        dialog_buttons_layout = QHBoxLayout()
        self.ok_button = QPushButton("确定")
        self.ok_button.clicked.connect(self.validate_and_accept)
        dialog_buttons_layout.addWidget(self.ok_button)

        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        dialog_buttons_layout.addWidget(self.cancel_button)

        main_layout.addLayout(dialog_buttons_layout)

        # 无需初始化UI状态

    def load_data(self):
        """
        加载数据到表格
        """
        # 清空表格
        self.elements_table.setRowCount(0)

        # 添加数据到表格
        for i, (range_val, depth_val) in enumerate(self.elements_data):
            self.elements_table.insertRow(i)
            self.elements_table.setItem(i, 0, QTableWidgetItem(str(range_val)))
            self.elements_table.setItem(i, 1, QTableWidgetItem(str(depth_val)))

    def add_row(self):
        """
        添加行
        """
        row = self.elements_table.rowCount()
        self.elements_table.insertRow(row)
        self.elements_table.setItem(row, 0, QTableWidgetItem("0"))
        self.elements_table.setItem(row, 1, QTableWidgetItem("0"))

    def remove_row(self):
        """
        删除选中的行
        """
        selected_rows = set()
        for item in self.elements_table.selectedItems():
            selected_rows.add(item.row())

        # 从后向前删除，避免索引变化
        for row in sorted(selected_rows, reverse=True):
            self.elements_table.removeRow(row)

    def import_data(self):
        """
        导入数据
        """
        file_path, _ = QFileDialog.getOpenFileName(self, "导入阵元位置", "", "文本文件 (*.txt);;CSV文件 (*.csv);;所有文件 (*)")
        if not file_path:
            return

        try:
            # 读取文件
            data = np.loadtxt(file_path)

            # 检查数据格式
            if data.ndim != 2 or data.shape[1] != 2:
                raise ValueError("数据格式错误，应为两列数据：距离和深度")

            # 清空表格
            self.elements_table.setRowCount(0)

            # 添加数据到表格
            for i in range(data.shape[0]):
                self.elements_table.insertRow(i)
                self.elements_table.setItem(i, 0, QTableWidgetItem(str(data[i, 0])))
                self.elements_table.setItem(i, 1, QTableWidgetItem(str(data[i, 1])))

            QMessageBox.information(self, "导入成功", f"成功导入阵元位置数据: {data.shape[0]}个点")

        except Exception as e:
            QMessageBox.warning(self, "导入失败", f"导入阵元位置数据失败: {e}")

    def export_data(self):
        """
        导出数据
        """
        file_path, _ = QFileDialog.getSaveFileName(self, "导出阵元位置", "", "文本文件 (*.txt);;CSV文件 (*.csv);;所有文件 (*)")
        if not file_path:
            return

        try:
            # 获取数据
            data = self.get_data()

            # 保存到文件
            np.savetxt(file_path, data, fmt='%.6f', delimiter='\t', header='Range(m)\tDepth(m)', comments='')

            QMessageBox.information(self, "导出成功", f"成功导出阵元位置数据: {len(data)}个点")

        except Exception as e:
            QMessageBox.warning(self, "导出失败", f"导出阵元位置数据失败: {e}")

    def get_data(self):
        """
        获取表格中的数据

        Returns:
            list: 阵元位置数据，格式为[[距离1, 深度1], [距离2, 深度2], ...]
        """
        data = []
        for row in range(self.elements_table.rowCount()):
            try:
                range_val = float(self.elements_table.item(row, 0).text())
                depth_val = float(self.elements_table.item(row, 1).text())
                data.append([range_val, depth_val])
            except (ValueError, AttributeError):
                # 跳过无效的行
                continue

        # 按距离排序
        data.sort(key=lambda x: (x[0], x[1]))
        return data

    def validate_and_accept(self):
        """
        验证数据并接受对话框

        验证阵元位置是否满足水平线列阵或垂直线列阵的要求
        """
        data = self.get_data()

        # 检查是否有数据
        if not data:
            QMessageBox.warning(self, "验证失败", "请至少添加一个阵元位置")
            return

        # 检查是否为水平线列阵或垂直线列阵
        ranges = [item[0] for item in data]
        depths = [item[1] for item in data]

        # 检查是否所有距离相同（垂直线列阵）
        all_same_range = len(set(ranges)) == 1

        # 检查是否所有深度相同（水平线列阵）
        all_same_depth = len(set(depths)) == 1

        if not (all_same_range or all_same_depth):
            QMessageBox.warning(
                self,
                "验证失败",
                "阵元位置必须满足以下条件之一：\n"
                "1. 所有阵元具有相同的距离（垂直线列阵）\n"
                "2. 所有阵元具有相同的深度（水平线列阵）"
            )
            return

        # 验证通过，接受对话框
        self.accept()
