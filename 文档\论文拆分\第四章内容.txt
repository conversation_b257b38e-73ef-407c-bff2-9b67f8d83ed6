4   面向复杂水声环境的噪声仿真系统的设计

基于第三章的需求分析和系统建模，本章将详细阐述面向复杂水声环境的噪声仿真系统的设计方案。设计阶段是连接需求分析与系统实现的关键桥梁，需要在满足功能性需求的基础上，充分考虑系统的可维护性、可扩展性等非功能性需求，形成指导后续实现的技术方案。

4.1 面向复杂水声环境的噪声仿真系统的概要设计
在软件系统的概要设计阶段，将对面向复杂水声环境的噪声仿真系统的软件体系结构和功能模块结构进行分析设计，结合系统的功能需求遴选合适的体系结构风格并采用树形结构来描述各个功能模块之间的层次关系。

4.1.1 面向复杂水声环境的噪声仿真系统的软件体系结构
为构建一个高效、易用且具备良好发展潜力的面向复杂水声环境的噪声仿真系统，本系统在软件体系结构设计上采用了经典的分层结构，并将职责明确地划分到不同的逻辑层次。这种分层设计不仅有助于降低系统复杂度，也为满足关键的性能及可维护性与可扩展性等非功能性需求提供了结构化支持。系统的核心层次及其职责如下：
1）表示层
该层作为系统与用户交互的直接窗口，全面负责用户界面的展示和用户输入的捕获。利用PyQT框架，表示层构建了包括参数配置面板和可视化区域两个主要的用户界面元素。其设计核心在于提供直观的用户操作体验，并确保界面对用户操作的即时响应，这直接关系到系统的良好的性能目标的实现。
2）业务逻辑层
此层是连接表示层与模型层的关键枢纽，核心职责在于处理用户请求并控制复杂的仿真业务流程。它主要由一个主仿真控制器及若干负责特定领域仿真任务的控制器构成。这些控制器接收来自表示层的用户指令，调度模型层提供的服务来执行具体的计算任务。基于PyQT的信号与槽机制，同时加入异步处理机制，将仿真计算放在后台线程中进行，确保了后台计算任务与前台界面操作的有效分离，避免阻塞用户界面，保障用户交互的顺畅体验。同时，其清晰的流程控制逻辑也为系统的可维护性奠定了基础。
3）模型层
模型层构成了系统的核心基础，封装了所有与仿真相关的算法实现。它包含了执行具体声学计算，如各类噪声信号的生成、声波传播模拟、声信号特性计算等的核心算法。一个单例的数据管理器在此层中负责对仿真过程中的各类数据进行统一存储和管理，并在数据状态发生变化时通知其他相关层。模型层的设计注重计算的准确性和算法的模块化，这使得未来对算法进行更新或集成新的物理模型更为便捷，从而有力地支持了系统的可维护性与可扩展性。

4.1.2 面向复杂水声环境的噪声仿真系统的功能模块结构
在系统的功能模块结构设计上，本文基于对系统业务单元的划分，将系统分为船舶辐射噪声仿真模块、海洋环境噪声仿真模块、声传播环境仿真模块、综合声场仿真模块以及项目管理与信号导出模块这五个核心功能模块。仿真系统的功能模块结构图如图4-1所示，其中，各个模块的具体功能划分与设计如下：
1）船舶辐射噪声仿真模块
该模块负责模拟船舶在不同工况下的水下辐射噪声特性。其主要功能包括：接收用户输入的工况参数以估算连续谱参数，或用户直接定义的连续谱、线谱及调制谱参数，基于内置的物理模型以及经验公式，计算并生成船舶辐射噪声的时域信号及其频谱特性。模块的输出是可供后续分析或直接导出的船舶辐射噪声信号。
2）海洋环境噪声仿真模块
该模块专注于模拟海洋环境背景噪声。该模块基于经典的Wenz海洋环境噪声曲线构建背景图谱，结合PyQt图形界面框架与Matplotlib绘图引擎，允许用户在画布上通过键鼠交互实时绘制自定义的海洋环境噪声频谱曲线，也支持参数输入及文件导入的方式。模块的核心功能是根据用户配置生成自定义频谱的噪声时域信号。
3）声传播环境仿真模块
此模块允许用户定义复杂的海底地形、海水声速剖面等声传播环境参数，利用成熟的声线追踪海洋声传播模型Bellhop，计算得到不同环境参数下的声线轨迹、传播损失以及声线到达结构等数据，通过可视化的形式展现给用户。同时，此模块还支持用户配置阵列几何以及计算所需的频率列表，通过并行计算的方式生成信道数据，即不同频率下各接收点的声线到达结构。这些信道数据是综合声场仿真的重要输入。
4）综合声场仿真模块
该模块是系统进行多因素综合仿真的核心。它整合了前述模块的输出，如船舶辐射噪声信号、可选的海洋环境噪声信号以及声传播环境仿真模块生成的信道数据。其主要功能包括：模拟声源信号通过已建模的复杂海洋信道传播至接收阵列的过程，得到各阵元的接收信号。在此基础上，模块还提供进一步的信号处理功能，计算谱级、指向性以及互功率谱等生成特性。
5）项目管理与信号导出模块
项目管理功能支持仿真项目的创建、保存和加载操作，能够将用户当前的全部仿真配置参数以及关键的仿真结果持久化存储，并在需要时重新载入。信号导出功能提供了将仿真生成的各类时域信号导出为标准格式文件（如WAV、MAT、NPZ等）的能力，使得用户可以方便地将本系统的仿真结果用于自定义处理或与其他工具集成。

图 4 1  系统功能模块结构图
4.2 面向复杂水声环境的噪声仿真系统的详细设计
面向复杂水声环境的噪声仿真系统的详细设计，是在概要设计的基础上，综合算计控件的理论和技术，对分析阶段得出的模型的完善，系统的整体类图如图4-2所示。此外，对动态模型的进一步分析表现在对对象之间传递消息的过程，以及行为的时间顺序等方面进行具体规划与研究，表现对象之间的交互。

图 4 2  系统的整体设计类图
4.2.1 船舶辐射噪声仿真模块的详细设计
船舶辐射噪声仿真模块是本系统的核心模块之一，主要操作是根据用户设定的参数模拟生成船舶在航行时产生的辐射噪声信号，操作的顺序图如图4-3所示。该模块采用逻辑与界面分离、任务异步处理的设计思路，旨在提升系统的响应性能与用户体验。

图 4 3  船舶辐射噪声仿真操作顺序图
模块由多个关键组件协同构成。其中，船舶辐射噪声仿真面板作为用户交互界面，负责参数输入与结果展示；控制器承担流程控制与逻辑协调的任务；计算服务封装了核心建模算法，负责执行噪声信号的合成与频谱计算；数据管理器则作为全局数据存储和状态通知的中心，保障各组件间的数据一致性。
当用户完成参数配置并启动仿真后，仿真面板通过异步信号将请求发送至控制器，避免因后台计算阻塞界面响应。控制器在一个独立线程中运行，从数据管理器获取当前任务所需参数，并调用计算服务进行仿真处理。计算服务根据用户输入的连续谱、线谱、调制谱参数以及采样率、信号时长等信息，生成对应的时域信号与频谱数据。完成后，结果返回控制器，并提交至数据管理器统一存储。
数据管理器在更新数据后，通过发射PyQT信号广播数据变更通知。仿真面板作为订阅者，在接收到通知后主动拉取最新数据，并触发图形界面的更新操作，从而实现仿真结果的实时展示。
整个模块通过职责解耦与异步执行机制，有效提升了系统的稳定性与交互体验。基于数据管理器也确保了各功能单元在复杂交互下的数据一致性。此设计不仅结构清晰、易于维护，也为后续的功能扩展与算法优化提供了良好的基础。

4.2.2 海洋环境噪声仿真模块的详细设计
海洋环境噪声仿真模块负责生成符合用户定义频谱特性的海洋背景噪声信号。该模块在架构设计上采用与船舶辐射噪声仿真模块一致的分层结构，包括用户界面面板、控制器、计算服务和数据管理器等组件，确保了系统架构的统一性和可维护性。

模块的设计重点在于解决海洋环境噪声的灵活建模问题。传统的海洋噪声建模往往基于固定的经验公式，难以适应复杂多变的海洋环境。本模块设计了基于Wenz曲线的交互式频谱定义机制，允许用户在经典噪声模型的基础上进行自定义调整。界面设计采用图形化的频谱编辑器，用户通过点选方式定义关键频率点，系统负责自动插值生成完整的频谱曲线。这种设计在保持科学性的同时，提供了充分的灵活性。

该模块的核心算法设计基于频域滤波的噪声合成方法，其设计流程如图4-4所示。算法设计需要解决三个关键问题：如何根据目标频谱设计合适的滤波器、如何生成具有理想统计特性的噪声源、如何确保输出信号的频谱特性与设计目标一致。算法采用FIR滤波器设计以保证相位线性，选择高斯白噪声作为激励源以满足随机性要求，并设计了谱级校准机制以确保频谱精度。这一算法设计不仅适用于海洋环境噪声仿真，也为船舶辐射噪声模块的信号合成提供了统一的技术基础。

图 4 4  海洋环境噪声仿真核心算法设计流程图

在模块接口设计上，计算服务类封装了完整的噪声生成流程，对外提供简洁的调用接口，隐藏了滤波器设计和信号处理的复杂性。模块与数据管理器的集成设计确保了仿真结果的统一存储和跨模块访问，为后续的综合声场仿真提供了可靠的数据源。模块还预留了扩展接口，支持未来集成更多的海洋噪声模型。


4.2.3 声传播环境仿真模块的详细设计
声传播环境仿真模块承担着复杂海洋环境中声波传播特性建模的重要职责。该模块的设计面临的核心挑战是如何在保证计算精度的前提下，实现复杂环境参数的灵活配置和大规模计算任务的高效调度。模块采用了基于成熟声学引擎封装的设计策略，通过PropagationModel类构建了完整的声传播计算框架。

模块的设计重点在于解决三个关键问题：环境参数的标准化建模、计算任务的并行化调度、以及计算结果的结构化存储。在环境建模方面，模块设计了统一的参数接口，支持声速剖面、海底地形、海底参数等多种环境要素的灵活配置。在计算调度方面，模块设计了多层次的并行计算架构，支持频率维度和空间维度的并行分解。在数据管理方面，模块设计了标准化的信道数据格式，确保计算结果的可重用性和可扩展性。

该模块的核心功能是信道数据制备，其设计的操作流程如图4-5所示。与前述噪声仿真模块相比，声传播仿真模块的设计特点在于计算密集性和数据密集性。模块需要为多个阵元位置和多个频率点计算声线到达结构，生成的信道数据需要支持后续的宽带信号传播建模。因此，模块设计了基于文件的数据存储机制，既保证了数据的持久化，又支持了大规模数据的高效访问。

图 4 5  生成信道数据操作顺序图

在架构设计上，模块采用了分层的计算框架设计。底层封装了BELLHOP声学引擎的调用接口，中间层实现了参数转换和结果解析的逻辑，上层提供了面向应用的计算服务接口。这种分层设计不仅隐藏了底层计算的复杂性，也为未来集成其他声学引擎提供了扩展性。模块还设计了完善的错误处理和进度反馈机制，确保长时间计算任务的可靠性和用户体验。

4.2.4 综合声场仿真模块的详细设计
综合声场仿真模块是整个系统的核心集成模块，承担着多源数据融合和复杂声场建模的重要职责。该模块的设计挑战在于如何有效整合来自不同模块的异构数据，实现声源信号通过复杂海洋信道传播至接收阵列的完整建模过程。模块需要处理的核心设计问题包括：多源数据的同步协调、宽带信号的信道建模、以及阵列信号的特性分析。

模块的设计架构以阵列信号生成算法为核心，辅以延迟求和波束成形算法进行信号特性分析。阵列信号生成算法是本模块最重要的创新算法，负责实现声源信号通过复杂海洋信道传播至接收阵列的完整建模过程。该算法通过文献调研和实践探索，解决了宽带信号与频率相关信道的高效卷积问题，是系统的核心技术贡献。延迟求和波束成形算法作为经典的阵列信号处理方法，主要用于生成信号的方向性分析和验证。

阵列信号生成的设计流程如图4-6所示。该操作的设计复杂性在于需要协调多个异构数据源：源信号数据、信道响应数据、以及可选的背景噪声数据。模块设计了统一的数据接口和同步机制，确保不同来源数据的时间对齐和幅度一致性。同时，模块还设计了灵活的信号合成策略，支持有无背景噪声的不同仿真场景。

图 4 6  生成阵列信号操作顺序图

阵列信号生成算法是本系统的核心创新算法，其设计面临的主要挑战是如何在保证计算精度的前提下，实现宽带信号与频率相关信道的高效卷积建模。传统的时域卷积方法在处理大规模阵列和长时间信号时计算复杂度过高，而简单的频域处理又难以准确处理信道的频率相关特性。本算法通过深入的理论分析和实践验证，设计了基于子带分解的频域卷积方法。

算法的核心设计思想如图4-8所示，将宽带源信号按频率分解为多个子带，为每个子带独立计算对应频率的信道冲激响应，然后通过优化的频域乘法实现卷积运算，最后将各子带结果进行相干合成。这种设计不仅解决了宽带信号建模的精度问题，还通过FFT算法显著提高了计算效率。算法还创新性地解决了时间轴对齐、相位一致性保持等关键技术问题，确保了多阵元信号的物理正确性。

图 4 8  阵列信号生成算法设计流程图

延迟求和波束成形算法的设计流程如图4-7所示。作为经典的阵列信号处理算法，该算法主要用于验证生成的阵列信号的正确性和进行方向性分析。算法设计支持多种时延补偿策略和加权方法，为用户提供了灵活的分析工具。

图 4 7  延迟求和波束成形算法设计流程图

在模块的整体架构设计上，采用了分层的服务架构。底层提供基础的信号处理服务，中间层实现核心的算法逻辑，上层提供面向应用的分析功能。模块还设计了完善的缓存机制和并行计算支持，以应对大规模阵列信号处理的性能需求。此外，模块预留了丰富的扩展接口，支持未来集成更多的阵列信号处理算法。


4.2.5 项目管理与信号导出模块的详细设计
项目管理与信号导出模块承担着系统数据持久化和结果输出的重要职责。该模块的设计重点在于解决复杂仿真系统中的数据管理和用户交互问题，虽然不涉及复杂的物理建模算法，但其设计质量直接影响系统的实用性和用户体验。模块的设计围绕两个核心功能展开：项目生命周期管理和多格式数据导出。

项目管理功能的设计核心是实现仿真项目的完整生命周期管理。模块设计了ProjectController类作为项目管理的控制中心，负责协调项目创建、保存、加载等操作。项目保存操作的设计流程如图4-9所示。该操作的设计挑战在于如何保证复杂数据结构的完整性和一致性。模块需要处理来自多个仿真模块的异构数据，包括参数配置、信号数据、分析结果等。为了平衡存储效率和访问性能，模块设计了混合存储策略：结构化参数数据采用JSON格式以保证可读性和可编辑性，大规模数组数据采用NPZ格式以实现高效压缩和快速加载。

图 4 9  项目保存操作顺序图

信号导出功能的设计目标是为用户提供灵活的数据输出能力。模块设计了ExportController类实现多格式信号导出功能，支持WAV、NPZ、MAT等标准格式。导出功能的设计需要考虑不同用户群体的需求差异：音频处理用户偏好WAV格式，Python用户偏好NPZ格式，MATLAB用户偏好MAT格式。对于多阵元信号的导出，模块设计了分文件存储策略，通过标准化的文件命名规则确保数据的可追溯性和组织性。

模块设计的核心组件是数据管理器的状态管理机制，其设计的状态转换逻辑如图4-10所示。数据管理器作为系统的核心数据中枢，其设计需要确保数据操作的安全性和一致性。模块设计了完整的状态机模型，定义了初始化、就绪、参数设置、结果存储、数据查询、项目保存、项目加载等关键状态。每个状态之间的转换都有明确的触发条件和执行动作，这种设计不仅保证了数据操作的原子性，也为系统的错误处理和恢复提供了可靠的机制。

图 4 10  数据管理器状态转换图

在模块的架构设计上，采用了事件驱动的设计模式。模块通过信号-槽机制实现组件间的松耦合通信，通过观察者模式实现数据变更的自动通知。模块还设计了完善的错误处理和进度反馈机制，确保长时间操作的用户体验。此外，模块预留了扩展接口，支持未来集成更多的数据格式和存储策略。


4.3 本章小结

本章从概要设计和详细设计两个层面全面阐述了面向复杂水声环境的噪声仿真系统的设计方案。在概要设计阶段，确定了基于分层架构的软件体系结构，将系统划分为表示层、业务逻辑层和模型层三个逻辑层次，明确了各层的职责分工和交互机制。同时，基于业务功能的划分，构建了包含五个核心功能模块的系统架构，为复杂水声环境仿真提供了完整的解决方案。

在详细设计阶段，本章深入分析了各功能模块的内部结构和关键算法。通过设计类图展现了系统的静态结构，通过顺序图描述了关键操作的动态交互过程，通过活动图和状态机图阐述了核心算法的处理流程和状态转换逻辑。船舶辐射噪声仿真模块实现了基于物理模型的噪声信号生成；海洋环境噪声仿真模块提供了基于Wenz曲线的交互式频谱定义和FIR滤波器的噪声合成算法；声传播环境仿真模块封装了BELLHOP引擎并实现了并行化的信道数据生成；综合声场仿真模块设计了创新的基于子带分解的频域卷积阵列信号生成算法，这是系统的核心技术贡献，同时集成了经典的延迟求和波束成形算法用于信号分析验证；项目管理与信号导出模块提供了完整的数据持久化和多格式导出功能。

设计方案充分考虑了系统的功能性需求和非功能性需求，采用了成熟的设计模式和架构风格，为系统的实现提供了清晰的技术路线。分层架构保证了系统的可维护性，模块化设计提供了良好的可扩展性，异步处理机制确保了系统的响应性，集中式数据管理保证了数据的一致性。这些设计决策为后续的系统实现和测试奠定了坚实的基础。
