# 声传播分析方法

## 1. 引言

本文档描述了水下声传播模拟及其信号处理方法，特别关注如何正确处理传播后信号的分析。

### 1.1 背景

水下声传播是水声学中的核心问题，对于水下通信、目标探测、环境监测等领域具有重要意义。在实际应用中，我们需要模拟声波在复杂海洋环境中的传播过程，并分析接收信号的特性。

### 1.2 挑战

声传播模拟面临以下主要挑战：

1. **多径传播**：声波在水中可以通过多条路径从发射点传播到接收点，导致接收信号具有复杂的时间结构
2. **频率依赖性**：不同频率的声波具有不同的传播特性，高频声波衰减更快
3. **时延差异**：不同路径的声波到达时间可能相差很大，特别是在深海远距离传播中
4. **信号处理**：如何从传播后的信号中提取有用信息，特别是确定合适的分析时间窗口

### 1.3 文档目标

本文档旨在提供一套系统的方法，用于：

1. 模拟声波在水中的传播过程
2. 计算传播后的接收信号
3. 确定信号分析的最佳时间窗口（稳态区域）
4. 计算接收信号的功率谱密度
5. 分析传输损失等声学特性

通过这些方法，我们可以更准确地模拟和分析水下声传播过程，为水声学研究和应用提供支持。

## 2. 声传播模拟基本流程

声传播模拟的基本流程包括以下几个步骤：

### 2.1 环境建模

首先需要建立水声环境模型，包括：

1. **水深**：水体的深度，可以是常数或随距离变化的函数
2. **声速剖面**：声速随深度的变化，通常由实测数据提供
3. **海底参数**：包括海底声速、密度、吸收系数等
4. **海面参数**：通常考虑海面的粗糙度和反射特性

在实际应用中，我们通常使用arlpy.uwapm等工具包来创建环境模型：

```python
import arlpy.uwapm as pm

# 创建声学环境
env = pm.create_env2d(
    depth=4000,                  # 水深(m)
    soundspeed=ssp,              # 声速剖面
    bottom_soundspeed=1600,      # 海底声速(m/s)
    bottom_density=1800,         # 海底密度(kg/m³)
    bottom_absorption=0.5,       # 海底吸收系数(dB/wavelength)
    tx_depth=60,                 # 发射深度(m)
    rx_depth=200,                # 接收深度(m)
    rx_range=20000,              # 接收距离(m)
    frequency=2500               # 频率(Hz)
)
```

### 2.2 声线追踪

基于建立的环境模型，使用声线理论计算声波的传播路径：

1. **发射角度**：从声源发射不同角度的声线
2. **路径追踪**：根据声线方程计算每条声线的传播路径
3. **到达信息**：记录每条到达接收点的声线的信息，包括到达时间、振幅、角度等

```python
# 计算到达信息
arrivals = pm.compute_arrivals(env)
```

### 2.3 冲击响应生成

基于到达信息，生成系统的冲击响应：

```python
# 转换为冲击响应
ir = pm.arrivals_to_impulse_response(arrivals, fs)
```

### 2.4 信号卷积

将源信号与冲击响应进行卷积，得到接收信号：

```python
# 进行卷积
output_signal = signal.fftconvolve(source_signal, ir, mode='full')
```

### 2.5 信号分析

对接收信号进行分析，包括：

1. **时域分析**：观察信号的时间结构
2. **频域分析**：计算功率谱密度
3. **传输损失**：计算声传播的能量损失

```python
# 计算功率谱密度
freqs, psd = signal.welch(output_signal, fs=fs)

# 计算传输损失
tl_db = 10 * np.log10(input_power / output_power)
```

## 3. 冲击响应计算

冲击响应是描述声传播系统特性的关键，它表示单位冲击输入后系统的响应。在声传播中，冲击响应包含了所有传播路径的信息。

### 3.1 到达信息计算

使用声线模型计算到达信息是生成冲击响应的第一步：

```python
def compute_impulse_response(env, fs, debug=False, abs_time=False):
    """
    计算冲击响应

    参数:
        env: 声学环境
        fs: 采样率 (Hz)
        debug: 是否输出调试信息
        abs_time: 是否使用绝对时间(True)或相对时间(False)

    返回:
        ir: 冲击响应
        arrivals: 到达信息
        t0: 冲击响应的起始时间(秒)
    """
    # 计算到达信息
    arrivals = pm.compute_arrivals(env, debug=debug)

    # 记录最早到达时间，用于后续时间轴计算
    t0 = 0 if abs_time else min(arrivals.time_of_arrival)

    # 转换为冲击响应
    ir = pm.arrivals_to_impulse_response(arrivals, fs, abs_time=abs_time)

    return ir, arrivals, t0
```

### 3.2 abs_time参数的选择

`arrivals_to_impulse_response`函数中的`abs_time`参数有两种选择：

#### 3.2.1 abs_time=False（默认）

- 冲击响应的起始时间将被设置为最早到达时间
- 冲击响应长度 = (最晚到达时间 - 最早到达时间) * fs + 1
- 优点：冲击响应更紧凑，只包含有效的到达信息
- 适用场景：当关注相对时间结构时

#### 3.2.2 abs_time=True

- 冲击响应的起始时间将被设置为0（发射时刻）
- 冲击响应长度 = 最晚到达时间 * fs + 1
- 优点：保留了绝对时间信息，便于多次模拟结果比较
- 适用场景：当需要考虑绝对传播时延时

### 3.3 到达信息分析

到达信息包含了每条声线的详细信息，可以用于分析声传播特性：

```python
# 打印到达信息
if arrivals is not None:
    print(f"到达路径数量: {len(arrivals)}")
    print(f"最早到达时间: {min(arrivals.time_of_arrival):.4f} 秒")
    print(f"最晚到达时间: {max(arrivals.time_of_arrival):.4f} 秒")

    # 打印前5条到达路径信息
    print("\n前5条到达路径信息:")
    print(arrivals.head(5)[['time_of_arrival', 'arrival_amplitude',
                           'angle_of_departure', 'angle_of_arrival',
                           'surface_bounces', 'bottom_bounces']])
```

### 3.4 冲击响应的频率依赖性

冲击响应具有强烈的频率依赖性，不同频率的声波传播特性差异很大：

1. **高频声波**：
   - 吸收损失大
   - 散射效应明显
   - 不同路径之间的能量差异可能达到50dB以上

2. **低频声波**：
   - 吸收损失小
   - 传播距离远
   - 不同路径之间的能量差异较小

在实际应用中，需要根据关注的频率范围选择合适的模拟频率。

## 4. 信号卷积处理

信号卷积是声传播模拟的核心步骤，它将源信号与冲击响应结合，生成接收信号。

### 4.1 卷积原理

卷积是一种数学运算，表示两个函数相互"混合"的程度。在声传播中，卷积描述了声传播系统（用冲击响应表示）对输入信号的影响。

对于离散信号，卷积公式为：

(f * g)[n] = Σ f[m] · g[n - m]

其中，f是输入信号，g是冲击响应，*表示卷积操作。

### 4.2 卷积模式选择

在使用`scipy.signal.fftconvolve`函数进行卷积时，有三种模式可选：

#### 4.2.1 'full'模式（默认）

- 返回完整的卷积结果，长度为 len(f) + len(g) - 1
- 包含所有可能的重叠点
- 适用场景：当需要保留完整的时间信息时

```python
# 使用'full'模式进行卷积
output_full = signal.fftconvolve(source_signal, ir, mode='full')
```

#### 4.2.2 'same'模式

- 返回与输入信号（第一个参数）相同长度的卷积结果
- 卷积结果会被截断或填充，使其长度与输入信号相同
- 适用场景：当需要保持信号处理前后的时间对齐时

```python
# 使用'same'模式进行卷积
output_same = signal.fftconvolve(source_signal, ir, mode='same')
```

#### 4.2.3 'valid'模式

- 只返回完全重叠的部分，长度为 max(len(f), len(g)) - min(len(f), len(g)) + 1
- 不包含任何边缘效应
- 适用场景：当只关注完全卷积的部分时

```python
# 使用'valid'模式进行卷积
output_valid = signal.fftconvolve(source_signal, ir, mode='valid')
```

### 4.3 源信号长度要求

在声传播模拟中，源信号长度是一个关键参数，它需要满足一定的要求：

#### 4.3.1 最小长度要求

源信号长度至少应该等于最大传播时延差：

```
最小源信号长度 = 最大到达时间 - 最早到达时间
```

如果源信号长度小于传播时延差，会导致接收信号中出现不连续的片段，无法形成稳态区域。

#### 4.3.2 推荐长度

为了获得更可靠的结果，建议使用以下长度：

1. **最小安全长度**：传播时延差 + 1秒
2. **推荐标准长度**：2 × 传播时延差
3. **保守长度**（用于高精度分析）：3 × 传播时延差

```python
# 计算传播时延差
t_min = min(arrivals.time_of_arrival)
t_max = max(arrivals.time_of_arrival)
delay_diff = t_max - t_min

# 检查源信号长度是否足够
if source_duration < delay_diff:
    print(f"警告: 源信号长度({source_duration:.2f}s)小于传播时延差({delay_diff:.2f}s)")
    print(f"建议增加源信号长度至少至{delay_diff+1:.2f}s")
```

### 4.4 源信号扩展方法

如果现有源信号长度不足，可以通过以下方法扩展：

#### 4.4.1 循环重复法

```python
# 计算需要的重复次数
num_repeats = int(np.ceil(required_duration / original_duration))

# 循环重复源信号
extended_source = np.tile(original_source, num_repeats)
extended_source = extended_source[:int(required_duration * fs)]
```

#### 4.4.2 生成新信号法

```python
# 生成新的、更长的源信号
extended_source = generate_source_signal(fs, required_duration)
```

### 4.5 时间轴处理

在卷积后，需要正确处理时间轴，特别是当使用相对时间模式时：

```python
# 创建输出信号的时间轴
t_output = np.arange(len(output_full)) / fs + t0
```

其中，t0是冲击响应的起始时间，通常是最早到达时间（当abs_time=False时）。

## 5. 稳态区域确定

稳态区域是接收信号中最适合进行频谱分析的时间段，它代表了系统达到稳定状态后的响应。

### 5.1 稳态区域的定义

稳态区域的正确定义是**所有传播路径都有贡献的时间段**：

1. **起始时间(t_steady_start)**：最晚路径开始到达的时间
   ```
   t_steady_start = t_max  # 最晚到达时间
   ```

2. **结束时间(t_steady_end)**：最早路径停止贡献的时间
   ```
   t_steady_end = t_min + source_duration  # 最早路径完全到达的时间
   ```

3. **有效条件**：只有当 t_steady_start < t_steady_end 时，稳态区域才存在

### 5.2 图解说明

下面是稳态区域的图解说明：

```
时间轴:  |-------|-------|-------|-------|-------|-------|
         t_min            t_max           t_min+source   t_max+source
         (最早到达)       (最晚到达)      (最早路径结束)  (最晚路径结束)

路径1:    [======源信号传播======>]
路径2:            [======源信号传播======>]
路径3:                     [======源信号传播======>]

稳态区域:                  [=====]
                          t_max  t_min+source
```

### 5.3 稳态区域的代码实现

```python
def determine_steady_state_region(arrivals, source_duration, fs, output_full, t0):
    """
    确定接收信号的有效稳态区域

    参数:
        arrivals: 到达信息
        source_duration: 源信号持续时间(秒)
        fs: 采样率(Hz)
        output_full: 完整的卷积输出信号
        t0: 输出信号的起始时间

    返回:
        steady_signal: 稳态区域信号
        t_steady: 稳态区域的时间轴
    """
    # 最早和最晚到达时间
    t_min = min(arrivals.time_of_arrival)
    t_max = max(arrivals.time_of_arrival)

    # 稳态区域的起始时间: 最晚路径开始到达的时间
    t_steady_start = t_max

    # 稳态区域的结束时间: 最早路径停止贡献的时间
    t_steady_end = t_min + source_duration

    # 检查稳态区域是否存在
    if t_steady_start < t_steady_end:
        print(f"有效稳态区域: {t_steady_start:.4f}s - {t_steady_end:.4f}s (持续{t_steady_end-t_steady_start:.4f}s)")

        # 转换为样本索引
        idx_start = int((t_steady_start - t0) * fs)
        idx_end = int((t_steady_end - t0) * fs)

        # 确保索引在有效范围内
        idx_start = max(0, idx_start)
        idx_end = min(len(output_full), idx_end)

        # 提取稳态区域信号
        steady_signal = output_full[idx_start:idx_end]
        t_steady = np.arange(len(steady_signal)) / fs + t_steady_start

        return steady_signal, t_steady
    else:
        print(f"警告: 无有效稳态区域! 最晚到达({t_max:.4f}s)晚于最早路径结束({t_min+source_duration:.4f}s)")
        print(f"建议增加源信号长度至少至 {t_max-t_min+1:.4f}s")

        # 返回完整信号作为后备
        return output_full, np.arange(len(output_full)) / fs + t0
```

### 5.4 稳态区域长度要求

稳态区域的长度应该足够长，以确保可靠的频谱分析：

1. **最小长度**：至少0.5秒
2. **频率相关长度**：至少包含信号最低频率周期的10倍
   - 如果最低频率是10 Hz，则需要至少1秒
   - 如果最低频率是1 Hz，则需要至少10秒

```python
# 检查稳态区域长度是否足够
steady_duration = t_steady_end - t_steady_start
min_duration = 10 / min_freq  # 最低频率10个周期

if steady_duration < min_duration:
    print(f"警告: 稳态区域({steady_duration:.4f}s)短于建议的最小长度({min_duration:.4f}s)")
```

### 5.5 基于能量分布的稳态区域确定

除了基于路径的方法外，还可以使用基于能量分布的方法确定稳态区域：

```python
def determine_optimal_steady_state_region(output_full, fs, t0, energy_threshold=0.9):
    """
    基于能量分布确定最优稳态区域

    参数:
        output_full: 完整的卷积输出信号
        fs: 采样率(Hz)
        t0: 输出信号的起始时间
        energy_threshold: 能量阈值比例(0-1)，默认0.9表示包含90%的能量

    返回:
        steady_signal: 稳态区域信号
        t_steady: 稳态区域的时间轴
    """
    # 计算信号的累积能量分布
    energy = np.abs(output_full)**2
    cumulative_energy = np.cumsum(energy)
    total_energy = cumulative_energy[-1]
    normalized_energy = cumulative_energy / total_energy

    # 找到包含指定比例能量的区间
    start_idx = np.argmax(normalized_energy >= (1 - energy_threshold) / 2)
    end_idx = np.argmax(normalized_energy >= (1 + energy_threshold) / 2)

    # 确保区间长度合理
    min_samples = int(fs * 0.5)  # 至少0.5秒
    if end_idx - start_idx < min_samples:
        center = (start_idx + end_idx) // 2
        half_width = min_samples // 2
        start_idx = max(0, center - half_width)
        end_idx = min(len(output_full) - 1, center + half_width)

    # 提取稳态区域信号
    steady_signal = output_full[start_idx:end_idx]
    t_steady = np.arange(len(steady_signal)) / fs + t0 + start_idx / fs

    print(f"确定的稳态区域: {t_steady[0]:.4f}s - {t_steady[-1]:.4f}s (持续{len(steady_signal)/fs:.4f}s)")
    print(f"该区域包含信号总能量的约{energy_threshold*100:.1f}%")

    return steady_signal, t_steady
```

这种方法的优势在于：
1. 不依赖于特定频率的显著路径定义
2. 直接基于接收信号的能量分布确定稳态区域
3. 适用于任何类型的宽带信号
4. 计算简单高效

## 6. 功率谱密度计算

功率谱密度(PSD)是描述信号功率如何分布在不同频率上的重要指标，对于声传播分析具有重要意义。

### 6.1 功率谱密度的定义

功率谱密度表示信号在单位频率带宽内的功率，单位通常为W/Hz或dB/Hz。对于离散信号，PSD可以通过信号的傅里叶变换计算：

PSD(f) = |X(f)|²/T

其中，X(f)是信号x(t)的傅里叶变换，T是信号的持续时间。

### 6.2 Welch方法

在实际应用中，通常使用Welch方法计算PSD，它通过分段平均提高了估计的可靠性：

```python
def calculate_psd_with_averaging(signal, fs, nperseg=None, noverlap=None, nfft=None):
    """
    使用Welch方法计算功率谱密度

    参数:
        signal: 输入信号
        fs: 采样率
        nperseg: 每段长度，默认为fs(1秒)
        noverlap: 重叠样本数，默认为nperseg//2
        nfft: FFT长度，默认为nperseg

    返回:
        freqs: 频率数组
        psd: 功率谱密度
    """
    if nperseg is None:
        nperseg = fs  # 默认使用1秒长度的段

    if noverlap is None:
        noverlap = nperseg // 2  # 默认50%重叠

    # 使用Welch方法计算PSD
    freqs, psd = signal.welch(signal, fs=fs, nperseg=nperseg,
                             noverlap=noverlap, nfft=nfft,
                             scaling='density', average='mean')

    return freqs, psd
```

### 6.3 参数选择

Welch方法的参数选择对PSD估计的质量有重要影响：

#### 6.3.1 nperseg（段长度）

- 较大的nperseg提供更高的频率分辨率，但统计可靠性降低
- 较小的nperseg提供更高的统计可靠性，但频率分辨率降低
- 建议值：fs（即1秒数据）或信号长度的1/4，取较小值

#### 6.3.2 noverlap（重叠长度）

- 增加重叠可以提高统计可靠性
- 建议值：nperseg的50%

#### 6.3.3 nfft（FFT长度）

- 增加nfft可以提高频率分辨率，但不会增加统计可靠性
- 建议值：与nperseg相同，或为获得更平滑的曲线而增大

### 6.4 频率分辨率

频率分辨率是PSD分析中的重要参数，它决定了能够分辨的最小频率差：

```
频率分辨率 = fs / nperseg
```

例如，如果fs=44100 Hz，nperseg=44100，则频率分辨率为1 Hz。

### 6.5 使用稳态区域计算PSD

为了获得最准确的PSD估计，应该使用稳态区域的信号：

```python
# 确定稳态区域
steady_signal, t_steady = determine_steady_state_region(
    arrivals, source_duration, fs, output_full, t0)

# 计算PSD
freqs, psd = calculate_psd_with_averaging(
    steady_signal, fs, nperseg=min(len(steady_signal), fs))

# 转换为dB
psd_db = 10 * np.log10(psd + 1e-10)  # 添加小值避免log(0)

# 绘制PSD
plt.figure(figsize=(10, 6))
plt.semilogx(freqs, psd_db)
plt.xlabel('频率 (Hz)')
plt.ylabel('PSD (dB/Hz)')
plt.grid(True)
plt.xlim([10, fs/2])
plt.title('接收信号功率谱密度')
```

### 6.6 传输损失计算

通过比较输入信号和输出信号的功率，可以计算传输损失：

```python
# 计算输入信号功率
input_power = np.mean(np.abs(source_signal)**2)

# 计算输出信号功率(使用稳态区域)
output_power = np.mean(np.abs(steady_signal)**2)

# 计算传输损失
tl_db = 10 * np.log10(input_power / output_power)
print(f"传输损失: {tl_db:.2f} dB")
```

### 6.7 频率相关的传输损失

对于宽带信号，可以计算频率相关的传输损失：

```python
# 计算输入信号的PSD
freqs_in, psd_in = calculate_psd_with_averaging(source_signal, fs)

# 计算输出信号的PSD
freqs_out, psd_out = calculate_psd_with_averaging(steady_signal, fs)

# 确保频率点相同
if len(freqs_in) != len(freqs_out) or not np.allclose(freqs_in, freqs_out):
    # 需要插值到相同的频率点
    psd_out_interp = np.interp(freqs_in, freqs_out, psd_out)
else:
    psd_out_interp = psd_out

# 计算频率相关的传输损失
tl_freq = 10 * np.log10((psd_in + 1e-10) / (psd_out_interp + 1e-10))

# 绘制频率相关的传输损失
plt.figure(figsize=(10, 6))
plt.semilogx(freqs_in, tl_freq)
plt.xlabel('频率 (Hz)')
plt.ylabel('传输损失 (dB)')
plt.grid(True)
plt.xlim([10, fs/2])
plt.title('频率相关的传输损失')
```

## 7. 实际应用案例

以下是一个完整的声传播模拟实例，展示了从环境建模到信号分析的全过程。

### 7.1 环境设置

```python
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import arlpy.uwapm as pm
import time
import os
import tempfile

# 参数设置
fs = 44100                # 采样率 (Hz)
duration = 5.0            # 信号持续时间 (s)
center_freq = 2500        # 中心频率 (Hz)
tx_depth = 60             # 发射深度 (m)
rx_depth = 200            # 接收深度 (m)
rx_range = 20000          # 接收距离 (m)

# 声速剖面
ssp = np.array([
    [0.00, 1548.52],
    [200.00, 1530.29],
    [250.00, 1526.69],
    [400.00, 1517.78],
    [600.00, 1509.49],
    [800.00, 1504.30],
    [1000.00, 1501.38],
    [1200.00, 1500.14],
    [1400.00, 1500.12],
    [1600.00, 1501.02],
    [1800.00, 1502.57],
    [2000.00, 1504.62],
    [2200.00, 1507.02],
    [2400.00, 1509.69],
    [2600.00, 1512.55],
    [2800.00, 1515.56],
    [3000.00, 1518.67],
    [3200.00, 1521.85],
    [3400.00, 1525.10],
    [3600.00, 1528.38],
    [3800.00, 1531.70],
    [4000.00, 1535.04]
])

# 创建声学环境
env = pm.create_env2d(
    depth = 4000,
    max_angle = 90,
    min_angle = -90,
    rx_range = rx_range,
    rx_depth = rx_depth,
    tx_depth = tx_depth,
    soundspeed = ssp,
    frequency = center_freq
)
```

### 7.2 源信号生成

```python
# 创建白噪声信号
def create_white_noise(fs, duration):
    t = np.arange(0, duration, 1/fs)
    x = np.random.randn(len(t))
    # 归一化
    x = x / np.sqrt(np.mean(x**2))
    return t, x

# 生成白噪声信号
t_source, source_signal = create_white_noise(fs, duration)
```

### 7.3 冲击响应计算

```python
# 计算冲击响应
ir, arrivals, t0 = compute_impulse_response(env, fs, debug=True, abs_time=False)

# 打印到达信息
print(f"到达路径数量: {len(arrivals)}")
print(f"最早到达时间: {min(arrivals.time_of_arrival):.4f} 秒")
print(f"最晚到达时间: {max(arrivals.time_of_arrival):.4f} 秒")
print(f"冲击响应长度: {len(ir)} 样本 ({len(ir)/fs:.4f} 秒)")
```

### 7.4 信号卷积

```python
# 检查源信号长度是否足够
t_min = min(arrivals.time_of_arrival)
t_max = max(arrivals.time_of_arrival)
delay_diff = t_max - t_min

if duration < delay_diff:
    print(f"警告: 源信号长度({duration:.2f}s)小于传播时延差({delay_diff:.2f}s)")
    # 扩展源信号
    num_repeats = int(np.ceil(delay_diff / duration)) + 1
    extended_source = np.tile(source_signal, num_repeats)
    extended_source = extended_source[:int((delay_diff + 1) * fs)]
    source_signal = extended_source
    duration = len(source_signal) / fs
    print(f"源信号已扩展至 {duration:.2f}s")

# 进行卷积
output_full = signal.fftconvolve(source_signal, ir, mode='full')
t_output = np.arange(len(output_full)) / fs + t0
```

### 7.5 稳态区域确定

```python
# 确定稳态区域
steady_signal, t_steady = determine_steady_state_region(
    arrivals, duration, fs, output_full, t0)

# 打印稳态区域信息
print(f"稳态区域: {t_steady[0]:.4f}s - {t_steady[-1]:.4f}s (持续{len(steady_signal)/fs:.4f}s)")
```

### 7.6 功率谱密度计算

```python
# 计算PSD
freqs, psd = calculate_psd_with_averaging(
    steady_signal, fs, nperseg=min(len(steady_signal), fs))

# 转换为dB
psd_db = 10 * np.log10(psd + 1e-10)

# 计算传输损失
input_power = np.mean(np.abs(source_signal)**2)
output_power = np.mean(np.abs(steady_signal)**2)
tl_db = 10 * np.log10(input_power / output_power)
print(f"传输损失: {tl_db:.2f} dB")
```

### 7.7 结果可视化

```python
# 绘制结果
plt.figure(figsize=(12, 12))

# 冲击响应
plt.subplot(3, 2, 1)
t_ir = np.arange(len(ir)) / fs + t0
plt.plot(t_ir, np.real(ir))
plt.title('冲击响应 (实部)')
plt.xlabel('时间 (s)')
plt.ylabel('幅度')
plt.grid(True)

plt.subplot(3, 2, 2)
plt.plot(t_ir, np.abs(ir))
plt.title('冲击响应 (幅度)')
plt.xlabel('时间 (s)')
plt.ylabel('幅度')
plt.grid(True)

# 源信号和接收信号
plt.subplot(3, 2, 3)
plt.plot(t_source, source_signal)
plt.title('源信号')
plt.xlabel('时间 (s)')
plt.ylabel('幅度')
plt.grid(True)

plt.subplot(3, 2, 4)
plt.plot(t_output, output_full, 'b', alpha=0.5, label='完整信号')
plt.plot(t_steady, steady_signal, 'r', label='稳态区域')
plt.title('接收信号')
plt.xlabel('时间 (s)')
plt.ylabel('幅度')
plt.legend()
plt.grid(True)

# 功率谱密度
plt.subplot(3, 2, 5)
plt.semilogx(freqs, psd_db)
plt.title(f'接收信号PSD (TL: {tl_db:.2f} dB)')
plt.xlabel('频率 (Hz)')
plt.ylabel('PSD (dB/Hz)')
plt.grid(True)
plt.xlim([10, fs/2])

plt.tight_layout()
plt.show()
```

## 8. 参考文献

1. Jensen, F. B., Kuperman, W. A., Porter, M. B., & Schmidt, H. (2011). Computational ocean acoustics. Springer Science & Business Media.

2. Etter, P. C. (2018). Underwater acoustic modeling and simulation. CRC press.

3. Porter, M. B. (2011). The BELLHOP manual and user's guide: Preliminary draft. Heat, Light, and Sound Research, Inc., La Jolla, CA, USA, Tech. Rep.

4. Stojanovic, M., & Preisig, J. (2009). Underwater acoustic communication channels: Propagation models and statistical characterization. IEEE communications magazine, 47(1), 84-89.

5. Oppenheim, A. V., & Schafer, R. W. (2014). Discrete-time signal processing. Pearson Education.

6. Welch, P. (1967). The use of fast Fourier transform for the estimation of power spectra: a method based on time averaging over short, modified periodograms. IEEE Transactions on audio and electroacoustics, 15(2), 70-73.

7. Hodgkiss, W. S., & Preisig, J. C. (2012). Kauai acomms MURI 2011 (KAM11) experiment. In Proceedings of the 11th European Conference on Underwater Acoustics (pp. 34-41).

8. Urick, R. J. (1983). Principles of underwater sound. McGraw-Hill.
