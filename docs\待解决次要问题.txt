1. 信道数据选择阵元会导致时域波形中展示的阵元变化，区分清楚几个阵元选择分别对应的是哪张图的修改，现在改哪个频谱图都没有改变！checked
2. 海洋环境噪声直接点击仿真会提示没有用户选择的点，预期应该是点击仿真后使用用户点外插，而不是一定要先外插才能点击开始仿真
3. 现在发现fftconvolve的性能要远优于lfilter，可以考虑在两个噪声生成模块都使用fftconvolve。
4. UI卡顿的问题要解决，一方面是要确保UI更新和耗时操作分离，另一方面可以考虑如何在UI更新时引入类似网页加载的加载中的界面、骨架。
5. 计算频谱图一点击应用就会未响应一段时间。这是什么原因？我看后台计算频谱实际上耗时很短，UI为什么会如此卡顿？同时计算全部的频谱的耗时也很短。checked
6. 信号区间选择的时间超出了信号实际时间时，可以自动赋值到信号实际时间边界。checked

    综合仿真模块的小问题：首先关于信号区间选择，其起始时间（小数点后四位）有可能小于实际的全局最早到达时间，导致运算时报错超出信号边界。我的想法是当用户
选择的时间超出区间时，自动以实际信号的第一个或最后一个索引处理。其次是这个信号区间选择的控件，应该和频谱分析以及指向性计算这两个group同级，因为这是
所有阵列分析处理的基础。然后现在的信号区间选择group内有频率相关的设置，这是不需要的。现有的频率相关的设置是为指向性计算服务的，所以放在指向性计算的
group内即可。接着是可以把指向性计算的group放置到频谱分析的group下面，保持顺序。checked
最后，1. 我们需要对波束成形进行并行化计算优化，因为现在的时域补偿方法虽然精确，但需要比较长的时间计算。2. 只有多阵元时才能进行指向性计算等需要阵列参与的计算。