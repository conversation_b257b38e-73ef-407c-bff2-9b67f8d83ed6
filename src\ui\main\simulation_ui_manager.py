# -*- coding: utf-8 -*-
"""
仿真UI管理器

负责处理仿真相关的UI操作，如开始、停止仿真等。
"""

from PyQt5.QtWidgets import QMessageBox


class SimulationUIManager:
    """
    仿真UI管理器

    负责处理仿真相关的UI操作，如开始、停止仿真等。
    """

    def __init__(self, main_window, simulation_controller):
        """
        初始化仿真UI管理器

        Args:
            main_window: 主窗口实例
            simulation_controller: 仿真控制器实例
        """
        self.main_window = main_window
        self.simulation_controller = simulation_controller
        self.data_manager = simulation_controller.get_data_manager()

    def on_start_simulation(self):
        """
        开始仿真事件处理
        """
        # 获取当前标签页索引
        current_tab_index = self.main_window.control_panel.tabs.currentIndex()

        # 根据当前标签页触发相应的仿真
        if current_tab_index == 0:  # 船舶辐射噪声
            # 手动触发船舶辐射噪声标签页的仿真请求
            self.main_window.ship_noise_tab.on_simulate_clicked()
        elif current_tab_index == 1:  # 海洋环境噪声
            # 手动触发海洋环境噪声标签页的仿真请求
            self.main_window.ambient_noise_tab.on_simulate_clicked()
        elif current_tab_index == 2:  # 声传播环境
            # 手动触发声传播环境标签页的仿真请求
            self.main_window.propagation_tab.on_simulate_clicked()
        elif current_tab_index == 3:  # 综合仿真
            # 手动触发综合仿真标签页的处理信号请求
            self.main_window.integrated_tab.on_process_signal_clicked()

    def on_stop_simulation(self):
        """
        停止仿真事件处理
        """
        # 获取当前标签页索引
        current_tab_index = self.main_window.control_panel.tabs.currentIndex()

        # 根据当前标签页停止相应的仿真
        if current_tab_index == 0:  # 船舶辐射噪声
            self.simulation_controller.cancel_ship_noise_simulation()
            self.main_window.statusBar().showMessage("船舶辐射噪声仿真已停止")
        elif current_tab_index == 1:  # 海洋环境噪声
            self.simulation_controller.cancel_ambient_noise_simulation()
            self.main_window.statusBar().showMessage("海洋环境噪声仿真已停止")
        elif current_tab_index == 2:  # 声传播环境
            self.simulation_controller.cancel_propagation_simulation()
            self.main_window.statusBar().showMessage("声传播环境仿真已停止")
        elif current_tab_index == 3:  # 综合仿真
            self.simulation_controller.cancel_integrated_simulation()
            self.main_window.statusBar().showMessage("综合仿真已停止")

    def on_simulation_started(self, module):
        """
        仿真开始信号处理

        Args:
            module (str): 模块名称
        """
        # 更新状态栏
        if module == 'ship_noise':
            self.main_window.statusBar().showMessage("船舶辐射噪声仿真开始...")
        elif module == 'ambient_noise':
            self.main_window.statusBar().showMessage("海洋环境噪声仿真开始...")
        elif module == 'propagation':
            self.main_window.statusBar().showMessage("声传播仿真开始...")

            # 更新宽带计算按钮状态
            if hasattr(self.main_window, 'propagation_tab'):
                self.main_window.propagation_tab.progress_bar.setValue(0)
                self.main_window.propagation_tab.status_label.setText("计算中...")
        elif module == 'integrated':
            self.main_window.statusBar().showMessage("综合仿真开始...")

    def on_simulation_progress(self, module, progress):
        """
        仿真进度信号处理

        Args:
            module (str): 模块名称
            progress (int): 进度百分比
        """
        # 更新状态栏
        if module == 'ship_noise':
            self.main_window.statusBar().showMessage(f"船舶辐射噪声仿真进度: {progress}%")
        elif module == 'ambient_noise':
            self.main_window.statusBar().showMessage(f"海洋环境噪声仿真进度: {progress}%")
        elif module == 'propagation':
            self.main_window.statusBar().showMessage(f"声传播仿真进度: {progress}%")

            # 更新进度条
            if hasattr(self.main_window, 'propagation_tab'):
                self.main_window.propagation_tab.progress_bar.setValue(progress)
        elif module == 'integrated':
            self.main_window.statusBar().showMessage(f"综合仿真进度: {progress}%")

    def on_simulation_completed(self, module):
        """
        仿真完成信号处理

        Args:
            module (str): 模块名称
        """
        # 更新状态栏
        if module == 'ship_noise':
            self.main_window.statusBar().showMessage("船舶辐射噪声仿真完成")
        elif module == 'ambient_noise':
            self.main_window.statusBar().showMessage("海洋环境噪声仿真完成")
        elif module == 'propagation':
            self.main_window.statusBar().showMessage("声传播仿真完成")
        elif module == 'integrated':
            self.main_window.statusBar().showMessage("综合仿真完成")

            # 获取当前计算类型
            if self.data_manager:
                params = self.data_manager.get_parameters('integrated')
                computation_type = params.get('computation_type', '')

                # 更新信道数据信息
                if computation_type == 'channel_data_analysis' and hasattr(self.main_window, 'integrated_tab'):
                    self.main_window.integrated_tab.update_channel_data_info()

            # 获取当前计算类型
            if self.data_manager:
                params = self.data_manager.get_parameters('propagation')
                computation_type = params.get('computation_type', '')

                # 更新UI状态
                if hasattr(self.main_window, 'propagation_tab'):
                    if computation_type == 'channel_preparation':
                        # 信道数据制备完成
                        self.main_window.propagation_tab.start_channel_button.setEnabled(True)
                        self.main_window.propagation_tab.stop_channel_button.setEnabled(False)

                        # 获取结果信息
                        results = self.data_manager.get_results('propagation')
                        if 'channel_preparation' in results:
                            channel_data = results['channel_preparation']
                            output_folder = channel_data.get('output_folder', '')
                            if output_folder:
                                self.main_window.propagation_tab.status_label.setText(f"计算完成，数据已保存至: {output_folder}")
                            else:
                                self.main_window.propagation_tab.status_label.setText("计算完成")
                        else:
                            self.main_window.propagation_tab.status_label.setText("计算完成")
                    else:
                        # 其他计算类型完成
                        self.main_window.propagation_tab.status_label.setText("计算完成")

    def on_simulation_error(self, module, error):
        """
        仿真错误信号处理

        Args:
            module (str): 模块名称
            error (str): 错误信息
        """
        # 显示错误对话框
        if module == 'ship_noise':
            QMessageBox.critical(self.main_window, "仿真错误", f"船舶辐射噪声仿真过程中发生错误: {error}")
            self.main_window.statusBar().showMessage("船舶辐射噪声仿真失败")
        elif module == 'ambient_noise':
            QMessageBox.critical(self.main_window, "仿真错误", f"海洋环境噪声仿真过程中发生错误: {error}")
            self.main_window.statusBar().showMessage("海洋环境噪声仿真失败")
        elif module == 'propagation':
            QMessageBox.critical(self.main_window, "仿真错误", f"声传播仿真过程中发生错误: {error}")
            self.main_window.statusBar().showMessage("声传播仿真失败")
        elif module == 'integrated':
            QMessageBox.critical(self.main_window, "仿真错误", f"综合仿真过程中发生错误: {error}")
            self.main_window.statusBar().showMessage("综合仿真失败")

            # 获取当前计算类型
            if self.data_manager:
                params = self.data_manager.get_parameters('propagation')
                computation_type = params.get('computation_type', '')

                # 更新UI状态
                if hasattr(self.main_window, 'propagation_tab'):
                    if computation_type == 'channel_preparation':
                        # 重置信道数据制备按钮状态
                        self.main_window.propagation_tab.start_channel_button.setEnabled(True)
                        self.main_window.propagation_tab.stop_channel_button.setEnabled(False)
                        self.main_window.propagation_tab.status_label.setText("仿真失败")
                    else:
                        # 其他计算类型失败
                        self.main_window.propagation_tab.status_label.setText("仿真失败")

    def on_ship_noise_simulation(self):
        """
        处理船舶辐射噪声仿真请求

        注意：参数已经通过UI控件直接更新到DataManager，不再需要通过信号传递
        """
        try:
            # 启动船舶辐射噪声仿真
            self.simulation_controller.simulate_ship_noise()

        except Exception as e:
            # 异常处理
            QMessageBox.critical(self.main_window, "仿真错误", f"船舶辐射噪声仿真过程中发生错误: {str(e)}")
            self.main_window.statusBar().showMessage("船舶辐射噪声仿真失败")

    def on_ambient_noise_simulation(self):
        """
        处理海洋环境噪声仿真请求

        注意：参数已经通过UI控件直接更新到DataManager，不再需要通过信号传递
        """
        try:
            # 启动海洋环境噪声仿真
            self.simulation_controller.simulate_ambient_noise()

        except Exception as e:
            # 异常处理
            QMessageBox.critical(self.main_window, "仿真错误", f"海洋环境噪声仿真过程中发生错误: {str(e)}")
            self.main_window.statusBar().showMessage("海洋环境噪声仿真失败")

    def on_propagation_simulation(self, computation_type='environment'):
        """
        处理声传播仿真请求

        Args:
            computation_type (str): 计算类型，默认为'environment'

        注意：参数已经通过UI控件直接更新到DataManager，不再需要通过信号传递
        """
        try:
            # 如果是更新传播损失显示范围，不需要重新计算
            if computation_type == 'update_tl_range':
                # 获取当前的传播损失结果
                results = self.data_manager.get_results('propagation')
                if 'transmission_loss' not in results:
                    # 如果没有传播损失结果，显示错误消息
                    QMessageBox.warning(self.main_window, "错误", "没有传播损失结果可供显示")
                    return

                # 发送数据变更信号，触发视图更新
                self.data_manager.results_changed.emit('propagation')
                return

            # 启动声传播仿真
            self.simulation_controller.simulate_propagation()

        except Exception as e:
            # 异常处理
            QMessageBox.critical(self.main_window, "仿真错误", f"声传播仿真过程中发生错误: {str(e)}")
            self.main_window.statusBar().showMessage("声传播仿真失败")

    def on_integrated_simulation(self, computation_type='channel_data_analysis'):
        """
        处理综合仿真请求

        Args:
            computation_type (str): 计算类型，默认为'channel_data_analysis'

        注意：参数已经通过UI控件直接更新到DataManager，不再需要通过信号传递
        """
        try:
            # 启动综合仿真
            self.simulation_controller.simulate_integrated(computation_type)

        except Exception as e:
            # 异常处理
            QMessageBox.critical(self.main_window, "仿真错误", f"综合仿真过程中发生错误: {str(e)}")
            self.main_window.statusBar().showMessage("综合仿真失败")
