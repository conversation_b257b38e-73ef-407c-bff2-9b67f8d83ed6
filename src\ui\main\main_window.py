# -*- coding: utf-8 -*-
"""
水声噪声仿真系统主窗口

提供应用程序的主界面，包括菜单栏、工具栏、状态栏和主布局
主布局采用左右分割的方式，左侧为视图区域，右侧为控制面板
"""

import os
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QSplitter, QMessageBox,
    QFileDialog, QHBoxLayout
)
from PyQt5.QtCore import Qt, QSettings

# 导入核心组件
from src.core.data.simulation_data_manager import SimulationDataManager
from src.core.project.project_manager import ProjectManager
from src.core.simulation.simulation_controller import SimulationController
from src.core.view_updaters.ship_noise_view_updater import ShipNoiseViewUpdater
from src.core.view_updaters.ambient_noise_view_updater import AmbientNoiseViewUpdater
from src.core.view_updaters.propagation_view_updater import PropagationViewUpdater
from src.core.view_updaters.integrated_view_updater import IntegratedViewUpdater

# 导入UI管理器
from src.ui.main.project_ui_manager import ProjectUIManager
from src.ui.main.simulation_ui_manager import SimulationUIManager
from src.ui.main.menu_manager import MenuManager

# 导入视图区域和控制面板
from src.ui.view.view_area import ViewArea
from src.ui.control.control_panel import ControlPanel

# 导入具体的视图
from src.ui.view.ship_noise_view import ShipNoiseView
from src.ui.view.ambient_noise_view import AmbientNoiseView
from src.ui.view.propagation_view import PropagationView
from src.ui.view.integrated_view import IntegratedView

# 导入具体的控制面板标签页
from src.ui.control.ship_noise_tab import ShipNoiseTab
from src.ui.control.ambient_noise_tab import AmbientNoiseTab
from src.ui.control.propagation_tab import PropagationTab
from src.ui.control.integrated_tab import IntegratedTab


class MainWindow(QMainWindow):
    """
    水声噪声仿真系统主窗口

    提供应用程序的主界面，包括菜单栏、工具栏、状态栏和主布局
    主布局采用左右分割的方式，左侧为视图区域，右侧为控制面板
    """

    def __init__(self):
        """
        初始化主窗口
        """
        super().__init__()

        # 设置窗口标题和大小
        self.setWindowTitle("面向复杂水声环境的噪声仿真系统")
        self.resize(1200, 800)

        # 创建数据管理器
        self.data_manager = SimulationDataManager()

        # 添加CPU核心数到全局参数
        import multiprocessing
        self.data_manager.set_global_param('cpu_cores', multiprocessing.cpu_count())

        # 创建项目管理器
        self.project_manager = ProjectManager(self.data_manager)

        # 创建仿真控制器
        self.simulation_controller = SimulationController(self.data_manager)

        # 创建UI管理器
        self.project_ui_manager = ProjectUIManager(self, self.project_manager, self.data_manager)
        self.simulation_ui_manager = SimulationUIManager(self, self.simulation_controller)

        # 初始化UI组件
        self.init_ui()

        # 创建菜单管理器
        self.menu_manager = MenuManager(self, self.project_ui_manager, self.simulation_ui_manager)

        # 连接仿真控制器信号
        self.simulation_controller.simulation_started.connect(self.simulation_ui_manager.on_simulation_started)
        self.simulation_controller.simulation_progress.connect(self.simulation_ui_manager.on_simulation_progress)
        self.simulation_controller.simulation_completed.connect(self.simulation_ui_manager.on_simulation_completed)
        self.simulation_controller.simulation_error.connect(self.simulation_ui_manager.on_simulation_error)

        # 连接数据管理器的仿真状态变更信号
        self.data_manager.simulation_state_changed.connect(self.update_ui_lock_state)

        # 注意：声传播控制器现在由SimulationController管理，不再需要在MainWindow中创建

        # 创建视图更新器
        self.ship_noise_view_updater = ShipNoiseViewUpdater(self.data_manager, self.ship_noise_view)
        self.ambient_noise_view_updater = AmbientNoiseViewUpdater(self.data_manager, self.ambient_noise_view)
        self.propagation_view_updater = PropagationViewUpdater(self.data_manager, self.propagation_view)
        self.integrated_view_updater = IntegratedViewUpdater(self.integrated_view, self.data_manager)

        # 加载设置
        self.load_settings()

        # 显示就绪状态
        self.statusBar().showMessage("就绪")

        # 初始化窗口标题
        self.project_ui_manager.on_modification_state_changed(False)

    def init_ui(self):
        """
        初始化UI组件
        """
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # 创建分割器
        self.splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(self.splitter)

        # 创建视图区域
        self.view_area = ViewArea()
        self.splitter.addWidget(self.view_area)

        # 创建控制面板
        self.control_panel = ControlPanel()
        self.splitter.addWidget(self.control_panel)

        # 设置分割器初始大小
        self.splitter.setSizes([int(self.width() * 0.7), int(self.width() * 0.3)])

        # 初始化视图
        self.init_views()

        # 初始化控制面板标签页
        self.init_control_tabs()

        # 初始化完成后将UI控件中的默认参数同步到数据管理器，并且要重置修改状态
        self.sync_ui_parameters_to_data_manager()
        self.project_manager.reset_modified_state()

        # 连接控制面板标签页切换信号
        self.control_panel.tab_changed.connect(self.on_tab_changed)

    def init_views(self):
        """
        初始化视图
        """
        # 创建船舶辐射噪声视图
        self.ship_noise_view = ShipNoiseView()
        self.view_area.stack.addWidget(self.ship_noise_view)

        # 创建海洋环境噪声视图
        self.ambient_noise_view = AmbientNoiseView()
        self.view_area.stack.addWidget(self.ambient_noise_view)

        # 创建声传播环境视图
        self.propagation_view = PropagationView()
        self.view_area.stack.addWidget(self.propagation_view)

        # 创建综合仿真视图
        self.integrated_view = IntegratedView()
        self.view_area.stack.addWidget(self.integrated_view)

    def init_control_tabs(self):
        """
        初始化控制面板标签页
        """
        # 创建船舶辐射噪声标签页
        self.ship_noise_tab = ShipNoiseTab(data_manager=self.data_manager)
        self.control_panel.tabs.addTab(self.ship_noise_tab, "船舶辐射噪声")

        # 连接船舶辐射噪声标签页的信号
        self.ship_noise_tab.simulation_requested.connect(self.simulation_ui_manager.on_ship_noise_simulation)

        # 创建海洋环境噪声标签页
        self.ambient_noise_tab = AmbientNoiseTab(main_window=self, data_manager=self.data_manager)
        self.control_panel.tabs.addTab(self.ambient_noise_tab, "海洋环境噪声")

        # 连接海洋环境噪声标签页的信号
        self.ambient_noise_tab.simulation_requested.connect(self.simulation_ui_manager.on_ambient_noise_simulation)

        # 连接海洋环境噪声视图的信号
        self.ambient_noise_view.point_added.connect(self.ambient_noise_tab.on_point_added)
        self.ambient_noise_view.point_selected.connect(self.ambient_noise_tab.on_point_selected)
        self.ambient_noise_view.point_updated.connect(self.ambient_noise_tab.on_point_updated)
        self.ambient_noise_view.point_removed.connect(self.ambient_noise_tab.on_point_removed)

        # 创建声传播环境标签页
        self.propagation_tab = PropagationTab(data_manager=self.data_manager)
        self.control_panel.tabs.addTab(self.propagation_tab, "声传播环境")

        # 连接声传播环境标签页的信号
        self.propagation_tab.simulation_requested.connect(self.simulation_ui_manager.on_propagation_simulation)
        self.propagation_tab.stop_requested.connect(lambda: self.simulation_controller.cancel_propagation_simulation())

        # 创建综合仿真标签页
        self.integrated_tab = IntegratedTab(data_manager=self.data_manager)
        self.control_panel.tabs.addTab(self.integrated_tab, "综合仿真")

        # 连接综合仿真标签页的信号
        self.integrated_tab.simulation_requested.connect(self.simulation_ui_manager.on_integrated_simulation)

        # 连接综合仿真视图的信号到控制器
        self.integrated_view.calculate_spectrum_requested.connect(
            self.simulation_controller.integrated_controller.on_calculate_spectrum_requested)

    def update_ui_lock_state(self, module=None, is_simulating=None):
        """
        更新UI锁定状态

        根据仿真状态更新UI控件的启用/禁用状态

        Args:
            module (str, optional): 模块名称，如'ship_noise'。如果为None，更新所有模块。
            is_simulating (bool, optional): 是否正在仿真。如果为None，从数据管理器获取状态。
        """
        # 检查是否有任何模块在仿真
        any_simulating = self.data_manager.any_module_simulating()

        # 锁定全局参数输入框（如果有任何模块在仿真）
        # 通过禁用系统参数设置动作来实现
        self.menu_manager.set_system_settings_enabled(not any_simulating)

        # 锁定船舶辐射噪声标签页
        if module == 'ship_noise' or module is None:
            is_ship_simulating = self.data_manager.is_module_simulating('ship_noise')
            self.ship_noise_tab.setEnabled(not is_ship_simulating)

        # 锁定海洋环境噪声标签页
        if module == 'ambient_noise' or module is None:
            is_ambient_simulating = self.data_manager.is_module_simulating('ambient_noise')
            self.ambient_noise_tab.setEnabled(not is_ambient_simulating)

            # 设置Wenz曲线视图的模式
            if is_ambient_simulating:
                # 仿真开始时，强制设置为锁定模式
                self.ambient_noise_view.set_interaction_mode('locked')
            else:
                # 仿真结束时，检查用户是否选择了只读模式
                if self.ambient_noise_tab.readonly_mode_button.isChecked():
                    # 如果用户选择了只读模式，保持锁定状态
                    self.ambient_noise_view.set_interaction_mode('locked')
                else:
                    # 否则恢复到之前的模式
                    self.ambient_noise_view.restore_previous_mode()

        # 锁定声传播标签页
        if module == 'propagation' or module is None:
            is_propagation_simulating = self.data_manager.is_module_simulating('propagation')
            self.propagation_tab.setEnabled(not is_propagation_simulating)

        # 锁定综合仿真标签页
        if module == 'integrated' or module is None:
            is_integrated_simulating = self.data_manager.is_module_simulating('integrated')
            self.integrated_tab.setEnabled(not is_integrated_simulating)

    def on_tab_changed(self, index):
        """
        控制面板标签页切换事件处理

        Args:
            index: 标签页索引
        """
        # 切换视图区域
        self.view_area.change_view(index)



    def sync_ui_parameters_to_data_manager(self):
        """
        同步UI中的参数到数据管理器

        注意：此方法用于在加载项目后手动同步一次UI到数据管理器
        """
        # 同步船舶辐射噪声参数
        ship_params = self.ship_noise_tab.get_current_parameters()
        self.data_manager.set_parameters('ship_noise', ship_params)

        # 同步海洋环境噪声参数
        user_points = self.ambient_noise_view.get_user_points()
        extrapolated_points = [(freq, level) for freq, level, _ in self.ambient_noise_view.extrapolated_points]

        # 获取外插设置
        low_freq_method = 'auto' if self.ambient_noise_tab.low_freq_auto_radio.isChecked() else 'manual'
        high_freq_method = 'auto' if self.ambient_noise_tab.high_freq_auto_radio.isChecked() else 'manual'
        low_freq_slope = self.ambient_noise_tab.low_freq_slope_input.value()
        high_freq_slope = self.ambient_noise_tab.high_freq_slope_input.value()

        # 获取滤波器阶数
        filter_order = int(self.ambient_noise_tab.filter_order_input.value())

        # 创建完整的参数字典
        ambient_params = {
            'user_defined_points': user_points,
            'extrapolated_points': extrapolated_points,
            'low_freq_extrapolation_method': low_freq_method,
            'high_freq_extrapolation_method': high_freq_method,
            'low_freq_slope': low_freq_slope,
            'high_freq_slope': high_freq_slope,
            'filter_order': filter_order
        }
        self.data_manager.set_parameters('ambient_noise', ambient_params)

        # 同步综合仿真标签页的参数
        # 获取当前信道数据目录
        channel_data_dir = self.integrated_tab.channel_dir_edit.text()

        # 获取当前选中的阵元和频率
        element_index = self.integrated_tab.element_combo.currentIndex()
        frequency_index = self.integrated_tab.frequency_combo.currentIndex()

        # 获取频率值
        selected_frequency = 0
        if frequency_index >= 0:
            results = self.data_manager.get_results('integrated')
            if results and 'channel_data' in results:
                frequencies = results['channel_data'].get('frequencies', [])
                if frequency_index < len(frequencies):
                    selected_frequency = frequencies[frequency_index]

        # 创建综合仿真参数字典
        integrated_params = self.data_manager.get_parameters('integrated') or {}
        integrated_params.update({
            'channel_data_dir': channel_data_dir,
            'selected_element_index': element_index if element_index >= 0 else 0,
            'selected_frequency': selected_frequency
        })

        self.data_manager.set_parameters('integrated', integrated_params)

        # TODO: 同步其他标签页的参数
        # 当其他模块实现后添加

    def update_ui_from_data_manager(self):
        """
        从数据管理器更新UI
        """
        # 更新船舶辐射噪声标签页
        ship_params = self.data_manager.get_parameters('ship_noise')
        if ship_params:  # 确保有参数
            self.ship_noise_tab.set_parameters(ship_params)

        # 更新声传播标签页
        propagation_params = self.data_manager.get_parameters('propagation')
        if propagation_params:  # 确保有参数
            self.propagation_tab.load_params_from_data_manager()

        # 更新综合仿真标签页
        integrated_params = self.data_manager.get_parameters('integrated')
        if integrated_params:  # 确保有参数
            self.integrated_tab.load_params_from_data_manager()

            # 检查是否已经有信道数据结果，如果没有才尝试加载
            integrated_results = self.data_manager.get_results('integrated')
            has_channel_data = integrated_results and 'channel_data' in integrated_results

            # 如果有信道数据目录但没有加载过信道数据，尝试加载信道数据
            if ('channel_data_dir' in integrated_params and
                integrated_params['channel_data_dir'] and
                not has_channel_data):
                channel_data_dir = integrated_params['channel_data_dir']
                if os.path.exists(channel_data_dir):
                    try:
                        print(f"主窗口: 加载信道数据 {channel_data_dir}")
                        # 使用非阻塞方式加载信道数据
                        self.simulation_controller.load_channel_data(channel_data_dir)
                        # 更新UI
                        self.integrated_tab.update_channel_data_info()
                    except Exception as e:
                        print(f"加载信道数据失败: {e}")
                else:
                    print(f"信道数据目录不存在: {channel_data_dir}")
            elif has_channel_data:
                print("主窗口: 信道数据已存在，跳过加载")
                # 如果已有信道数据，只需要更新UI
                self.integrated_tab.update_channel_data_info()

        # 更新海洋环境噪声标签页的参数
        ambient_params = self.data_manager.get_parameters('ambient_noise')
        if ambient_params:
            # 海洋环境噪声的参数恢复通过view_updater的on_parameters_changed处理
            pass
            # 更新用户定义的点
            user_points = ambient_params.get('user_defined_points', [])
            self.ambient_noise_view.set_user_points(user_points)

            # 更新外插点
            extrapolated_points = ambient_params.get('extrapolated_points', [])
            self.ambient_noise_view.update_extrapolated_curve(extrapolated_points)

            # 更新外插设置
            low_freq_method = ambient_params.get('low_freq_extrapolation_method', 'auto')
            high_freq_method = ambient_params.get('high_freq_extrapolation_method', 'auto')

            # 设置低频外插方式
            if low_freq_method == 'auto':
                self.ambient_noise_tab.low_freq_auto_radio.setChecked(True)
                self.ambient_noise_tab.low_freq_slope_input.setEnabled(False)
            else:
                self.ambient_noise_tab.low_freq_manual_radio.setChecked(True)
                self.ambient_noise_tab.low_freq_slope_input.setEnabled(True)

            # 设置高频外插方式
            if high_freq_method == 'auto':
                self.ambient_noise_tab.high_freq_auto_radio.setChecked(True)
                self.ambient_noise_tab.high_freq_slope_input.setEnabled(False)
            else:
                self.ambient_noise_tab.high_freq_manual_radio.setChecked(True)
                self.ambient_noise_tab.high_freq_slope_input.setEnabled(True)

            # 设置斜率值
            low_freq_slope = ambient_params.get('low_freq_slope', -9.0)
            high_freq_slope = ambient_params.get('high_freq_slope', -5.0)
            self.ambient_noise_tab.low_freq_slope_input.setValue(low_freq_slope)
            self.ambient_noise_tab.high_freq_slope_input.setValue(high_freq_slope)

            # 设置滤波器阶数
            filter_order = ambient_params.get('filter_order', 16385)
            self.ambient_noise_tab.filter_order_input.setValue(filter_order)

        # TODO: 更新其他标签页
        # 当其他模块实现后添加

    def on_copy(self):
        """
        复制事件处理
        """
        # TODO: 实现复制逻辑
        self.statusBar().showMessage("复制")

    def on_paste(self):
        """
        粘贴事件处理
        """
        # TODO: 实现粘贴逻辑
        self.statusBar().showMessage("粘贴")

    def on_preferences(self):
        """
        首选项事件处理
        """
        from src.ui.dialogs.system_settings_dialog import SystemSettingsDialog
        dialog = SystemSettingsDialog(self)
        if dialog.exec_():
            self.statusBar().showMessage("系统参数已更新")

    # 移除on_calculate_spectrum_requested方法，将其逻辑移至IntegratedController

    def on_toggle_fullscreen(self, checked):
        """
        切换全屏事件处理

        Args:
            checked: 是否选中
        """
        if checked:
            self.showFullScreen()
        else:
            self.showNormal()

    def on_about(self):
        """
        关于事件处理
        """
        QMessageBox.about(
            self,
            "关于面向复杂水声环境的噪声仿真系统",
            "面向复杂水声环境的噪声仿真系统 v1.0\n\n"
            "本系统用于模拟海洋环境中的声学传播和噪声特性。\n"
            "系统集成了BELLHOP声学模型，能够计算和可视化水下噪声场的频谱、传播损失等特性。"
        )

    def load_settings(self):
        """
        加载应用程序设置
        """
        settings = QSettings("WaterAcoustics", "NoiseSimulation")

        # 恢复窗口位置和大小
        geometry = settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)

        # 恢复分割器位置
        splitter_state = settings.value("splitter_state")
        if splitter_state:
            self.splitter.restoreState(splitter_state)

    def save_settings(self):
        """
        保存应用程序设置
        """
        settings = QSettings("WaterAcoustics", "NoiseSimulation")

        # 保存窗口位置和大小
        settings.setValue("geometry", self.saveGeometry())

        # 保存分割器位置
        settings.setValue("splitter_state", self.splitter.saveState())

    def closeEvent(self, event):
        """
        窗口关闭事件处理

        Args:
            event: 关闭事件
        """
        # 委托给项目UI管理器处理
        if not self.project_ui_manager.handle_close_event(event):
            return

        # 保存设置
        self.save_settings()

        # 接受关闭事件
        event.accept()
