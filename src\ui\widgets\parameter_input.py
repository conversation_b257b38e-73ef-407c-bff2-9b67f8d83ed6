# -*- coding: utf-8 -*-
"""
参数输入组件

提供各种参数输入控件的封装
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                            QLabel, QLineEdit, QSpinBox, QDoubleSpinBox, 
                            QPushButton, QGroupBox, QCheckBox, QComboBox,
                            QSlider)
from PyQt5.QtCore import Qt, pyqtSignal


class ParameterGroup(QGroupBox):
    """
    参数组
    
    将相关的参数控件组织在一个组框中
    """
    
    def __init__(self, title, parent=None):
        """
        初始化参数组
        
        Args:
            title: 组标题
            parent: 父窗口
        """
        super().__init__(title, parent)
        
        # 创建表单布局
        self.form_layout = QFormLayout(self)
    
    def add_parameter(self, label, widget):
        """
        添加参数控件
        
        Args:
            label: 参数标签
            widget: 参数控件
        """
        self.form_layout.addRow(label, widget)


class NumericParameter(QWidget):
    """
    数值参数输入控件
    
    提供数值参数的输入，包括微调框和滑动条
    """
    
    # 自定义信号
    value_changed = pyqtSignal(float)
    
    def __init__(self, min_value, max_value, default_value, decimals=0, step=1, suffix="", parent=None):
        """
        初始化数值参数输入控件
        
        Args:
            min_value: 最小值
            max_value: 最大值
            default_value: 默认值
            decimals: 小数位数
            step: 步长
            suffix: 后缀
            parent: 父窗口
        """
        super().__init__(parent)
        
        # 创建布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建微调框
        if decimals > 0:
            self.spin_box = QDoubleSpinBox()
            self.spin_box.setSingleStep(step)
        else:
            self.spin_box = QSpinBox()
            self.spin_box.setSingleStep(int(step))
        
        self.spin_box.setRange(min_value, max_value)
        self.spin_box.setValue(default_value)
        self.spin_box.setSuffix(suffix)
        self.spin_box.setDecimals(decimals)
        
        # 创建滑动条
        self.slider = QSlider(Qt.Horizontal)
        
        # 对于浮点数，将值映射到整数范围
        if decimals > 0:
            self.slider_scale = 10 ** decimals
            self.slider.setRange(int(min_value * self.slider_scale), int(max_value * self.slider_scale))
            self.slider.setValue(int(default_value * self.slider_scale))
        else:
            self.slider_scale = 1
            self.slider.setRange(int(min_value), int(max_value))
            self.slider.setValue(int(default_value))
        
        # 添加到布局
        layout.addWidget(self.spin_box, 1)  # 1是拉伸因子
        layout.addWidget(self.slider, 2)    # 2是拉伸因子，使滑动条占据更多空间
        
        # 连接信号
        self.spin_box.valueChanged.connect(self.on_spin_box_changed)
        self.slider.valueChanged.connect(self.on_slider_changed)
    
    def on_spin_box_changed(self, value):
        """
        微调框值变化事件处理
        
        Args:
            value: 新值
        """
        # 更新滑动条
        self.slider.setValue(int(value * self.slider_scale))
        
        # 发射信号
        self.value_changed.emit(value)
    
    def on_slider_changed(self, value):
        """
        滑动条值变化事件处理
        
        Args:
            value: 新值
        """
        # 更新微调框
        self.spin_box.setValue(value / self.slider_scale)
    
    def value(self):
        """
        获取当前值
        
        Returns:
            当前值
        """
        return self.spin_box.value()
    
    def setValue(self, value):
        """
        设置当前值
        
        Args:
            value: 新值
        """
        self.spin_box.setValue(value)


class ChoiceParameter(QComboBox):
    """
    选择参数输入控件
    
    提供选择参数的输入
    """
    
    def __init__(self, choices, default_index=0, parent=None):
        """
        初始化选择参数输入控件
        
        Args:
            choices: 选项列表
            default_index: 默认选中项索引
            parent: 父窗口
        """
        super().__init__(parent)
        
        # 添加选项
        self.addItems(choices)
        
        # 设置默认值
        self.setCurrentIndex(default_index)


class BooleanParameter(QCheckBox):
    """
    布尔参数输入控件
    
    提供布尔参数的输入
    """
    
    def __init__(self, text, default_value=False, parent=None):
        """
        初始化布尔参数输入控件
        
        Args:
            text: 显示文本
            default_value: 默认值
            parent: 父窗口
        """
        super().__init__(text, parent)
        
        # 设置默认值
        self.setChecked(default_value)


class TextParameter(QLineEdit):
    """
    文本参数输入控件
    
    提供文本参数的输入
    """
    
    def __init__(self, default_text="", placeholder="", parent=None):
        """
        初始化文本参数输入控件
        
        Args:
            default_text: 默认文本
            placeholder: 占位文本
            parent: 父窗口
        """
        super().__init__(default_text, parent)
        
        # 设置占位文本
        self.setPlaceholderText(placeholder)
