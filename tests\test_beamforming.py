# -*- coding: utf-8 -*-
"""
波束形成算法测试脚本

使用理想平面波测试验证波束形成算法的正确性。
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from src.models.integrated_simulation import IntegratedSimulation

# 添加matplotlib中文支持
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
matplotlib.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号


def test_ideal_plane_wave(theta_arrival=20, f_center=1000, fs=44100, duration=0.1,
                          n_elements=8, element_spacing=0.5, sound_speed=1500,
                          scan_angles=None, delay_method='fractional'):
    """
    使用理想平面波测试波束形成算法

    Args:
        theta_arrival (float): 入射角度（度）
        f_center (float): 信号中心频率（Hz）
        fs (int): 采样率（Hz）
        duration (float): 信号持续时间（秒）
        n_elements (int): 阵元数量
        element_spacing (float): 阵元间距（米）
        sound_speed (float): 声速（米/秒）
        scan_angles (numpy.ndarray, optional): 扫描角度数组，默认为-90到90度，步长为1度
        delay_method (str): 时延补偿方法，'phase', 'integer', 'fractional'

    Returns:
        tuple: (angles, beam_pattern) 角度数组和对应的波束方向图
    """
    print(f"\n开始理想平面波测试:")
    print(f"  入射角度: {theta_arrival}°")
    print(f"  信号中心频率: {f_center} Hz")
    print(f"  采样率: {fs} Hz")
    print(f"  信号持续时间: {duration} 秒")
    print(f"  阵元数量: {n_elements}")
    print(f"  阵元间距: {element_spacing} 米")
    print(f"  声速: {sound_speed} 米/秒")
    print(f"  时延补偿方法: {delay_method}")

    # 创建时间轴
    t = np.arange(0, duration, 1/fs)
    signal_length = len(t)

    # 创建阵元位置（垂直阵列，z轴方向）
    # 使用二维坐标 (x, z)，其中x=0表示垂直阵列
    element_positions = np.zeros((n_elements, 2))
    for i in range(n_elements):
        element_positions[i, 0] = 0  # x坐标为0
        element_positions[i, 1] = i * element_spacing  # z坐标

    # 打印阵元位置
    print("\n阵元位置:")
    for i, pos in enumerate(element_positions):
        print(f"  阵元 {i}: 位置 = {pos}")

    # 设置参考位置为阵列中心
    ref_pos = np.mean(element_positions, axis=0)
    print(f"\n参考位置: {ref_pos}")

    # 计算相对位置
    relative_positions = element_positions - ref_pos
    print("\n相对位置:")
    for i, pos in enumerate(relative_positions):
        print(f"  阵元 {i}: 相对位置 = {pos}")

    # 计算入射方向向量
    direction_arrival = np.array([
        np.cos(np.radians(theta_arrival)),
        np.sin(np.radians(theta_arrival))
    ])
    print(f"\n入射方向向量: {direction_arrival}")

    # 计算真实时延
    true_delays = np.dot(relative_positions, direction_arrival) / sound_speed
    print("\n真实时延:")
    for i, delay in enumerate(true_delays):
        print(f"  阵元 {i}: 时延 = {delay:.6f} 秒")

    # 生成每个阵元的理想信号
    signal_segments = {}
    for i in range(n_elements):
        # 创建带有时延的正弦信号
        signal = np.sin(2 * np.pi * f_center * (t - true_delays[i]))
        signal_segments[i] = signal

    # 创建综合仿真模型实例
    model = IntegratedSimulation(fs)

    # 设置扫描角度
    if scan_angles is None:
        scan_angles = np.arange(-90, 91, 1)  # 从-90度到90度，步长为1度

    # 执行波束形成
    # 注意：reference_position需要是字符串'center'、'first'、'last'或具体坐标
    # 这里我们使用'center'而不是直接传递ref_pos数组
    angles, beam_pattern = model.delay_and_sum_beamforming(
        signal_segments, element_positions, scan_angles,
        sound_speed, 'center', 'uniform', delay_method
    )

    return angles, beam_pattern


def plot_beam_pattern(angles, beam_pattern, theta_arrival, save_path=None):
    """
    绘制波束方向图

    Args:
        angles (numpy.ndarray): 角度数组
        beam_pattern (numpy.ndarray): 波束方向图
        theta_arrival (float): 入射角度
        save_path (str, optional): 保存路径
    """
    plt.figure(figsize=(10, 6))
    plt.plot(angles, beam_pattern)
    plt.axvline(x=theta_arrival, color='r', linestyle='--', label=f'入射角度 {theta_arrival}°')

    # 找出最大值位置
    max_idx = np.argmax(beam_pattern)
    max_angle = angles[max_idx]
    plt.axvline(x=max_angle, color='g', linestyle='--', label=f'最大值位置 {max_angle}°')

    plt.grid(True)
    plt.xlabel('角度 (度)')
    plt.ylabel('归一化功率 (dB)')
    plt.title('波束方向图')
    plt.legend()

    # 设置y轴范围
    plt.ylim([-30, 5])

    if save_path:
        plt.savefig(save_path)

    plt.show()


def analyze_results(angles, beam_pattern, theta_arrival):
    """
    分析波束形成结果

    Args:
        angles (numpy.ndarray): 角度数组
        beam_pattern (numpy.ndarray): 波束方向图
        theta_arrival (float): 入射角度
    """
    # 找出最大值位置
    max_idx = np.argmax(beam_pattern)
    max_angle = angles[max_idx]

    print("\n波束形成结果分析:")
    print(f"  入射角度: {theta_arrival}°")
    print(f"  最大值位置: {max_angle}°")
    print(f"  角度误差: {abs(max_angle - theta_arrival):.2f}°")

    # 计算-3dB波束宽度
    threshold_db = -3
    left_idx = max_idx
    while left_idx > 0 and beam_pattern[left_idx] > threshold_db:
        left_idx -= 1

    right_idx = max_idx
    while right_idx < len(angles) - 1 and beam_pattern[right_idx] > threshold_db:
        right_idx += 1

    left_angle = angles[left_idx]
    right_angle = angles[right_idx]
    beam_width = right_angle - left_angle

    print(f"  -3dB波束宽度: {beam_width:.2f}°")
    print(f"  -3dB左边界: {left_angle:.2f}°")
    print(f"  -3dB右边界: {right_angle:.2f}°")

    # 计算旁瓣电平
    # 排除主瓣区域
    main_lobe_width = 2 * beam_width  # 假设主瓣宽度是-3dB宽度的2倍
    main_lobe_left = max_angle - main_lobe_width/2
    main_lobe_right = max_angle + main_lobe_width/2

    side_lobe_mask = (angles < main_lobe_left) | (angles > main_lobe_right)
    if np.any(side_lobe_mask):
        side_lobe_levels = beam_pattern[side_lobe_mask]
        max_side_lobe = np.max(side_lobe_levels)
        max_side_lobe_idx = np.where(beam_pattern == max_side_lobe)[0][0]
        max_side_lobe_angle = angles[max_side_lobe_idx]

        print(f"  最大旁瓣电平: {max_side_lobe:.2f} dB")
        print(f"  最大旁瓣位置: {max_side_lobe_angle:.2f}°")
        print(f"  主瓣与旁瓣比: {beam_pattern[max_idx] - max_side_lobe:.2f} dB")


def run_tests():
    """
    运行一系列测试
    """
    # 测试单一入射角度，验证修复后的波束形成算法
    print(f"\n\n测试入射角度 20°")
    angles, beam_pattern = test_ideal_plane_wave(
        theta_arrival=20,
        f_center=1000,
        fs=22050,
        duration=0.1,
        n_elements=8,
        element_spacing=0.25,
        sound_speed=1500,
        delay_method='fractional'
    )

    analyze_results(angles, beam_pattern, 20)
    plot_beam_pattern(angles, beam_pattern, 20, "beam_pattern_20deg.png")

    # # 测试不同时延补偿方法
    # delay_methods = ['phase', 'integer', 'fractional']

    # for method in delay_methods:
    #     print(f"\n\n测试时延补偿方法 {method}")
    #     angles, beam_pattern = test_ideal_plane_wave(
    #         theta_arrival=20,
    #         f_center=1000,
    #         fs=44100,
    #         duration=0.1,
    #         n_elements=8,
    #         element_spacing=0.5,
    #         sound_speed=1500,
    #         delay_method=method
    #     )

    #     analyze_results(angles, beam_pattern, 20)
    #     plot_beam_pattern(angles, beam_pattern, 20, f"beam_pattern_{method}.png")


if __name__ == "__main__":
    run_tests()
