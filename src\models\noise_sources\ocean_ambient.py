# -*- coding: utf-8 -*-
"""
海洋环境噪声模型

基于Wenz曲线模型，提供海洋环境噪声的频谱定义和信号生成功能。
允许用户通过选择频率-噪声级点来定义自定义噪声谱。
"""

import numpy as np
from scipy import signal


class OceanAmbientNoise:
    """
    海洋环境噪声模型

    基于Wenz曲线模型，提供海洋环境噪声的频谱定义和信号生成功能。
    允许用户通过选择频率-噪声级点来定义自定义噪声谱。
    """

    def __init__(self):
        """
        初始化海洋环境噪声模型

        """
        # 基本参数
        self.sampling_rate = None
        self.duration = None
        self.filter_order = 16385  # 滤波器阶数，默认16385

        # 用户定义的频率-噪声级点列表，每个元素为(freq, level)元组
        self.user_defined_points = []

        # 系统外插的频率-噪声级点列表，每个元素为(freq, level)元组
        self.extrapolated_points = []

        # 插值后的完整频谱，字典形式{freq: level}
        self.interpolated_spectrum = {}

        # 生成的时域信号
        self.generated_signal = None

        # FIR滤波器系数
        self.fir_coeffs = None

        # 缩放因子
        self.scaling_factor = None

        # 频段划分定义
        self.frequency_bands = {
            'low': (1, 20),      # 低频段：1-20Hz
            'mid': (20, 500),    # 中频段：20-500Hz
            'high': (500, 20000) # 高频段：500-20000Hz（根据Wenz曲线定义）
        }

        # 各频段斜率因子（dB/倍频程）
        self.slope_factors = {
            'low': -9.0,    # 低频段斜率：-9dB/倍频程
            'mid': -3.0,    # 中频段斜率：-3dB/倍频程
            'high': -5.0    # 高频段斜率：-5dB/倍频程
        }

        # 外插方式设置
        self.extrapolation_methods = {
            'low_freq': 'auto',   # 低频外插方式：'auto'(自动)或'manual'(手动指定斜率)
            'high_freq': 'auto'   # 高频外插方式：'auto'(自动)或'manual'(手动指定斜率)
        }

        # 用户指定的外插斜率（dB/倍频程）
        self.user_slopes = {
            'low_freq': -9.0,    # 低频用户指定斜率
            'high_freq': -5.0    # 高频用户指定斜率
        }

    def set_simulation_params(self, sampling_rate, duration, filter_order=None):
        """
        设置仿真参数

        Args:
            sampling_rate (float): 采样率，单位Hz
            duration (float): 信号持续时间，单位秒
            filter_order (int, optional): 滤波器阶数，应为奇数
        """
        self.sampling_rate = sampling_rate
        self.duration = duration

        # 如果提供了滤波器阶数，则更新
        if filter_order is not None:
            # 确保滤波器阶数为奇数
            if filter_order % 2 == 0:
                filter_order += 1
            self.filter_order = filter_order

    def add_point(self, freq, level):
        """
        添加一个用户定义点

        Args:
            freq (float): 频率，单位Hz
            level (float): 噪声级，单位dB re 1μPa²/Hz

        Returns:
            int: 添加点的索引
        """
        # 添加点到用户定义点列表
        self.user_defined_points.append((freq, level))

        # 按频率排序
        self.user_defined_points.sort(key=lambda x: x[0])

        # 返回新添加点的索引
        return self.user_defined_points.index((freq, level))

    def add_extrapolated_point(self, freq, level):
        """
        添加一个外插点

        Args:
            freq (float): 频率，单位Hz
            level (float): 噪声级，单位dB re 1μPa²/Hz

        Returns:
            int: 添加点的索引
        """
        # 添加点到外插点列表
        self.extrapolated_points.append((freq, level))

        # 按频率排序
        self.extrapolated_points.sort(key=lambda x: x[0])

    def get_extrapolated_points(self):
        """
        获取外插点列表

        Returns:
            list: 外插点列表，每个元素为(freq, level)元组
        """
        return self.extrapolated_points

    def remove_point(self, index):
        """
        删除指定索引的点

        Args:
            index (int): 点的索引

        Returns:
            bool: 是否成功删除
        """
        if 0 <= index < len(self.user_defined_points):
            self.user_defined_points.pop(index)
            return True
        return False

    def update_point(self, index, freq, level):
        """
        更新指定索引的点

        Args:
            index (int): 点的索引
            freq (float): 新频率，单位Hz
            level (float): 新噪声级，单位dB re 1μPa²/Hz

        Returns:
            bool: 是否成功更新
        """
        if 0 <= index < len(self.user_defined_points):
            self.user_defined_points[index] = (freq, level)

            # 按频率排序
            self.user_defined_points.sort(key=lambda x: x[0])

            # 返回新的索引
            return self.user_defined_points.index((freq, level))
        return -1

    def clear_points(self):
        """
        清除所有点
        """
        self.user_defined_points = []
        self.extrapolated_points = []
        self.interpolated_spectrum = {}

    def set_extrapolation_method(self, freq_range, method):
        """
        设置外插方式

        Args:
            freq_range (str): 频率范围，'low_freq'或'high_freq'
            method (str): 外插方式，'auto'(自动)或'manual'(手动指定斜率)
        """
        if freq_range in self.extrapolation_methods and method in ['auto', 'manual']:
            self.extrapolation_methods[freq_range] = method

    def set_user_slope(self, freq_range, slope):
        """
        设置用户指定的外插斜率

        Args:
            freq_range (str): 频率范围，'low_freq'或'high_freq'
            slope (float): 斜率，单位dB/倍频程
        """
        if freq_range in self.user_slopes:
            self.user_slopes[freq_range] = slope

    def extrapolate_spectrum(self, user_points=None, sampling_rate=None,
                          low_freq_method=None, high_freq_method=None,
                          low_freq_slope=None, high_freq_slope=None):
        """
        执行频谱外插，确保噪声谱覆盖完整的频率范围（1Hz到奈奎斯特频率）

        支持两种外插方式：
        1. 自动外插：根据用户曲线自动计算斜率
           - 利用频率最小的两个点计算向低频的斜率
           - 利用频率最大的两个点计算向高频的斜率
        2. 手动外插：使用用户指定的斜率
           - 使用用户指定的低频斜率向低频外插
           - 使用用户指定的高频斜率向高频外插

        Args:
            user_points (list, optional): 用户定义的点列表，每个元素为(freq, level)元组。
                                         如果为None，则使用self.user_defined_points
            sampling_rate (float, optional): 采样率，用于计算奈奎斯特频率。
                                           如果为None，则使用self.sampling_rate
            low_freq_method (str, optional): 低频外插方式，'auto'或'manual'。
                                           如果为None，则使用self.extrapolation_methods['low_freq']
            high_freq_method (str, optional): 高频外插方式，'auto'或'manual'。
                                            如果为None，则使用self.extrapolation_methods['high_freq']
            low_freq_slope (float, optional): 低频斜率，单位dB/倍频程。
                                            如果为None，则使用self.user_slopes['low_freq']
            high_freq_slope (float, optional): 高频斜率，单位dB/倍频程。
                                             如果为None，则使用self.user_slopes['high_freq']

        Returns:
            list: 外插后的点列表，如果用户输入点不满足要求则返回空列表

        Note:
            当不传入任何参数时，此方法会修改模型的内部状态（self.extrapolated_points）。
            当传入参数时，此方法不会修改模型的内部状态，而是返回计算得到的外插点列表。
        """
        # 使用传入的参数或默认值
        points = user_points if user_points is not None else self.user_defined_points
        fs = sampling_rate if sampling_rate is not None else self.sampling_rate

        # 如果没有用户定义点，无法进行外插
        if not points:
            return []

        # 如果只有一个点，无法进行自动外插
        if len(points) < 2:
            raise ValueError("至少需要两个点才能进行外插")

        # 创建外插点列表
        extrapolated_points = []

        # 获取用户定义的最小和最大频率点
        sorted_points = sorted(points, key=lambda x: x[0])
        min_freq_point = sorted_points[0]
        max_freq_point = sorted_points[-1]
        min_freq, min_freq_level = min_freq_point
        max_freq, max_freq_level = max_freq_point

        # 获取奈奎斯特频率
        nyquist = fs / 2

        # 向低频外插 - 只计算1Hz点
        if min_freq > 1.0:  # 如果最小频率大于1Hz，需要向低频外插
            # 根据外插方式选择斜率
            if low_freq_method is None:
                low_method = self.extrapolation_methods['low_freq']
            else:
                low_method = low_freq_method

            if low_method == 'auto':
                # 自动计算斜率：使用频率最小的两个点
                if len(sorted_points) >= 2:
                    freq1, level1 = sorted_points[0]
                    freq2, level2 = sorted_points[1]
                    # 计算斜率（dB/倍频程）
                    slope = (level2 - level1) / np.log2(freq2 / freq1)
                else:
                    # 如果只有一个点，使用默认低频斜率
                    slope = self.slope_factors['low']
            else:
                # 使用用户指定的斜率
                if low_freq_slope is None:
                    slope = self.user_slopes['low_freq']
                else:
                    slope = low_freq_slope

            # 使用计算得到的斜率向低频外插到1Hz
            level_1hz = self._extrapolate_with_slope(min_freq, min_freq_level, 1.0, slope)
            extrapolated_points.append((1.0, level_1hz))

        # 向高频外插 - 只计算奈奎斯特频率点
        if max_freq < nyquist:  # 如果最大频率小于奈奎斯特频率，需要向高频外插
            # 根据外插方式选择斜率
            if high_freq_method is None:
                high_method = self.extrapolation_methods['high_freq']
            else:
                high_method = high_freq_method

            if high_method == 'auto':
                # 自动计算斜率：使用频率最大的两个点
                if len(sorted_points) >= 2:
                    freq1, level1 = sorted_points[-1]
                    freq2, level2 = sorted_points[-2]
                    # 计算斜率（dB/倍频程）
                    slope = (level1 - level2) / np.log2(freq1 / freq2)
                else:
                    # 如果只有一个点，使用默认高频斜率
                    slope = self.slope_factors['high']
            else:
                # 使用用户指定的斜率
                if high_freq_slope is None:
                    slope = self.user_slopes['high_freq']
                else:
                    slope = high_freq_slope

            # 使用计算得到的斜率向高频外插到奈奎斯特频率
            level_nyquist = self._extrapolate_with_slope(max_freq, max_freq_level, nyquist, slope)
            extrapolated_points.append((nyquist, level_nyquist))

        # 如果没有传入参数，则更新模型的内部状态
        if user_points is None and sampling_rate is None and low_freq_method is None and high_freq_method is None and low_freq_slope is None and high_freq_slope is None:
            self.extrapolated_points = extrapolated_points

        # 返回外插点列表
        return extrapolated_points

    def _extrapolate_to_low_freq(self, start_freq, start_level):
        """
        向低频外插到1Hz

        Args:
            start_freq (float): 起始频率
            start_level (float): 起始电平
        """
        if start_freq <= self.frequency_bands['low'][0]:
            return

        # 如果起始频率在中频段
        if start_freq > self.frequency_bands['mid'][0]:
            # 先用中频段斜率外插到中频段下限
            mid_low_level = self._extrapolate_with_slope(
                start_freq, start_level,
                self.frequency_bands['mid'][0],
                self.slope_factors['mid']
            )
            # 添加中频段下限点
            self.extrapolated_points.append((self.frequency_bands['mid'][0], mid_low_level))

            # 更新起始点为中频段下限
            start_freq = self.frequency_bands['mid'][0]
            start_level = mid_low_level

        # 用低频段斜率外插到低频段下限
        low_freq_level = self._extrapolate_with_slope(
            start_freq, start_level,
            self.frequency_bands['low'][0],
            self.slope_factors['low']
        )

        # 添加低频段下限点
        self.extrapolated_points.append((self.frequency_bands['low'][0], low_freq_level))

    def _extrapolate_to_high_freq(self, start_freq, start_level):
        """
        向高频外插到奈奎斯特频率

        Args:
            start_freq (float): 起始频率
            start_level (float): 起始电平
        """
        # 获取奈奎斯特频率
        nyquist = self.sampling_rate / 2.0

        # 如果起始频率已经超过奈奎斯特频率，直接返回
        if start_freq >= nyquist:
            return

        # 如果起始频率在中频段
        if start_freq < self.frequency_bands['high'][0]:
            # 先用中频段斜率外插到高频段下限
            high_low_level = self._extrapolate_with_slope(
                start_freq, start_level,
                self.frequency_bands['high'][0],
                self.slope_factors['mid']
            )
            # 添加高频段下限点
            self.extrapolated_points.append((self.frequency_bands['high'][0], high_low_level))

            # 更新起始点为高频段下限
            start_freq = self.frequency_bands['high'][0]
            start_level = high_low_level

        # 确定外插的目标频率（高频段上限或奈奎斯特频率，取较小值）
        target_freq = min(self.frequency_bands['high'][1], nyquist)

        # 如果起始频率小于目标频率，进行外插
        if start_freq < target_freq:
            # 用高频段斜率外插到目标频率
            target_level = self._extrapolate_with_slope(
                start_freq, start_level,
                target_freq,
                self.slope_factors['high']
            )
            # 添加目标频率点
            self.extrapolated_points.append((target_freq, target_level))

            # 如果目标频率是奈奎斯特频率，已经完成外插
            if target_freq == nyquist:
                return

            # 否则，更新起始点为高频段上限，准备继续外插到奈奎斯特频率
            start_freq = target_freq
            start_level = target_level

        # 如果高频段上限小于奈奎斯特频率，继续外插到奈奎斯特频率
        if self.frequency_bands['high'][1] < nyquist:
            # 使用高频段斜率继续外插到奈奎斯特频率
            nyquist_level = self._extrapolate_with_slope(
                start_freq, start_level,
                nyquist,
                self.slope_factors['high']  # 继续使用高频段斜率
            )
            # 添加奈奎斯特频率点
            self.extrapolated_points.append((nyquist, nyquist_level))

    def _extrapolate_with_slope(self, start_freq, start_level, target_freq, slope_db_oct):
        """
        使用给定斜率进行外插

        Args:
            start_freq (float): 起始频率
            start_level (float): 起始电平
            target_freq (float): 目标频率
            slope_db_oct (float): 斜率，单位dB/倍频程

        Returns:
            float: 目标频率处的电平
        """
        # 计算频率比的对数（以2为底）
        log2_ratio = np.log2(target_freq / start_freq)

        # 根据斜率计算电平变化
        level_change = slope_db_oct * log2_ratio

        # 计算目标频率处的电平
        target_level = start_level + level_change

        return target_level

    def prepare_filter_parameters(self):
        """
        准备滤波器设计所需的频率和幅度参数

        根据用户定义点和外插点，准备用于FIR滤波器设计的归一化频率和幅度参数。
        确保频率范围覆盖从0Hz到奈奎斯特频率，并使用合适的斜率进行外插。

        注意：此方法主要用于UI展示，不再用于滤波器设计。
        滤波器设计请使用prepare_dense_filter_parameters方法。

        Returns:
            tuple: (频率数组, 幅度数组)
        """
        # 确保有足够的点进行滤波器设计
        if len(self.user_defined_points) + len(self.extrapolated_points) < 2:
            raise ValueError("至少需要两个点才能设计滤波器")

        # 合并用户定义点和外插点，按频率排序
        all_points = self.user_defined_points + self.extrapolated_points
        all_points.sort(key=lambda x: x[0])

        # 提取频率和电平
        freqs = [point[0] for point in all_points]
        levels = [point[1] for point in all_points]

        # 将dB值转换为线性幅度值
        amplitudes = [10 ** (level / 20) for level in levels]

        # 归一化频率（相对于Nyquist频率）
        nyquist = self.sampling_rate / 2
        norm_freqs = [freq / nyquist for freq in freqs]

        # 添加0Hz点（如果不存在）
        if norm_freqs[0] > 0:
            norm_freqs.insert(0, 0)
            amplitudes.insert(0, 0)  # 0Hz处设置为0

        # 添加奈奎斯特频率点（如果不存在）
        if norm_freqs[-1] < 1:
            # 获取最后一个点
            last_freq = freqs[-1]
            last_level = levels[-1]

            # 使用高频段斜率外插到奈奎斯特频率
            nyquist_level = self._extrapolate_with_slope(
                last_freq, last_level,
                nyquist,
                self.slope_factors['high']
            )

            # 将dB值转换为线性幅度
            nyquist_amplitude = 10 ** (nyquist_level / 20)

            # 添加奈奎斯特频率点
            norm_freqs.append(1)
            amplitudes.append(nyquist_amplitude)

        return norm_freqs, amplitudes

    def prepare_dense_filter_parameters(self):
        """
        准备密集的滤波器设计参数

        根据用户定义点和外插点，准备用于FIR滤波器设计的归一化频率和幅度参数。
        与prepare_filter_parameters不同，此方法生成更密集的频率点，
        特别是在低频区域，以提高滤波器设计的精度。

        Returns:
            tuple: (频率数组, 幅度数组)
        """
        # # 打印调试信息：用户定义点和外插点
        # print("\n===== 滤波器设计调试信息 =====")
        # print("用户定义点:")
        # for i, (freq, level) in enumerate(self.user_defined_points):
        #     print(f"  点{i+1}: 频率 = {freq:.2f} Hz, 电平 = {level:.2f} dB")

        # print("\n外插点:")
        # for i, (freq, level) in enumerate(self.extrapolated_points):
        #     print(f"  点{i+1}: 频率 = {freq:.2f} Hz, 电平 = {level:.2f} dB")
        # 确保有足够的点进行滤波器设计
        if len(self.user_defined_points) + len(self.extrapolated_points) < 2:
            raise ValueError("至少需要两个点才能设计滤波器")

        # 合并用户定义点和外插点，按频率排序
        all_points = self.user_defined_points + self.extrapolated_points
        all_points.sort(key=lambda x: x[0])

        # 提取频率和电平
        user_freqs = [point[0] for point in all_points]
        user_levels = [point[1] for point in all_points]

        # 获取奈奎斯特频率
        nyquist = self.sampling_rate / 2

        # 准备倍频程间隔的频率点
        # 从1Hz开始，按倍频程间隔生成频率点，直到奈奎斯特频率
        dense_freqs = [1.0]  # 从1Hz开始
        current_freq = 1.0

        # 每倍频程生成多个点，以提高精度
        points_per_octave = 4  # 每倍频程4个点
        octave_factor = 2 ** (1 / points_per_octave)

        # print(f"\n倍频程因子: {octave_factor}, 每倍频程点数: {points_per_octave}")

        while current_freq * octave_factor < nyquist:
            current_freq *= octave_factor
            dense_freqs.append(current_freq)

        # 添加奈奎斯特频率
        if dense_freqs[-1] < nyquist:
            dense_freqs.append(nyquist)

        # 对于每个密集频率点，根据用户定义点和外插点之间的斜率计算对应的电平
        dense_levels = []

        for freq in dense_freqs:
            # 找到freq在user_freqs中的位置
            idx = 0
            while idx < len(user_freqs) - 1 and user_freqs[idx + 1] < freq:
                idx += 1

            if idx == len(user_freqs) - 1 and user_freqs[idx] < freq:
                # freq大于所有用户定义点，使用高频段斜率外插
                dense_levels.append(self._extrapolate_with_slope(
                    user_freqs[idx], user_levels[idx],
                    freq,
                    self.slope_factors['high']
                ))
            elif user_freqs[idx] == freq:
                # freq正好等于某个用户定义点
                dense_levels.append(user_levels[idx])
            elif idx == 0 and freq < user_freqs[0]:
                # freq小于所有用户定义点，使用低频段斜率外插
                dense_levels.append(self._extrapolate_with_slope(
                    user_freqs[0], user_levels[0],
                    freq,
                    self.slope_factors['low']
                ))
            else:
                # freq在两个用户定义点之间，使用线性插值（对数频率域）
                log_freq_ratio = np.log2(freq / user_freqs[idx])
                log_freq_interval = np.log2(user_freqs[idx + 1] / user_freqs[idx])
                level_interval = user_levels[idx + 1] - user_levels[idx]

                # 计算斜率（dB/倍频程）
                slope = level_interval / log_freq_interval

                # 使用斜率计算插值电平
                interpolated_level = user_levels[idx] + slope * log_freq_ratio
                dense_levels.append(interpolated_level)

        # 将dB值转换为线性幅度值
        dense_amplitudes = [10 ** (level / 20) for level in dense_levels]

        # 添加0Hz点
        dense_freqs.insert(0, 0)
        dense_amplitudes.insert(0, 0)  # 0Hz处设置为0

        # 归一化频率（相对于Nyquist频率）
        norm_dense_freqs = [freq / nyquist for freq in dense_freqs]

        # 归一化幅度，确保幅度在[0,1]范围内
        max_amplitude = max(dense_amplitudes)
        if max_amplitude > 0:
            norm_dense_amplitudes = [amp / max_amplitude for amp in dense_amplitudes]
        else:
            norm_dense_amplitudes = dense_amplitudes

        # # 打印调试信息：生成的密集频率点和对应的电平
        # print("\n生成的密集频率点（前20个和后10个）:")
        # nyquist = self.sampling_rate / 2
        # actual_freqs = [f * nyquist for f in norm_dense_freqs]

        # # 将归一化幅度转换回dB
        # db_levels = []
        # for i, amp in enumerate(norm_dense_amplitudes):
        #     if i == 0 or amp <= 0:  # 0Hz点或幅度为0
        #         db_level = -200  # 一个很小的dB值
        #     else:
        #         # 先将归一化幅度转换回原始幅度
        #         orig_amp = amp * max_amplitude
        #         # 然后转换为dB
        #         db_level = 20 * np.log10(orig_amp)
        #     db_levels.append(db_level)

        # # 打印所有点
        # for i in range(len(actual_freqs)):
        #     print(f"  点{i+1}: 频率 = {actual_freqs[i]:.2f} Hz, 电平 = {db_levels[i]:.2f} dB, 归一化幅度 = {norm_dense_amplitudes[i]:.6f}")

        # # 打印后10个点
        # if len(actual_freqs) > 30:
        #     print("  ...")
        #     for i in range(len(actual_freqs) - 10, len(actual_freqs)):
        #         print(f"  点{i+1}: 频率 = {actual_freqs[i]:.2f} Hz, 电平 = {db_levels[i]:.2f} dB, 归一化幅度 = {norm_dense_amplitudes[i]:.6f}")

        # print("===== 调试信息结束 =====\n")

        # 返回归一化频率和归一化幅度
        return norm_dense_freqs, norm_dense_amplitudes

    def design_filter(self):
        """
        设计FIR滤波器并保存系数

        根据用户定义的频谱特性设计FIR滤波器，并保存滤波器系数以供复用。

        Returns:
            ndarray: FIR滤波器系数

        Raises:
            ValueError: 当用户输入点不满足频率范围要求时
        """
        # 如果没有点，无法设计滤波器
        if not self.user_defined_points:
            return None

        try:
            # 执行频谱外插，确保覆盖完整频率范围
            self.extrapolate_spectrum()  # 不传参数，会更新self.extrapolated_points

            # 准备密集的滤波器参数，使用新方法以提高滤波器设计精度
            norm_freqs, norm_amplitudes = self.prepare_dense_filter_parameters()

            # 设计FIR滤波器，使用用户指定的滤波器阶数
            self.fir_coeffs = signal.firwin2(self.filter_order, norm_freqs, norm_amplitudes, window=None)

            return self.fir_coeffs

        except ValueError as e:
            # 捕获并重新抛出频率范围验证错误
            raise ValueError(f"设计滤波器失败: {str(e)}")

    def generate_signal(self):
        """
        生成时域噪声信号

        Returns:
            ndarray: 生成的时域信号

        Raises:
            ValueError: 当用户输入点不满足频率范围要求时
        """
        # 如果没有点，无法生成信号
        if not self.user_defined_points:
            return np.zeros(int(self.sampling_rate * self.duration))

        try:
            # 如果滤波器系数尚未设计，先设计滤波器
            if self.fir_coeffs is None:
                self.design_filter()

            # 使用设计好的滤波器生成信号
            filtered_signal = self.generate_signal_with_filter(self.fir_coeffs, self.duration)

            # 计算功率谱密度缩放因子
            if self.user_defined_points and self.scaling_factor is None:
                # 使用改进的全频谱缩放方法，默认使用几何平均法
                scaling_method = 'geometric'  # 默认使用几何平均法
                self.scaling_factor = self._calculate_improved_scaling_factor(filtered_signal, method=scaling_method)

            # 应用缩放因子（如果有）
            if self.scaling_factor is not None:
                filtered_signal = filtered_signal * self.scaling_factor

            # 保存生成的信号
            self.generated_signal = filtered_signal

            return filtered_signal

        except ValueError as e:
            # 捕获并重新抛出频率范围验证错误
            raise ValueError(f"生成信号失败: {str(e)}")

    def generate_signal_with_filter(self, fir_coeffs, duration):
        """
        使用给定的FIR滤波器系数生成噪声信号

        Args:
            fir_coeffs (ndarray): FIR滤波器系数
            duration (float): 信号持续时间，单位秒

        Returns:
            ndarray: 生成的时域信号

        Raises:
            ValueError: 当参数无效时
        """
        if fir_coeffs is None:
            raise ValueError("滤波器系数不能为空")

        if duration <= 0:
            raise ValueError("信号持续时间必须大于0")

        # 获取滤波器阶数
        filter_order = len(fir_coeffs)

        # 生成高斯白噪声
        num_samples = int(self.sampling_rate * duration)
        white_noise = np.random.randn(num_samples + filter_order - 1)

        # 应用滤波器
        filtered_signal = signal.lfilter(fir_coeffs, 1.0, white_noise)

        # 去除滤波器延迟
        filtered_signal = filtered_signal[filter_order-1:]

        # 截取所需长度
        filtered_signal = filtered_signal[:num_samples]

        return filtered_signal

    def calculate_psd(self, signal=None):
        """
        计算功率谱密度

        Args:
            signal (ndarray, optional): 要计算PSD的信号，默认为生成的信号

        Returns:
            tuple: (频率数组, PSD数组, PSD_dB数组)

        Raises:
            ValueError: 当用户输入点不满足频率范围要求导致无法生成信号时
        """
        if signal is None:
            if self.generated_signal is None:
                # 这里可能会抛出ValueError
                self.generate_signal()
            signal = self.generated_signal

        # 计算功率谱密度
        # 使用scipy.signal.welch而不是参数signal
        from scipy import signal as scipy_signal
        freqs, psd = scipy_signal.welch(signal, fs=self.sampling_rate, nperseg=self.sampling_rate,
                                 scaling='density', detrend=False)

        # 转换为dB
        psd_db = 10 * np.log10(psd + 1e-10)  # 添加小值避免log(0)

        return freqs, psd, psd_db

    def get_filter_coeffs(self):
        """
        获取FIR滤波器系数

        如果滤波器系数尚未设计，先设计滤波器

        Returns:
            ndarray: FIR滤波器系数
        """
        if self.fir_coeffs is None:
            self.design_filter()
        return self.fir_coeffs

    def get_scaling_factor(self):
        """
        获取缩放因子

        Returns:
            float: 缩放因子，如果未计算则返回1.0
        """
        return self.scaling_factor if self.scaling_factor is not None else 1.0

    def save_spectrum(self, file_path):
        """
        保存用户定义的频谱到文件

        Args:
            file_path (str): 文件路径

        Returns:
            bool: 是否成功保存
        """
        import json

        try:
            data = {
                'user_defined_points': self.user_defined_points,
                'extrapolated_points': self.extrapolated_points,
                'sampling_rate': self.sampling_rate,
                'duration': self.duration
            }

            with open(file_path, 'w') as f:
                json.dump(data, f, indent=4)

            return True
        except Exception as e:
            print(f"保存频谱失败: {e}")
            return False

    def load_spectrum(self, file_path):
        """
        从文件加载用户定义的频谱

        Args:
            file_path (str): 文件路径

        Returns:
            bool: 是否成功加载
        """
        import json

        try:
            with open(file_path, 'r') as f:
                data = json.load(f)

            self.user_defined_points = data.get('user_defined_points', [])
            self.extrapolated_points = data.get('extrapolated_points', [])
            self.sampling_rate = data.get('sampling_rate', 44100)
            self.duration = data.get('duration', 1.0)

            return True
        except Exception as e:
            print(f"加载频谱失败: {e}")
            return False

    def _calculate_improved_scaling_factor(self, filtered_signal, method='geometric'):
        """
        计算基于全频谱的缩放因子

        使用对数频率轴上的dB值线性插值，计算在有效频率范围内的平均功率比值，
        得到一个更准确的缩放因子。

        支持两种计算方法：
        1. 几何平均法（'geometric'）：
           - 计算每个有效频率点的dB差：diff_db[i] = target_psd_db_interp[i] - Pxx_welch_db[i]
           - 计算这些dB差的算术平均值：mean_diff_db = np.mean(diff_db)
           - 计算功率缩放因子：k_power = 10**(mean_diff_db / 10)
           - 计算幅度缩放因子：scaling_factor = np.sqrt(k_power)

        2. 算术平均法（'arithmetic'）：
           - 将目标PSD和实际PSD转换为线性值
           - 计算线性功率比值：power_ratios = target_psd_linear / Pxx_welch
           - 计算比值的算术平均值：mean_power_ratio = np.mean(power_ratios)
           - 计算幅度缩放因子：scaling_factor = np.sqrt(mean_power_ratio)

        Args:
            filtered_signal: 滤波后的信号
            method: 计算方法，'geometric'（几何平均）或'arithmetic'（算术平均）

        Returns:
            float: 缩放因子
        """
        from scipy.interpolate import interp1d

        # 验证方法参数
        if method not in ['geometric', 'arithmetic']:
            # print(f"警告：未知的计算方法 '{method}'，使用默认的几何平均法")
            method = 'geometric'

        # 1. 计算实际PSD（包括dB值）
        f_welch, Pxx_welch, Pxx_welch_db = self.calculate_psd(filtered_signal)

        # 2. 准备目标频率和PSD
        # 合并用户定义点和外插点
        all_points = self.user_defined_points + self.extrapolated_points
        all_points.sort(key=lambda x: x[0])

        # 提取频率和电平
        target_freqs = np.array([point[0] for point in all_points])
        target_psd_db = np.array([point[1] for point in all_points])

        # 3. 确定有效频率范围
        min_target_f = target_freqs.min()
        max_target_f = target_freqs.max()
        valid_indices = (f_welch >= min_target_f) & (f_welch <= max_target_f)
        f_welch_valid = f_welch[valid_indices]

        # 4. 在对数频率轴上对dB值进行线性插值
        try:
            interp_func_db = interp1d(
                np.log10(target_freqs),
                target_psd_db,
                kind='linear',
                bounds_error=False,
                fill_value=np.nan
            )
            target_psd_db_interp = interp_func_db(np.log10(f_welch_valid))

            # 获取有效的dB值和线性PSD值
            Pxx_welch_db_valid = Pxx_welch_db[valid_indices]
            Pxx_welch_valid = Pxx_welch[valid_indices]

            # 5. 找出有效的计算点（非NaN值）
            valid_calc_indices = np.isfinite(target_psd_db_interp)

            if np.sum(valid_calc_indices) == 0:
                # print("警告：无法在有效频率范围内计算缩放因子。检查目标谱和计算谱。")
                scaling_factor = 1.0  # 出错时返回默认值
            else:
                # 根据选择的方法计算缩放因子
                if method == 'geometric':
                    # 几何平均法（直接使用基础定义）
                    # 将目标PSD转换为线性值
                    target_psd_linear = 10**(target_psd_db_interp[valid_calc_indices] / 10)

                    # 计算线性功率比值 r = target_psd / measured_psd
                    power_ratios = target_psd_linear / (Pxx_welch_valid[valid_calc_indices] + 1e-20)  # 避免除以零

                    # 计算几何平均值：(r₁ × r₂ × ... × rₙ)^(1/n)
                    # 等价于 exp(mean(log(r)))
                    log_ratios = np.log(power_ratios + 1e-20)  # 避免log(0)
                    mean_log_ratio = np.mean(log_ratios)
                    geo_mean_power_ratio = np.exp(mean_log_ratio)

                    # 计算幅度缩放因子（功率比的平方根）
                    scaling_factor = np.sqrt(geo_mean_power_ratio)

                    # # 计算等效dB差异（用于调试信息）
                    # equiv_db_diff = 10 * np.log10(geo_mean_power_ratio)

                    # # 打印一些调试信息，帮助诊断问题
                    # print(f"功率比值范围: {np.min(power_ratios):.2e} 到 {np.max(power_ratios):.2e}")

                    # # 调试信息
                    # print(f"改进的功率谱密度缩放（几何平均法 - 基础定义）：")
                    # print(f"几何平均功率比: {geo_mean_power_ratio:.2e}")
                    # print(f"等效dB差异: {equiv_db_diff:.2f} dB")

                else:  # 'arithmetic'
                    # 算术平均法（线性域平均）
                    # 将目标PSD转换为线性值
                    target_psd_linear = 10**(target_psd_db_interp[valid_calc_indices] / 10)

                    # 计算功率比值
                    power_ratios = target_psd_linear / (Pxx_welch_valid[valid_calc_indices] + 1e-20)  # 避免除以零

                    # # 打印一些调试信息
                    # print(f"功率比值范围: {np.min(power_ratios):.2e} 到 {np.max(power_ratios):.2e}")

                    # 计算功率比值的算术平均值
                    mean_power_ratio = np.mean(power_ratios)

                    # 计算幅度缩放因子
                    scaling_factor = np.sqrt(mean_power_ratio)

                    # # 调试信息
                    # print(f"改进的功率谱密度缩放（算术平均法）：")
                    # print(f"功率比值平均值: {mean_power_ratio:.2e}")

                # # 额外检查：如果缩放因子异常大或小，给出警告
                # if scaling_factor > 100 or scaling_factor < 0.01:
                #     print(f"警告：计算的缩放因子 {scaling_factor} 异常，可能存在问题！")

            # # 通用调试信息
            # print(f"有效频率范围: {min_target_f:.2f}Hz - {max_target_f:.2f}Hz")
            # print(f"有效计算点数: {np.sum(valid_calc_indices)} / {len(f_welch_valid)}")
            # print(f"计算得到的幅度缩放因子: {scaling_factor}")

            return scaling_factor

        except Exception as e:
            print(f"计算缩放因子时出错: {e}")
            return 1.0  # 出错时返回默认值