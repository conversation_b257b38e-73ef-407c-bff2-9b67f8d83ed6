# 水声噪声仿真系统架构文档

## 1. 系统概述

水声噪声仿真系统是一个用于模拟海洋环境中声学传播和噪声特性的应用程序。系统集成了多种仿真模型，能够计算和可视化水下噪声场的频谱、传播损失等特性。系统采用模块化设计，各个组件职责明确，便于维护和扩展。

## 2. 系统架构

系统采用分层架构，主要分为以下几层：

1. **核心层**：包含数据管理、项目管理和仿真控制等核心功能
2. **控制器层**：包含各种专门的仿真控制器
3. **UI层**：包含用户界面组件和UI管理器
4. **视图更新层**：负责将仿真结果转换为视图更新

### 2.1 目录结构

```
src/
  core/                   # 核心层
    data/                 # 数据管理
      simulation_data_manager.py
    project/              # 项目管理
      project_manager.py
    simulation/           # 仿真控制
      simulation_controller.py
    controllers/          # 专门控制器
      ship_noise_controller.py
      ambient_noise_controller.py
      propagation_controller.py
    view_updaters/        # 视图更新器
      ship_noise_view_updater.py
      ambient_noise_view_updater.py
      propagation_view_updater.py
  ui/                     # UI层
    main/                 # 主窗口相关
      main_window.py
      menu_manager.py
      project_ui_manager.py
      simulation_ui_manager.py
    view/                 # 视图组件
      view_area.py
      ship_noise_view.py
      ambient_noise_view.py
      propagation_view.py
      integrated_view.py
    control/              # 控制面板组件
      control_panel.py
      ship_noise_tab.py
      ambient_noise_tab.py
      propagation_tab.py
      integrated_tab.py
  models/                 # 模型层
    noise_sources/        # 噪声源模型
      ship_radiated.py
      ocean_ambient.py
```

## 3. 模块职责

### 3.1 核心层

#### 3.1.1 数据管理

**SimulationDataManager**
- 职责：集中管理所有仿真参数和结果数据
- 功能：
  - 提供线程安全的数据访问接口
  - 存储和管理全局参数（如采样率、持续时间）
  - 存储和管理各模块的参数和结果
  - 发送数据变更信号
  - 提供细粒度参数更新方法（update_parameter和update_nested_parameter）
  - 支持直接从UI控件更新单个参数

#### 3.1.2 项目管理

**ProjectManager**
- 职责：管理项目文件和项目状态
- 功能：
  - 保存和加载项目文件
  - 跟踪项目修改状态
  - 提供项目重置功能
  - 提供另存为功能
  - 使用简化的修改状态判断逻辑

#### 3.1.3 仿真控制

**SimulationController**
- 职责：协调各个专门控制器的工作
- 功能：
  - 管理整体仿真流程
  - 提供对数据管理器的访问
  - 转发仿真信号
  - 职责更加聚焦，不再负责项目管理相关功能

#### 3.1.4 专门控制器

**ShipNoiseController**
- 职责：执行船舶辐射噪声仿真
- 功能：
  - 在后台线程中执行仿真计算
  - 发送仿真进度和结果信号

**AmbientNoiseController**
- 职责：执行海洋环境噪声仿真
- 功能：
  - 在后台线程中执行仿真计算
  - 发送仿真进度和结果信号

**PropagationController**
- 职责：执行声传播仿真
- 功能：
  - 在后台线程中执行仿真计算
  - 发送仿真进度和结果信号

#### 3.1.5 视图更新器

**ShipNoiseViewUpdater**
- 职责：将船舶辐射噪声仿真结果转换为视图更新
- 功能：
  - 监听数据管理器的结果变更信号
  - 更新船舶辐射噪声视图

### 3.2 UI层

#### 3.2.1 主窗口相关

**MainWindow**
- 职责：提供应用程序的主界面
- 功能：
  - 创建和管理UI组件
  - 委托具体操作给专门的管理器
  - 直接创建和管理核心组件（DataManager和ProjectManager）
  - 将核心组件注入给需要的UI组件

**MenuManager**
- 职责：创建和管理菜单和工具栏
- 功能：
  - 创建菜单和工具栏
  - 连接菜单和工具栏动作到相应的处理函数

**ProjectUIManager**
- 职责：处理项目相关的UI操作
- 功能：
  - 处理新建、打开、保存项目等操作
  - 处理项目修改状态变更
  - 提供另存为功能
  - 直接持有ProjectManager引用，不再通过SimulationController间接调用

**SimulationUIManager**
- 职责：处理仿真相关的UI操作
- 功能：
  - 处理开始、停止仿真等操作
  - 处理仿真进度和结果的UI更新

#### 3.2.2 视图组件

**ViewArea**
- 职责：管理视图区域
- 功能：
  - 切换不同的视图

**ShipNoiseView**
- 职责：显示船舶辐射噪声仿真结果
- 功能：
  - 显示时域信号和频谱图

#### 3.2.3 控制面板组件

**ControlPanel**
- 职责：管理控制面板
- 功能：
  - 切换不同的控制面板标签页

**ShipNoiseTab**
- 职责：提供船舶辐射噪声仿真参数设置界面
- 功能：
  - 设置和获取仿真参数
  - 发送仿真请求信号
  - 直接持有DataManager引用
  - 参数变化时直接更新DataManager

### 3.3 模型层

**ShipRadiated**
- 职责：实现船舶辐射噪声模型
- 功能：
  - 生成连续谱信号
  - 生成线谱信号
  - 合成总信号

**OceanAmbient**
- 职责：实现海洋环境噪声模型
- 功能：
  - 生成海洋环境噪声信号

## 4. 数据流向

以船舶辐射噪声仿真为例，数据流向如下：

### 4.1 参数设置阶段

#### 原有数据流向（已优化）
1. 用户在 `ShipNoiseTab` 中设置仿真参数
2. 用户点击"开始仿真"按钮
3. `ShipNoiseTab` 发送 `simulation_requested` 信号，携带参数
4. `SimulationUIManager` 接收信号，调用 `data_manager.set_parameters('ship_noise', params)`
5. `SimulationUIManager` 调用 `simulation_controller.simulate_ship_noise()`

#### 优化后的参数更新数据流向
1. 用户在 `ShipNoiseTab` 中修改参数（如调整滑块、输入数值等）
2. `ShipNoiseTab` 的控件值变化事件触发 `on_parameter_changed()` 方法
3. `ShipNoiseTab` 直接调用 `data_manager.update_parameter('ship_noise', param_key, value)` 更新单个参数
4. `DataManager` 发送 `parameters_changed` 信号
5. `ProjectManager` 接收信号，更新修改状态

#### 进一步优化的仿真请求数据流向
1. 用户在 `ShipNoiseTab` 中设置仿真参数（参数已实时更新到DataManager）
2. 用户点击"开始仿真"按钮
3. `ShipNoiseTab` 验证参数有效性
4. `ShipNoiseTab` 发送 `simulation_requested` 信号（不再携带参数）
5. `SimulationUIManager` 接收信号，直接调用 `simulation_controller.simulate_ship_noise()`
6. `SimulationController` 从 `data_manager` 获取最新参数并执行仿真

### 4.2 仿真执行阶段

1. `SimulationController` 调用 `ship_noise_controller.start()`
2. `ShipNoiseController` 在后台线程中执行以下步骤：
   - 从 `data_manager` 获取参数
   - 创建 `ShipRadiated` 实例
   - 调用模型方法生成信号
   - 计算频谱和其他分析结果
   - 将结果存储到 `data_manager`
   - 发送 `simulation_completed` 信号

### 4.3 结果显示阶段

1. `ShipNoiseViewUpdater` 监听 `data_manager.results_changed` 信号
2. 当 `ship_noise` 模块的结果变更时，`ShipNoiseViewUpdater` 调用 `update_view()`
3. `ShipNoiseViewUpdater` 从 `data_manager` 获取结果数据
4. `ShipNoiseViewUpdater` 调用 `ship_noise_view` 的方法更新视图
5. `ShipNoiseView` 更新图表，显示时域信号和频谱图

### 4.4 项目保存阶段

#### 原有数据流向（已优化）
1. 用户点击"保存项目"按钮
2. `ProjectUIManager` 调用 `main_window.sync_ui_parameters_to_data_manager()`
3. `ProjectUIManager` 调用 `simulation_controller.save_project(file_path)`
4. `SimulationController` 调用 `project_manager.save_project(file_path)`
5. `ProjectManager` 从 `data_manager` 获取所有参数和结果
6. `ProjectManager` 将数据序列化为 JSON 格式
7. `ProjectManager` 将 JSON 数据保存到文件
8. `ProjectManager` 更新项目文件路径和修改状态
9. `ProjectManager` 发送 `modification_state_changed` 信号

#### 优化后的数据流向
1. 用户点击"保存项目"按钮或"另存为"按钮
2. `ProjectUIManager` 调用 `main_window.sync_ui_parameters_to_data_manager()`
3. `ProjectUIManager` 直接调用 `project_manager.save_project(file_path)`
4. `ProjectManager` 从 `data_manager` 获取所有参数和结果
5. `ProjectManager` 将数据序列化为 JSON 格式
6. `ProjectManager` 将 JSON 数据保存到文件
7. `ProjectManager` 更新项目文件路径
8. `ProjectManager` 调用 `reset_modified_state()` 重置修改状态
9. `ProjectManager` 发送 `project_saved` 信号

## 5. 开发规范

### 5.1 新模块开发

开发新模块时，应遵循以下步骤：

1. 在适当的目录中创建模块文件
2. 明确模块的职责和接口
3. 实现模块功能
4. 与现有系统集成
5. 编写单元测试
6. 更新文档

### 5.2 数据流设计

设计数据流时，应遵循以下原则：

1. 数据应通过 `SimulationDataManager` 进行共享
2. 参数应从 UI 组件直接更新到数据管理器
3. UI 控件应直接持有数据管理器引用，实现参数的细粒度更新
4. 结果应从模型流向数据管理器
5. 视图更新应通过监听数据管理器的信号触发
6. 避免组件之间的直接数据传递
7. 使用细粒度参数更新方法（update_parameter和update_nested_parameter）而非整体更新

### 5.3 线程安全

确保线程安全的原则：

1. 在后台线程中执行耗时操作
2. 使用锁保护共享数据
3. 使用信号和槽机制进行线程间通信
4. 避免在 UI 线程中执行耗时操作

### 5.4 错误处理

错误处理原则：

1. 使用异常处理机制捕获和处理错误
2. 在 UI 层显示友好的错误消息
3. 记录详细的错误信息
4. 确保错误不会导致应用程序崩溃

## 6. 结论

通过这次重构，系统架构变得更加清晰和模块化，各个组件职责明确，便于维护和扩展。后续开发应遵循本文档中的架构和规范，确保系统的可维护性和可扩展性。
