# -*- coding: utf-8 -*-
"""
项目UI管理器

负责处理项目相关的UI操作，如新建、打开、保存项目等。
"""

import os
from PyQt5.QtWidgets import QMessageBox, QFileDialog


class ProjectUIManager:
    """
    项目UI管理器

    负责处理项目相关的UI操作，如新建、打开、保存项目等。
    """

    def __init__(self, main_window, project_manager, data_manager):
        """
        初始化项目UI管理器

        Args:
            main_window: 主窗口实例
            project_manager: 项目管理器实例
            data_manager: 数据管理器实例
        """
        self.main_window = main_window
        self.project_manager = project_manager
        self.data_manager = data_manager

        # 连接项目管理器的信号
        self.project_manager.modification_state_changed.connect(self.on_modification_state_changed)

    def on_new_project(self):
        """
        新建项目事件处理
        """
        # 检查是否有未保存的修改
        if self.confirm_save_if_modified():
            return  # 用户取消了操作

        # 完全重置项目状态
        self.project_manager.reset_project()

        # 重置UI参数到默认值
        self.main_window.ship_noise_tab.reset_parameters()
        self.main_window.propagation_tab.reset_parameters()
        self.main_window.integrated_tab.reset_parameters()

        # 同步重置后的参数到数据管理器
        self.main_window.sync_ui_parameters_to_data_manager()

        # 更新UI以反映重置后的状态
        self.main_window.update_ui_from_data_manager()

        # 重置修改状态（在UI更新后）
        self.project_manager.reset_modified_state()

        # 更新状态栏
        self.main_window.statusBar().showMessage("新建项目")

    def on_open_project(self):
        """
        打开项目事件处理
        """
        # 检查是否有未保存的修改
        if self.confirm_save_if_modified():
            return  # 用户取消了操作

        file_path, _ = QFileDialog.getOpenFileName(
            self.main_window, "打开项目", "", "项目文件 (*.json);;所有文件 (*)"
        )
        if file_path:
            try:
                # 加载项目
                self.project_manager.load_project(file_path)

                # 更新UI以反映加载的参数
                self.main_window.update_ui_from_data_manager()

                # 重置修改状态（在UI更新后）
                self.project_manager.reset_modified_state()

                # 更新状态栏
                self.main_window.statusBar().showMessage(f"打开项目: {file_path}")
            except Exception as e:
                # 异常处理
                QMessageBox.critical(self.main_window, "打开错误", f"打开项目时发生错误: {str(e)}")
                self.main_window.statusBar().showMessage("打开项目失败")

    def on_save_project(self):
        """
        保存项目事件处理
        """
        # 同步UI参数到数据管理器
        self.main_window.sync_ui_parameters_to_data_manager()

        # 检查项目是否已保存
        if self.project_manager.is_project_saved():
            # 如果已保存，直接保存到当前文件
            file_path = self.project_manager.get_project_file_path()
            try:
                self.project_manager.save_project(file_path)
                self.main_window.statusBar().showMessage(f"项目已保存到: {file_path}")
            except Exception as e:
                # 异常处理
                QMessageBox.critical(self.main_window, "保存错误", f"保存项目时发生错误: {str(e)}")
                self.main_window.statusBar().showMessage("保存项目失败")
        else:
            # 如果未保存，调用另存为对话框
            self.on_save_project_as()

    def on_save_project_as(self):
        """
        另存为事件处理
        """
        # 同步UI参数到数据管理器
        self.main_window.sync_ui_parameters_to_data_manager()

        file_path, _ = QFileDialog.getSaveFileName(
            self.main_window, "另存为", "", "项目文件 (*.json);;所有文件 (*)"
        )
        if file_path:
            # 确保文件扩展名为.json
            if not file_path.endswith('.json'):
                file_path += '.json'

            try:
                # 保存项目
                self.project_manager.save_project(file_path)
                self.main_window.statusBar().showMessage(f"项目已保存为: {file_path}")
            except Exception as e:
                # 异常处理
                QMessageBox.critical(self.main_window, "保存错误", f"保存项目时发生错误: {str(e)}")
                self.main_window.statusBar().showMessage("保存项目失败")

    def on_export_signals(self):
        """
        导出信号事件处理
        """
        try:
            # 导入信号导出对话框
            from src.ui.dialogs.signal_export_dialog import SignalExportDialog

            # 创建并显示对话框
            dialog = SignalExportDialog(self.main_window, self.data_manager)
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self.main_window, "导出错误", f"打开导出对话框时发生错误: {str(e)}")
            self.main_window.statusBar().showMessage("打开导出对话框失败")

    def confirm_save_if_modified(self):
        """
        如果有未保存的修改，询问用户是否保存

        Returns:
            bool: 如果用户取消了操作，返回True；否则返回False
        """
        if self.project_manager.is_modified():
            # 询问用户是否保存
            reply = QMessageBox.question(
                self.main_window, "保存修改",
                "项目已修改，是否保存更改？",
                QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel,
                QMessageBox.Save
            )

            if reply == QMessageBox.Save:
                # 保存项目
                self.on_save_project()
                # 如果保存后仍然有未保存的修改（可能用户取消了保存对话框）
                if self.project_manager.is_modified():
                    return True  # 用户取消了操作
            elif reply == QMessageBox.Cancel:
                # 用户取消了操作
                return True

        return False  # 可以继续操作

    def on_modification_state_changed(self, is_modified):
        """
        处理修改状态变更

        Args:
            is_modified (bool): 是否已修改
        """
        try:
            # 更新窗口标题，显示修改状态
            title = "面向复杂水声环境的噪声仿真系统"

            # 获取项目文件路径
            file_path = self.project_manager.get_project_file_path()
            if file_path:
                # 提取文件名（不包含扩展名）
                file_name = os.path.splitext(os.path.basename(file_path))[0]
                title = f"{file_name} - {title}"

            # 如果已修改，添加标记
            if is_modified:
                title = f"*{title}"

            self.main_window.setWindowTitle(title)
        except Exception as e:
            print(f"Error updating window title: {str(e)}")

    def handle_close_event(self, event):
        """
        处理窗口关闭事件

        Args:
            event: 关闭事件

        Returns:
            bool: 如果应该继续关闭，返回True；否则返回False
        """
        # 检查是否有未保存的修改
        if self.project_manager.is_modified():
            # 询问用户是否保存
            reply = QMessageBox.question(
                self.main_window, "保存修改",
                "项目已修改，是否保存更改？",
                QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel,
                QMessageBox.Save
            )

            if reply == QMessageBox.Save:
                # 保存项目
                self.on_save_project()
                # 如果保存后仍然有未保存的修改（可能用户取消了保存对话框）
                if self.project_manager.is_modified():
                    event.ignore()  # 忽略关闭事件
                    return False
            elif reply == QMessageBox.Cancel:
                # 取消关闭
                event.ignore()
                return False

        return True  # 可以继续关闭
