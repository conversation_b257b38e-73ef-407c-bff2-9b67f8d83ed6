# 全局参数设置文档

## 1. 概述

为了确保不同仿真模块之间的信号可以正确叠加，系统引入了全局参数设置机制。关键的全局参数包括采样率(fs)，该参数对所有仿真模块统一生效，确保生成的信号具有相同的采样率。

## 2. 全局参数列表

当前系统支持的全局参数包括：

| 参数名 | 描述 | 默认值 | 单位 |
|-------|------|-------|-----|
| fs | 采样率 | 44100 | Hz |

## 3. 全局参数设置方式

### 3.1 通过系统设置对话框

用户可以通过以下方式访问系统参数设置对话框：

1. 在菜单栏中选择"编辑" -> "系统参数设置"
2. 在工具栏中点击"系统参数"按钮

在系统参数设置对话框中，用户可以：
- 设置采样率(fs)，范围为8000Hz至192000Hz

### 3.2 通过项目文件

全局参数会随项目一起保存和加载。当用户保存项目时，全局参数会被保存到项目文件中；当用户加载项目时，全局参数会从项目文件中恢复。

## 4. 全局参数的应用

### 4.1 船舶辐射噪声模块

船舶辐射噪声模块使用全局参数和模块参数生成时域信号：
- 采样率(fs)决定了信号的频率分辨率和最高可表示频率(Nyquist频率为fs/2)
- 持续时间(duration)作为模块参数，决定了信号的长度和频率分辨率(δf = 1/duration)

### 4.2 海洋环境噪声模块

海洋环境噪声模块同样使用全局参数和模块参数生成时域信号：
- 采样率(fs)决定了信号的频率分辨率和最高可表示频率
- 持续时间(duration)作为模块参数，决定了信号的长度

### 4.3 信号叠加

当不同模块使用相同的采样率时，可以进行信号叠加操作。如果持续时间不同，需要进行适当的处理：

```python
# 假设从不同模块获取的信号
ship_noise_signal = data_manager.get_result('ship_noise', 'total_signal')
ambient_noise_signal = data_manager.get_result('ambient_noise', 'ambient_signal')

# 获取信号长度
ship_length = len(ship_noise_signal)
ambient_length = len(ambient_noise_signal)

# 如果长度不同，需要进行处理
if ship_length != ambient_length:
    # 方法1：截断较长的信号
    min_length = min(ship_length, ambient_length)
    ship_noise_signal = ship_noise_signal[:min_length]
    ambient_noise_signal = ambient_noise_signal[:min_length]

    # 方法2：对较短的信号进行零填充
    # max_length = max(ship_length, ambient_length)
    # if ship_length < max_length:
    #     ship_noise_signal = np.pad(ship_noise_signal, (0, max_length - ship_length))
    # if ambient_length < max_length:
    #     ambient_noise_signal = np.pad(ambient_noise_signal, (0, max_length - ambient_length))

# 叠加信号
combined_signal = ship_noise_signal + ambient_noise_signal
```

## 5. 技术实现

### 5.1 数据管理器中的全局参数

全局参数存储在`SimulationDataManager`的`_global_params`字典中：

```python
self._global_params = {
    'fs': 44100,        # 默认采样率 (Hz)
}
```

### 5.2 模块参数中的持续时间

持续时间作为模块参数存储在`SimulationDataManager`的`_parameters`字典中：

```python
self._parameters = {
    'ship_noise': {
        'duration': 5.0,     # 默认持续时间 (s)
        # 其他参数...
    },
    'ambient_noise': {
        'duration': 5.0,     # 默认持续时间 (s)
        # 其他参数...
    },
    # 其他模块...
}
```

### 5.3 访问参数

控制器和模型通过以下方式访问参数：

```python
# 获取全局参数
fs = data_manager.get_global_param('fs')

# 获取模块参数
params = data_manager.get_parameters('ship_noise')
duration = params.get('duration', 5.0)  # 提供默认值
```

### 5.4 设置参数

系统设置对话框和模块标签页通过以下方式设置参数：

```python
# 设置全局参数
data_manager.set_global_param('fs', new_fs_value)

# 设置模块参数
data_manager.update_parameter('ship_noise', 'duration', new_duration_value)
```

## 6. 注意事项

1. **采样率范围**：采样率有合理的取值范围，超出范围的值可能导致内存问题或计算精度问题
2. **采样率一致性**：确保所有模块使用相同的采样率，以便信号可以正确处理
3. **持续时间设置**：不同模块可以使用不同的持续时间，但在信号叠加时需要进行适当处理
4. **项目兼容性**：更改参数后，之前生成的仿真结果可能不再有效，需要重新执行仿真
5. **性能考虑**：较高的采样率和较长的持续时间会增加内存使用和计算时间
6. **频率分辨率**：系统计算功率谱密度时默认采用1Hz的频率分辨率，与信号持续时间无关
