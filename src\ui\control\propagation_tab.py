# -*- coding: utf-8 -*-
"""
声传播环境标签页

用于设置声传播环境的参数和控制仿真
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout,
    QLabel, QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox,
    QPushButton, QGroupBox, QCheckBox, QFileDialog, QTableWidget,
    QTableWidgetItem, QHeaderView, QScrollArea, QTabWidget,
    QRadioButton, QButtonGroup, QProgressBar, QFrame
)
from PyQt5.QtCore import pyqtSignal, Qt
import numpy as np
import os
import multiprocessing

# 导入对话框
from src.ui.dialogs.ssp_dialog import SSPDialog
from src.ui.dialogs.bathymetry_dialog import BathymetryDialog
from src.ui.dialogs.directivity_dialog import DirectivityDialog
from src.ui.dialogs.array_elements_dialog import ArrayElementsDialog

# 定义插值类型常量
LINEAR = 'linear'
SPLINE = 'spline'
CURVILINEAR = 'curvilinear'


class PropagationTab(QWidget):
    """
    声传播环境标签页

    用于设置声传播环境的参数和控制仿真
    """

    # 自定义信号
    simulation_requested = pyqtSignal(str, str)  # 仿真请求信号，参数为模块名称和计算类型
    stop_requested = pyqtSignal()  # 停止仿真请求信号

    def __init__(self, data_manager=None, parent=None):
        """
        初始化声传播环境标签页

        Args:
            data_manager: 数据管理器实例
            parent: 父窗口
        """
        super().__init__(parent)

        # 保存数据管理器引用
        self.data_manager = data_manager

        # 创建布局
        self.init_ui()

        # 从数据管理器加载参数
        self.load_params_from_data_manager()

    def init_ui(self):
        """
        初始化UI
        """
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        # 创建各个分组
        self.create_environment_group(scroll_layout)
        self.create_computation_group(scroll_layout)
        self.create_broadband_group(scroll_layout)

        # 设置滚动区域
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)

        # 初始化控件状态
        self.on_depth_type_changed()
        self.on_ssp_type_changed()
        self.on_directivity_changed(Qt.Unchecked)
        self.on_auto_beam_changed(Qt.Checked)
        self.on_freq_mode_changed()

        # 连接控件信号
        self.connect_signals()

    def create_environment_group(self, parent_layout):
        """
        创建环境设置组

        Args:
            parent_layout: 父布局
        """
        # 创建分组
        group = QGroupBox("环境设置 (Environment Setup)")
        layout = QVBoxLayout()

        # 基本参数组
        basic_group = QGroupBox("基本参数")
        basic_form = QFormLayout()

        # 仿真名称
        self.simulation_name_edit = QLineEdit("默认仿真")
        basic_form.addRow("仿真名称:", self.simulation_name_edit)

        # 探测频率
        self.probing_freq_spin = QDoubleSpinBox()
        self.probing_freq_spin.setRange(1, 100000)
        self.probing_freq_spin.setValue(1000)
        self.probing_freq_spin.setSuffix(" Hz")
        basic_form.addRow("探测频率:", self.probing_freq_spin)

        basic_group.setLayout(basic_form)
        layout.addWidget(basic_group)

        # 水体设置组
        water_group = QGroupBox("水体设置")
        water_layout = QVBoxLayout()

        # 声速类型
        ssp_type_layout = QVBoxLayout()
        ssp_type_label = QLabel("声速类型:")
        ssp_type_layout.addWidget(ssp_type_label)

        self.ssp_type_group = QButtonGroup(self)

        self.ssp_constant_radio = QRadioButton("恒定声速")
        self.ssp_constant_radio.setChecked(True)
        self.ssp_constant_radio.toggled.connect(self.on_ssp_type_changed)
        self.ssp_type_group.addButton(self.ssp_constant_radio)
        ssp_type_layout.addWidget(self.ssp_constant_radio)

        self.ssp_depth_radio = QRadioButton("深度依赖")
        self.ssp_depth_radio.toggled.connect(self.on_ssp_type_changed)
        self.ssp_type_group.addButton(self.ssp_depth_radio)
        ssp_type_layout.addWidget(self.ssp_depth_radio)

        water_layout.addLayout(ssp_type_layout)

        # 恒定声速设置
        self.constant_ssp_layout = QFormLayout()
        self.constant_ssp_spin = QDoubleSpinBox()
        self.constant_ssp_spin.setRange(1300, 1700)
        self.constant_ssp_spin.setValue(1500)
        self.constant_ssp_spin.setSuffix(" m/s")
        self.constant_ssp_layout.addRow("声速值:", self.constant_ssp_spin)
        water_layout.addLayout(self.constant_ssp_layout)

        # 深度依赖声速设置
        self.depth_ssp_layout = QVBoxLayout()

        # 编辑按钮
        self.edit_ssp_button = QPushButton("编辑声速剖面")
        self.edit_ssp_button.clicked.connect(self.edit_ssp)
        self.depth_ssp_layout.addWidget(self.edit_ssp_button)

        # 数据预览标签
        self.ssp_preview_label = QLabel("未设置声速剖面数据")
        self.depth_ssp_layout.addWidget(self.ssp_preview_label)

        # 插值方法
        interp_layout = QFormLayout()
        self.ssp_interp_combo = QComboBox()
        self.ssp_interp_combo.addItems(["线性", "样条"])
        self.ssp_interp_combo.setCurrentIndex(1)  # 默认选择样条
        interp_layout.addRow("声速插值方法:", self.ssp_interp_combo)
        self.depth_ssp_layout.addLayout(interp_layout)

        water_layout.addLayout(self.depth_ssp_layout)

        # 初始隐藏深度依赖设置
        self.depth_ssp_layout.setEnabled(False)

        water_group.setLayout(water_layout)
        layout.addWidget(water_group)

        # 海底设置组
        bottom_group = QGroupBox("海底设置")
        bottom_layout = QVBoxLayout()

        # 水深设置组
        depth_group = QGroupBox("水深设置")
        depth_layout = QVBoxLayout()

        # 水深类型
        depth_type_layout = QVBoxLayout()
        depth_type_label = QLabel("水深类型:")
        depth_type_layout.addWidget(depth_type_label)

        self.depth_type_group = QButtonGroup(self)

        self.depth_flat_radio = QRadioButton("平坦水深")
        self.depth_flat_radio.setChecked(True)
        self.depth_flat_radio.toggled.connect(self.on_depth_type_changed)
        self.depth_type_group.addButton(self.depth_flat_radio)
        depth_type_layout.addWidget(self.depth_flat_radio)

        self.depth_range_radio = QRadioButton("距离依赖")
        self.depth_range_radio.toggled.connect(self.on_depth_type_changed)
        self.depth_type_group.addButton(self.depth_range_radio)
        depth_type_layout.addWidget(self.depth_range_radio)

        depth_layout.addLayout(depth_type_layout)

        # 平坦水深设置
        self.flat_depth_layout = QFormLayout()
        self.water_depth_spin = QDoubleSpinBox()
        self.water_depth_spin.setRange(1, 11000)
        self.water_depth_spin.setValue(100)
        self.water_depth_spin.setSuffix(" m")
        self.flat_depth_layout.addRow("水深值:", self.water_depth_spin)
        depth_layout.addLayout(self.flat_depth_layout)

        # 随距离变化水深设置
        self.range_depth_layout = QVBoxLayout()

        # 编辑按钮
        self.edit_bathymetry_button = QPushButton("编辑水深")
        self.edit_bathymetry_button.clicked.connect(self.edit_bathymetry)
        self.range_depth_layout.addWidget(self.edit_bathymetry_button)

        # 数据预览标签
        self.bathymetry_preview_label = QLabel("未设置水深数据")
        self.range_depth_layout.addWidget(self.bathymetry_preview_label)

        # 插值方法
        self.bathymetry_interp_layout = QFormLayout()
        self.bathymetry_interp_combo = QComboBox()
        self.bathymetry_interp_combo.addItems(["线性", "曲线"])
        self.bathymetry_interp_layout.addRow("水深插值方法:", self.bathymetry_interp_combo)
        self.range_depth_layout.addLayout(self.bathymetry_interp_layout)

        depth_layout.addLayout(self.range_depth_layout)

        # 初始隐藏随距离变化设置
        self.range_depth_layout.setEnabled(False)

        # 设置水深组布局
        depth_group.setLayout(depth_layout)
        bottom_layout.addWidget(depth_group)

        # 海底参数组
        bottom_params_group = QGroupBox("海底参数")
        bottom_params_layout = QFormLayout()

        # 海底声速
        self.bottom_speed_spin = QDoubleSpinBox()
        self.bottom_speed_spin.setRange(1000, 5000)
        self.bottom_speed_spin.setValue(1600)
        self.bottom_speed_spin.setSuffix(" m/s")
        bottom_params_layout.addRow("海底声速:", self.bottom_speed_spin)

        # 海底密度
        self.bottom_density_spin = QDoubleSpinBox()
        self.bottom_density_spin.setRange(1000, 4000)
        self.bottom_density_spin.setValue(1600)
        self.bottom_density_spin.setSuffix(" kg/m³")
        bottom_params_layout.addRow("海底密度:", self.bottom_density_spin)

        # 海底吸收
        self.bottom_absorption_spin = QDoubleSpinBox()
        self.bottom_absorption_spin.setRange(0, 10)
        self.bottom_absorption_spin.setValue(0.1)
        self.bottom_absorption_spin.setSingleStep(0.1)
        self.bottom_absorption_spin.setSuffix(" dB/wavelength")
        bottom_params_layout.addRow("海底吸收:", self.bottom_absorption_spin)

        # 海底粗糙度
        self.bottom_roughness_spin = QDoubleSpinBox()
        self.bottom_roughness_spin.setRange(0, 10)
        self.bottom_roughness_spin.setValue(0)
        self.bottom_roughness_spin.setSingleStep(0.1)
        self.bottom_roughness_spin.setSuffix(" m rms")
        bottom_params_layout.addRow("海底粗糙度:", self.bottom_roughness_spin)

        # 设置海底参数组布局
        bottom_params_group.setLayout(bottom_params_layout)
        bottom_layout.addWidget(bottom_params_group)

        # 设置海底设置组布局
        bottom_group.setLayout(bottom_layout)
        layout.addWidget(bottom_group)

        # 声源设置组
        source_group = QGroupBox("声源设置")
        source_layout = QVBoxLayout()

        # 声源深度
        source_depth_form = QFormLayout()
        self.source_depth_spin = QDoubleSpinBox()
        self.source_depth_spin.setRange(0, 11000)
        self.source_depth_spin.setValue(50)
        self.source_depth_spin.setSuffix(" m")
        source_depth_form.addRow("声源深度:", self.source_depth_spin)
        source_layout.addLayout(source_depth_form)

        # 声源指向性
        self.directivity_check = QCheckBox("启用声源指向性")
        self.directivity_check.setChecked(False)
        self.directivity_check.stateChanged.connect(self.on_directivity_changed)
        source_layout.addWidget(self.directivity_check)

        # 声源指向性编辑按钮
        self.edit_directivity_button = QPushButton("编辑声源指向性")
        self.edit_directivity_button.clicked.connect(self.edit_directivity)
        self.edit_directivity_button.setEnabled(False)  # 初始禁用
        source_layout.addWidget(self.edit_directivity_button)

        # 声源指向性数据预览标签
        self.directivity_preview_label = QLabel("未设置声源指向性数据")
        self.directivity_preview_label.setEnabled(False)  # 初始禁用
        source_layout.addWidget(self.directivity_preview_label)

        source_group.setLayout(source_layout)
        layout.addWidget(source_group)

        # 接收器设置组
        receiver_group = QGroupBox("接收器设置")
        receiver_layout = QFormLayout()

        # 接收器深度
        self.receiver_depth_spin = QDoubleSpinBox()
        self.receiver_depth_spin.setRange(0, 11000)
        self.receiver_depth_spin.setValue(20)
        self.receiver_depth_spin.setSuffix(" m")
        receiver_layout.addRow("接收器深度:", self.receiver_depth_spin)

        # 接收器距离
        self.receiver_range_spin = QDoubleSpinBox()
        self.receiver_range_spin.setRange(1, 100000)
        self.receiver_range_spin.setValue(1000)
        self.receiver_range_spin.setSuffix(" m")
        receiver_layout.addRow("接收器距离:", self.receiver_range_spin)

        receiver_group.setLayout(receiver_layout)
        layout.addWidget(receiver_group)

        # 绘制按钮
        draw_buttons_layout = QHBoxLayout()

        self.draw_environment_button = QPushButton("绘制环境")
        self.draw_environment_button.clicked.connect(self.on_draw_environment)
        draw_buttons_layout.addWidget(self.draw_environment_button)

        self.draw_ssp_button = QPushButton("绘制声速剖面")
        self.draw_ssp_button.clicked.connect(self.on_draw_ssp)
        draw_buttons_layout.addWidget(self.draw_ssp_button)

        layout.addLayout(draw_buttons_layout)

        # 设置分组布局
        group.setLayout(layout)
        parent_layout.addWidget(group)

    # 声源与接收器设置已移至环境设置组内

    def create_computation_group(self, parent_layout):
        """
        创建计算与结果部分

        Args:
            parent_layout: 父布局
        """
        # 创建分组
        group = QGroupBox("计算与结果 (Computation & Results)")
        layout = QVBoxLayout()

        # 射线参数组
        ray_group = QGroupBox("射线参数")
        ray_layout = QVBoxLayout()

        # 角度范围
        angle_layout = QHBoxLayout()

        self.min_angle_spin = QDoubleSpinBox()
        self.min_angle_spin.setRange(-90, 90)
        self.min_angle_spin.setValue(-80)
        self.min_angle_spin.setSuffix(" deg")
        angle_layout.addWidget(QLabel("最小角度:"))
        angle_layout.addWidget(self.min_angle_spin)

        angle_layout.addSpacing(20)

        self.max_angle_spin = QDoubleSpinBox()
        self.max_angle_spin.setRange(-90, 90)
        self.max_angle_spin.setValue(80)
        self.max_angle_spin.setSuffix(" deg")
        angle_layout.addWidget(QLabel("最大角度:"))
        angle_layout.addWidget(self.max_angle_spin)

        ray_layout.addLayout(angle_layout)

        # 声束数量
        beam_layout = QVBoxLayout()

        # 自动/手动选择
        self.auto_beam_check = QCheckBox("自动设置声束数量")
        self.auto_beam_check.setChecked(True)
        self.auto_beam_check.stateChanged.connect(self.on_auto_beam_changed)
        beam_layout.addWidget(self.auto_beam_check)

        # 手动设置
        beam_count_layout = QFormLayout()
        self.beam_count_spin = QSpinBox()
        self.beam_count_spin.setRange(1, 10000)
        self.beam_count_spin.setValue(50)
        self.beam_count_spin.setEnabled(False)  # 初始禁用
        beam_count_layout.addRow("声束数量:", self.beam_count_spin)
        beam_layout.addLayout(beam_count_layout)

        ray_layout.addLayout(beam_layout)

        ray_group.setLayout(ray_layout)
        layout.addWidget(ray_group)

        # 声线追踪组
        ray_trace_group = QGroupBox("声线追踪")
        ray_trace_layout = QVBoxLayout()

        # 声线类型
        ray_type_group = QGroupBox("声线类型")
        ray_type_layout = QVBoxLayout()

        self.ray_type_group = QButtonGroup(self)

        self.normal_ray_radio = QRadioButton("普通声线")
        self.normal_ray_radio.setChecked(True)
        self.ray_type_group.addButton(self.normal_ray_radio)
        ray_type_layout.addWidget(self.normal_ray_radio)

        self.eigenray_radio = QRadioButton("本征射线")
        self.ray_type_group.addButton(self.eigenray_radio)
        ray_type_layout.addWidget(self.eigenray_radio)

        ray_type_group.setLayout(ray_type_layout)
        ray_trace_layout.addWidget(ray_type_group)

        # 计算按钮
        self.compute_rays_button = QPushButton("计算声线")
        self.compute_rays_button.clicked.connect(self.on_compute_rays)
        ray_trace_layout.addWidget(self.compute_rays_button)

        ray_trace_group.setLayout(ray_trace_layout)
        layout.addWidget(ray_trace_group)

        # 到达结构组
        arrival_group = QGroupBox("到达结构")
        arrival_layout = QVBoxLayout()

        arrival_info = QLabel("用于信道建模和冲击响应生成")
        arrival_layout.addWidget(arrival_info)

        # 计算按钮
        self.compute_arrivals_button = QPushButton("计算到达结构")
        self.compute_arrivals_button.clicked.connect(self.on_compute_arrivals)
        arrival_layout.addWidget(self.compute_arrivals_button)

        arrival_group.setLayout(arrival_layout)
        layout.addWidget(arrival_group)

        # 传播损失组
        tl_group = QGroupBox("传播损失")
        tl_layout = QVBoxLayout()

        # 传播损失模式
        tl_mode_group = QGroupBox("传播损失模式")
        tl_mode_layout = QVBoxLayout()

        self.tl_mode_group = QButtonGroup(self)

        self.coherent_tl_radio = QRadioButton("相干 (Coherent)")
        self.coherent_tl_radio.setChecked(True)
        self.tl_mode_group.addButton(self.coherent_tl_radio)
        tl_mode_layout.addWidget(self.coherent_tl_radio)

        self.incoherent_tl_radio = QRadioButton("非相干 (Incoherent)")
        self.tl_mode_group.addButton(self.incoherent_tl_radio)
        tl_mode_layout.addWidget(self.incoherent_tl_radio)

        self.semicoherent_tl_radio = QRadioButton("半相干 (Semi-coherent)")
        self.tl_mode_group.addButton(self.semicoherent_tl_radio)
        tl_mode_layout.addWidget(self.semicoherent_tl_radio)

        tl_mode_group.setLayout(tl_mode_layout)
        tl_layout.addWidget(tl_mode_group)

        # 接收器网格设置
        grid_group = QGroupBox("接收器网格设置")
        grid_layout = QVBoxLayout()

        # 深度范围
        depth_range_layout = QHBoxLayout()

        self.depth_start_spin = QDoubleSpinBox()
        self.depth_start_spin.setRange(0, 11000)
        self.depth_start_spin.setValue(0)
        self.depth_start_spin.setSuffix(" m")
        depth_range_layout.addWidget(QLabel("起始深度:"))
        depth_range_layout.addWidget(self.depth_start_spin)

        self.depth_end_spin = QDoubleSpinBox()
        self.depth_end_spin.setRange(0, 11000)
        self.depth_end_spin.setValue(100)
        self.depth_end_spin.setSuffix(" m")
        depth_range_layout.addWidget(QLabel("结束深度:"))
        depth_range_layout.addWidget(self.depth_end_spin)

        self.depth_step_spin = QDoubleSpinBox()
        self.depth_step_spin.setRange(0.1, 1000)
        self.depth_step_spin.setValue(5)
        self.depth_step_spin.setSuffix(" m")
        depth_range_layout.addWidget(QLabel("步长:"))
        depth_range_layout.addWidget(self.depth_step_spin)

        grid_layout.addLayout(depth_range_layout)

        # 距离范围
        range_range_layout = QHBoxLayout()

        self.range_start_spin = QDoubleSpinBox()
        self.range_start_spin.setRange(0, 100000)
        self.range_start_spin.setValue(0)
        self.range_start_spin.setSuffix(" m")
        range_range_layout.addWidget(QLabel("起始距离:"))
        range_range_layout.addWidget(self.range_start_spin)

        self.range_end_spin = QDoubleSpinBox()
        self.range_end_spin.setRange(0, 100000)
        self.range_end_spin.setValue(1000)
        self.range_end_spin.setSuffix(" m")
        range_range_layout.addWidget(QLabel("结束距离:"))
        range_range_layout.addWidget(self.range_end_spin)

        self.range_step_spin = QDoubleSpinBox()
        self.range_step_spin.setRange(0.1, 10000)
        self.range_step_spin.setValue(50)
        self.range_step_spin.setSuffix(" m")
        range_range_layout.addWidget(QLabel("步长:"))
        range_range_layout.addWidget(self.range_step_spin)

        grid_layout.addLayout(range_range_layout)

        # 预览
        preview_layout = QHBoxLayout()
        self.grid_preview_label = QLabel("接收器数量: 0")
        preview_layout.addWidget(self.grid_preview_label)
        self.update_grid_preview_button = QPushButton("更新预览")
        self.update_grid_preview_button.clicked.connect(self.update_grid_preview)
        preview_layout.addWidget(self.update_grid_preview_button)
        grid_layout.addLayout(preview_layout)

        grid_group.setLayout(grid_layout)
        tl_layout.addWidget(grid_group)

        # 计算按钮
        self.compute_tl_button = QPushButton("计算传播损失")
        self.compute_tl_button.clicked.connect(self.on_compute_tl)
        tl_layout.addWidget(self.compute_tl_button)

        # 传播损失显示范围设置
        tl_range_group = QGroupBox("传播损失显示范围")
        tl_range_layout = QVBoxLayout()

        # 范围设置
        range_layout = QHBoxLayout()

        # 最小值
        self.tl_min_spin = QDoubleSpinBox()
        self.tl_min_spin.setRange(0, 200)
        self.tl_min_spin.setValue(40)
        self.tl_min_spin.setSuffix(" dB")
        range_layout.addWidget(QLabel("最小值:"))
        range_layout.addWidget(self.tl_min_spin)

        # 最大值
        self.tl_max_spin = QDoubleSpinBox()
        self.tl_max_spin.setRange(0, 200)
        self.tl_max_spin.setValue(100)
        self.tl_max_spin.setSuffix(" dB")
        range_layout.addWidget(QLabel("最大值:"))
        range_layout.addWidget(self.tl_max_spin)

        tl_range_layout.addLayout(range_layout)

        # 应用按钮
        self.apply_tl_range_button = QPushButton("按照设置范围调整")
        self.apply_tl_range_button.clicked.connect(self.apply_tl_range)
        tl_range_layout.addWidget(self.apply_tl_range_button)

        tl_range_group.setLayout(tl_range_layout)
        tl_layout.addWidget(tl_range_group)

        tl_group.setLayout(tl_layout)
        layout.addWidget(tl_group)

        # 高级选项组
        advanced_group = QGroupBox("高级选项")
        advanced_layout = QVBoxLayout()

        self.debug_mode_check = QCheckBox("调试模式（保留临时文件）")
        self.debug_mode_check.setChecked(False)
        advanced_layout.addWidget(self.debug_mode_check)

        advanced_group.setLayout(advanced_layout)
        layout.addWidget(advanced_group)

        # 设置分组布局
        group.setLayout(layout)
        parent_layout.addWidget(group)

    def create_broadband_group(self, parent_layout):
        """
        创建信道数据制备部分

        Args:
            parent_layout: 父布局
        """
        # 创建分组
        group = QGroupBox("信道数据制备 (Channel Data Preparation)")
        layout = QVBoxLayout()

        # 阵元位置设置组
        array_group = QGroupBox("阵元位置设置")
        array_layout = QVBoxLayout()

        # 编辑按钮
        self.edit_array_button = QPushButton("编辑阵元位置")
        self.edit_array_button.clicked.connect(self.edit_array_elements)
        array_layout.addWidget(self.edit_array_button)

        # 数据预览标签
        self.array_preview_label = QLabel("未设置阵元位置数据")
        array_layout.addWidget(self.array_preview_label)

        array_group.setLayout(array_layout)
        layout.addWidget(array_group)

        # 频率设置组
        freq_group = QGroupBox("频率设置")
        freq_layout = QVBoxLayout()

        # 频率设置方式
        self.freq_mode_group = QButtonGroup(self)

        self.freq_range_radio = QRadioButton("频率范围")
        self.freq_range_radio.setChecked(True)
        self.freq_range_radio.toggled.connect(self.on_freq_mode_changed)
        self.freq_mode_group.addButton(self.freq_range_radio)
        freq_layout.addWidget(self.freq_range_radio)

        self.freq_list_radio = QRadioButton("自定义列表")
        self.freq_list_radio.toggled.connect(self.on_freq_mode_changed)
        self.freq_mode_group.addButton(self.freq_list_radio)
        freq_layout.addWidget(self.freq_list_radio)

        # 频率范围参数
        self.freq_range_layout = QFormLayout()

        self.freq_start_spin = QDoubleSpinBox()
        self.freq_start_spin.setRange(1, 100000)
        self.freq_start_spin.setValue(250)
        self.freq_start_spin.setSuffix(" Hz")
        self.freq_range_layout.addRow("起始频率:", self.freq_start_spin)

        self.freq_end_spin = QDoubleSpinBox()
        self.freq_end_spin.setRange(1, 100000)
        self.freq_end_spin.setValue(4750)
        self.freq_end_spin.setSuffix(" Hz")
        self.freq_range_layout.addRow("结束频率:", self.freq_end_spin)

        self.freq_step_spin = QDoubleSpinBox()
        self.freq_step_spin.setRange(0.1, 5000)
        self.freq_step_spin.setValue(500)
        self.freq_step_spin.setSuffix(" Hz")
        self.freq_range_layout.addRow("频率步长:", self.freq_step_spin)

        freq_layout.addLayout(self.freq_range_layout)

        # 自定义列表参数
        self.freq_list_layout = QVBoxLayout()

        freq_list_label = QLabel("频率列表 (空格分隔):")
        self.freq_list_layout.addWidget(freq_list_label)

        self.freq_list_edit = QLineEdit("250 750 1250 1750 2250 2750 3250 3750 4250 4750")
        self.freq_list_layout.addWidget(self.freq_list_edit)

        freq_layout.addLayout(self.freq_list_layout)

        # 初始隐藏自定义列表
        self.freq_list_layout.setEnabled(False)

        freq_group.setLayout(freq_layout)
        layout.addWidget(freq_group)

        # 输出设置组
        output_group = QGroupBox("输出设置")
        output_layout = QVBoxLayout()

        # 输出目录
        output_dir_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit(os.path.join(os.getcwd(), "channel_data"))
        output_dir_layout.addWidget(self.output_dir_edit)

        self.browse_output_button = QPushButton("浏览...")
        self.browse_output_button.clicked.connect(self.browse_output_dir)
        output_dir_layout.addWidget(self.browse_output_button)

        output_layout.addLayout(output_dir_layout)

        # 并行计算设置
        parallel_layout = QFormLayout()

        # 最大工作进程
        self.max_workers_spin = QSpinBox()
        self.max_workers_spin.setRange(1, multiprocessing.cpu_count())
        # 使用全局参数中的cpu_cores作为默认值
        cpu_cores = self.data_manager.get_global_param('cpu_cores') if self.data_manager else multiprocessing.cpu_count()
        self.max_workers_spin.setValue(cpu_cores)
        parallel_layout.addRow("最大工作进程:", self.max_workers_spin)

        output_layout.addLayout(parallel_layout)
        output_group.setLayout(output_layout)
        layout.addWidget(output_group)

        # 控制按钮
        control_layout = QVBoxLayout()

        # 进度显示
        self.progress_layout = QVBoxLayout()
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_layout.addWidget(self.progress_bar)

        self.status_label = QLabel("就绪")
        self.progress_layout.addWidget(self.status_label)

        control_layout.addLayout(self.progress_layout)

        # 按钮组
        buttons_layout = QHBoxLayout()

        self.start_channel_button = QPushButton("开始制备信道数据")
        self.start_channel_button.clicked.connect(self.on_start_channel_preparation)
        buttons_layout.addWidget(self.start_channel_button)

        self.stop_channel_button = QPushButton("停止计算")
        self.stop_channel_button.clicked.connect(self.on_stop_channel_preparation)
        self.stop_channel_button.setEnabled(False)
        buttons_layout.addWidget(self.stop_channel_button)

        control_layout.addLayout(buttons_layout)

        layout.addLayout(control_layout)

        # 添加说明标签
        info_label = QLabel("信道数据制备用于计算指定阵元位置和频率下的声传播特性，生成的数据可用于综合仿真")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 设置分组布局
        group.setLayout(layout)
        parent_layout.addWidget(group)

    # 事件处理方法
    def load_params_from_data_manager(self):
        """
        从数据管理器加载参数并更新UI控件
        """
        if not self.data_manager:
            return

        try:
            # 获取参数
            params = self.data_manager.get_parameters('propagation')
            if not params:
                return

            # 更新基本参数
            if 'name' in params:
                self.simulation_name_edit.setText(params['name'])

            if 'frequency' in params:
                self.probing_freq_spin.setValue(params['frequency'])

            # 更新水深设置
            if 'depth_type' in params:
                if params['depth_type'] == 'flat':
                    self.depth_flat_radio.setChecked(True)
                    if 'depth' in params:
                        self.water_depth_spin.setValue(params['depth'])
                else:
                    self.depth_range_radio.setChecked(True)
                    if 'depth_interp' in params:
                        index = 1 if params['depth_interp'] == CURVILINEAR else 0
                        self.bathymetry_interp_combo.setCurrentIndex(index)

                    # 更新水深数据预览
                    bathymetry_data = params.get('bathymetry_data', [])
                    if bathymetry_data:
                        self.bathymetry_preview_label.setText(f"已设置 {len(bathymetry_data)} 个水深数据点")
                    else:
                        self.bathymetry_preview_label.setText("未设置水深数据")

            # 更新声速设置
            if 'ssp_type' in params:
                if params['ssp_type'] == 'constant':
                    self.ssp_constant_radio.setChecked(True)
                    if 'soundspeed' in params:
                        self.constant_ssp_spin.setValue(params['soundspeed'])
                else:
                    self.ssp_depth_radio.setChecked(True)
                    if 'soundspeed_interp' in params:
                        index = 1 if params['soundspeed_interp'] == SPLINE else 0
                        self.ssp_interp_combo.setCurrentIndex(index)

                    # 更新声速剖面数据预览
                    ssp_data = params.get('ssp_data', [])
                    if ssp_data:
                        self.ssp_preview_label.setText(f"已设置 {len(ssp_data)} 个声速剖面数据点")
                    else:
                        self.ssp_preview_label.setText("未设置声速剖面数据")

            # 更新海底参数
            if 'bottom_soundspeed' in params:
                self.bottom_speed_spin.setValue(params['bottom_soundspeed'])
            if 'bottom_density' in params:
                self.bottom_density_spin.setValue(params['bottom_density'])
            if 'bottom_absorption' in params:
                self.bottom_absorption_spin.setValue(params['bottom_absorption'])
            if 'bottom_roughness' in params:
                self.bottom_roughness_spin.setValue(params['bottom_roughness'])

            # 更新声源和接收器设置
            if 'tx_depth' in params:
                self.source_depth_spin.setValue(params['tx_depth'])
            if 'rx_depth' in params:
                self.receiver_depth_spin.setValue(params['rx_depth'])
            if 'rx_range' in params:
                self.receiver_range_spin.setValue(params['rx_range'])

            # 更新声源指向性设置
            if 'tx_directivity_enabled' in params:
                self.directivity_check.setChecked(params['tx_directivity_enabled'])
                if params['tx_directivity_enabled']:
                    directivity_data = params.get('directivity_data', [])
                    if directivity_data:
                        self.directivity_preview_label.setText(f"已设置 {len(directivity_data)} 个指向性数据点")
                    else:
                        self.directivity_preview_label.setText("未设置声源指向性数据")

            # 更新射线参数
            if 'min_angle' in params:
                self.min_angle_spin.setValue(params['min_angle'])
            if 'max_angle' in params:
                self.max_angle_spin.setValue(params['max_angle'])
            if 'nbeams' in params:
                auto_beam = params['nbeams'] == 0
                self.auto_beam_check.setChecked(auto_beam)
                if not auto_beam:
                    self.beam_count_spin.setValue(params['nbeams'])

            # 更新传播损失网格参数
            if 'depth_grid' in params:
                depth_grid = params['depth_grid']
                if 'start' in depth_grid:
                    self.depth_start_spin.setValue(depth_grid['start'])
                if 'end' in depth_grid:
                    self.depth_end_spin.setValue(depth_grid['end'])
                if 'step' in depth_grid:
                    self.depth_step_spin.setValue(depth_grid['step'])

            if 'range_grid' in params:
                range_grid = params['range_grid']
                if 'start' in range_grid:
                    self.range_start_spin.setValue(range_grid['start'])
                if 'end' in range_grid:
                    self.range_end_spin.setValue(range_grid['end'])
                if 'step' in range_grid:
                    self.range_step_spin.setValue(range_grid['step'])

            # 更新传播损失显示范围
            if 'tl_range' in params:
                tl_range = params['tl_range']
                if tl_range and 'vmin' in tl_range and 'vmax' in tl_range:
                    self.tl_min_spin.setValue(tl_range['vmin'])
                    self.tl_max_spin.setValue(tl_range['vmax'])

            # 更新传播损失模式
            if 'tl_mode' in params:
                if params['tl_mode'] == 'coherent':
                    self.coherent_tl_radio.setChecked(True)
                elif params['tl_mode'] == 'incoherent':
                    self.incoherent_tl_radio.setChecked(True)
                else:
                    self.semicoherent_tl_radio.setChecked(True)

            # 更新宽带计算参数
            if 'freq_mode' in params:
                if params['freq_mode'] == 'range':
                    self.freq_range_radio.setChecked(True)
                    if 'freq_start' in params:
                        self.freq_start_spin.setValue(params['freq_start'])
                    if 'freq_end' in params:
                        self.freq_end_spin.setValue(params['freq_end'])
                    if 'freq_step' in params:
                        self.freq_step_spin.setValue(params['freq_step'])
                else:
                    self.freq_list_radio.setChecked(True)
                    if 'freq_list' in params:
                        freq_list_str = ', '.join(str(f) for f in params['freq_list'])
                        self.freq_list_edit.setText(freq_list_str)

            if 'max_workers' in params:
                self.max_workers_spin.setValue(params['max_workers'])
            if 'output_dir' in params:
                self.output_dir_edit.setText(params['output_dir'])

            # 更新高级选项
            if 'debug_mode' in params:
                self.debug_mode_check.setChecked(params['debug_mode'])

        except Exception as e:
            print(f"从数据管理器加载参数失败: {e}")

    def on_depth_type_changed(self, _=None):
        """
        水深类型变更事件处理

        Args:
            _: 单选按钮状态，未使用
        """
        if self.depth_flat_radio.isChecked():
            # 平坦水深模式
            # self.flat_depth_layout.setEnabled(True)
            self.water_depth_spin.setEnabled(True)
            self.edit_bathymetry_button.setEnabled(False)
            self.bathymetry_preview_label.setEnabled(False)
            self.bathymetry_interp_combo.setEnabled(False)
        else:
            # 随距离变化模式
            # self.flat_depth_layout.setEnabled(False)
            self.water_depth_spin.setEnabled(False)
            self.edit_bathymetry_button.setEnabled(True)
            self.bathymetry_preview_label.setEnabled(True)
            self.bathymetry_interp_combo.setEnabled(True)

    def on_ssp_type_changed(self, _=None):
        """
        声速类型变更事件处理

        Args:
            _: 单选按钮状态，未使用
        """
        if self.ssp_constant_radio.isChecked():
            # 恒定声速模式
            # self.constant_ssp_layout.setEnabled(True)
            self.constant_ssp_spin.setEnabled(True)
            self.edit_ssp_button.setEnabled(False)
            self.ssp_preview_label.setEnabled(False)
            self.ssp_interp_combo.setEnabled(False)
        else:
            # 深度依赖模式
            # self.constant_ssp_layout.setEnabled(False)
            self.constant_ssp_spin.setEnabled(False)
            self.depth_ssp_layout.setEnabled(True)
            self.edit_ssp_button.setEnabled(True)
            self.ssp_preview_label.setEnabled(True)
            self.ssp_interp_combo.setEnabled(True)

    def on_auto_beam_changed(self, state):
        """
        自动声束数量设置变更事件处理

        Args:
            state: 复选框状态
        """
        self.beam_count_spin.setEnabled(not state)

    def on_freq_mode_changed(self, _=None):
        """
        频率设置方式变更事件处理

        Args:
            _: 单选按钮状态，未使用
        """
        if self.freq_range_radio.isChecked():
            # self.freq_range_layout.setEnabled(True)
            self.freq_start_spin.setEnabled(True)
            self.freq_end_spin.setEnabled(True)
            self.freq_step_spin.setEnabled(True)
            # self.freq_list_layout.setEnabled(False)
            self.freq_list_edit.setEnabled(False)
        else:
            # self.freq_range_layout.setEnabled(False)
            self.freq_start_spin.setEnabled(False)
            self.freq_end_spin.setEnabled(False)
            self.freq_step_spin.setEnabled(False)
            # self.freq_list_layout.setEnabled(True)
            self.freq_list_edit.setEnabled(True)



    def connect_signals(self):
        """
        连接控件信号
        """
        # 基本参数
        self.simulation_name_edit.editingFinished.connect(self.update_basic_params)
        self.probing_freq_spin.valueChanged.connect(self.update_basic_params)

        # 水深设置
        self.water_depth_spin.valueChanged.connect(self.update_depth_params)
        self.bathymetry_interp_combo.currentIndexChanged.connect(self.update_depth_params)

        # 声速设置
        self.constant_ssp_spin.valueChanged.connect(self.update_ssp_params)
        self.ssp_interp_combo.currentIndexChanged.connect(self.update_ssp_params)

        # 海底参数
        self.bottom_speed_spin.valueChanged.connect(self.update_bottom_params)
        self.bottom_density_spin.valueChanged.connect(self.update_bottom_params)
        self.bottom_absorption_spin.valueChanged.connect(self.update_bottom_params)
        self.bottom_roughness_spin.valueChanged.connect(self.update_bottom_params)

        # 声源和接收器设置
        self.source_depth_spin.valueChanged.connect(self.update_source_receiver_params)
        self.receiver_depth_spin.valueChanged.connect(self.update_source_receiver_params)
        self.receiver_range_spin.valueChanged.connect(self.update_source_receiver_params)

        # 射线参数
        self.min_angle_spin.valueChanged.connect(self.update_ray_params)
        self.max_angle_spin.valueChanged.connect(self.update_ray_params)
        self.beam_count_spin.valueChanged.connect(self.update_ray_params)

        # 网格参数
        self.depth_start_spin.valueChanged.connect(self.update_grid_params)
        self.depth_end_spin.valueChanged.connect(self.update_grid_params)
        self.depth_step_spin.valueChanged.connect(self.update_grid_params)
        self.range_start_spin.valueChanged.connect(self.update_grid_params)
        self.range_end_spin.valueChanged.connect(self.update_grid_params)
        self.range_step_spin.valueChanged.connect(self.update_grid_params)

        # 传播损失显示范围参数
        self.tl_min_spin.valueChanged.connect(self.update_grid_params)
        self.tl_max_spin.valueChanged.connect(self.update_grid_params)

        # 宽带计算参数
        self.freq_start_spin.valueChanged.connect(self.update_broadband_params)
        self.freq_end_spin.valueChanged.connect(self.update_broadband_params)
        self.freq_step_spin.valueChanged.connect(self.update_broadband_params)
        self.freq_list_edit.editingFinished.connect(self.update_broadband_params)
        self.max_workers_spin.valueChanged.connect(self.update_broadband_params)
        self.output_dir_edit.editingFinished.connect(self.update_broadband_params)

        # 高级选项
        self.debug_mode_check.stateChanged.connect(self.update_advanced_params)

    def reset_parameters(self):
        """
        重置参数到默认值
        """
        # 重置基本参数
        self.simulation_name_edit.setText("默认仿真")
        self.probing_freq_spin.setValue(1000)

        # 重置水体设置
        self.water_depth_spin.setValue(100)
        self.depth_flat_radio.setChecked(True)
        self.ssp_constant_radio.setChecked(True)
        self.constant_ssp_spin.setValue(1500)

        # 重置海底参数
        self.bottom_speed_spin.setValue(1600)
        self.bottom_density_spin.setValue(1.8)
        self.bottom_absorption_spin.setValue(0.5)
        self.bottom_roughness_spin.setValue(0.0)

        # 重置声源和接收器参数
        self.source_depth_spin.setValue(10)
        self.receiver_depth_spin.setValue(50)
        self.receiver_range_spin.setValue(1000)

        # 重置射线参数
        self.min_angle_spin.setValue(-80)
        self.max_angle_spin.setValue(80)
        self.auto_beam_check.setChecked(True)
        self.beam_count_spin.setValue(100)

        # 重置网格参数
        self.depth_start_spin.setValue(0)
        self.depth_end_spin.setValue(100)
        self.depth_step_spin.setValue(1)
        self.range_start_spin.setValue(0)
        self.range_end_spin.setValue(1000)
        self.range_step_spin.setValue(10)
        self.tl_min_spin.setValue(40)
        self.tl_max_spin.setValue(120)

        # 重置宽带计算参数
        self.freq_range_radio.setChecked(True)
        self.freq_start_spin.setValue(250)
        self.freq_end_spin.setValue(4750)
        self.freq_step_spin.setValue(500)
        self.max_workers_spin.setValue(4)
        self.output_dir_edit.setText("")

        # 重置高级选项
        self.debug_mode_check.setChecked(False)
        self.directivity_check.setChecked(False)

        # 重置预览标签
        self.ssp_preview_label.setText("未设置声速剖面数据")
        self.bathymetry_preview_label.setText("未设置水深数据")
        self.directivity_preview_label.setText("未设置声源指向性数据")
        self.array_preview_label.setText("未设置阵元位置数据")

    def update_basic_params(self):
        """
        更新基本参数
        """
        if not self.data_manager:
            return

        try:
            self.data_manager.update_parameter('propagation', 'name', self.simulation_name_edit.text())
            self.data_manager.update_parameter('propagation', 'type', '2D')  # 固定为2D
            self.data_manager.update_parameter('propagation', 'frequency', self.probing_freq_spin.value())
        except Exception as e:
            print(f"更新基本参数失败: {e}")

    def update_depth_params(self):
        """
        更新水深参数
        """
        if not self.data_manager:
            return

        try:
            # 水深类型
            depth_type = 'flat' if self.depth_flat_radio.isChecked() else 'range_dependent'
            self.data_manager.update_parameter('propagation', 'depth_type', depth_type)

            # 水深值
            if depth_type == 'flat':
                self.data_manager.update_parameter('propagation', 'depth', self.water_depth_spin.value())
            else:
                # 水深插值方法
                depth_interp = CURVILINEAR if self.bathymetry_interp_combo.currentText() == '曲线' else LINEAR
                self.data_manager.update_parameter('propagation', 'depth_interp', depth_interp)
        except Exception as e:
            print(f"更新水深参数失败: {e}")

    def update_ssp_params(self):
        """
        更新声速参数
        """
        if not self.data_manager:
            return

        try:
            # 声速类型
            ssp_type = 'constant' if self.ssp_constant_radio.isChecked() else 'depth_dependent'
            self.data_manager.update_parameter('propagation', 'ssp_type', ssp_type)

            # 声速值
            if ssp_type == 'constant':
                self.data_manager.update_parameter('propagation', 'soundspeed', self.constant_ssp_spin.value())
            else:
                # 声速插值方法
                soundspeed_interp = SPLINE if self.ssp_interp_combo.currentText() == '样条' else LINEAR
                self.data_manager.update_parameter('propagation', 'soundspeed_interp', soundspeed_interp)
        except Exception as e:
            print(f"更新声速参数失败: {e}")

    def update_bottom_params(self):
        """
        更新海底参数
        """
        if not self.data_manager:
            return

        try:
            self.data_manager.update_parameter('propagation', 'bottom_soundspeed', self.bottom_speed_spin.value())
            self.data_manager.update_parameter('propagation', 'bottom_density', self.bottom_density_spin.value())
            self.data_manager.update_parameter('propagation', 'bottom_absorption', self.bottom_absorption_spin.value())
            self.data_manager.update_parameter('propagation', 'bottom_roughness', self.bottom_roughness_spin.value())
        except Exception as e:
            print(f"更新海底参数失败: {e}")

    def update_source_receiver_params(self):
        """
        更新声源和接收器参数
        """
        if not self.data_manager:
            return

        try:
            self.data_manager.update_parameter('propagation', 'tx_depth', self.source_depth_spin.value())
            self.data_manager.update_parameter('propagation', 'rx_depth', self.receiver_depth_spin.value())
            self.data_manager.update_parameter('propagation', 'rx_range', self.receiver_range_spin.value())
        except Exception as e:
            print(f"更新声源和接收器参数失败: {e}")

    def update_ray_params(self):
        """
        更新射线参数
        """
        if not self.data_manager:
            return

        try:
            self.data_manager.update_parameter('propagation', 'min_angle', self.min_angle_spin.value())
            self.data_manager.update_parameter('propagation', 'max_angle', self.max_angle_spin.value())

            # 声束数量
            nbeams = 0 if self.auto_beam_check.isChecked() else self.beam_count_spin.value()
            self.data_manager.update_parameter('propagation', 'nbeams', nbeams)
        except Exception as e:
            print(f"更新射线参数失败: {e}")

    def update_grid_params(self):
        """
        更新网格参数
        """
        if not self.data_manager:
            return

        try:
            # 深度网格
            depth_grid = {
                'start': self.depth_start_spin.value(),
                'end': self.depth_end_spin.value(),
                'step': self.depth_step_spin.value()
            }
            self.data_manager.update_parameter('propagation', 'depth_grid', depth_grid)

            # 距离网格
            range_grid = {
                'start': self.range_start_spin.value(),
                'end': self.range_end_spin.value(),
                'step': self.range_step_spin.value()
            }
            self.data_manager.update_parameter('propagation', 'range_grid', range_grid)

            # 传播损失显示范围
            tl_range = {
                'vmin': self.tl_min_spin.value(),
                'vmax': self.tl_max_spin.value()
            }
            self.data_manager.update_parameter('propagation', 'tl_range', tl_range)

            # 更新网格预览
            self.update_grid_preview()
        except Exception as e:
            print(f"更新网格参数失败: {e}")

    def update_broadband_params(self):
        """
        更新宽带计算参数
        """
        if not self.data_manager:
            return

        try:
            # 频率模式
            freq_mode = 'range' if self.freq_range_radio.isChecked() else 'list'
            self.data_manager.update_parameter('propagation', 'freq_mode', freq_mode)

            # 频率参数
            if freq_mode == 'range':
                self.data_manager.update_parameter('propagation', 'freq_start', self.freq_start_spin.value())
                self.data_manager.update_parameter('propagation', 'freq_end', self.freq_end_spin.value())
                self.data_manager.update_parameter('propagation', 'freq_step', self.freq_step_spin.value())
            else:
                # 解析频率列表
                try:
                    freq_list_str = self.freq_list_edit.text()
                    freq_list = [float(f.strip()) for f in freq_list_str.split() if f.strip()]
                    self.data_manager.update_parameter('propagation', 'freq_list', freq_list)
                except ValueError:
                    print("频率列表格式错误")

            # 并行计算参数
            self.data_manager.update_parameter('propagation', 'max_workers', self.max_workers_spin.value())
            self.data_manager.update_parameter('propagation', 'output_dir', self.output_dir_edit.text())
        except Exception as e:
            print(f"更新宽带计算参数失败: {e}")

    def update_advanced_params(self):
        """
        更新高级选项
        """
        if not self.data_manager:
            return

        try:
            self.data_manager.update_parameter('propagation', 'debug_mode', self.debug_mode_check.isChecked())
        except Exception as e:
            print(f"更新高级选项失败: {e}")

    def edit_ssp(self):
        """
        编辑声速剖面
        """
        # 获取当前声速剖面数据
        ssp_data = self.get_ssp_data()

        # 打开对话框
        dialog = SSPDialog(self, ssp_data)
        if dialog.exec_():
            # 获取编辑后的数据
            ssp_data = dialog.get_data()

            # 更新预览标签
            if ssp_data:
                self.ssp_preview_label.setText(f"已设置 {len(ssp_data)} 个声速剖面数据点")
            else:
                self.ssp_preview_label.setText("未设置声速剖面数据")

            # 保存到数据管理器
            if self.data_manager:
                try:
                    self.data_manager.update_parameter('propagation', 'ssp_data', ssp_data)
                except Exception as e:
                    print(f"保存声速剖面数据失败: {e}")

    def edit_bathymetry(self):
        """
        编辑水深随距离变化
        """
        # 获取当前水深数据
        bathymetry_data = self.get_bathymetry_data()

        # 打开对话框
        dialog = BathymetryDialog(self, bathymetry_data)
        if dialog.exec_():
            # 获取编辑后的数据
            bathymetry_data = dialog.get_data()

            # 更新预览标签
            if bathymetry_data:
                self.bathymetry_preview_label.setText(f"已设置 {len(bathymetry_data)} 个水深数据点")
            else:
                self.bathymetry_preview_label.setText("未设置水深数据")

            # 保存到数据管理器
            if self.data_manager:
                try:
                    self.data_manager.update_parameter('propagation', 'bathymetry_data', bathymetry_data)
                except Exception as e:
                    print(f"保存水深数据失败: {e}")

    def edit_directivity(self):
        """
        编辑声源指向性
        """
        # 获取当前指向性数据
        directivity_data = self.get_directivity_data()

        # 打开对话框
        dialog = DirectivityDialog(self, directivity_data)
        if dialog.exec_():
            # 获取编辑后的数据
            directivity_data = dialog.get_data()

            # 更新预览标签
            if directivity_data:
                self.directivity_preview_label.setText(f"已设置 {len(directivity_data)} 个指向性数据点")
            else:
                self.directivity_preview_label.setText("未设置声源指向性数据")

            # 保存到数据管理器
            if self.data_manager:
                try:
                    self.data_manager.update_parameter('propagation', 'directivity_data', directivity_data)
                except Exception as e:
                    print(f"保存声源指向性数据失败: {e}")

    def edit_array_elements(self):
        """
        编辑阵元位置
        """
        # 获取当前阵元位置数据
        array_elements_data = self.get_array_elements_data()

        # 打开对话框
        dialog = ArrayElementsDialog(self, array_elements_data)
        if dialog.exec_():
            # 获取编辑后的数据
            array_elements_data = dialog.get_data()

            # 更新预览标签
            if array_elements_data:
                self.array_preview_label.setText(f"已设置 {len(array_elements_data)} 个阵元位置")
            else:
                self.array_preview_label.setText("未设置阵元位置数据")

            # 保存到数据管理器
            if self.data_manager:
                try:
                    self.data_manager.update_parameter('propagation', 'array_elements_data', array_elements_data)
                except Exception as e:
                    print(f"保存阵元位置数据失败: {e}")

    def on_directivity_changed(self, state):
        """
        声源指向性复选框状态变更事件处理

        Args:
            state: 复选框状态
        """
        is_enabled = state == Qt.Checked
        self.edit_directivity_button.setEnabled(is_enabled)
        self.directivity_preview_label.setEnabled(is_enabled)

    def on_draw_environment(self):
        """
        绘制环境按钮点击事件处理
        """
        # 先更新所有参数到数据管理器
        self.update_basic_params()
        self.update_depth_params()
        self.update_ssp_params()
        self.update_bottom_params()
        self.update_source_receiver_params()
        self.update_ray_params()

        # 收集环境参数
        params = self.collect_common_params()

        # 添加计算类型
        params['computation_type'] = 'environment'

        # 验证参数
        try:
            self.validate_params(params)
        except ValueError as e:
            # 显示错误消息
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "参数验证失败", str(e))
            return

        # 保存参数到数据管理器
        if self.data_manager:
            try:
                self.data_manager.update_parameter('propagation', 'computation_type', 'environment')

                # 确保所有参数都被保存到数据管理器
                for key, value in params.items():
                    if key != 'computation_type':  # 已经更新过了
                        self.data_manager.update_parameter('propagation', key, value)
            except Exception as e:
                print(f"保存环境参数失败: {e}")

        # 发送仿真请求信号
        self.simulation_requested.emit('propagation', 'environment')

    def on_draw_ssp(self):
        """
        绘制声速剖面按钮点击事件处理
        """
        # 先更新所有参数到数据管理器
        self.update_basic_params()
        self.update_depth_params()
        self.update_ssp_params()
        self.update_bottom_params()
        self.update_source_receiver_params()
        self.update_ray_params()

        # 收集环境参数
        params = self.collect_common_params()

        # 添加计算类型
        params['computation_type'] = 'environment'

        # 验证参数
        try:
            self.validate_params(params)
        except ValueError as e:
            # 显示错误消息
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "参数验证失败", str(e))
            return

        # 保存参数到数据管理器
        if self.data_manager:
            try:
                self.data_manager.update_parameter('propagation', 'computation_type', 'environment')

                # 确保所有参数都被保存到数据管理器
                for key, value in params.items():
                    if key != 'computation_type':  # 已经更新过了
                        self.data_manager.update_parameter('propagation', key, value)

                # 特别处理声速剖面数据，确保它被正确保存
                if params['ssp_type'] == 'depth_dependent':
                    ssp_data = self.get_ssp_data()
                    self.data_manager.update_parameter('propagation', 'ssp_data', ssp_data)
                    print(f"声速剖面数据已更新: {len(ssp_data)} 个点")
            except Exception as e:
                print(f"保存声速剖面参数失败: {e}")

        # 发送仿真请求信号
        self.simulation_requested.emit('propagation', 'environment')

    def browse_output_dir(self):
        """
        浏览输出目录按钮点击事件处理
        """
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录", self.output_dir_edit.text())
        if dir_path:
            self.output_dir_edit.setText(dir_path)

    def on_start_channel_preparation(self):
        """
        开始信道数据制备按钮点击事件处理
        """
        # 先更新所有参数到数据管理器
        self.update_basic_params()
        self.update_depth_params()
        self.update_ssp_params()
        self.update_bottom_params()
        self.update_source_receiver_params()
        self.update_ray_params()
        self.update_broadband_params()
        self.update_advanced_params()

        # 收集参数
        params = self.collect_common_params()

        # 添加信道数据制备特定参数
        params['computation_type'] = 'channel_preparation'

        # 获取阵元位置数据
        array_elements_data = self.get_array_elements_data()
        if not array_elements_data:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "参数错误", "请先设置阵元位置")
            return

        params['array_elements_data'] = array_elements_data

        # 频率设置
        freq_params = {}
        if self.freq_range_radio.isChecked():
            freq_params['freq_mode'] = 'range'
            freq_params['freq_start'] = self.freq_start_spin.value()
            freq_params['freq_end'] = self.freq_end_spin.value()
            freq_params['freq_step'] = self.freq_step_spin.value()

            params.update(freq_params)
        else:
            freq_params['freq_mode'] = 'list'
            freq_list_str = self.freq_list_edit.text()
            try:
                # 解析空格分隔的频率列表
                freq_list = [float(f.strip()) for f in freq_list_str.split() if f.strip()]
                freq_params['freq_list'] = freq_list

                params.update(freq_params)
            except ValueError:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "参数错误", "频率列表格式错误，请使用空格分隔的数字列表")
                return

        # 输出设置
        output_params = {
            'max_workers': self.max_workers_spin.value(),
            'output_dir': self.output_dir_edit.text()
        }
        params.update(output_params)

        # 验证参数
        try:
            self.validate_params(params)
        except ValueError as e:
            # 显示错误消息
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "参数验证失败", str(e))
            return

        # 保存参数到数据管理器
        if self.data_manager:
            try:
                self.data_manager.update_parameter('propagation', 'computation_type', 'channel_preparation')

                # 保存阵元位置数据
                self.data_manager.update_parameter('propagation', 'array_elements_data', array_elements_data)

                # 保存频率参数
                for key, value in freq_params.items():
                    self.data_manager.update_parameter('propagation', key, value)

                # 保存输出参数
                for key, value in output_params.items():
                    self.data_manager.update_parameter('propagation', key, value)
            except Exception as e:
                print(f"保存信道数据制备参数失败: {e}")

        # 禁用开始按钮，启用停止按钮
        self.start_channel_button.setEnabled(False)
        self.stop_channel_button.setEnabled(True)

        # 更新状态
        self.status_label.setText("计算中...")
        self.progress_bar.setValue(0)

        # 发送仿真请求信号
        self.simulation_requested.emit('propagation', 'channel_preparation')

    def on_stop_channel_preparation(self):
        """
        停止信道数据制备计算按钮点击事件处理
        """
        # 通知主窗口停止计算
        # 注意：这里不直接调用controller的方法，而是通过信号通知主窗口
        # 主窗口会处理停止计算的逻辑
        from PyQt5.QtWidgets import QMessageBox
        reply = QMessageBox.question(self, '确认', '确定要停止当前计算吗？',
                                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply == QMessageBox.Yes:
            # 发送停止信号
            self.stop_requested.emit()

            # 更新UI状态
            self.status_label.setText("已请求停止")

    # 这些方法已被对话框替代

    # 这些方法已被对话框替代或不再需要

    def on_simulate_clicked(self):
        """
        仿真按钮点击事件处理

        此方法由SimulationUIManager调用，用于响应工具栏的仿真按钮
        """
        # 获取当前选中的计算类型
        if self.coherent_tl_radio.isChecked() or self.incoherent_tl_radio.isChecked() or self.semicoherent_tl_radio.isChecked():
            # 如果选中了传播损失相关的单选按钮，执行传播损失计算
            self.on_compute_tl()
        elif self.eigenray_radio.isChecked() or self.normal_ray_radio.isChecked():
            # 如果选中了声线相关的单选按钮，执行声线计算
            self.on_compute_rays()
        else:
            # 默认执行环境绘制
            self.on_draw_environment()

    def update_grid_preview(self):
        """
        更新接收器网格预览
        """
        try:
            # 计算深度点数
            depth_start = self.depth_start_spin.value()
            depth_end = self.depth_end_spin.value()
            depth_step = self.depth_step_spin.value()
            depth_points = int((depth_end - depth_start) / depth_step) + 1

            # 计算距离点数
            range_start = self.range_start_spin.value()
            range_end = self.range_end_spin.value()
            range_step = self.range_step_spin.value()
            range_points = int((range_end - range_start) / range_step) + 1

            # 计算总接收器数量
            total_receivers = depth_points * range_points

            # 更新预览标签
            self.grid_preview_label.setText(f"接收器数量: {total_receivers} (深度: {depth_points}, 距离: {range_points})")

        except Exception as e:
            print(f"更新接收器网格预览失败: {e}")
            self.grid_preview_label.setText("接收器数量: 计算错误")

    def on_compute_rays(self):
        """
        计算声线按钮点击事件处理
        """
        # 先更新所有参数到数据管理器
        self.update_basic_params()
        self.update_depth_params()
        self.update_ssp_params()
        self.update_bottom_params()
        self.update_source_receiver_params()
        self.update_ray_params()

        # 收集参数
        params = self.collect_common_params()

        # 添加声线特定参数
        ray_type = 'eigenray' if self.eigenray_radio.isChecked() else 'ray'
        params['ray_type'] = ray_type

        # 设置计算类型
        computation_type = 'eigenrays' if ray_type == 'eigenray' else 'rays'
        params['computation_type'] = computation_type

        # 验证参数
        try:
            self.validate_params(params)
        except ValueError as e:
            # 显示错误消息
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "参数验证失败", str(e))
            return

        # 保存参数到数据管理器
        if self.data_manager:
            try:
                self.data_manager.update_parameter('propagation', 'computation_type', computation_type)
                self.data_manager.update_parameter('propagation', 'ray_type', ray_type)

                # 确保所有参数都被保存到数据管理器
                for key, value in params.items():
                    if key not in ['computation_type', 'ray_type']:  # 已经更新过了
                        self.data_manager.update_parameter('propagation', key, value)
            except Exception as e:
                print(f"保存声线计算参数失败: {e}")

        # 发送仿真请求信号
        self.simulation_requested.emit('propagation', 'rays')

    def on_compute_arrivals(self):
        """
        计算到达结构按钮点击事件处理
        """
        # 先更新所有参数到数据管理器
        self.update_basic_params()
        self.update_depth_params()
        self.update_ssp_params()
        self.update_bottom_params()
        self.update_source_receiver_params()
        self.update_ray_params()

        # 收集参数
        params = self.collect_common_params()

        # 添加到达结构特定参数
        params['computation_type'] = 'arrivals'

        # 验证参数
        try:
            self.validate_params(params)
        except ValueError as e:
            # 显示错误消息
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "参数验证失败", str(e))
            return

        # 保存参数到数据管理器
        if self.data_manager:
            try:
                self.data_manager.update_parameter('propagation', 'computation_type', 'arrivals')

                # 确保所有参数都被保存到数据管理器
                for key, value in params.items():
                    if key != 'computation_type':  # 已经更新过了
                        self.data_manager.update_parameter('propagation', key, value)
            except Exception as e:
                print(f"保存到达结构计算参数失败: {e}")

        # 发送仿真请求信号
        self.simulation_requested.emit('propagation', 'arrivals')



    def apply_tl_range(self):
        """
        应用传播损失显示范围设置
        """
        if not self.data_manager:
            return

        # 获取当前的传播损失结果
        results = self.data_manager.get_results('propagation')
        if 'transmission_loss' not in results:
            # 如果没有传播损失结果，显示错误消息
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", "没有传播损失结果可供显示")
            return

        # 设置传播损失范围
        tl_range = {
            'vmin': self.tl_min_spin.value(),
            'vmax': self.tl_max_spin.value()
        }

        # 验证范围设置
        if tl_range['vmin'] >= tl_range['vmax']:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "参数错误", "最小值必须小于最大值")
            return

        # 更新传播损失范围参数
        self.data_manager.update_parameter('propagation', 'tl_range', tl_range)

        # 发送信号，触发视图更新（但不重新计算）
        self.simulation_requested.emit('propagation', 'update_tl_range')

    def on_compute_tl(self):
        """
        计算传播损失按钮点击事件处理
        """
        # 先更新所有参数到数据管理器
        self.update_basic_params()
        self.update_depth_params()
        self.update_ssp_params()
        self.update_bottom_params()
        self.update_source_receiver_params()
        self.update_ray_params()
        self.update_grid_params()

        # 收集参数
        params = self.collect_common_params()

        # 添加传播损失特定参数
        params['computation_type'] = 'transmission_loss'

        if self.coherent_tl_radio.isChecked():
            params['tl_mode'] = 'coherent'
        elif self.incoherent_tl_radio.isChecked():
            params['tl_mode'] = 'incoherent'
        else:
            params['tl_mode'] = 'semicoherent'

        # 添加接收器网格参数
        depth_grid = {
            'start': self.depth_start_spin.value(),
            'end': self.depth_end_spin.value(),
            'step': self.depth_step_spin.value()
        }
        params['depth_grid'] = depth_grid

        range_grid = {
            'start': self.range_start_spin.value(),
            'end': self.range_end_spin.value(),
            'step': self.range_step_spin.value()
        }
        params['range_grid'] = range_grid

        # 验证参数
        try:
            self.validate_params(params)
        except ValueError as e:
            # 显示错误消息
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "参数验证失败", str(e))
            return

        # 保存网格参数到数据管理器
        if self.data_manager:
            try:
                self.data_manager.update_parameter('propagation', 'computation_type', 'transmission_loss')
                self.data_manager.update_parameter('propagation', 'depth_grid', depth_grid)
                self.data_manager.update_parameter('propagation', 'range_grid', range_grid)
                self.data_manager.update_parameter('propagation', 'tl_mode', params['tl_mode'])

                # 设置传播损失显示范围
                tl_range = {
                    'vmin': self.tl_min_spin.value(),
                    'vmax': self.tl_max_spin.value()
                }

                # 验证范围设置
                if tl_range['vmin'] >= tl_range['vmax']:
                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.warning(self, "参数错误", "最小值必须小于最大值")
                    return

                self.data_manager.update_parameter('propagation', 'tl_range', tl_range)

                # 确保所有参数都被保存到数据管理器
                for key, value in params.items():
                    if key not in ['computation_type', 'depth_grid', 'range_grid', 'tl_mode']:  # 已经更新过了
                        self.data_manager.update_parameter('propagation', key, value)
            except Exception as e:
                print(f"保存传播损失参数失败: {e}")

        # 发送仿真请求信号
        self.simulation_requested.emit('propagation', 'transmission_loss')

    def collect_common_params(self):
        """
        收集通用参数

        Returns:
            dict: 参数字典
        """
        params = {
            'name': self.simulation_name_edit.text(),
            'type': '2D',  # 固定为2D
            'frequency': self.probing_freq_spin.value(),
            'min_angle': self.min_angle_spin.value(),
            'max_angle': self.max_angle_spin.value(),
            'nbeams': 0 if self.auto_beam_check.isChecked() else self.beam_count_spin.value(),
            'tx_depth': self.source_depth_spin.value(),
            'rx_depth': self.receiver_depth_spin.value(),
            'rx_range': self.receiver_range_spin.value(),
            'debug_mode': self.debug_mode_check.isChecked(),
        }

        # 水深设置
        if self.depth_flat_radio.isChecked():
            params['depth_type'] = 'flat'
            params['depth'] = self.water_depth_spin.value()
        else:
            params['depth_type'] = 'range_dependent'
            # 水深数据将在需要时从对话框获取
            params['depth_interp'] = 'curvilinear' if self.bathymetry_interp_combo.currentText() == '曲线' else 'linear'

        # 声速设置
        if self.ssp_constant_radio.isChecked():
            params['ssp_type'] = 'constant'
            params['soundspeed'] = self.constant_ssp_spin.value()
        else:
            params['ssp_type'] = 'depth_dependent'
            # 声速剖面数据将在需要时从对话框获取
            params['soundspeed_interp'] = 'spline' if self.ssp_interp_combo.currentText() == '样条' else 'linear'

        # 海底参数
        params['bottom_soundspeed'] = self.bottom_speed_spin.value()
        params['bottom_density'] = self.bottom_density_spin.value()
        params['bottom_absorption'] = self.bottom_absorption_spin.value()
        params['bottom_roughness'] = self.bottom_roughness_spin.value()

        # 声源指向性
        if self.directivity_check.isChecked():
            params['tx_directivity_enabled'] = True
            # 指向性数据将在需要时从对话框获取
        else:
            params['tx_directivity_enabled'] = False

        return params

    def validate_params(self, params):
        """
        验证参数是否有效

        基于arlpy.uwapm模块的check_env2d函数实现参数验证

        Args:
            params: 参数字典

        Raises:
            ValueError: 如果参数无效
        """
        # 验证水深
        if params.get('depth_type') == 'flat':
            depth = params.get('depth')
            if depth <= 0:
                raise ValueError("水深必须为正值")
        else:
            # 验证水深数据
            bathymetry_data = self.get_bathymetry_data()
            if bathymetry_data is None or len(bathymetry_data) < 2:
                raise ValueError("距离依赖水深模式需要至少2个水深数据点")

            # 验证水深数据格式
            try:
                bathymetry_array = np.array(bathymetry_data)
                if bathymetry_array.ndim != 2 or bathymetry_array.shape[1] != 2:
                    raise ValueError("水深数据格式错误，应为Nx2数组")

                # 验证水深数据范围
                if bathymetry_array[0, 0] > 0:
                    raise ValueError("水深数据的第一个距离点必须为0 m")

                max_range = params.get('rx_range')
                if bathymetry_array[-1, 0] < max_range:
                    raise ValueError(f"水深数据的最后一个距离点必须大于最大接收距离: {max_range} m")

                # 验证水深数据单调性
                if not np.all(np.diff(bathymetry_array[:, 0]) > 0):
                    raise ValueError("水深数据必须在距离上严格单调递增")

                # 验证插值类型
                interp_type = params.get('depth_interp')
                if interp_type not in [LINEAR, CURVILINEAR]:
                    raise ValueError(f"无效的水深插值类型: {interp_type}")
            except Exception as e:
                raise ValueError(f"水深数据验证失败: {e}")

        # 验证声速
        if params.get('ssp_type') == 'constant':
            # 恒定声速模式，验证声速值
            soundspeed = params.get('soundspeed')
            if soundspeed <= 0:
                raise ValueError("声速必须为正值")
        else:
            # 深度依赖声速模式，验证声速剖面
            ssp_data = self.get_ssp_data()
            if ssp_data is None or len(ssp_data) < 4:
                raise ValueError("声速剖面必须至少有4个点")

            # 验证声速剖面格式
            try:
                ssp_array = np.array(ssp_data)
                if ssp_array.ndim != 2 or ssp_array.shape[1] != 2:
                    raise ValueError("声速剖面格式错误，应为Nx2数组")

                # 验证声速剖面范围
                if ssp_array[0, 0] > 0:
                    raise ValueError("声速剖面的第一个深度点必须为0 m")

                # 获取最大水深
                if params.get('depth_type') == 'flat':
                    max_depth = params.get('depth')
                else:
                    bathymetry_data = self.get_bathymetry_data()
                    bathymetry_array = np.array(bathymetry_data)
                    max_depth = np.max(bathymetry_array[:, 1])

                # 检查声速剖面的最后一个深度点是否大于等于水深
                if ssp_array[-1, 0] < max_depth:
                    raise ValueError(f"声速剖面的最后一个深度点必须大于等于水深: {max_depth} m")

                # 检查水深是否大于声速剖面的倒数第二个深度点
                # 这可能是Bellhop的一个特殊要求，否则可能导致运行失败
                if len(ssp_array) >= 2 and max_depth <= ssp_array[-2, 0]:
                    raise ValueError(f"最大水深({max_depth}m)必须大于声速剖面的倒数第二个深度点({ssp_array[-2, 0]}m)，否则Bellhop可能运行失败")

                # 验证声速剖面单调性
                if not np.all(np.diff(ssp_array[:, 0]) > 0):
                    raise ValueError("声速剖面必须在深度上严格单调递增")

                # 验证插值类型
                interp_type = params.get('soundspeed_interp')
                if interp_type not in [LINEAR, SPLINE]:
                    raise ValueError(f"无效的声速插值类型: {interp_type}")
            except Exception as e:
                raise ValueError(f"声速剖面验证失败: {e}")

        # 验证声源和接收器深度
        tx_depth = params.get('tx_depth')
        rx_depth = params.get('rx_depth')

        # 获取最大水深
        if params.get('depth_type') == 'flat':
            max_depth = params.get('depth')
        else:
            bathymetry_data = self.get_bathymetry_data()
            bathymetry_array = np.array(bathymetry_data)
            max_depth = np.max(bathymetry_array[:, 1])

        if tx_depth > max_depth:
            raise ValueError(f"声源深度不能超过水深: {max_depth} m")

        if rx_depth > max_depth:
            raise ValueError(f"接收器深度不能超过水深: {max_depth} m")

        # 验证角度范围
        min_angle = params.get('min_angle')
        max_angle = params.get('max_angle')

        if min_angle <= -180 or min_angle >= 180:
            raise ValueError("最小角度必须在(-180, 180)度范围内")

        if max_angle <= -180 or max_angle >= 180:
            raise ValueError("最大角度必须在(-180, 180)度范围内")

        if min_angle >= max_angle:
            raise ValueError("最小角度必须小于最大角度")

        # 验证声源指向性
        if params.get('tx_directivity_enabled'):
            directivity_data = self.get_directivity_data()
            if directivity_data:
                try:
                    directivity_array = np.array(directivity_data)
                    if directivity_array.ndim != 2 or directivity_array.shape[1] != 2:
                        raise ValueError("声源指向性格式错误，应为Nx2数组")

                    # 验证角度范围
                    if np.any(directivity_array[:, 0] < -180) or np.any(directivity_array[:, 0] > 180):
                        raise ValueError("声源指向性角度必须在[-180, 180]度范围内")
                except Exception as e:
                    raise ValueError(f"声源指向性验证失败: {e}")

        # 验证传播损失计算的接收器网格参数
        if 'depth_grid' in params and 'range_grid' in params:
            depth_grid = params.get('depth_grid')
            range_grid = params.get('range_grid')

            # 验证深度网格
            if depth_grid.get('start') < 0:
                raise ValueError("深度网格起始值不能为负")

            if depth_grid.get('end') <= depth_grid.get('start'):
                raise ValueError("深度网格结束值必须大于起始值")

            if depth_grid.get('step') <= 0:
                raise ValueError("深度网格步长必须为正值")

            if depth_grid.get('end') > max_depth:
                raise ValueError(f"深度网格结束值不能超过水深: {max_depth} m")

            # 验证距离网格
            if range_grid.get('start') < 0:
                raise ValueError("距离网格起始值不能为负")

            if range_grid.get('end') <= range_grid.get('start'):
                raise ValueError("距离网格结束值必须大于起始值")

            if range_grid.get('step') <= 0:
                raise ValueError("距离网格步长必须为正值")

        # 验证信道数据制备的频率参数
        if params.get('computation_type') in ['broadband', 'channel_preparation']:
            if params.get('freq_mode') == 'range':
                freq_start = params.get('freq_start')
                freq_end = params.get('freq_end')
                freq_step = params.get('freq_step')

                if freq_start <= 0:
                    raise ValueError("起始频率必须为正值")

                if freq_end <= freq_start:
                    raise ValueError("结束频率必须大于起始频率")

                if freq_step <= 0:
                    raise ValueError("频率步长必须为正值")
            else:
                freq_list = params.get('freq_list', [])

                if not freq_list:
                    raise ValueError("频率列表不能为空")

                if any(f <= 0 for f in freq_list):
                    raise ValueError("频率列表中的所有频率必须为正值")

        # 验证信道数据制备的阵元位置参数
        if params.get('computation_type') == 'channel_preparation':
            # 获取水深信息
            depth_type = params.get('depth_type')
            water_depth = None

            if depth_type == 'flat':
                water_depth = params.get('depth')
            else:
                # 对于随距离变化的水深，需要获取水深数据
                bathymetry_data = params.get('bathymetry_data', [])
                if bathymetry_data:
                    # 获取最小水深作为限制
                    depths = [point[1] for point in bathymetry_data]
                    water_depth = min(depths) if depths else None

            # 验证阵元位置数据
            array_elements_data = params.get('array_elements_data', [])

            if not array_elements_data:
                raise ValueError("阵元位置数据不能为空")

            # 验证阵元位置数据格式
            try:
                array_elements_array = np.array(array_elements_data)
                if array_elements_array.ndim != 2 or array_elements_array.shape[1] != 2:
                    raise ValueError("阵元位置数据格式错误，应为Nx2数组")

                # 验证阵元位置是否为水平线列阵或垂直线列阵
                ranges = array_elements_array[:, 0]
                depths = array_elements_array[:, 1]

                # 检查是否所有距离相同（垂直线列阵）
                all_same_range = len(set(ranges)) == 1

                # 检查是否所有深度相同（水平线列阵）
                all_same_depth = len(set(depths)) == 1

                if not (all_same_range or all_same_depth):
                    raise ValueError("阵元位置必须满足以下条件之一：\n"
                                    "1. 所有阵元具有相同的距离（垂直线列阵）\n"
                                    "2. 所有阵元具有相同的深度（水平线列阵）")

                # 验证阵元深度是否超过水深
                if water_depth is not None:
                    max_depth = max(depths)
                    if max_depth > water_depth:
                        raise ValueError(f"阵元深度不能超过水深: {water_depth} m")

                # 验证输出目录
                output_dir = params.get('output_dir', '')
                if not output_dir:
                    raise ValueError("输出目录不能为空")

            except Exception as e:
                raise ValueError(f"阵元位置数据验证失败: {e}")

        return True

    def get_ssp_data(self):
        """
        获取声速剖面数据

        Returns:
            list: 声速剖面数据列表，格式为[(深度, 声速), ...]
        """
        # 从数据管理器获取声速剖面数据
        if self.data_manager:
            try:
                params = self.data_manager.get_parameters('propagation')
                ssp_data = params.get('ssp_data', [])
                if ssp_data:
                    return ssp_data
            except Exception as e:
                print(f"获取声速剖面数据失败: {e}")

        # 如果没有数据，返回一个默认的声速剖面
        return [
            (0, 1500),
            (50, 1490),
            (100, 1480),
            (200, 1470)
        ]

    def get_bathymetry_data(self):
        """
        获取水深数据

        Returns:
            list: 水深数据列表，格式为[(距离, 水深), ...]
        """
        # 从数据管理器获取水深数据
        if self.data_manager:
            try:
                params = self.data_manager.get_parameters('propagation')
                bathymetry_data = params.get('bathymetry_data', [])
                if bathymetry_data:
                    return bathymetry_data
            except Exception as e:
                print(f"获取水深数据失败: {e}")

        # 如果没有数据，返回一个默认的水深数据
        return [
            (0, 100),
            (500, 120),
            (1000, 150),
            (2000, 200)
        ]

    def get_directivity_data(self):
        """
        获取声源指向性数据

        Returns:
            list: 声源指向性数据列表，格式为[(角度, 增益), ...]
        """
        # 从数据管理器获取声源指向性数据
        if self.data_manager:
            try:
                params = self.data_manager.get_parameters('propagation')
                directivity_data = params.get('directivity_data', [])
                if directivity_data:
                    return directivity_data
            except Exception as e:
                print(f"获取声源指向性数据失败: {e}")

        # 如果没有数据，返回一个默认的声源指向性数据
        return [
            (-180, -40),
            (-90, -20),
            (0, 0),
            (90, -20),
            (180, -40)
        ]

    def get_array_elements_data(self):
        """
        获取阵元位置数据

        Returns:
            list: 阵元位置数据列表，格式为[(距离, 深度), ...]
        """
        # 从数据管理器获取阵元位置数据
        if self.data_manager:
            try:
                params = self.data_manager.get_parameters('propagation')
                array_elements_data = params.get('array_elements_data', [])
                if array_elements_data:
                    return array_elements_data
            except Exception as e:
                print(f"获取阵元位置数据失败: {e}")

        # 如果没有数据，返回一个默认的阵元位置数据（垂直阵列）
        return [
            (1000, 49),
            (1000, 49.5),
            (1000, 50),
            (1000, 50.5),
        ]