#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
高级并行计算多个频率的声传播信息，使用arlpy的uwapm模块调用Bellhop模型。
提供更多功能和灵活性。
"""

import os
import time
import uuid
import json
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import concurrent.futures
import arlpy.uwapm as pm
import arlpy.plot as plt_arlpy

class BellhopParallelComputer:
    """Bellhop并行计算类"""
    
    def __init__(self, config=None, debug=False):
        """
        初始化
        
        参数:
            config: 配置字典或配置文件路径
            debug: 是否输出调试信息
        """
        self.debug = debug
        self.env_params = self._load_config(config)
        self.results = {}
        
    def _load_config(self, config):
        """加载配置"""
        # 默认环境参数
        default_params = {
            'depth': 100,                      # 水深 (m)
            'soundspeed': 1500,                # 声速 (m/s)
            'tx_depth': 50,                    # 发射深度 (m)
            'rx_depth': list(np.linspace(0, 100, 101)),  # 接收深度范围 (m)
            'rx_range': list(np.linspace(100, 5000, 50)),  # 接收距离范围 (m)
            'bottom_soundspeed': 1600,         # 海底声速 (m/s)
            'bottom_density': 1800,            # 海底密度 (kg/m^3)
            'bottom_absorption': 0.5,          # 海底吸收系数 (dB/wavelength)
            'min_angle': -80,                  # 最小发射角度 (度)
            'max_angle': 80,                   # 最大发射角度 (度)
            'nbeams': 0                        # 光线数量 (0表示自动)
        }
        
        # 如果提供了配置
        if config:
            # 如果是字符串，假定是文件路径
            if isinstance(config, str):
                try:
                    with open(config, 'r') as f:
                        user_params = json.load(f)
                except Exception as e:
                    print(f"加载配置文件出错: {str(e)}")
                    user_params = {}
            # 否则假定是字典
            else:
                user_params = config
                
            # 更新默认参数
            default_params.update(user_params)
            
        return default_params
    
    def compute_single_frequency(self, freq):
        """
        计算单个频率的声传播信息
        
        参数:
            freq: 频率 (Hz)
        
        返回:
            包含arrivals信息的DataFrame
        """
        # 创建一个唯一的临时文件名基础，避免并行计算时的文件冲突
        fname_base = f"bellhop_temp_{uuid.uuid4().hex}"
        
        # 创建环境
        env_params = self.env_params.copy()
        env_params['frequency'] = freq
        
        # 处理numpy数组
        for key, value in env_params.items():
            if isinstance(value, list):
                env_params[key] = np.array(value)
        
        env = pm.create_env2d(**env_params)
        
        # 计算arrivals
        start_time = time.time()
        try:
            arrivals = pm.compute_arrivals(env, debug=self.debug, fname_base=fname_base)
            # 添加频率信息到结果中
            arrivals['frequency'] = freq
            
            if self.debug:
                elapsed = time.time() - start_time
                print(f"计算频率 {freq} Hz 完成，耗时: {elapsed:.2f}秒")
            
            # 添加元数据
            metadata = {
                'frequency': freq,
                'fs': env_params.get('fs', None),
                'duration': env_params.get('duration', None),
                'filter_order': env_params.get('filter_order', None),
                'tx_depth': env_params['tx_depth'],
                'rx_depth': env_params['rx_depth'],
                'rx_range': env_params['rx_range'],
                'depth': env_params['depth'],
                'soundspeed': env_params['soundspeed']
            }
            
            return {'data': arrivals, 'metadata': metadata}
        except Exception as e:
            print(f"计算频率 {freq} Hz 时出错: {str(e)}")
            return None
        finally:
            # 清理可能残留的临时文件
            for ext in ['.env', '.bty', '.ssp', '.ati', '.sbp', '.prt', '.log', '.arr', '.ray', '.shd']:
                try:
                    os.unlink(fname_base + ext)
                except:
                    pass
    
    def parallel_compute(self, frequencies, max_workers=None):
        """
        并行计算多个频率的声传播信息
        
        参数:
            frequencies: 频率列表 (Hz)
            max_workers: 最大并行进程数，None表示使用CPU核心数
        
        返回:
            self，以支持链式调用
        """
        self.results = {}
        
        print(f"开始并行计算 {len(frequencies)} 个频率的声传播信息...")
        start_time = time.time()
        
        # 使用ProcessPoolExecutor进行并行计算
        with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有计算任务
            future_to_freq = {
                executor.submit(self.compute_single_frequency, freq): freq 
                for freq in frequencies
            }
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_freq):
                freq = future_to_freq[future]
                try:
                    result = future.result()
                    if result is not None:
                        self.results[freq] = result
                        print(f"频率 {freq} Hz 计算完成，已完成: {len(self.results)}/{len(frequencies)}")
                except Exception as e:
                    print(f"处理频率 {freq} Hz 的结果时出错: {str(e)}")
        
        total_time = time.time() - start_time
        print(f"所有计算完成，总耗时: {total_time:.2f}秒")
        
        return self
    
    def save_results(self, output_dir="results", base_filename="bellhop_arrivals"):
        """
        保存计算结果
        
        参数:
            output_dir: 输出目录
            base_filename: 基础文件名
        
        返回:
            self，以支持链式调用
        """
        if not self.results:
            print("没有结果可保存")
            return self
            
        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 保存每个频率的结果
        for freq, result in self.results.items():
            # 保存数据
            data_filename = f"{base_filename}_{freq}Hz.csv"
            data_filepath = output_path / data_filename
            result['data'].to_csv(data_filepath)
            
            # 保存元数据
            meta_filename = f"{base_filename}_{freq}Hz_metadata.json"
            meta_filepath = output_path / meta_filename
            
            # 处理元数据中的numpy数组
            metadata = result['metadata'].copy()
            for key, value in metadata.items():
                if isinstance(value, np.ndarray):
                    if value.size <= 10:  # 如果数组较小，直接转换为列表
                        metadata[key] = value.tolist()
                    else:  # 如果数组较大，只保存一些基本信息
                        metadata[key] = {
                            'type': 'ndarray',
                            'shape': value.shape,
                            'min': float(np.min(value)),
                            'max': float(np.max(value)),
                            'mean': float(np.mean(value))
                        }
            
            with open(meta_filepath, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            print(f"已保存频率 {freq} Hz 的结果到 {data_filepath} 和 {meta_filepath}")
        
        # 合并所有结果并保存
        all_data = []
        for result in self.results.values():
            all_data.append(result['data'])
            
        if all_data:
            all_arrivals = pd.concat(all_data)
            all_filepath = output_path / f"{base_filename}_all.csv"
            all_arrivals.to_csv(all_filepath)
            print(f"已保存所有频率的合并结果到 {all_filepath}")
        
        return self
    
    def plot_results(self, output_dir="results", show=True):
        """
        绘制结果图表
        
        参数:
            output_dir: 输出目录
            show: 是否显示图表
        
        返回:
            self，以支持链式调用
        """
        if not self.results:
            print("没有结果可绘制")
            return self
            
        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 为每个频率绘制到达信息
        for freq, result in self.results.items():
            arrivals = result['data']
            
            # 使用arlpy绘制到达信息
            plt.figure(figsize=(12, 6))
            pm.pyplot_arrivals(arrivals, dB=True)
            plt.title(f'频率 {freq} Hz 的到达信息')
            plt.tight_layout()
            
            # 保存图表
            plot_filepath = output_path / f"arrivals_{freq}Hz.png"
            plt.savefig(plot_filepath, dpi=300)
            print(f"已保存频率 {freq} Hz 的到达信息图表到 {plot_filepath}")
            
            if not show:
                plt.close()
        
        # 绘制所有频率的到达时间与频率的关系
        plt.figure(figsize=(12, 6))
        for freq, result in self.results.items():
            arrivals = result['data']
            arrival_times = arrivals['time_of_arrival'].values
            plt.scatter([freq] * len(arrival_times), arrival_times, alpha=0.5, label=f'{freq} Hz')
        
        plt.xlabel('频率 (Hz)')
        plt.ylabel('到达时间 (s)')
        plt.title('不同频率的到达时间')
        plt.grid(True)
        plt.tight_layout()
        
        # 保存图表
        plot_filepath = output_path / "arrival_times_vs_frequency.png"
        plt.savefig(plot_filepath, dpi=300)
        print(f"已保存到达时间与频率关系图表到 {plot_filepath}")
        
        if show:
            plt.show()
        else:
            plt.close()
            
        return self

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='高级并行计算多个频率的声传播信息')
    parser.add_argument('--freq_start', type=float, default=100, help='起始频率 (Hz)')
    parser.add_argument('--freq_end', type=float, default=1000, help='结束频率 (Hz)')
    parser.add_argument('--freq_step', type=float, default=100, help='频率步长 (Hz)')
    parser.add_argument('--freq_list', type=str, help='频率列表，以逗号分隔，优先于范围参数')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--max_workers', type=int, help='最大并行进程数')
    parser.add_argument('--output_dir', type=str, default='results', help='输出目录')
    parser.add_argument('--no_plot', action='store_true', help='不绘制图表')
    parser.add_argument('--debug', action='store_true', help='输出调试信息')
    args = parser.parse_args()
    
    # 确定要计算的频率列表
    if args.freq_list:
        frequencies = [float(f) for f in args.freq_list.split(',')]
    else:
        frequencies = np.arange(args.freq_start, args.freq_end + args.freq_step/2, args.freq_step)
    
    print(f"将计算以下频率的声传播信息: {frequencies} Hz")
    
    # 创建计算器实例
    computer = BellhopParallelComputer(config=args.config, debug=args.debug)
    
    # 执行计算、保存结果并绘制图表
    computer.parallel_compute(frequencies, max_workers=args.max_workers) \
            .save_results(output_dir=args.output_dir)
    
    if not args.no_plot:
        computer.plot_results(output_dir=args.output_dir, show=False)

if __name__ == "__main__":
    main()
