# -*- coding: utf-8 -*-
"""
声传播环境视图

用于显示声传播环境、声速剖面、声线追踪、到达结构和传播损失等
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QTabWidget, QTableWidget, QTableWidgetItem,
    QHeaderView, QSplitter, QHBoxLayout
)
from PyQt5.QtCore import Qt
from matplotlib.figure import Figure
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd


class PropagationView(QWidget):
    """
    声传播环境视图

    用于显示声传播环境、声速剖面、声线追踪、到达结构和传播损失等
    """

    def __init__(self, parent=None):
        """
        初始化声传播环境视图

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 创建布局
        layout = QVBoxLayout(self)

        # 创建选项卡用于切换不同图表
        self.tabs = QTabWidget()

        # 创建环境示意图
        self.env_canvas = FigureCanvas(Figure(figsize=(8, 6)))
        self.env_fig = self.env_canvas.figure
        self.env_ax = self.env_fig.add_subplot(111)
        self.env_ax.set_title('声传播环境')
        self.env_ax.set_xlabel('距离 (m)')
        self.env_ax.set_ylabel('深度 (m)')
        self.env_ax.grid(True)
        self.env_toolbar = NavigationToolbar(self.env_canvas, self)

        # 创建声速剖面图
        self.ssp_canvas = FigureCanvas(Figure(figsize=(8, 6)))
        self.ssp_fig = self.ssp_canvas.figure
        self.ssp_ax = self.ssp_fig.add_subplot(111)
        self.ssp_ax.set_title('声速剖面')
        self.ssp_ax.set_xlabel('声速 (m/s)')
        self.ssp_ax.set_ylabel('深度 (m)')
        self.ssp_ax.grid(True)
        self.ssp_toolbar = NavigationToolbar(self.ssp_canvas, self)

        # 创建声线图（包含普通声线和本征声线两个子图）
        self.ray_canvas = FigureCanvas(Figure(figsize=(8, 6)))
        self.ray_fig = self.ray_canvas.figure
        self.ray_ax = self.ray_fig.add_subplot(211)  # 普通声线
        self.ray_ax.set_title('声线追踪')
        self.ray_ax.set_xlabel('距离 (m)')
        self.ray_ax.set_ylabel('深度 (m)')
        self.ray_ax.grid(True)

        self.eigenray_ax = self.ray_fig.add_subplot(212)  # 本征声线
        self.eigenray_ax.set_title('本征声线')
        self.eigenray_ax.set_xlabel('距离 (m)')
        self.eigenray_ax.set_ylabel('深度 (m)')
        self.eigenray_ax.grid(True)

        self.ray_fig.tight_layout()
        self.ray_toolbar = NavigationToolbar(self.ray_canvas, self)

        # 创建到达结构图和表格
        self.arrivals_splitter = QSplitter(Qt.Vertical)

        # 到达结构图
        self.arrivals_canvas = FigureCanvas(Figure(figsize=(8, 4)))
        self.arrivals_fig = self.arrivals_canvas.figure
        self.arrivals_ax = self.arrivals_fig.add_subplot(111)
        self.arrivals_ax.set_title('到达结构')
        self.arrivals_ax.set_xlabel('到达时间 (s)')
        self.arrivals_ax.set_ylabel('幅度')
        self.arrivals_ax.grid(True)
        self.arrivals_toolbar = NavigationToolbar(self.arrivals_canvas, self)

        # 到达结构表格
        self.arrivals_table = QTableWidget()
        self.arrivals_table.setColumnCount(7)
        self.arrivals_table.setHorizontalHeaderLabels([
            '到达序号', '到达时间 (s)', '幅度', '出射角 (°)', '到达角 (°)',
            '海面反射次数', '海底反射次数'
        ])
        self.arrivals_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.arrivals_table.setAlternatingRowColors(True)
        self.arrivals_table.setEditTriggers(QTableWidget.NoEditTriggers)  # 设置为只读

        # 将图表和表格添加到分割器
        arrivals_chart_widget = QWidget()
        arrivals_chart_layout = QVBoxLayout(arrivals_chart_widget)
        arrivals_chart_layout.addWidget(self.arrivals_toolbar)
        arrivals_chart_layout.addWidget(self.arrivals_canvas)
        arrivals_chart_layout.setContentsMargins(0, 0, 0, 0)

        self.arrivals_splitter.addWidget(arrivals_chart_widget)
        self.arrivals_splitter.addWidget(self.arrivals_table)
        self.arrivals_splitter.setSizes([200, 100])  # 设置初始大小比例

        # 创建传播损失图
        self.tl_canvas = FigureCanvas(Figure(figsize=(8, 6)))
        self.tl_fig = self.tl_canvas.figure
        self.tl_ax = self.tl_fig.add_subplot(111)
        self.tl_ax.set_title('传播损失')
        self.tl_ax.set_xlabel('距离 (m)')
        self.tl_ax.set_ylabel('深度 (m)')
        self.tl_ax.grid(True)
        self.tl_toolbar = NavigationToolbar(self.tl_canvas, self)

        # 保存色温条的引用，用于更新时移除旧的色温条
        self.tl_colorbar = None

        # 创建容器并添加到选项卡
        self.create_tab_containers()

        # 添加到布局
        layout.addWidget(self.tabs)

    def create_tab_containers(self):
        """创建选项卡容器并添加到选项卡"""
        # 环境示意图容器
        self.env_container = QWidget()
        env_layout = QVBoxLayout(self.env_container)
        env_layout.addWidget(self.env_toolbar)
        env_layout.addWidget(self.env_canvas)
        env_layout.setContentsMargins(0, 0, 0, 0)
        env_layout.setSpacing(0)

        # 声速剖面容器
        self.ssp_container = QWidget()
        ssp_layout = QVBoxLayout(self.ssp_container)
        ssp_layout.addWidget(self.ssp_toolbar)
        ssp_layout.addWidget(self.ssp_canvas)
        ssp_layout.setContentsMargins(0, 0, 0, 0)
        ssp_layout.setSpacing(0)

        # 声线图容器
        self.ray_container = QWidget()
        ray_layout = QVBoxLayout(self.ray_container)
        ray_layout.addWidget(self.ray_toolbar)
        ray_layout.addWidget(self.ray_canvas)
        ray_layout.setContentsMargins(0, 0, 0, 0)
        ray_layout.setSpacing(0)

        # 到达结构容器
        self.arrivals_container = QWidget()
        arrivals_layout = QVBoxLayout(self.arrivals_container)
        arrivals_layout.addWidget(self.arrivals_splitter)
        arrivals_layout.setContentsMargins(0, 0, 0, 0)
        arrivals_layout.setSpacing(0)

        # 传播损失容器
        self.tl_container = QWidget()
        tl_layout = QVBoxLayout(self.tl_container)
        tl_layout.addWidget(self.tl_toolbar)
        tl_layout.addWidget(self.tl_canvas)
        tl_layout.setContentsMargins(0, 0, 0, 0)
        tl_layout.setSpacing(0)

        # 添加到选项卡
        self.tabs.addTab(self.env_container, "环境示意图")
        self.tabs.addTab(self.ssp_container, "声速剖面")
        self.tabs.addTab(self.ray_container, "声线图")
        self.tabs.addTab(self.arrivals_container, "到达结构")
        self.tabs.addTab(self.tl_container, "传播损失")

    def draw_environment_outline(self, ax, env, xr=None, use_km=None, draw_receivers=True):
        """
        绘制环境轮廓（海面、海底、声源、接收器）

        Args:
            ax: matplotlib轴对象
            env: 环境参数字典
            xr: 可选的x轴范围，用于传播损失图等
            use_km: 是否使用千米作为距离单位，如果为None则自动判断
            draw_receivers: 是否绘制接收器，默认为True
        """
        # 获取环境参数
        min_x = 0
        max_x = env.get('rx_range', 1000)
        if isinstance(max_x, (list, np.ndarray)):
            max_x = np.max(max_x)

        # 如果提供了x轴范围，使用提供的范围
        if xr is not None:
            min_x, max_x = xr

        # 确定距离单位
        if use_km is None:
            use_km = max_x > 10000

        divisor = 1000 if use_km else 1
        xlabel = '距离 (km)' if use_km else '距离 (m)'

        # 绘制海面
        if 'surface' not in env or env['surface'] is None:
            ax.plot([min_x/divisor, max_x/divisor], [0, 0], color='dodgerblue')
        else:
            s = env['surface']
            ax.plot(s[:, 0]/divisor, s[:, 1], color='dodgerblue')

        # 绘制海底
        if isinstance(env.get('depth'), (int, float)):
            ax.plot([min_x/divisor, max_x/divisor], [env['depth'], env['depth']], color='peru')
        else:
            s = env['depth']
            ax.plot(s[:, 0]/divisor, s[:, 1], color='peru')

        # 绘制声源
        txd = env.get('tx_depth', 5)
        if not isinstance(txd, (list, np.ndarray)):
            txd = [txd]
        ax.plot([0] * len(txd), np.array(txd), marker='*', markersize=10, linestyle='', color='orangered')

        # 绘制接收器（如果需要）
        if draw_receivers:
            rxr = env.get('rx_range', 1000)
            rxd = env.get('rx_depth', 10)

            if not isinstance(rxr, (list, np.ndarray)):
                rxr = [rxr]
            if not isinstance(rxd, (list, np.ndarray)):
                rxd = [rxd]

            # 如果接收器数量不超过2000个，绘制所有接收器
            if len(rxr) * len(rxd) < 2000:
                for r in rxr:
                    ax.plot([r/divisor] * len(rxd), np.array(rxd), marker='o', linestyle='', color='midnightblue')

        return xlabel

    def update_environment(self, env):
        """
        更新环境示意图

        Args:
            env: 环境参数字典，包含水深、声源位置、接收器位置等
        """
        self.env_ax.clear()

        # 获取环境参数
        min_x = 0
        max_x = env.get('rx_range', 1000)
        if isinstance(max_x, (list, np.ndarray)):
            max_x = np.max(max_x)

        # 确定距离单位
        use_km = max_x > 10000
        divisor = 1000 if use_km else 1

        # 获取水深
        if 'surface' in env and env['surface'] is not None:
            min_y = np.min(env['surface'][:, 1])
        else:
            min_y = 0

        if isinstance(env.get('depth'), (list, np.ndarray)) and len(env['depth'].shape) > 1:
            max_y = np.max(env['depth'][:, 1])
        else:
            max_y = env.get('depth', 100)

        # 计算边距
        mgn_x = 0.01 * (max_x - min_x)
        mgn_y = 0.1 * (max_y - min_y)

        # 绘制环境轮廓
        xlabel = self.draw_environment_outline(self.env_ax, env, use_km=use_km)

        # 设置坐标轴
        self.env_ax.set_xlabel(xlabel)
        self.env_ax.set_ylabel('深度 (m)')
        self.env_ax.set_xlim(min_x/divisor - mgn_x/divisor, max_x/divisor + mgn_x/divisor)
        self.env_ax.set_ylim(min_y - mgn_y, max_y + mgn_y)
        self.env_ax.set_title('声传播环境')
        self.env_ax.grid(True)

        # 反转Y轴，使深度增加向下
        self.env_ax.invert_yaxis()

        # 更新图表
        self.env_fig.tight_layout()
        self.env_canvas.draw()

    def update_sound_speed_profile(self, env):
        """
        更新声速剖面图

        Args:
            env: 环境参数字典，包含声速剖面数据
        """
        self.ssp_ax.clear()

        # 获取声速剖面数据
        svp = env.get('soundspeed', 1500)

        # 处理不同类型的声速剖面数据
        if isinstance(svp, pd.DataFrame):
            # 转换DataFrame为numpy数组
            svp = np.hstack((np.array([svp.index]).T, np.asarray(svp)))

        if np.size(svp) == 1:
            # 常数声速
            if isinstance(env.get('depth'), (list, np.ndarray)) and len(env['depth'].shape) > 1:
                max_y = np.max(env['depth'][:, 1])
            else:
                max_y = env.get('depth', 100)

            # 绘制垂直线表示恒定声速
            self.ssp_ax.plot([svp, svp], [0, max_y], 'b-')
        elif isinstance(svp, np.ndarray) and svp.ndim == 2 and svp.shape[1] == 2:
            # 确保声速剖面数据是二维数组，且每行有两个元素（深度和声速）
            # 按深度排序
            svp = svp[svp[:, 0].argsort()]

            # 绘制原始数据点
            self.ssp_ax.plot(svp[:, 1], svp[:, 0], 'b.', markersize=8)

            if env.get('soundspeed_interp') == 'spline':
                # 样条插值声速剖面
                from scipy import interpolate

                # 样条插值
                try:
                    # 创建更密集的深度点以获得平滑的曲线
                    ynew = np.linspace(np.min(svp[:, 0]), np.max(svp[:, 0]), 100)
                    tck = interpolate.splrep(svp[:, 0], svp[:, 1], s=0)
                    xnew = interpolate.splev(ynew, tck, der=0)

                    # 绘制插值曲线
                    self.ssp_ax.plot(xnew, ynew, 'b-')
                except Exception as e:
                    print(f"声速剖面样条插值失败: {e}")
                    # 如果样条插值失败，退回到线性插值
                    self.ssp_ax.plot(svp[:, 1], svp[:, 0], 'b-')
            else:
                # 线性插值声速剖面
                self.ssp_ax.plot(svp[:, 1], svp[:, 0], 'b-')
        else:
            # 无效的声速剖面数据
            print(f"无效的声速剖面数据: {svp}")
            # 绘制默认声速剖面
            default_svp = np.array([[0, 1500], [100, 1500]])
            self.ssp_ax.plot(default_svp[:, 1], default_svp[:, 0], 'r-', label='默认声速剖面')
            self.ssp_ax.legend()

        # 反转Y轴，使深度增加向下
        self.ssp_ax.invert_yaxis()

        # 设置坐标轴
        self.ssp_ax.set_xlabel('声速 (m/s)')
        self.ssp_ax.set_ylabel('深度 (m)')
        self.ssp_ax.set_title('声速剖面')
        self.ssp_ax.grid(True)

        # 更新图表
        self.ssp_fig.tight_layout()
        self.ssp_canvas.draw()

    def update_rays(self, rays, env=None, ray_type='ray', invert_colors=False):
        """
        更新声线图

        Args:
            rays: 声线数据，包含声线路径和属性
            env: 环境参数字典，可选，用于绘制环境轮廓
            ray_type: 声线类型，'ray'表示普通声线，'eigenray'表示本征声线
            invert_colors: 是否反转颜色，True表示高强度声线使用白色，False表示高强度声线使用黑色
        """
        # 确定要更新的坐标轴
        ax = self.eigenray_ax if ray_type == 'eigenray' else self.ray_ax
        ax.clear()

        # 如果没有声线数据，直接返回
        if rays is None or len(rays) == 0:
            ax.set_title('本征声线' if ray_type == 'eigenray' else '声线追踪')
            ax.set_xlabel('距离 (m)')
            ax.set_ylabel('深度 (m)')
            ax.grid(True)
            self.ray_fig.tight_layout()
            self.ray_canvas.draw()
            return

        # 按照海底反射次数排序
        rays = rays.sort_values('bottom_bounces', ascending=False)

        # 确定最大振幅
        max_amp = np.max(np.abs(rays.bottom_bounces)) if len(rays.bottom_bounces) > 0 else 1
        if max_amp <= 0:
            max_amp = 1

        # 确定距离单位
        r = []
        for _, row in rays.iterrows():
            r += list(row.ray[:, 0])

        if max(r) - min(r) > 10000:
            divisor = 1000
            xlabel = '距离 (km)'
        else:
            divisor = 1
            xlabel = '距离 (m)'

        # 绘制每条声线
        for _, row in rays.iterrows():
            # 根据海底反射次数确定颜色深浅
            c = int(255 * np.abs(row.bottom_bounces) / max_amp)
            if invert_colors:
                c = 255 - c

            # 使用RGB颜色，与uwapm.py中的绘制方式一致
            color = (c/255, c/255, c/255)

            # 绘制声线
            ax.plot(row.ray[:, 0]/divisor, row.ray[:, 1], color=color)

        # 如果提供了环境参数，绘制环境轮廓
        if env is not None:
            # 确定x轴范围
            xr = (0, np.max(r))
            use_km = (xr[1] - xr[0]) > 10000

            # 使用通用方法绘制环境轮廓
            self.draw_environment_outline(ax, env, xr=xr, use_km=use_km)

        # 设置坐标轴
        ax.set_xlabel(xlabel)
        ax.set_ylabel('深度 (m)')
        ax.set_title('本征声线' if ray_type == 'eigenray' else '声线追踪')
        ax.grid(True)

        # 反转Y轴，使深度增加向下
        ax.invert_yaxis()

        # 更新图表
        self.ray_fig.tight_layout()
        self.ray_canvas.draw()

    def update_arrivals(self, arrivals, dB=False):
        """
        更新到达结构图和表格

        Args:
            arrivals: 到达结构数据，包含到达时间、幅度等信息
            dB: 是否使用分贝刻度，默认为False
        """
        # 清除图表
        self.arrivals_ax.clear()

        # 如果没有到达结构数据，直接返回
        if arrivals is None or len(arrivals) == 0:
            self.arrivals_ax.set_title('到达结构')
            self.arrivals_ax.set_xlabel('到达时间 (s)')
            self.arrivals_ax.set_ylabel('幅度')
            self.arrivals_ax.grid(True)
            self.arrivals_fig.tight_layout()
            self.arrivals_canvas.draw()

            # 清空表格
            self.arrivals_table.setRowCount(0)
            return

        # 获取到达时间范围
        t0 = min(arrivals.time_of_arrival)
        t1 = max(arrivals.time_of_arrival)

        # 根据是否使用分贝刻度设置不同的绘图方式
        if dB:
            # 计算最小分贝值（最大幅度-60dB）
            min_y = 20 * np.log10(np.max(np.abs(arrivals.arrival_amplitude))) - 60
            ylabel = '幅度 (dB)'
        else:
            # 绘制零线
            self.arrivals_ax.plot([t0, t1], [0, 0], 'b-')
            ylabel = '幅度'
            min_y = 0

        # 绘制每个到达的幅度线
        for _, row in arrivals.iterrows():
            t = row.time_of_arrival.real
            y = np.abs(row.arrival_amplitude)

            if dB:
                # 转换为分贝值，并确保不小于最小值
                y = max(20 * np.log10(np.finfo(float).eps + y), min_y)

            # 绘制垂直线，表示到达幅度
            self.arrivals_ax.plot([t, t], [min_y, y], 'b-')

        # 设置坐标轴
        self.arrivals_ax.set_xlabel('到达时间 (s)')
        self.arrivals_ax.set_ylabel(ylabel)
        if dB:
            self.arrivals_ax.set_ylim(min_y, min_y + 70)  # 设置分贝刻度范围
        self.arrivals_ax.set_title('到达结构')
        self.arrivals_ax.grid(True)

        # 更新图表
        self.arrivals_fig.tight_layout()
        self.arrivals_canvas.draw()

        # 更新表格
        self.arrivals_table.setRowCount(len(arrivals))

        for i, (_, row) in enumerate(arrivals.iterrows()):
            # 到达序号，只显示实部且应为整数
            if hasattr(row.arrival_number, 'real'):
                arrival_number = int(row.arrival_number.real)
            else:
                arrival_number = int(row.arrival_number)
            self.arrivals_table.setItem(i, 0, QTableWidgetItem(str(arrival_number)))

            # 到达时间 - 只显示实部
            if hasattr(row.time_of_arrival, 'real'):
                arrival_time = row.time_of_arrival.real
            else:
                arrival_time = row.time_of_arrival
            self.arrivals_table.setItem(i, 1, QTableWidgetItem(f"{arrival_time:.6f}"))

            # 幅度 - 使用科学计数法显示复数的实部和虚部
            amp_complex = row.arrival_amplitude

            # 检查是否为复数
            if hasattr(amp_complex, 'real') and hasattr(amp_complex, 'imag'):
                # 使用科学计数法格式化实部和虚部
                real_part = f"{amp_complex.real:.2e}"
                # 处理虚部的符号，避免出现 "+-" 的情况
                if amp_complex.imag >= 0:
                    imag_part = f"+{amp_complex.imag:.2e}"
                else:
                    imag_part = f"{amp_complex.imag:.2e}"  # 负数会自带负号
                amp_str = f"{real_part}{imag_part}j"
            else:
                # 如果不是复数，直接使用科学计数法格式化
                amp_str = f"{amp_complex:.2e}"

            self.arrivals_table.setItem(i, 2, QTableWidgetItem(amp_str))

            # 出射角 - 只显示实部
            if hasattr(row.angle_of_departure, 'real'):
                departure_angle = row.angle_of_departure.real
            else:
                departure_angle = row.angle_of_departure
            self.arrivals_table.setItem(i, 3, QTableWidgetItem(f"{departure_angle:.6f}"))

            # 到达角 - 只显示实部
            if hasattr(row.angle_of_arrival, 'real'):
                arrival_angle = row.angle_of_arrival.real
            else:
                arrival_angle = row.angle_of_arrival
            self.arrivals_table.setItem(i, 4, QTableWidgetItem(f"{arrival_angle:.6f}"))

            # 海面反射次数 - 只显示实部
            if hasattr(row.surface_bounces, 'real'):
                surface_bounces = int(row.surface_bounces.real)
            else:
                surface_bounces = int(row.surface_bounces)
            self.arrivals_table.setItem(i, 5, QTableWidgetItem(str(surface_bounces)))

            # 海底反射次数 - 只显示实部
            if hasattr(row.bottom_bounces, 'real'):
                bottom_bounces = int(row.bottom_bounces.real)
            else:
                bottom_bounces = int(row.bottom_bounces)
            self.arrivals_table.setItem(i, 6, QTableWidgetItem(str(bottom_bounces)))

    def update_transmission_loss(self, tloss, env=None, vmin=None, vmax=None, **kwargs):
        """
        更新传播损失图

        Args:
            tloss: 传播损失数据，DataFrame格式，索引为深度，列为距离
            env: 环境参数字典，可选，用于绘制环境轮廓
            vmin: 传播损失最小值 (dB)，默认为自动计算
            vmax: 传播损失最大值 (dB)，默认为自动计算
            **kwargs: 其他传递给imshow的参数
        """
        # 如果存在色温条，先移除
        if self.tl_colorbar is not None:
            self.tl_colorbar.remove()
            self.tl_colorbar = None

        # 清除图表
        self.tl_ax.clear()

        # 如果没有传播损失数据，直接返回
        if tloss is None or len(tloss) == 0:
            self.tl_ax.set_title('传播损失')
            self.tl_ax.set_xlabel('距离 (m)')
            self.tl_ax.set_ylabel('深度 (m)')
            self.tl_ax.grid(True)
            self.tl_fig.tight_layout()
            self.tl_canvas.draw()
            return

        # 获取距离和深度范围
        xr = (min(tloss.columns), max(tloss.columns))
        yr = (min(tloss.index), max(tloss.index))

        # 确定距离单位
        if xr[1] - xr[0] > 10000:
            xr = (min(tloss.columns) / 1000, max(tloss.columns) / 1000)
            xlabel = '距离 (km)'
        else:
            xlabel = '距离 (m)'

        # 计算传播损失（dB）
        # 处理NaN和无穷大
        pressure_values = np.abs(np.flipud(np.array(tloss)))
        pressure_values = np.nan_to_num(pressure_values, nan=1e-37, posinf=1.0, neginf=1e-37)
        # 设置最小阈值，避免log10出错
        pressure_values[pressure_values < 1e-37] = 1e-37
        # 计算传播损失（正dB值，符合声学领域惯例）
        trans_loss = -20 * np.log10(pressure_values)

        # 自动计算色标范围（如果未指定）
        if vmin is None or vmax is None:
            # 找出有效值（排除极小值和极大值）
            valid_values = trans_loss[np.isfinite(trans_loss)]
            if len(valid_values) > 0:
                # 计算中值和标准差
                tlmed = np.median(valid_values)
                tlstd = np.std(valid_values)

                # 计算色标范围（参考Bellhop的方法）
                auto_vmax = tlmed + 0.75 * tlstd
                auto_vmax = 10 * round(auto_vmax / 10)  # 四舍五入到最接近的10的倍数
                auto_vmin = auto_vmax - 50  # 最小值为最大值减去50dB

                # 使用自动计算的值或用户指定的值
                vmin = vmin if vmin is not None else auto_vmin
                vmax = vmax if vmax is not None else auto_vmax

        # 如果仍然没有设置vmin和vmax（可能是因为没有有效值），使用默认值
        if vmin is None:
            vmin = 40
        if vmax is None:
            vmax = 100

        # 使用反转的jet色标（符合声学领域惯例）
        cmap = plt.cm.jet.reversed()

        # 使用imshow直接显示像素值，保留干涉图样
        im = self.tl_ax.imshow(
            trans_loss,
            extent=[xr[0], xr[1], yr[0], yr[1]],
            aspect='auto',
            origin='upper',  # 使用upper原点，配合后面的invert_yaxis
            cmap=cmap,
            vmin=vmin,
            vmax=vmax,
            **kwargs
        )

        # 设置刻度线向外
        self.tl_ax.tick_params(direction='out')

        # 添加新的色温条
        try:
            self.tl_colorbar = self.tl_fig.colorbar(im, ax=self.tl_ax)
            self.tl_colorbar.set_label('传播损失 (dB)')
            self.tl_colorbar.ax.tick_params(direction='out')  # 色温条刻度线也向外
        except Exception as e:
            print(f"添加色温条时出错: {e}")
            self.tl_colorbar = None

        # 添加更多信息到标题（如果有环境参数）
        title = '传播损失'
        if env is not None:
            freq = env.get('frequency', None)
            tx_depth = env.get('tx_depth', None)
            if freq is not None and tx_depth is not None:
                if isinstance(tx_depth, (list, np.ndarray)):
                    tx_depth = tx_depth[0]  # 取第一个声源深度
                title = f'传播损失 (频率: {freq} Hz, 声源深度: {tx_depth} m)'
        self.tl_ax.set_title(title)

        # 如果提供了环境参数，绘制环境轮廓
        if env is not None:
            # 使用通用方法绘制环境轮廓，但不绘制接收器（因为传播损失图已经包含了接收器网格）
            use_km = xlabel == '距离 (km)'
            self.draw_environment_outline(self.tl_ax, env, xr=xr, use_km=use_km, draw_receivers=False)

        # 设置坐标轴
        self.tl_ax.set_xlabel(xlabel)
        self.tl_ax.set_ylabel('深度 (m)')
        self.tl_ax.set_title('传播损失')
        self.tl_ax.grid(True)

        # 反转Y轴，使深度增加向下
        self.tl_ax.invert_yaxis()

        # 更新图表
        self.tl_fig.tight_layout()
        self.tl_canvas.draw()

    def reset_all_charts(self):
        """
        重置所有图表
        """
        self.reset_environment_chart()
        self.reset_ssp_chart()
        self.reset_ray_charts()
        self.reset_arrivals_chart()
        self.reset_transmission_loss_chart()

    def reset_environment_chart(self):
        """
        重置环境示意图
        """
        self.env_ax.clear()
        self.env_ax.set_title('声传播环境')
        self.env_ax.set_xlabel('距离 (m)')
        self.env_ax.set_ylabel('深度 (m)')
        self.env_ax.grid(True)
        self.env_ax.invert_yaxis()  # 反转Y轴，使深度增加向下
        self.env_fig.tight_layout()
        self.env_canvas.draw()

    def reset_ssp_chart(self):
        """
        重置声速剖面图
        """
        self.ssp_ax.clear()
        self.ssp_ax.set_title('声速剖面')
        self.ssp_ax.set_xlabel('声速 (m/s)')
        self.ssp_ax.set_ylabel('深度 (m)')
        self.ssp_ax.grid(True)
        self.ssp_fig.tight_layout()
        self.ssp_canvas.draw()

    def reset_ray_charts(self):
        """
        重置声线图
        """
        self.ray_ax.clear()
        self.ray_ax.set_title('声线追踪')
        self.ray_ax.set_xlabel('距离 (m)')
        self.ray_ax.set_ylabel('深度 (m)')
        self.ray_ax.grid(True)
        self.ray_ax.invert_yaxis()  # 反转Y轴，使深度增加向下

        self.eigenray_ax.clear()
        self.eigenray_ax.set_title('本征声线')
        self.eigenray_ax.set_xlabel('距离 (m)')
        self.eigenray_ax.set_ylabel('深度 (m)')
        self.eigenray_ax.grid(True)
        self.eigenray_ax.invert_yaxis()  # 反转Y轴，使深度增加向下

        self.ray_fig.tight_layout()
        self.ray_canvas.draw()

    def reset_arrivals_chart(self):
        """
        重置到达结构图和表格
        """
        self.arrivals_ax.clear()
        self.arrivals_ax.set_title('到达结构')
        self.arrivals_ax.set_xlabel('到达时间 (s)')
        self.arrivals_ax.set_ylabel('幅度')
        self.arrivals_ax.grid(True)
        self.arrivals_fig.tight_layout()
        self.arrivals_canvas.draw()

        # 清空表格
        self.arrivals_table.setRowCount(0)

    def reset_transmission_loss_chart(self):
        """
        重置传播损失图
        """
        # 如果存在色温条，先移除
        if self.tl_colorbar is not None:
            self.tl_colorbar.remove()
            self.tl_colorbar = None

        self.tl_ax.clear()
        self.tl_ax.set_title('传播损失')
        self.tl_ax.set_xlabel('距离 (m)')
        self.tl_ax.set_ylabel('深度 (m)')
        self.tl_ax.grid(True)
        self.tl_ax.invert_yaxis()  # 反转Y轴，使深度增加向下
        self.tl_fig.tight_layout()
        self.tl_canvas.draw()

    # def update_broadband_arrivals(self, frequencies, arrivals_dict):
    #     """
    #     更新宽带计算结果

    #     Args:
    #         frequencies: 频率列表
    #         arrivals_dict: 到达结构字典，键为频率，值为对应的到达结构DataFrame
    #     """
    #     # 如果没有数据，直接返回
    #     if not arrivals_dict:
    #         return

    #     # 选择第一个频率的到达结构进行显示
    #     first_freq = sorted(arrivals_dict.keys())[0]
    #     arrivals = arrivals_dict[first_freq]

    #     # 更新到达结构图和表格
    #     self.update_arrivals(arrivals, dB=True)

    #     # 添加频率信息到标题
    #     self.arrivals_ax.set_title(f'到达结构 (频率: {first_freq} Hz)')
    #     self.arrivals_fig.tight_layout()
    #     self.arrivals_canvas.draw()

    #     # 在表格中添加频率列
    #     if 'frequency' in arrivals.columns:
    #         # 如果表格没有频率列，添加一列
    #         if self.arrivals_table.columnCount() < 8:
    #             self.arrivals_table.setColumnCount(8)
    #             headers = [
    #                 '到达序号', '到达时间 (s)', '幅度', '出射角 (°)', '到达角 (°)',
    #                 '海面反射次数', '海底反射次数', '频率 (Hz)'
    #             ]
    #             self.arrivals_table.setHorizontalHeaderLabels(headers)

    #         # 更新频率列
    #         for i, (_, row) in enumerate(arrivals.iterrows()):
    #             self.arrivals_table.setItem(i, 7, QTableWidgetItem(f"{row.frequency:.1f}"))

    def set_simulating_state(self, is_simulating):
        """
        设置仿真状态

        Args:
            is_simulating: 是否正在仿真
        """
        # 这里可以添加一些视觉反馈，例如禁用某些控件或显示进度条
        # 但由于PropagationView主要是显示结果，所以这里暂时不做特殊处理
        # 使用参数避免IDE警告
        if is_simulating:
            # 可以在这里添加仿真开始时的视觉反馈
            pass
        else:
            # 可以在这里添加仿真结束时的视觉反馈
            pass
