## 系统架构概述

复杂水声噪声仿真系统采用模块化、多层次的架构设计，将数据管理、仿真计算、视图更新和用户交互分离，形成清晰的职责划分。系统主要由以下核心组件构成：

1. **数据管理器**：中央数据存储和管理中心
2. **仿真控制器**：负责执行各类仿真任务和数据处理
3. **视图更新器**：负责更新UI显示
4. **主窗口**：用户交互界面
5. **模型类**：实现具体的仿真算法

## 各模块详细设计

### 1. 数据管理器 (SimulationDataManager)

数据管理器是系统的核心组件，作为中央数据存储和管理中心，负责集中管理所有仿真参数和结果数据。

**设计细节**：

* 采用线程安全的设计，使用可重入锁(RLock)保护共享数据
* 提供统一的数据访问接口，包括参数设置/获取、结果存储/读取
* 实现项目保存和加载功能，支持将整个项目状态序列化为JSON文件
* 提供数据变更通知机制，当数据更新时通过信号通知相关组件

**数据组织**：

* 全局参数（如采样率、持续时间）集中存储，适用于所有模块
* 按模块分类存储参数和结果（船舶辐射噪声、海洋环境噪声、声传播等）
* 参数数据以字典形式存储，便于序列化和UI交互
* 结果数据包含原始信号和处理后的数据（如PSD计算结果），支持大型数组的高效存储

**线程安全**：

* 所有数据访问方法都通过锁机制保护，确保多线程环境下的数据一致性
* 数据变更通知在锁外发送，避免死锁问题

### 2. 仿真控制器 (Simulation Controllers)

仿真控制器负责执行具体的仿真任务和数据处理，在后台线程中运行以保持UI响应性。

**设计细节**：

* 采用多层次结构：主控制器协调各专门控制器的工作
* 专门控制器（如ShipNoiseController）继承自QThread，在后台线程中执行仿真
* 通过信号机制与UI线程通信，报告仿真进度和状态
* 直接从数据管理器读取参数，将结果存储到数据管理器

**数据处理**：

* 负责所有仿真相关的数据处理，包括信号生成和分析（如PSD计算）
* 处理后的结果（原始信号、PSD等）一并存储到数据管理器，避免重复计算
* 不直接传递大量数据，而是通过数据管理器间接共享

**执行流程**:

1. 从数据管理器读取参数
2. 配置仿真模型
3. 执行仿真计算
4. 处理仿真结果（包括PSD计算等）
5. 将完整结果存储到数据管理器
6. 发送仿真完成信号（不包含结果数据）

### 3. 主控制器 (SimulationController)

主控制器协调各个专门控制器的工作，管理整体仿真流程，为主窗口提供高级接口。

**设计细节**：

* 创建和管理数据管理器和各专门控制器
* 提供高级仿真接口，简化主窗口对仿真过程的管理
* 处理模块间的依赖关系，确保仿真按正确顺序执行

**功能接口**：

* 提供各类仿真的启动方法（船舶噪声、环境噪声、声传播等）
* 提供项目保存和加载的高级接口
* 支持综合仿真，协调多个模块的交互

**依赖管理**：

* 检查仿真依赖条件（如声传播仿真可能依赖船舶噪声结果）
* 管理仿真参数的有效性验证
* 处理仿真异常和错误恢复

### 4. 视图更新器 (View Updaters)

视图更新器负责将仿真结果转换为视图更新，将UI更新逻辑从主窗口分离出来。

**设计细节**：

* 为每种视图创建专门的更新器（如ShipNoiseViewUpdater）
* 直接从数据管理器读取结果数据
* 响应数据管理器的数据变更信号，自动更新视图
* 不执行数据处理或计算，只负责数据展示

**更新机制**：

* 被动更新：响应数据管理器的数据变更信号
* 主动更新：可由主窗口触发更新
* 支持部分更新，只更新变化的数据

**视图适配**：

* 将数据管理器中的结果格式转换为视图组件需要的格式
* 处理视图特定的显示逻辑（如坐标轴范围、颜色映射等）
* 支持多种视图模式（时域、频域等）

### 5. 主窗口 (MainWindow)

主窗口负责整体UI布局和用户交互，是系统的用户界面入口。

**设计细节**：

* 创建和管理UI组件（视图区域、控制面板等）
* 创建主控制器和视图更新器
* 连接UI事件和系统功能
* 处理用户交互和反馈

**交互流程**：

1. 用户通过UI设置参数并启动仿真
2. 主窗口将参数存储到数据管理器
3. 主窗口通过主控制器启动仿真
4. 仿真控制器执行仿真并将结果存储到数据管理器
5. 数据管理器发送数据变更信号
6. 视图更新器响应信号，从数据管理器读取数据并更新视图
7. 主窗口处理仿真状态变化（如启用/禁用按钮、更新状态栏）

**项目管理**：

* 提供项目保存和加载的用户界面
* 在项目加载后更新UI控件以反映加载的参数
* 管理应用程序设置和用户偏好

## 模块交互流程

### 1. 船舶辐射噪声仿真流程

1. **参数设置**：

* 用户在船舶辐射噪声标签页设置参数并点击"开始仿真"
* 主窗口收集参数并调用 `data_manager.set_parameters("ship_noise", params)`
* 主窗口调用 `simulation_controller.simulate_ship_noise()`启动仿真

2. **仿真执行**：

* 船舶噪声控制器从数据管理器读取参数
* 控制器在后台线程中执行仿真，发送进度信号
* 控制器计算PSD等分析结果
* 控制器将原始信号和处理结果存储到数据管理器
* 控制器发送仿真完成信号

3. **结果显示**：

* 数据管理器发送数据变更信号
* 船舶噪声视图更新器响应信号，从数据管理器读取结果
* 视图更新器更新视图显示时域信号和PSD
* 主窗口处理仿真完成事件，更新UI状态

### 2. 多模型组合仿真流程

1. **前置仿真**：

* 用户先执行船舶辐射噪声仿真和海洋环境噪声仿真
* 结果分别存储在数据管理器中

2. **声传播仿真**：

* 声传播控制器从数据管理器读取船舶噪声结果作为输入
* 控制器执行声传播计算并存储结果
* 用户设置声传播参数并启动仿真

3. **综合分析**：

* 用户启动综合分析
* 分析控制器从数据管理器读取声传播结果和环境噪声结果
* 控制器执行叠加和声场特性计算
* 结果存储到数据管理器并显示在综合视图中

### 3. 项目保存和加载流程

1. **保存项目**：

* 用户点击"保存项目"
* 主窗口调用`simulation_controller.save_project(file_path)`
* 数据管理器将参数和结果序列化为JSON格式
* 大型数组数据特殊处理后保存

2. **加载项目**：

* 用户点击"加载项目"
* 主窗口调用`simulation_controller.load_project(file_path)`
* 数据管理器从JSON文件加载数据
* 数据管理器发送数据变更信号
* 视图更新器响应信号更新视图
* 主窗口更新UI控件以反映加载的参数

## 设计优势

1. **模块化与解耦**：各组件职责明确，相互独立，便于维护和扩展
2. **数据集中管理**：通过数据管理器集中管理所有数据，简化数据共享和持久化
3. **线程安全**：采用防御性同步机制，确保多线程环境下的数据一致性
4. **响应式UI**：仿真在后台线程执行，保持UI响应性
5. **灵活的数据流**：支持灵活的仿真流程，允许选择性地重用或重新生成数据
6. **完整的项目管理**：支持项目的保存和加载，便于用户管理工作

## 实现建议

1. **分阶段实现**：先实现核心组件和基本功能，再逐步添加高级特性
2. **优先数据管理器**：数据管理器是系统的核心，应优先实现并充分测试
3. **关注线程安全**：确保多线程环境下的数据一致性和UI更新安全
4. **处理大数据**：对于大型信号数据，需要特别注意内存管理和序列化策略
5. **用户体验**：提供清晰的进度反馈和错误处理，确保良好的用户体验

这种设计方案充分考虑了系统的功能需求、性能要求和可维护性，特别适合船舶辐射噪声仿真这类需要多模型组合、数据共享和灵活工作流的应用场景。通过统一的数据管理和清晰的职责划分，系统可以高效地支持复杂的仿真任务，同时保持代码的清晰和可维护性。
