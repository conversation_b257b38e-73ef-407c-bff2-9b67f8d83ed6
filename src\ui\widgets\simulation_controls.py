# -*- coding: utf-8 -*-
"""
仿真控制组件

提供仿真控制相关的UI组件
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, 
                            QPushButton, QProgressBar, QLabel,
                            QGroupBox)
from PyQt5.QtCore import pyqtSignal, Qt


class SimulationControlPanel(QWidget):
    """
    仿真控制面板
    
    提供开始、暂停、停止等仿真控制功能
    """
    
    # 自定义信号
    start_clicked = pyqtSignal()
    pause_clicked = pyqtSignal()
    stop_clicked = pyqtSignal()
    reset_clicked = pyqtSignal()
    
    def __init__(self, parent=None):
        """
        初始化仿真控制面板
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        
        # 创建布局
        layout = QVBoxLayout(self)
        
        # 创建按钮布局
        button_layout = QHBoxLayout()
        
        # 创建开始按钮
        self.start_button = QPushButton("开始仿真")
        self.start_button.clicked.connect(self.on_start_clicked)
        button_layout.addWidget(self.start_button)
        
        # 创建暂停按钮
        self.pause_button = QPushButton("暂停")
        self.pause_button.clicked.connect(self.on_pause_clicked)
        self.pause_button.setEnabled(False)
        button_layout.addWidget(self.pause_button)
        
        # 创建停止按钮
        self.stop_button = QPushButton("停止")
        self.stop_button.clicked.connect(self.on_stop_clicked)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        # 创建重置按钮
        self.reset_button = QPushButton("重置")
        self.reset_button.clicked.connect(self.on_reset_clicked)
        button_layout.addWidget(self.reset_button)
        
        # 添加按钮布局
        layout.addLayout(button_layout)
        
        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)
        
        # 创建状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
    
    def on_start_clicked(self):
        """
        开始按钮点击事件处理
        """
        self.start_button.setEnabled(False)
        self.pause_button.setEnabled(True)
        self.stop_button.setEnabled(True)
        self.reset_button.setEnabled(False)
        
        self.status_label.setText("仿真中...")
        
        # 发射信号
        self.start_clicked.emit()
    
    def on_pause_clicked(self):
        """
        暂停按钮点击事件处理
        """
        if self.pause_button.text() == "暂停":
            self.pause_button.setText("继续")
            self.status_label.setText("已暂停")
        else:
            self.pause_button.setText("暂停")
            self.status_label.setText("仿真中...")
        
        # 发射信号
        self.pause_clicked.emit()
    
    def on_stop_clicked(self):
        """
        停止按钮点击事件处理
        """
        self.start_button.setEnabled(True)
        self.pause_button.setEnabled(False)
        self.pause_button.setText("暂停")
        self.stop_button.setEnabled(False)
        self.reset_button.setEnabled(True)
        
        self.status_label.setText("已停止")
        
        # 发射信号
        self.stop_clicked.emit()
    
    def on_reset_clicked(self):
        """
        重置按钮点击事件处理
        """
        self.progress_bar.setValue(0)
        self.status_label.setText("就绪")
        
        # 发射信号
        self.reset_clicked.emit()
    
    def update_progress(self, value, status=None):
        """
        更新进度
        
        Args:
            value: 进度值（0-100）
            status: 状态文本
        """
        self.progress_bar.setValue(value)
        
        if status:
            self.status_label.setText(status)
        
        # 如果进度达到100%，恢复按钮状态
        if value >= 100:
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.pause_button.setText("暂停")
            self.stop_button.setEnabled(False)
            self.reset_button.setEnabled(True)
            
            if not status:
                self.status_label.setText("完成")


class SimulationStatusPanel(QGroupBox):
    """
    仿真状态面板
    
    显示仿真的状态信息
    """
    
    def __init__(self, title="仿真状态", parent=None):
        """
        初始化仿真状态面板
        
        Args:
            title: 面板标题
            parent: 父窗口
        """
        super().__init__(title, parent)
        
        # 创建布局
        layout = QVBoxLayout(self)
        
        # 创建状态标签
        self.elapsed_time_label = QLabel("已用时间: 0:00:00")
        layout.addWidget(self.elapsed_time_label)
        
        self.memory_usage_label = QLabel("内存使用: 0 MB")
        layout.addWidget(self.memory_usage_label)
        
        self.cpu_usage_label = QLabel("CPU使用: 0%")
        layout.addWidget(self.cpu_usage_label)
        
        self.current_step_label = QLabel("当前步骤: 无")
        layout.addWidget(self.current_step_label)
    
    def update_elapsed_time(self, hours, minutes, seconds):
        """
        更新已用时间
        
        Args:
            hours: 小时
            minutes: 分钟
            seconds: 秒
        """
        self.elapsed_time_label.setText(f"已用时间: {hours}:{minutes:02d}:{seconds:02d}")
    
    def update_memory_usage(self, usage_mb):
        """
        更新内存使用
        
        Args:
            usage_mb: 内存使用量（MB）
        """
        self.memory_usage_label.setText(f"内存使用: {usage_mb:.1f} MB")
    
    def update_cpu_usage(self, usage_percent):
        """
        更新CPU使用
        
        Args:
            usage_percent: CPU使用率（%）
        """
        self.cpu_usage_label.setText(f"CPU使用: {usage_percent:.1f}%")
    
    def update_current_step(self, step):
        """
        更新当前步骤
        
        Args:
            step: 步骤描述
        """
        self.current_step_label.setText(f"当前步骤: {step}")
