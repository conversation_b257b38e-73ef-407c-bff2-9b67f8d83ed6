# -*- coding: utf-8 -*-
"""
视图区域基类

提供视图区域的基本功能和接口
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QStackedWidget


class ViewArea(QWidget):
    """
    视图区域基类
    
    提供视图区域的基本功能和接口，包括视图切换等
    """
    
    def __init__(self, parent=None):
        """
        初始化视图区域
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        
        # 创建堆叠部件用于切换不同视图
        self.stack = QStackedWidget()
        
        # 设置布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.stack)
        
    def change_view(self, index):
        """
        切换当前视图
        
        Args:
            index: 视图索引
        """
        self.stack.setCurrentIndex(index)
