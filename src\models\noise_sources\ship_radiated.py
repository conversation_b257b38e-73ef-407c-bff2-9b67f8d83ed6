# -*- coding: utf-8 -*-
"""
船舶辐射噪声模型

实现船舶辐射噪声信号的模拟，包括连续谱、线谱和调制谱分量。
基于公式 S(t) = [1 + a(t)] × g_x(t) + g_L(t)
"""

import numpy as np
from scipy import signal


class ShipRadiatedNoise:
    """
    船舶辐射噪声模型

    模拟船舶辐射噪声的连续谱、线谱和调制谱特性。
    """

    def __init__(self, name="船舶辐射噪声"):
        """初始化船舶辐射噪声模型"""
        self.name = name

        # 连续谱参数
        self.f0 = 250.0  # 峰值频率 (Hz)
        self.sl0 = 130.0  # 峰值声级 (dB re 1μPa)
        self.a1_oct = 3.0  # 功率谱上升斜率 (dB/倍频程)
        self.a2_oct = -4.2  # 功率谱下降斜率 (dB/倍频程)

        # 线谱参数
        self.line_frequencies = None  # 线谱频率数组 (Hz)
        self.line_levels_diff = None  # 线谱相对连续谱的dB差值数组

        # 调制谱参数
        self.mod_f1 = 8.0  # 轴频 (Hz)
        self.mod_f2 = 32.0  # 叶频 (Hz)
        self.mod_A1 = 0.05  # 轴频分量振幅
        self.mod_A2 = 0.3   # 叶频分量振幅
        self.mod_p = 0.1    # 轴频衰减系数
        self.mod_q = 0.1    # 叶频衰减系数
        self.mod_N = 5      # 轴频谐波数量
        self.mod_M = 5      # 叶频谐波数量

        # 仿真参数
        self.fs = 10000  # 采样频率 (Hz)
        self.duration = 5.0  # 信号持续时间 (秒)
        self.filter_order = 16385  # 滤波器阶数

        # 缓存生成的信号
        self._cached_continuous_signal = None
        self._cached_continuous_scaling_factor = None
        self._cached_line_signal = None
        self._cached_modulation_signal = None
        self._cached_total_signal = None

    def set_continuous_params(self, f0=None, sl0=None, a1_oct=None, a2_oct=None):
        """
        设置连续谱参数

        Args:
            f0 (float, optional): 峰值频率 (Hz)
            sl0 (float, optional): 峰值声级 (dB re 1μPa)
            a1_oct (float, optional): 功率谱上升斜率 (dB/倍频程)
            a2_oct (float, optional): 功率谱下降斜率 (dB/倍频程)
        """
        if f0 is not None:
            if f0 <= 0:
                raise ValueError("峰值频率f0必须为正值")
            self.f0 = f0

        if sl0 is not None:
            self.sl0 = sl0

        if a1_oct is not None:
            if a1_oct < 0:
                raise ValueError("上升斜率a1_oct必须为非负值")
            self.a1_oct = a1_oct

        if a2_oct is not None:
            self.a2_oct = a2_oct

        # 清除缓存的信号
        self._cached_continuous_signal = None
        self._cached_continuous_scaling_factor = None
        self._cached_total_signal = None

    def set_line_params(self, frequencies, levels_diff):
        """
        设置线谱参数

        Args:
            frequencies (numpy.ndarray): 线谱频率数组 (Hz)
            levels_diff (numpy.ndarray): 线谱相对连续谱的dB差值数组
        """
        if not isinstance(frequencies, (list, np.ndarray)):
            raise ValueError("频率必须为列表或numpy数组")

        if not isinstance(levels_diff, (list, np.ndarray)):
            raise ValueError("电平差值必须为列表或numpy数组")

        if len(frequencies) != len(levels_diff):
            raise ValueError("频率数组和电平差值数组长度必须相同")

        self.line_frequencies = np.asarray(frequencies)
        self.line_levels_diff = np.asarray(levels_diff)

        # 清除缓存的信号
        self._cached_line_signal = None
        self._cached_total_signal = None

    def set_modulation_params(self, f1=None, f2=None, A1=None, A2=None, p=None, q=None, N=None, M=None):
        """
        设置调制谱参数

        Args:
            f1 (float, optional): 轴频 (Hz)
            f2 (float, optional): 叶频 (Hz)
            A1 (float, optional): 轴频分量振幅
            A2 (float, optional): 叶频分量振幅
            p (float, optional): 轴频衰减系数
            q (float, optional): 叶频衰减系数
            N (int, optional): 轴频谐波数量
            M (int, optional): 叶频谐波数量
        """
        if f1 is not None:
            if not (isinstance(f1, (int, float)) and f1 > 0):
                raise ValueError("轴频f1必须为正值")
            self.mod_f1 = f1

        if f2 is not None:
            if not (isinstance(f2, (int, float)) and f2 > 0):
                raise ValueError("叶频f2必须为正值")
            self.mod_f2 = f2

        if A1 is not None:
            if not (isinstance(A1, (int, float)) and A1 >= 0):
                raise ValueError("轴频分量振幅A1必须为非负值")
            self.mod_A1 = A1

        if A2 is not None:
            if not (isinstance(A2, (int, float)) and A2 >= 0):
                raise ValueError("叶频分量振幅A2必须为非负值")
            self.mod_A2 = A2

        if p is not None:
            if not (isinstance(p, (int, float)) and p >= 0):
                raise ValueError("轴频衰减系数p必须为非负值")
            self.mod_p = p

        if q is not None:
            if not (isinstance(q, (int, float)) and q >= 0):
                raise ValueError("叶频衰减系数q必须为非负值")
            self.mod_q = q

        if N is not None:
            if not (isinstance(N, int) and N > 0):
                raise ValueError("轴频谐波数量N必须为正整数")
            self.mod_N = N

        if M is not None:
            if not (isinstance(M, int) and M > 0):
                raise ValueError("叶频谐波数量M必须为正整数")
            self.mod_M = M

        # 清除缓存的调制谱信号和总信号
        self._cached_modulation_signal = None
        self._cached_total_signal = None

    def set_simulation_params(self, fs=None, duration=None, filter_order=None):
        """
        设置仿真参数

        Args:
            fs (int, optional): 采样频率 (Hz)
            duration (float, optional): 信号持续时间 (秒)
            filter_order (int, optional): 滤波器阶数
        """
        if fs is not None:
            if not (isinstance(fs, int) and fs > 0):
                raise ValueError("采样频率fs必须为正整数")
            self.fs = fs

        if duration is not None:
            if not (isinstance(duration, (int, float)) and duration > 0):
                raise ValueError("信号持续时间duration必须为正值")
            self.duration = duration

        if filter_order is not None:
            if not (isinstance(filter_order, int) and filter_order > 0):
                raise ValueError("滤波器阶数filter_order必须为正整数")
            self.filter_order = filter_order

        # 清除所有缓存的信号
        self._cached_continuous_signal = None
        self._cached_continuous_scaling_factor = None
        self._cached_line_signal = None
        self._cached_modulation_signal = None
        self._cached_total_signal = None

    def simulate_continuous_spectrum(self):
        """
        模拟连续谱信号

        Returns:
            numpy.ndarray: 连续谱信号
            float: 缩放因子
        """
        # 如果已经有缓存的信号，直接返回
        if self._cached_continuous_signal is not None:
            return self._cached_continuous_signal, self._cached_continuous_scaling_factor

        # 参数和常量
        f_nyq = self.fs / 2.0
        numtaps = self.filter_order

        # 使用generate_frequency_points方法生成频率点
        freq_pts = self.generate_frequency_points(points_per_octave=4)

        # 计算目标频率点的声级
        sl_values = self.calculate_sl(freq_pts)

        # 转换为线性幅度
        amps_linear = 10**(sl_values / 20.0)
        if freq_pts[0] == 0:
            amps_linear[0] = 0.0

        # 归一化频率和线性幅度用于FIR滤波器设计，以确保幅度在[0,1]范围内
        norm_freqs = freq_pts / f_nyq
        norm_amps = amps_linear / np.max(amps_linear)

        # 设计FIR滤波器
        b = signal.firwin2(numtaps, norm_freqs, norm_amps, window=None)

        # 生成白噪声，长度增加以补偿滤波器延迟
        n_samples = int(self.duration * self.fs)
        noise = np.random.randn(n_samples + numtaps - 1)

        # 滤波噪声
        filtered_signal = signal.lfilter(b, 1.0, noise)

        # 去除滤波器延迟
        filtered_signal = filtered_signal[numtaps-1:]

        # 截取所需长度
        filtered_signal = filtered_signal[:n_samples]

        # 使用改进的全频谱缩放方法计算缩放因子
        # 默认使用几何平均法，可以通过修改method参数在'geometric'和'arithmetic'之间切换
        scaling_method = 'geometric'  # 默认使用几何平均法
        # 使用peak_only=False参数，保持与原来相同的缩放效果
        scaling_factor = self._calculate_improved_scaling_factor(filtered_signal, method=scaling_method, peak_only=False)

        # 应用缩放因子
        continuous_signal = filtered_signal * scaling_factor

        # 缓存连续谱信号
        self._cached_continuous_signal = continuous_signal
        self._cached_continuous_scaling_factor = scaling_factor

        return continuous_signal, scaling_factor

    def simulate_line_spectrum(self):
        """
        模拟线谱信号

        Returns:
            numpy.ndarray: 线谱信号
        """
        # 如果已经有缓存的信号，直接返回
        if self._cached_line_signal is not None:
            return self._cached_line_signal

        # 检查是否设置了线谱参数
        if self.line_frequencies is None or self.line_levels_diff is None:
            # 如果没有设置线谱参数，返回零信号
            n_samples = int(self.duration * self.fs)
            self._cached_line_signal = np.zeros(n_samples)
            return self._cached_line_signal

        # 生成时间数组
        n_samples = int(self.duration * self.fs)
        t = np.linspace(0, self.duration, n_samples, endpoint=False)

        # 初始化线谱信号
        line_signal = np.zeros(n_samples)

        # 循环处理每个线谱分量
        for _, (fk, level_diff_k) in enumerate(zip(self.line_frequencies, self.line_levels_diff)):
            # 1. 计算该频率 fk 处的未调制连续谱电平 SL_cont(fk)
            # 确保单个频率值作为浮点数处理
            sl_continuous = float(self.calculate_sl(float(fk)))

            # 2. 计算该线谱的目标绝对电平 SL_line_target
            sl_line_target = sl_continuous + float(level_diff_k)

            # 3. 计算线谱所需贡献的线性功率 P_line_required
            p_line_target_linear = 10**(sl_line_target / 10)
            p_cont_linear = 10**(sl_continuous / 10)
            p_line_required = max(0, p_line_target_linear - p_cont_linear)  # 确保非负

            # 4. 计算幅度 Ak (核心假设: 基于 δf=1Hz)
            ak = np.sqrt(2 * p_line_required * 1.0)  # 明确假设 δf=1Hz

            # 5. 生成正弦波并累加
            phase_k = np.random.uniform(0, 2*np.pi)  # 随机相位
            line_signal += ak * np.sin(2 * np.pi * fk * t + phase_k)

        # 缓存线谱信号
        self._cached_line_signal = line_signal

        return line_signal

    def simulate_modulation_spectrum(self):
        """
        模拟调制谱分量

        水下航行器辐射噪声拥有明显的节奏感，这是因为螺旋桨在水中旋转一周，
        每个叶片产生的噪声对连续信号具有调制作用。使得叶频整数倍上的频谱强度
        远大于轴频整数倍的频谱强度。

        数学模型为：
        a(t) = ∑(k=1 to N) A1·e^(-pk)·sin(2πkf1t + φ1) + ∑(i=1 to M) A2·e^(-qi)·sin(2πif2t + φ2)

        其中：
        - f1: 轴频 (Hz)
        - f2: 叶频 (Hz)
        - φ1, φ2: 随机起伏相位
        - p, q: 衰减系数
        - if2: 叶频谐波频率
        - kf1: 非叶频及叶频谐波处的轴频谐波频率

        Returns:
            numpy.ndarray: 调制谱信号
        """
        # 如果已经有缓存的信号，直接返回
        if self._cached_modulation_signal is not None:
            return self._cached_modulation_signal

        # 生成时间数组
        n_samples = int(self.duration * self.fs)
        t = np.linspace(0, self.duration, n_samples, endpoint=False)

        # 生成随机相位
        phi1 = np.random.uniform(0, 2*np.pi)  # 轴频相位
        phi2 = np.random.uniform(0, 2*np.pi)  # 叶频相位

        # 初始化调制谱信号
        modulation_signal = np.zeros(n_samples)

        # 生成轴频分量
        for k in range(1, self.mod_N+1):
            # 检查轴频谐波是否与叶频谐波重叠
            # 叶频 f2 = f1 * 叶片数，因此当 k 是叶片数的倍数时，轴频谐波与叶频谐波重叠
            # 假设叶片数 = f2/f1
            blade_count = round(self.mod_f2 / self.mod_f1)
            is_overlap = (k % blade_count == 0)  # 如果 k 是叶片数的倍数，则重叠

            if not is_overlap:
                amplitude = self.mod_A1 * np.exp(-self.mod_p * k)
                modulation_signal += amplitude * np.sin(2 * np.pi * k * self.mod_f1 * t + phi1)

        # 生成叶频分量
        for i in range(1, self.mod_M+1):
            amplitude = self.mod_A2 * np.exp(-self.mod_q * i)
            modulation_signal += amplitude * np.sin(2 * np.pi * i * self.mod_f2 * t + phi2)

        # 缓存调制谱信号
        self._cached_modulation_signal = modulation_signal

        return modulation_signal

    def simulate_radiated_noise(self, include_line=True, include_modulation=False):
        """
        模拟船舶辐射噪声

        Args:
            include_line (bool): 是否包含线谱
            include_modulation (bool): 是否包含调制谱

        Returns:
            numpy.ndarray: 船舶辐射噪声信号
        """
        # 如果已经有缓存的信号，直接返回
        if self._cached_total_signal is not None:
            return self._cached_total_signal

        # 生成连续谱信号 g_x(t)
        continuous_signal, _ = self.simulate_continuous_spectrum()

        # 初始化辐射噪声信号
        total_signal = np.copy(continuous_signal)

        # 如果包含调制谱，生成调制谱分量 a(t)
        if include_modulation:
            modulation_signal = self.simulate_modulation_spectrum()
            # 应用调制: [1 + a(t)] × g_x(t)
            total_signal = (1 + modulation_signal) * continuous_signal

        # 如果包含线谱，生成线谱信号 g_L(t) 并叠加
        if include_line:
            line_signal = self.simulate_line_spectrum()
            # 叠加线谱: [1 + a(t)] × g_x(t) + g_L(t)
            total_signal += line_signal

        # 缓存总信号
        self._cached_total_signal = total_signal

        return total_signal

    def calculate_psd(self, signal_data):
        """
        计算功率谱密度

        Args:
            signal_data (numpy.ndarray): 信号数据

        Returns:
            tuple: (frequencies, psd_values, psd_db)
        """
        # 使用Welch方法计算PSD
        # 设置nperseg=fs，使分辨率delta f = 1Hz
        nperseg = self.fs

        # 计算功率谱密度
        frequencies, psd = signal.welch(signal_data, fs=self.fs, nperseg=nperseg,
                                      scaling='density', average='mean')

        # 转换为dB/Hz
        epsilon_db = 1e-20  # 避免log10(0)
        psd_db = 10 * np.log10(psd + epsilon_db)  # PSD使用10*log10

        return frequencies, psd, psd_db

    def _calculate_improved_scaling_factor(self, filtered_signal, method='geometric', peak_only=False):
        """
        计算基于全频谱的缩放因子

        使用对数频率轴上的dB值线性插值，计算在有效频率范围内的平均功率比值，
        得到一个更准确的缩放因子。

        支持两种计算方法：
        1. 几何平均法（'geometric'）：
           - 计算每个有效频率点的功率比值：r = target_psd / measured_psd
           - 计算这些比值的几何平均值：(r₁ × r₂ × ... × rₙ)^(1/n)
           - 计算幅度缩放因子：scaling_factor = sqrt(几何平均功率比)

        2. 算术平均法（'arithmetic'）：
           - 计算每个有效频率点的功率比值：r = target_psd / measured_psd
           - 计算这些比值的算术平均值：(r₁ + r₂ + ... + rₙ)/n
           - 计算幅度缩放因子：scaling_factor = sqrt(算术平均功率比)

        Args:
            filtered_signal: 滤波后的信号
            method: 计算方法，'geometric'（几何平均）或'arithmetic'（算术平均）
            peak_only: 是否只使用峰值点计算缩放因子，默认为False

        Returns:
            float: 缩放因子
        """
        from scipy.interpolate import interp1d
        import numpy as np

        # 验证方法参数
        if method not in ['geometric', 'arithmetic']:
            method = 'geometric'

        try:
            # 如果只使用峰值点计算缩放因子
            if peak_only:
                # 计算功率谱密度 (PSD)
                f_welch, Pxx_welch, _ = self.calculate_psd(filtered_signal)

                # 1. 找到计算出的功率谱密度psd中的最大值
                psd_peak = np.max(Pxx_welch)

                # 2. 计算sl0的线性PSD
                # 将dB转换为线性功率谱密度（注意这里是10^(SL0/10)而不是20）
                psd_target_peak = 10**(self.sl0 / 10.0)

                # 3. 计算功率缩放因子
                k_power = psd_target_peak / psd_peak

                # 4. 计算应用于时域信号的缩放因子（应用平方根）
                scaling_factor = np.sqrt(k_power)

                return scaling_factor

            # 否则使用全频谱缩放方法
            # 1. 计算实际PSD
            f_welch, Pxx_welch, _ = self.calculate_psd(filtered_signal)

            # 2. 准备目标频率和PSD
            # 使用generate_frequency_points方法生成频率点
            target_freqs = self.generate_frequency_points(points_per_octave=4)

            # 计算目标频率点的声级
            target_psd_db = self.calculate_sl(target_freqs)

            # 3. 确定有效频率范围
            min_target_f = target_freqs.min()
            max_target_f = target_freqs.max()
            valid_indices = (f_welch >= min_target_f) & (f_welch <= max_target_f)
            f_welch_valid = f_welch[valid_indices]
            Pxx_welch_valid = Pxx_welch[valid_indices]

            # 4. 在对数频率轴上对目标PSD进行插值
            # 避免log(0)问题
            log_target_freqs = np.log10(target_freqs + 1e-9)
            log_f_welch_valid = np.log10(f_welch_valid + 1e-9)

            # 创建插值函数
            interp_func = interp1d(log_target_freqs, target_psd_db, kind='linear',
                                  bounds_error=False, fill_value='extrapolate')

            # 对有效频率点进行插值
            target_psd_db_interp = interp_func(log_f_welch_valid)

            # 5. 确定有效计算点（排除插值结果中的异常值）
            valid_calc_indices = np.isfinite(target_psd_db_interp)

            if np.sum(valid_calc_indices) == 0:
                scaling_factor = 1.0  # 出错时返回默认值
            else:
                # 根据选择的方法计算缩放因子
                if method == 'geometric':
                    # 几何平均法（直接使用基础定义）
                    # 将目标PSD转换为线性值
                    target_psd_linear = 10**(target_psd_db_interp[valid_calc_indices] / 10)

                    # 计算线性功率比值 r = target_psd / measured_psd
                    power_ratios = target_psd_linear / (Pxx_welch_valid[valid_calc_indices] + 1e-20)  # 避免除以零

                    # 计算几何平均值：(r₁ × r₂ × ... × rₙ)^(1/n)
                    # 等价于 exp(mean(log(r)))
                    log_ratios = np.log(power_ratios + 1e-20)  # 避免log(0)
                    mean_log_ratio = np.mean(log_ratios)
                    geo_mean_power_ratio = np.exp(mean_log_ratio)

                    # 计算幅度缩放因子（功率比的平方根）
                    scaling_factor = np.sqrt(geo_mean_power_ratio)

                else:  # 'arithmetic'
                    # 算术平均法（线性域平均）
                    # 将目标PSD转换为线性值
                    target_psd_linear = 10**(target_psd_db_interp[valid_calc_indices] / 10)

                    # 计算功率比值
                    power_ratios = target_psd_linear / (Pxx_welch_valid[valid_calc_indices] + 1e-20)  # 避免除以零

                    # 计算功率比值的算术平均值
                    mean_power_ratio = np.mean(power_ratios)

                    # 计算幅度缩放因子
                    scaling_factor = np.sqrt(mean_power_ratio)

            return scaling_factor

        except Exception as e:
            print(f"计算缩放因子时出错: {e}")
            return 1.0  # 出错时返回默认值

    def reset_cache(self):
        """
        重置缓存的信号，强制在下次调用时重新生成信号
        """
        self._cached_continuous_signal = None
        self._cached_continuous_scaling_factor = None
        self._cached_line_signal = None
        self._cached_modulation_signal = None
        self._cached_total_signal = None

    def generate_frequency_points(self, points_per_octave=4):
        """
        生成从峰值频率向两侧按倍频程采样的频率点

        Args:
            points_per_octave (int): 每倍频程的点数，默认为4

        Returns:
            numpy.ndarray: 频率点数组
        """
        # 获取奈奎斯特频率
        f_nyq = self.fs / 2.0

        # 计算倍频程因子
        octave_factor = 2 ** (1 / points_per_octave)

        # 初始化频率点集合，包含0Hz、1Hz、峰值频率和奈奎斯特频率
        freq_pts = {0.0, 1.0, self.f0, f_nyq}

        # 从峰值频率向低频方向采样
        f_curr = self.f0
        while f_curr > 1.0:
            for i in range(1, points_per_octave):
                # 在一个倍频程内均匀分布points_per_octave个点
                f_next = f_curr / (octave_factor ** i)
                if f_next >= 1.0:
                    freq_pts.add(f_next)
            f_curr /= 2.0  # 移动到下一个倍频程
            freq_pts.add(f_curr)  # 添加倍频程的边界点

        # 从峰值频率向高频方向采样
        f_curr = self.f0
        while f_curr < f_nyq:
            for i in range(1, points_per_octave):
                # 在一个倍频程内均匀分布points_per_octave个点
                f_next = f_curr * (octave_factor ** i)
                if f_next <= f_nyq:
                    freq_pts.add(f_next)
            f_curr *= 2.0  # 移动到下一个倍频程
            if f_curr <= f_nyq:
                freq_pts.add(f_curr)  # 添加倍频程的边界点

        # 排序并转换为数组
        freq_pts = sorted([f for f in freq_pts if 0 <= f <= f_nyq])
        return np.array(freq_pts)

    def calculate_sl(self, frequency):
        """
        计算连续谱在指定频率处的声级

        Args:
            frequency (float or numpy.ndarray): 频率 (Hz)

        Returns:
            float or numpy.ndarray: 声级 (dB re 1μPa)
        """
        f = np.asarray(frequency)
        sl = np.zeros_like(f, dtype=float)

        # 避免log2(0)问题
        f = np.maximum(f, 1e-9)

        # 上升斜率部分 (f < f0)
        mask_low = (f < self.f0)
        sl[mask_low] = self.sl0 + self.a1_oct * np.log2(f[mask_low] / self.f0)

        # 峰值 (f == f0)
        sl[f == self.f0] = self.sl0

        # 下降斜率部分 (f > f0)
        mask_high = (f > self.f0)
        sl[mask_high] = self.sl0 + self.a2_oct * np.log2(f[mask_high] / self.f0)

        return sl
