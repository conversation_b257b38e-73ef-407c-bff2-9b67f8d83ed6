# -*- coding: utf-8 -*-
"""
综合仿真模块测试脚本

用于测试综合仿真模块阶段二第一部分的功能。
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from src.models.integrated_simulation import IntegratedSimulation
from src.models.noise_sources.ship_radiated import ShipRadiatedNoise

# 添加matplotlib中文支持
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
matplotlib.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号



def create_ship_noise_signal(fs=44100, duration=5.0):
    """
    使用船舶辐射噪声模型创建源信号

    Args:
        fs: 采样率 (Hz)
        duration: 持续时间 (s)

    Returns:
        tuple: (signal, metadata)
    """
    # 创建船舶辐射噪声模型实例
    ship_model = ShipRadiatedNoise()

    # 设置采样率和持续时间
    ship_model.fs = fs
    ship_model.duration = duration

    # 设置滤波器阶数
    ship_model.filter_order = 16385

    # 设置连续谱参数（使用默认值）
    ship_model.f0 = 250.0  # 峰值频率 (Hz)
    ship_model.sl0 = 130.0  # 峰值声级 (dB re 1μPa)
    ship_model.a1_oct = 3.0  # 功率谱上升斜率 (dB/倍频程)
    ship_model.a2_oct = -4.2  # 功率谱下降斜率 (dB/倍频程)

    # 生成船舶辐射噪声信号
    signal = ship_model.simulate_radiated_noise(include_line=True, include_modulation=True)

    # 创建元数据
    metadata = {
        'fs': fs,
        'duration': duration,
        'name': 'ship_noise_result',
        'generation_time': '2023-05-15T10:30:45.123456'
    }

    return signal, metadata

def test_integrated_simulation():
    """
    测试综合仿真模块
    """
    # 使用指定的信道数据目录
    channel_data_dir = r"E:\作业\毕业设计\channel_data\channel_data_20250513_005236"

    # 创建综合仿真模型
    fs = 44100
    model = IntegratedSimulation(fs)

    # 加载信道数据
    if not model.load_channel_data(channel_data_dir):
        print("加载信道数据失败")
        return

    # 获取源信号时长建议
    min_duration, recommended_duration = model.get_duration_suggestions()
    print(f"建议的最小源信号时长: {min_duration} 秒")
    print(f"推荐的源信号时长: {recommended_duration} 秒")

    # 创建船舶辐射噪声信号，使用推荐的时长
    source_signal, metadata = create_ship_noise_signal(fs=fs, duration=recommended_duration)

    # 设置源信号
    model.set_source_signal(source_signal, metadata)

    # 测试使用全部到达声线
    model.set_truncation_parameters(use_all_arrivals=True)
    print("使用全部到达声线进行测试")

    # 处理信号
    results = model.process_signal()
    if results is None:
        print("处理信号失败")
        return

    # 打印结果信息
    print(f"全局最早到达时间: {results['t_first_global']:.6f} 秒")
    print(f"全局最晚到达时间: {results['t_last_global']:.6f} 秒")
    print(f"有效的全局最晚到达时间: {results['t_last_global_eff']:.6f} 秒")
    print(f"最大相对时延: {results['max_relative_delay']:.6f} 秒")
    print(f"冲击响应长度: {results['impulse_response_length']} 样本")

    # 绘制源信号和接收信号
    plt.figure(figsize=(12, 8))

    # 绘制源信号
    plt.subplot(2, 1, 1)
    plt.plot(results['source_signal']['time_data'], results['source_signal']['signal'])
    plt.title('源信号')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.grid(True)

    # 绘制接收信号（第一个阵元）
    plt.subplot(2, 1, 2)
    element_id = 0
    if element_id in results['received_signals']:
        plt.plot(results['received_signals'][element_id]['time_data'], results['received_signals'][element_id]['signal'])
        plt.title(f'接收信号 (阵元 #{element_id})')
        plt.xlabel('时间 (s)')
        plt.ylabel('幅度')
        plt.grid(True)

    plt.tight_layout()
    plt.savefig('test_integrated_simulation_results.png')
    plt.close()

    # 测试设置截断时间
    truncation_time = results['t_first_global'] + 0.5  # 设置一个合理的截断时间
    model.set_truncation_parameters(use_all_arrivals=False, truncation_time=truncation_time)
    print(f"使用截断时间 {truncation_time:.6f} 秒进行测试")

    # 处理信号
    results_truncated = model.process_signal()
    if results_truncated is None:
        print("处理信号失败")
        return

    # 打印结果信息
    print(f"全局最早到达时间: {results_truncated['t_first_global']:.6f} 秒")
    print(f"全局最晚到达时间: {results_truncated['t_last_global']:.6f} 秒")
    print(f"有效的全局最晚到达时间: {results_truncated['t_last_global_eff']:.6f} 秒")
    print(f"最大相对时延: {results_truncated['max_relative_delay']:.6f} 秒")
    print(f"冲击响应长度: {results_truncated['impulse_response_length']} 样本")

    # 绘制源信号和接收信号（截断时间）
    plt.figure(figsize=(12, 8))

    # 绘制源信号
    plt.subplot(2, 1, 1)
    plt.plot(results_truncated['source_signal']['time_data'], results_truncated['source_signal']['signal'])
    plt.title('源信号')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.grid(True)

    # 绘制接收信号（第一个阵元）
    plt.subplot(2, 1, 2)
    element_id = 0
    if element_id in results_truncated['received_signals']:
        plt.plot(results_truncated['received_signals'][element_id]['time_data'], results_truncated['received_signals'][element_id]['signal'])
        plt.title(f'接收信号 (阵元 #{element_id}，截断时间 {truncation_time:.6f} 秒)')
        plt.xlabel('时间 (s)')
        plt.ylabel('幅度')
        plt.grid(True)

    plt.tight_layout()
    plt.savefig('test_integrated_simulation_results_truncated.png')
    plt.close()

    print("测试完成，结果已保存到 test_integrated_simulation_results.png 和 test_integrated_simulation_results_truncated.png")

if __name__ == '__main__':
    test_integrated_simulation()
