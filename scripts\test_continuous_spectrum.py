import numpy as np
import scipy.signal as signal
import matplotlib.pyplot as plt

# -- 1. Define Parameters --

# Given spectral parameters
f0 = 249.0      # Peak frequency (Hz)
SL0 = 130.16    # Peak Sound Level (dB re 1 uPa) at f0
a1 = 3.0        # Rising slope (dB/Oct)
a2 = -4.236     # Falling slope (dB/Oct)

# Simulation parameters
fs = 20000       # Sampling frequency (Hz). Choose > 2 * max frequency of interest
duration = 2    # Signal duration (seconds)
num_samples = int(fs * duration)
f_nyquist = fs / 2.0

# FIR filter design parameters
num_taps = 16385 # Filter order + 1 (number of coefficients). Should be odd for Type I linear phase.
                # Higher order allows sharper transitions.

# -- 2. Define Target Spectrum Function --

def calculate_target_sl(freq, f0, SL0, a1, a2):
    """Calculates the target Sound Level (SL) in dB based on the formula."""
    sl = np.zeros_like(freq)
    # Avoid log2(0) issues, handle f=0 separately if needed, here assume f > 0
    freq = np.maximum(freq, 1e-9) # Prevent log2(0)

    # Rising slope part (f < f0)
    mask_low = (freq < f0)
    # Original formula: SL0 - a1 * log2(f0 / f) = SL0 + a1 * log2(f / f0)
    sl[mask_low] = SL0 + a1 * np.log2(freq[mask_low] / f0)

    # Peak (f == f0) - handled by continuity or explicitly setting
    sl[freq == f0] = SL0

    # Falling slope part (f > f0)
    mask_high = (freq > f0)
    sl[mask_high] = SL0 + a2 * np.log2(freq[mask_high] / f0)

    return sl

# -- 3. Prepare fir2 Inputs --

# Define frequency points for fir2 design. Include 0, f0, f_nyquist.
# Use octave steps around f0, ensuring points cover the desired range.
num_octaves_down = 11 # How many octaves below f0
num_octaves_up = 11   # How many octaves above f0

freq_pts = [1.0]
# Frequencies below f0
for i in range(num_octaves_down, 0, -1):
    f = f0 / (2**i)
    # Ensure we don't go below a minimum practical frequency (e.g., 1 Hz)
    if f >= 1.0:
        freq_pts.append(f)
freq_pts.append(f0)

# Frequencies above f0
for i in range(1, num_octaves_up + 1):
    f = f0 * (2**i)
    # Ensure we don't exceed Nyquist
    if f < f_nyquist:
        freq_pts.append(f)
freq_pts.append(f_nyquist)

freq_pts = np.array(sorted(list(set(freq_pts)))) # Remove duplicates and sort
print(f"Using {len(freq_pts)} frequency points for fir2 design.")
print(f"Frequency points: {freq_pts}")

# Calculate target SL at these points
sl_pts = calculate_target_sl(freq_pts, f0, SL0, a1, a2)
print(f"Target SL at frequency points: {sl_pts}")

# Convert SL (dB) to linear magnitude (Pressure, proportional to sqrt(Power))
# Amplitude = 10^(SL/20)
mag_pts = 10**(sl_pts / 20.0)
print(f"Magnitude at frequency points: {mag_pts}")

# Special handling for f=0: Extrapolate or set to a small value
# Let's extrapolate using the a1 slope from the lowest calculated point
if freq_pts[0] == 0.0:
     # Find first point > 0
    first_pos_idx = np.where(freq_pts > 0)[0][0]
    f1 = freq_pts[first_pos_idx]
    sl1 = sl_pts[first_pos_idx]
    # Approximate SL at a very small frequency (e.g., 0.1 Hz) using the a1 slope
    f_near_zero = 0.1
    sl_near_zero = sl1 + a1 * np.log2(f_near_zero / f1) if f1 > f_near_zero else sl1 - 200 # Assign very low dB if f1 is too small
    mag_pts[0] = 10**(sl_near_zero / 20.0)
    # Alternatively, simply set mag_pts[0] = 0 or a very small number
    # mag_pts[0] = 1e-10

# Normalize magnitude so the maximum is 1.0 (required by fir2 interpretation)
mmax = np.max(mag_pts)
if mmax > 0:
    norm_mag_pts = mag_pts / mmax
else:
    norm_mag_pts = np.zeros_like(mag_pts) # Avoid division by zero if all magnitudes are zero

# Normalize frequency points to [0, 1] for fir2
f_norm_pts = freq_pts / f_nyquist

# Ensure first point is 0 and last is 1
f_norm_pts[0] = 0.0
f_norm_pts[-1] = 1.0

# -- 4. Design FIR Filter --
print("Designing FIR filter...")
fir_coeffs = signal.firwin2(num_taps, f_norm_pts, norm_mag_pts)
print(f"Filter design complete. Number of coefficients: {len(fir_coeffs)}")

# -- 5. Generate White Noise --
print("Generating white noise...")
# Use np.random.randn for Gaussian white noise (mean 0, variance 1)
white_noise = np.random.randn(num_samples)

# -- 6. Apply Filter --
print("Applying filter to white noise...")
filtered_noise = signal.lfilter(fir_coeffs, [1.0], white_noise)

# 绘制白噪声的功率谱密度以及模拟的噪声信号的功率谱密度
# 绘制白噪声的功率谱密度
plt.figure(figsize=(10, 6))
# 使用Welch方法估计功率谱密度
nperseg_welch = min(num_taps * 2, num_samples // 4)  # 设置分段长度
freq_axis_psd_white, psd_white = signal.welch(white_noise, fs, nperseg=fs//10, scaling='density')
# 转换为dB单位
# psd_white_db = 10 * np.log10(psd_white + 1e-17)  # 添加小量避免log10(0)
plt.plot(freq_axis_psd_white, psd_white, label='白噪声功率谱密度')
plt.title('白噪声功率谱密度')
plt.xlabel('频率 (Hz)')
plt.ylabel('功率谱密度 (dB/Hz)')
plt.grid(True)
plt.legend()
plt.show()

# 绘制模拟噪声信号的功率谱密度
plt.figure(figsize=(10, 6))
# 使用Welch方法估计功率谱密度
freq_axis_psd_filtered, psd_filtered = signal.welch(filtered_noise, fs, nperseg=fs//2, noverlap=fs//4, scaling='density')
# 转换为dB单位
psd_filtered_db = 10 * np.log10(psd_filtered + 1e-17)
plt.plot(freq_axis_psd_filtered, psd_filtered_db, label='模拟噪声功率谱密度')
plt.title('模拟噪声功率谱密度')
plt.xlabel('频率 (Hz)')
plt.ylabel('功率谱密度 (dB/Hz)')
plt.grid(True)
plt.legend()
plt.show()


# 绘制原始噪声时域信号
plt.figure(figsize=(10, 6))
time_axis = np.arange(num_samples) / fs
plt.plot(time_axis, white_noise)

# 绘制滤波后的噪声时域信号
plt.figure(figsize=(10, 6))
time_axis = np.arange(num_samples) / fs
plt.plot(time_axis, filtered_noise)
plt.title('滤波后的噪声信号 (时域)')
plt.xlabel('时间 (s)')
plt.ylabel('幅度')
plt.grid(True)
plt.xlim(0, 1)
plt.show()



# -- 7. Analyze and Plot --
print("Plotting results...")

# Plot White Noise Time Domain
plt.figure(figsize=(12, 8))
plt.subplot(2, 1, 1)
time_axis = np.arange(num_samples) / fs
plt.plot(time_axis, white_noise)
plt.title('Generated White Noise (Time Domain)')
plt.xlabel('Time (s)')
plt.ylabel('Amplitude')
plt.grid(True)
plt.xlim(0, 1)

# Plot Filtered Noise Power Spectral Density (PSD)
plt.subplot(2, 1, 2)
# Use Welch's method for PSD estimation
# nperseg controls frequency resolution vs. variance trade-off
nperseg_welch = min(num_taps * 2, num_samples // 4) # Example segment length
freq_axis_psd, psd = signal.welch(filtered_noise, fs, nperseg=fs//10, scaling='density')

# Convert PSD to dB. The absolute level depends on noise variance and filter gain.
# We plot 10*log10(PSD) relative to some reference (or just the shape).
# To approximate the original SL scale, we might need calibration,
# but the shape should match the target SL shape.
psd_db = 10 * np.log10(psd + 1e-17) # Add small epsilon to avoid log10(0)

# Find the peak of the calculated PSD to align plots vertically (optional)
peak_psd_db = np.max(psd_db)
db_offset = SL0 - peak_psd_db # Calculate offset to align peak at SL0

plt.semilogx(freq_axis_psd, psd_db + db_offset, label='Simulated Spectrum (PSD)')

# Plot the target spectrum for comparison (using a denser frequency grid)
freq_target_plot = np.logspace(np.log10(max(1, freq_axis_psd[1])), np.log10(f_nyquist), 500)
sl_target_plot = calculate_target_sl(freq_target_plot, f0, SL0, a1, a2)
plt.semilogx(freq_target_plot, sl_target_plot, 'r--', label='Target Spectrum (Model)')

plt.title('Simulated Continuous Spectrum (PSD)')
plt.xlabel('Frequency (Hz)')
plt.ylabel('Power Spectral Density (dB re target peak)') # Adjusted ylabel
plt.legend()
plt.grid(True, which='both')
plt.ylim(SL0 - 60, SL0 + 10) # Adjust y-limits for better view
plt.xlim(10, f_nyquist)     # Focus on relevant frequency range

plt.tight_layout()
plt.show()

# Optional: Plot Filter Frequency Response
plt.figure(figsize=(10, 6))
w, h = signal.freqz(fir_coeffs, worN=8000)
freq_resp_hz = w * f_nyquist / np.pi
resp_db = 20 * np.log10(np.abs(h) + 1e-9)

# Normalize filter response peak to SL0 for comparison
filter_peak_db = np.max(resp_db)
filter_offset = SL0 - (filter_peak_db + 20*np.log10(mmax)) # Adjust for normalization in fir2 input

plt.semilogx(freq_resp_hz, resp_db + filter_offset, label='Designed FIR Filter Response')
plt.semilogx(freq_target_plot, sl_target_plot, 'r--', label='Target Spectrum (Model)')
plt.title('FIR Filter Frequency Response vs. Target')
plt.xlabel('Frequency (Hz)')
plt.ylabel('Magnitude (dB)')
plt.legend()
plt.grid(True, which='both')
plt.ylim(SL0 - 60, SL0 + 10)
plt.xlim(10, f_nyquist)
plt.show()

print("Simulation finished.")