# 水声噪声仿真系统

## 项目概述

水声噪声仿真系统是一个专业的水声学仿真平台，用于模拟海洋环境中的声学传播和噪声特性。系统集成了BELLHOP声学模型，为水声学研究人员、声纳系统设计工程师以及相关专业的学生和教师提供了一个完整的仿真解决方案。

### 核心功能

系统通过四个主要功能模块实现从噪声源建模到空间声场分析的完整仿真流程：

#### 🚢 船舶辐射噪声仿真
- **连续谱建模**：基于物理模型生成船舶宽带噪声
- **线谱建模**：模拟螺旋桨和机械设备产生的窄带噪声
- **工况参数计算**：根据船舶航行参数自动估算噪声特性
- **调制谱支持**：模拟螺旋桨调制效应

#### 🌊 海洋环境噪声仿真
- **Wenz曲线建模**：基于经典海洋噪声模型
- **交互式谱定义**：通过图形界面直观定义噪声功率谱密度
- **智能频谱外插**：自动补全完整频率范围的噪声特性
<!-- - **预设环境模板**：提供典型海洋环境噪声配置 -->

#### 🔊 声传播环境仿真
- **BELLHOP集成**：使用业界标准的声线追踪模型
- **复杂环境建模**：支持变深度、分层海洋环境
- **多径传播计算**：精确模拟声波的多路径传播效应
- **传播损失分析**：计算频率相关的声传播特性
- **信道数据制备**：并行调用Bellhop计算不同频率下的不同位置的到达信息

#### 📡 综合仿真与空间分析
- **多阵元信号合成**：模拟阵列接收系统的信号处理过程
- **波束形成分析**：计算延迟-求和波束形成器的指向性特性
- **空间相关分析**：分析阵元间的互功率谱和相干函数
<!-- - **信噪比评估**：评估目标信号与环境噪声的空间分布特性 -->

### 系统特色

- **物理真实性**：基于成熟的声学理论和模型，确保仿真结果的物理可信度
- **模块化设计**：各功能模块独立运行，支持灵活的仿真流程组合
- **交互式界面**：直观的图形用户界面，支持实时参数调整和结果可视化
- **完整工作流**：从噪声源建模到空间声场分析的端到端仿真能力

## 安装与运行

### 环境要求

- **Python 3.8+**
- **PyQt5** - 图形用户界面框架
- **NumPy, SciPy** - 科学计算库
- **Matplotlib** - 数据可视化
- **arlpy** - 水声学工具包
- **BELLHOP** - 声学传播模型（通过arlpy调用）

### 安装步骤

1. **克隆仓库**
   ```bash
   git clone <repository-url>
   cd 水声噪声仿真系统
   ```

2. **安装Python依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置BELLHOP环境**
   - 确保系统已安装BELLHOP声学模型
   - arlpy会自动处理BELLHOP的调用，无需额外配置

### 运行应用

```bash
python main.py
```

## 快速开始

### 基本仿真流程

1. **设置船舶噪声源**
   - 切换到"船舶辐射噪声"标签页
   - 设置连续谱参数或使用工况参数计算
   - 点击"开始仿真"生成噪声信号

2. **定义海洋环境噪声**
   - 切换到"海洋环境噪声"标签页
   - 在Wenz曲线图上选择点定义噪声谱
   - 执行外插和仿真生成环境噪声

3. **配置声传播环境**
   - 切换到"声传播环境"标签页
   - 设置海洋环境参数和阵列几何
   - 执行声传播计算生成信道数据

4. **综合仿真分析**
   - 切换到"综合仿真"标签页
   - 加载信道数据并执行信号处理
   - 分析多阵元接收信号和空间特性

### 项目管理

- **新建项目**：文件 → 新建项目
- **保存项目**：文件 → 保存项目
- **加载项目**：文件 → 打开项目
- **导出信号**：工具 → 导出信号

## 项目结构与开发指南

```
├── src/                    # 源代码目录
│   ├── core/              # 核心功能模块
│   ├── ui/                # 用户界面组件
│   ├── models/            # 仿真模型实现
│   └── utils/             # 工具函数和数据结构
├── docs/                  # 项目文档
├── data/                  # 数据文件（Wenz曲线等）
├── tests/                 # 测试代码
└── requirements.txt       # Python依赖列表
```

详细的开发指南和系统架构说明请参考 `docs/` 目录下的文档：
- `docs/system_architecture.md` - 系统架构设计
- `docs/user_guide.md` - 用户使用指南
- `docs/design_notes.md` - 设计说明和开发规范

## 技术支持

如有问题或建议，请查阅项目文档或提交Issue。
