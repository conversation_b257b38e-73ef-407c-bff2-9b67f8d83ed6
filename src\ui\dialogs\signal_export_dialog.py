# -*- coding: utf-8 -*-
"""
信号导出对话框

提供用户友好的信号导出界面，支持信号选择、格式选择、阵元选择等功能。
"""

import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QComboBox, QPushButton,
                            QGroupBox, QListWidget, QListWidgetItem, QProgressBar,
                            QFileDialog, QMessageBox, QTextEdit, QSplitter,
                            QFrame, QButtonGroup, QRadioButton)
from PyQt5.QtCore import Qt, QThread, pyqtSignal


class SignalExportWorker(QThread):
    """
    信号导出工作线程
    """
    progress = pyqtSignal(int)
    finished = pyqtSignal(str)  # 导出完成，参数为导出路径
    error = pyqtSignal(str)     # 导出错误

    def __init__(self, export_manager, module_name, signal_key, export_path, format_type, options):
        super().__init__()
        self.export_manager = export_manager
        self.module_name = module_name
        self.signal_key = signal_key
        self.export_path = export_path
        self.format_type = format_type
        self.options = options

        # 连接信号 - 正确的连接方式
        self.export_manager.export_progress.connect(self.progress)
        self.export_manager.export_completed.connect(self.finished)
        self.export_manager.export_error.connect(self.error)

    def run(self):
        """执行导出任务"""
        self.export_manager.export_signal(
            self.module_name, self.signal_key, self.export_path,
            self.format_type, self.options
        )


class SignalExportDialog(QDialog):
    """
    信号导出对话框
    """

    def __init__(self, parent=None, data_manager=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.export_manager = None
        self.available_signals = {}
        self.current_signal_info = None
        self.export_worker = None

        self.setWindowTitle("导出信号")
        self.setModal(True)
        self.resize(800, 600)

        self.init_ui()
        self.load_available_signals()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)

        # 左侧：信号选择
        left_frame = QFrame()
        left_layout = QVBoxLayout(left_frame)

        # 信号选择组
        signal_group = QGroupBox("选择信号")
        signal_layout = QVBoxLayout(signal_group)

        self.signal_list = QListWidget()
        self.signal_list.itemSelectionChanged.connect(self.on_signal_selection_changed)
        signal_layout.addWidget(self.signal_list)

        left_layout.addWidget(signal_group)

        # 信号信息组
        info_group = QGroupBox("信号信息")
        info_layout = QVBoxLayout(info_group)

        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        self.info_text.setMaximumHeight(150)
        info_layout.addWidget(self.info_text)

        left_layout.addWidget(info_group)

        splitter.addWidget(left_frame)

        # 右侧：导出设置
        right_frame = QFrame()
        right_layout = QVBoxLayout(right_frame)

        # 格式选择组
        format_group = QGroupBox("导出格式")
        format_layout = QVBoxLayout(format_group)

        self.format_button_group = QButtonGroup()
        self.wav_radio = QRadioButton("WAV音频文件 (*.wav)")
        self.npz_radio = QRadioButton("NumPy压缩文件 (*.npz)")
        self.mat_radio = QRadioButton("MATLAB文件 (*.mat)")

        self.wav_radio.setChecked(True)  # 默认选择WAV格式

        self.format_button_group.addButton(self.wav_radio, 0)
        self.format_button_group.addButton(self.npz_radio, 1)
        self.format_button_group.addButton(self.mat_radio, 2)

        format_layout.addWidget(self.wav_radio)
        format_layout.addWidget(self.npz_radio)
        format_layout.addWidget(self.mat_radio)

        right_layout.addWidget(format_group)

        # 移除阵元选择功能，简化UI - 统一导出所有阵元

        # 导出选项组
        options_group = QGroupBox("导出选项")
        options_layout = QGridLayout(options_group)

        # 导出模式选择（仅对多阵元信号显示）
        self.export_mode_label = QLabel("导出模式:")
        self.export_mode_combo = QComboBox()
        self.export_mode_combo.addItems(["单文件（所有阵元）", "多文件（每阵元一个文件）"])
        self.export_mode_combo.setCurrentIndex(0)
        self.export_mode_combo.setToolTip(
            "导出模式：\n"
            "• 单文件：所有阵元打包到一个文件中\n"
            "• 多文件：每个阵元导出为独立文件，以阵元ID命名"
        )
        self.export_mode_combo.currentIndexChanged.connect(self.on_export_mode_changed)

        options_layout.addWidget(self.export_mode_label, 0, 0)
        options_layout.addWidget(self.export_mode_combo, 0, 1)

        # 归一化模式选择
        self.normalize_label = QLabel("归一化:")
        self.normalize_combo = QComboBox()
        self.normalize_combo.addItems(["无归一化", "全局归一化"])
        self.normalize_combo.setCurrentIndex(1)  # 默认选择全局归一化
        self.normalize_combo.setToolTip(
            "归一化模式：\n"
            "• 无归一化：保持原始幅度，适用于科学分析\n"
            "• 全局归一化：所有阵元使用相同因子，保持相对幅度关系\n"
            "缩放因子会保存在元数据中，可用于恢复原始幅度"
        )

        options_layout.addWidget(self.normalize_label, 1, 0)
        options_layout.addWidget(self.normalize_combo, 1, 1)

        # WAV格式特有选项
        self.bit_depth_label = QLabel("位深度:")
        self.bit_depth_combo = QComboBox()
        self.bit_depth_combo.addItems(["16位", "32位", "浮点"])
        self.bit_depth_combo.setCurrentText("16位")
        self.bit_depth_combo.setToolTip(
            "WAV文件的位深度：\n"
            "• 16位：标准音频格式，文件较小\n"
            "• 32位：更高精度，文件较大\n"
            "• 浮点：保持浮点精度，专业音频软件支持"
        )

        options_layout.addWidget(self.bit_depth_label, 2, 0)
        options_layout.addWidget(self.bit_depth_combo, 2, 1)

        # 初始时隐藏导出模式选项（仅对多阵元信号显示）
        self.export_mode_label.setVisible(False)
        self.export_mode_combo.setVisible(False)

        right_layout.addWidget(options_group)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        right_layout.addWidget(self.progress_bar)

        # 按钮
        button_layout = QHBoxLayout()

        self.export_button = QPushButton("导出")
        self.export_button.clicked.connect(self.on_export_clicked)
        self.export_button.setEnabled(False)

        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addStretch()
        button_layout.addWidget(self.export_button)
        button_layout.addWidget(self.cancel_button)

        right_layout.addLayout(button_layout)

        splitter.addWidget(right_frame)

        # 设置分割器比例
        splitter.setSizes([400, 400])

        # 连接格式选择信号
        self.format_button_group.buttonClicked.connect(self.on_format_changed)

    def load_available_signals(self):
        """加载可用的信号列表"""
        if not self.data_manager:
            return

        # 导入并创建导出管理器
        from src.core.export.signal_export_manager import SignalExportManager
        self.export_manager = SignalExportManager(self.data_manager)

        # 获取可用信号
        self.available_signals = self.export_manager.get_available_signals()

        # 填充信号列表
        self.signal_list.clear()

        module_names = {
            'ship_noise': '船舶辐射噪声',
            'ambient_noise': '海洋环境噪声',
            'integrated': '综合仿真'
        }

        for module_name, signals in self.available_signals.items():
            module_display_name = module_names.get(module_name, module_name)

            for signal_info in signals:
                item_text = f"[{module_display_name}] {signal_info['name']}"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, {
                    'module_name': module_name,
                    'signal_info': signal_info
                })
                self.signal_list.addItem(item)

    def on_signal_selection_changed(self):
        """信号选择变化处理"""
        current_item = self.signal_list.currentItem()
        if not current_item:
            self.current_signal_info = None
            self.export_button.setEnabled(False)
            self.info_text.clear()
            self.channel_group.setVisible(False)
            return

        # 获取信号信息
        data = current_item.data(Qt.UserRole)
        self.current_signal_info = data

        signal_info = data['signal_info']

        # 更新信息显示
        info_text = f"信号名称: {signal_info['name']}\n"
        info_text += f"描述: {signal_info['description']}\n"

        metadata = signal_info.get('metadata', {})
        if 'fs' in metadata:
            info_text += f"采样率: {metadata['fs']} Hz\n"
        if 'duration' in metadata:
            info_text += f"持续时间: {metadata['duration']} 秒\n"
        if 'signal_shape' in metadata:
            shape = metadata['signal_shape']
            if isinstance(shape, (list, tuple)) and len(shape) == 2:
                info_text += f"信号形状: {shape[0]}个阵元 × {shape[1]}个采样点\n"
            else:
                info_text += f"信号长度: {shape}个采样点\n"

        self.info_text.setText(info_text)

        # 处理多阵元信号的导出模式选择
        is_multi_channel = signal_info.get('is_multi_channel', False)
        if is_multi_channel and 'signal_shape' in metadata:
            shape = metadata['signal_shape']
            if isinstance(shape, (list, tuple)) and len(shape) == 2:
                # 显示导出模式选择
                self.export_mode_label.setVisible(True)
                self.export_mode_combo.setVisible(True)
            else:
                self.export_mode_label.setVisible(False)
                self.export_mode_combo.setVisible(False)
        else:
            self.export_mode_label.setVisible(False)
            self.export_mode_combo.setVisible(False)

        self.export_button.setEnabled(True)

    # 移除了阵元选择相关方法，简化UI逻辑

    def on_export_mode_changed(self):
        """导出模式变化处理"""
        is_single_file = self.export_mode_combo.currentIndex() == 0

        # 更新提示信息
        if is_single_file:
            self.export_mode_combo.setToolTip(
                "单文件模式：\n"
                "• 所有阵元打包到一个多声道文件中\n"
                "• 每个声道对应一个阵元\n"
                "• 文件包含声道映射信息"
            )
        else:
            self.export_mode_combo.setToolTip(
                "多文件模式：\n"
                "• 每个阵元导出为独立文件\n"
                "• 文件名格式：基础名_element_阵元ID.扩展名\n"
                "• 自动导出所有阵元"
            )

    def on_format_changed(self):
        """格式选择变化处理"""
        # WAV格式特有选项只在选择WAV时显示
        is_wav = self.wav_radio.isChecked()
        self.bit_depth_label.setVisible(is_wav)
        self.bit_depth_combo.setVisible(is_wav)

        # 根据格式调整归一化默认值
        if is_wav:
            # WAV格式推荐全局归一化
            self.normalize_combo.setCurrentIndex(1)  # 全局归一化
        else:
            # NPZ/MAT格式可以选择无归一化
            self.normalize_combo.setCurrentIndex(0)  # 无归一化

    def on_export_clicked(self):
        """导出按钮点击处理"""
        if not self.current_signal_info:
            QMessageBox.warning(self, "警告", "请先选择要导出的信号")
            return

        # 获取导出格式
        if self.wav_radio.isChecked():
            format_type = 'wav'
            file_filter = "WAV音频文件 (*.wav)"
            default_ext = '.wav'
        elif self.npz_radio.isChecked():
            format_type = 'npz'
            file_filter = "NumPy压缩文件 (*.npz)"
            default_ext = '.npz'
        else:  # mat格式
            format_type = 'mat'
            file_filter = "MATLAB文件 (*.mat)"
            default_ext = '.mat'

        # 选择保存路径
        signal_info = self.current_signal_info['signal_info']
        default_filename = signal_info['name'].replace(' ', '_') + default_ext

        export_path, _ = QFileDialog.getSaveFileName(
            self, "导出信号", default_filename, file_filter
        )

        if not export_path:
            return

        # 准备导出选项
        options = {}

        # 归一化模式
        normalize_index = self.normalize_combo.currentIndex()
        if normalize_index == 0:
            options['normalize'] = 'none'
        else:  # normalize_index == 1
            options['normalize'] = 'global'

        # 导出模式（仅对多阵元信号）
        if self.export_mode_label.isVisible():
            export_mode_index = self.export_mode_combo.currentIndex()
            if export_mode_index == 0:
                options['export_mode'] = 'single_file'
            else:
                options['export_mode'] = 'multiple_files'

        # WAV格式特有选项
        if format_type == 'wav':
            bit_depth_text = self.bit_depth_combo.currentText()
            if bit_depth_text == "16位":
                options['bit_depth'] = 16
            elif bit_depth_text == "32位":
                options['bit_depth'] = 32
            else:  # 浮点
                options['bit_depth'] = 32  # 使用32位浮点

        # 移除阵元选择逻辑，统一导出所有阵元

        # 开始导出
        self.start_export(export_path, format_type, options)

    def start_export(self, export_path, format_type, options):
        """开始导出任务"""
        # 禁用界面
        self.export_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 创建并启动工作线程
        module_name = self.current_signal_info['module_name']
        signal_key = self.current_signal_info['signal_info']['key']

        self.export_worker = SignalExportWorker(
            self.export_manager, module_name, signal_key,
            export_path, format_type, options
        )

        # 连接信号
        self.export_worker.progress.connect(self.progress_bar.setValue)
        self.export_worker.finished.connect(self.on_export_finished)
        self.export_worker.error.connect(self.on_export_error)

        # 启动线程
        self.export_worker.start()

    def on_export_finished(self, export_path):
        """导出完成处理"""
        self.progress_bar.setVisible(False)
        self.export_button.setEnabled(True)

        QMessageBox.information(self, "导出完成", f"信号已成功导出到:\n{export_path}")

        # 询问是否打开文件所在目录
        reply = QMessageBox.question(
            self, "打开目录", "是否打开文件所在目录？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            import subprocess
            import platform

            try:
                if platform.system() == "Windows":
                    subprocess.run(["explorer", "/select,", export_path])
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", "-R", export_path])
                else:  # Linux
                    subprocess.run(["xdg-open", os.path.dirname(export_path)])
            except Exception as e:
                print(f"打开目录失败: {e}")

    def on_export_error(self, error_message):
        """导出错误处理"""
        self.progress_bar.setVisible(False)
        self.export_button.setEnabled(True)

        QMessageBox.critical(self, "导出错误", error_message)

    def closeEvent(self, event):
        """关闭事件处理"""
        # 如果有正在运行的导出任务，先停止
        if self.export_worker and self.export_worker.isRunning():
            self.export_worker.terminate()
            self.export_worker.wait()

        event.accept()
