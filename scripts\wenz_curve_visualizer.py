import json
import os
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.ticker import ScalarFormatter
import matplotlib.font_manager as fm
from scipy import interpolate
# import matplotlib.colors as mcolors  # 暂时不需要

# 设置中文字体
def set_chinese_font():
    """
    设置matplotlib使用中文字体
    """
    try:
        # 查找系统中的中文字体
        chinese_fonts = [f.name for f in fm.fontManager.ttflist
                        if 'SimHei' in f.name or 'Microsoft YaHei' in f.name
                        or 'SimSun' in f.name or 'FangSong' in f.name]

        if chinese_fonts:
            # 使用找到的第一个中文字体
            plt.rcParams['font.sans-serif'] = [chinese_fonts[0]] + plt.rcParams['font.sans-serif']
            print(f"使用中文字体: {chinese_fonts[0]}")
        else:
            # 如果没有找到中文字体，尝试使用一些常见的中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'FangSong'] + plt.rcParams['font.sans-serif']
            print("尝试使用默认中文字体")

        # 解决负号显示问题
        plt.rcParams['axes.unicode_minus'] = False
    except Exception as e:
        print(f"设置中文字体时出错: {e}")
        print("将使用默认字体，中文可能无法正确显示")

# 基本配置
JSON_FILE = os.path.join('data', 'wenz_curves.json')  # 使用预处理的JSON文件
STYLE_CONFIG_FILE = os.path.join('data', 'wenz_style_config.json')  # 样式配置文件

# 加载样式配置
def load_style_config(config_file=STYLE_CONFIG_FILE):
    """
    从JSON文件加载Wenz曲线样式配置

    Args:
        config_file: 配置文件路径

    Returns:
        包含样式配置的字典，如果文件不存在则返回默认配置
    """
    # 默认配置
    default_config = {
        "plot_style": {
            "title": "Wenz Curves",
            "title_fontsize": 14,
            "title_fontweight": "bold",
            "xlabel": "频率 (Hz)",
            "xlabel_fontsize": 12,
            "ylabel": "噪声谱级 (dB re 1 μPa^2/Hz)",
            "ylabel_fontsize": 12,
            "min_freq": 1,
            "max_freq": 100000,
            "min_power": 0,
            "max_power": 140,
            "grid_major_alpha": 0.7,
            "grid_major_linestyle": "--",
            "grid_minor_alpha": 0.4,
            "grid_minor_linestyle": ":",
            "tick_labelsize": 10,
            "legend_loc": "upper right",
            "legend_fontsize": 10,
            "figsize": [12, 8],
            "dpi": 300
        },
        "band_areas": [
            {
                "name": "low-frequency-very-shallow-wind",
                "top": "low-frequency-very-shallow-wind-top",
                "bottom": "low-frequency-very-shallow-wind-bottom",
                "color": "#C7AD7D",
                "alpha": 0.5,
                "label": "极浅海域风生噪声"
            },
            {
                "name": "usual-traffic-noise-shallow",
                "top": "usual-traffic-noise-shallow-top",
                "bottom": "usual-traffic-noise-shallow-bottom",
                "color": "red",
                "alpha": 0.5,
                "label": "浅海交通噪声"
            },
            {
                "name": "usual-traffic-noise-deep",
                "top": "usual-traffic-noise-deep-top",
                "bottom": "usual-traffic-noise-deep-bottom",
                "color": "#FF69B4",
                "alpha": 0.7,
                "label": "深海交通噪声"
            }
        ],
        "important_curves": {
            "thermal-noise": {
                "color": "gold",
                "label": "热噪声",
                "linestyle": "--",
                "linewidth": 2.0,
                "alpha": 1.0
            },
            "heavy-precipitation": {
                "color": "purple",
                "label": "强降水",
                "linestyle": "--",
                "linewidth": 2.0,
                "alpha": 1.0
            },
            "earth-quakes-and-explosions": {
                "color": "blue",
                "label": "地震和爆炸",
                "linestyle": "-.",
                "linewidth": 2.0,
                "alpha": 1.0
            },
            "heavy-traffic-noise": {
                "color": "red",
                "label": "重度交通噪声",
                "linestyle": "--",
                "linewidth": 2.0,
                "alpha": 1.0
            },
            "limits-of-prevailing-noise-top": {
                "color": "black",
                "label": "主要噪声范围",
                "linestyle": "-",
                "linewidth": 1.5,
                "alpha": 1.0,
                "is_limit_pair": "true",
                "pair_with": "limits-of-prevailing-noise-bottom"
            },
            "limits-of-prevailing-noise-bottom": {
                "color": "black",
                "linestyle": "-",
                "linewidth": 1.5,
                "alpha": 1.0,
                "is_limit_pair": "true",
                "show_in_legend": "false"
            }
        },
        "sea_state_curves": {
            "color_map": "Blues",
            "color_range": [0.5, 1.0],
            "label_prefix": "海况 ",
            "linestyle": "-",
            "linewidth": 1.5,
            "alpha": 1.0
        }
    }

    try:
        if os.path.exists(config_file):
            print(f"加载样式配置: {config_file}")
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("样式配置加载成功")
            return config
        else:
            print(f"样式配置文件 {config_file} 不存在，使用默认配置")
            return default_config
    except Exception as e:
        print(f"加载样式配置出错: {e}，使用默认配置")
        return default_config

def load_wenz_data(json_file=JSON_FILE, use_interpolated=True):
    """
    从JSON文件加载Wenz曲线数据

    Args:
        json_file: JSON文件路径
        use_interpolated: 是否使用插值后的数据

    Returns:
        包含所有曲线数据的字典、带状区域数据和元数据
    """
    print(f"加载Wenz曲线数据: {json_file}")
    with open(json_file, 'r') as f:
        data = json.load(f)

    raw_curves = data['curves']
    metadata = data['metadata']

    # 加载带状区域数据（如果存在）
    band_areas = data.get('band_areas', {})

    # 提取原始数据或插值数据
    curves = {}
    for curve_name, curve_data in raw_curves.items():
        if use_interpolated and 'interpolated' in curve_data:
            curves[curve_name] = curve_data['interpolated']
        else:
            curves[curve_name] = curve_data['original']

    # 打印一些基本信息
    print(f"加载了 {len(curves)} 条曲线")
    if band_areas:
        print(f"加载了 {len(band_areas)} 个带状区域预处理数据")
    print(f"数据单位: 频率 - {metadata['units']['frequency']}, 功率谱级 - {metadata['units']['power_level']}")
    if use_interpolated:
        print(f"使用插值后的数据，平均每条曲线 {metadata['stats']['interpolated'][list(curves.keys())[0]]['point_count']} 个点")
    else:
        print(f"使用原始数据，平均每条曲线 {metadata['stats']['original'][list(curves.keys())[0]]['point_count']} 个点")

    return curves, band_areas, metadata

def interpolate_curve(freqs, powers, num_points=1000, kind='cubic'):
    """
    对曲线进行插值，生成更平滑的曲线
    注意：由于我们使用的是预处理的JSON数据，此函数通常不会被调用
    保留此函数仅用于特殊情况下的手动插值

    Args:
        freqs: 频率数组
        powers: 功率谱级数组
        num_points: 插值后的点数
        kind: 插值方法，可选 'linear', 'cubic', 'quadratic'

    Returns:
        插值后的频率数组和功率谱级数组
    """
    # 确保数据按频率排序
    sorted_indices = np.argsort(freqs)
    freqs = np.array(freqs)[sorted_indices]
    powers = np.array(powers)[sorted_indices]

    # 对数频率插值
    log_freqs = np.log10(freqs)

    # 创建插值函数
    try:
        interp_func = interpolate.interp1d(
            log_freqs, powers,
            kind=kind,
            bounds_error=False,
            fill_value="extrapolate"
        )

        # 生成新的频率点（对数均匀分布）
        new_log_freqs = np.linspace(log_freqs.min(), log_freqs.max(), num_points)
        new_freqs = 10 ** new_log_freqs

        # 计算插值后的功率谱级
        new_powers = interp_func(new_log_freqs)

        return new_freqs, new_powers
    except Exception as e:
        print(f"插值出错: {e}")
        return freqs, powers  # 如果插值失败，返回原始数据

def plot_single_curve(ax, curve_name, freqs, powers, color=None, linestyle='-', linewidth=1.5,
                     interpolate=False, num_points=1000, label=None, alpha=1.0):
    """
    绘制单条曲线

    Args:
        ax: matplotlib轴对象
        curve_name: 曲线名称
        freqs: 频率数组
        powers: 功率谱级数组
        color: 线条颜色
        linestyle: 线型
        linewidth: 线宽
        interpolate: 是否进行插值（默认为False，因为使用的是预处理的插值数据）
        num_points: 插值点数
        label: 图例标签，如果为None则使用curve_name
        alpha: 透明度

    Returns:
        绘制的线条对象
    """
    if interpolate and len(freqs) > 2:
        # 进行插值，生成更平滑的曲线
        smooth_freqs, smooth_powers = interpolate_curve(freqs, powers, num_points)

        # 绘制平滑曲线
        line, = ax.semilogx(smooth_freqs, smooth_powers,
                           color=color,
                           linestyle=linestyle,
                           linewidth=linewidth,
                           label=label if label is not None else curve_name,
                           alpha=alpha)
    else:
        # 直接绘制原始数据点
        line, = ax.semilogx(freqs, powers,
                           color=color,
                           linestyle=linestyle,
                           linewidth=linewidth,
                           label=label if label is not None else curve_name,
                           alpha=alpha)

    return line

def plot_band_area(ax, top_curve_name, bottom_curve_name, top_freqs, top_powers, bottom_freqs, bottom_powers,
                  color=None, alpha=0.3, interpolate=False, num_points=1000, label=None, band_data=None):
    """
    绘制带状区域（使用fill_between）

    Args:
        ax: matplotlib轴对象
        top_curve_name: 上边界曲线名称
        bottom_curve_name: 下边界曲线名称
        top_freqs: 上边界频率数组
        top_powers: 上边界功率谱级数组
        bottom_freqs: 下边界频率数组
        bottom_powers: 下边界功率谱级数组
        color: 填充颜色
        alpha: 透明度
        interpolate: 是否进行插值（已废弃，保留参数是为了兼容性）
        num_points: 插值点数（已废弃，保留参数是为了兼容性）
        label: 图例标签
        band_data: 预处理好的带状区域数据，如果提供则直接使用

    Returns:
        填充区域对象
    """
    # 如果提供了预处理好的带状区域数据，则直接使用
    if band_data is not None:
        common_freqs = np.array(band_data['common_frequencies'])
        top_interp = np.array(band_data['top_powers'])
        bottom_interp = np.array(band_data['bottom_powers'])

        # 绘制填充区域
        fill = ax.fill_between(common_freqs, bottom_interp, top_interp,
                              color=color, alpha=alpha,
                              label=label if label is not None else f"{top_curve_name}-{bottom_curve_name}")
        return fill

    # 以下代码仅在没有预处理数据时使用，为了向后兼容
    # 确定共同的频率范围
    min_freq = max(min(top_freqs), min(bottom_freqs))
    max_freq = min(max(top_freqs), max(bottom_freqs))

    # 创建共同的频率点，增加点数以提高分辨率
    common_freqs = np.logspace(np.log10(min_freq), np.log10(max_freq), 500)

    # 在共同频率点上插值上下边界曲线
    top_interp = np.interp(np.log10(common_freqs), np.log10(top_freqs), top_powers)
    bottom_interp = np.interp(np.log10(common_freqs), np.log10(bottom_freqs), bottom_powers)

    # 绘制填充区域
    fill = ax.fill_between(common_freqs, bottom_interp, top_interp,
                          color=color, alpha=alpha,
                          label=label if label is not None else f"{top_curve_name}-{bottom_curve_name}")

    return fill

def setup_wenz_plot_style(ax, style_config=None):
    """
    设置Wenz曲线图的样式

    Args:
        ax: matplotlib轴对象
        style_config: 样式配置字典，如果为None则使用默认配置
    """
    # 如果没有提供样式配置，则加载默认配置
    if style_config is None:
        style_config = load_style_config()

    # 获取绘图样式配置
    plot_style = style_config.get("plot_style", {})

    # 设置标题和轴标签
    title = plot_style.get("title", "Wenz Curves")
    title_fontsize = plot_style.get("title_fontsize", 14)
    title_fontweight = plot_style.get("title_fontweight", "bold")
    ax.set_title(title, fontsize=title_fontsize, fontweight=title_fontweight)

    xlabel = plot_style.get("xlabel", "频率 (Hz)")
    xlabel_fontsize = plot_style.get("xlabel_fontsize", 12)
    ax.set_xlabel(xlabel, fontsize=xlabel_fontsize)

    ylabel = plot_style.get("ylabel", "噪声谱级 (dB re 1 μPa^2/Hz)")
    ylabel_fontsize = plot_style.get("ylabel_fontsize", 12)
    ax.set_ylabel(ylabel, fontsize=ylabel_fontsize)

    # 设置坐标轴范围
    min_freq = plot_style.get("min_freq", 1)
    max_freq = plot_style.get("max_freq", 100000)
    min_power = plot_style.get("min_power", 0)
    max_power = plot_style.get("max_power", 140)
    ax.set_xlim(min_freq, max_freq)
    ax.set_ylim(min_power, max_power)

    # 设置网格
    grid_major_alpha = plot_style.get("grid_major_alpha", 0.7)
    grid_major_linestyle = plot_style.get("grid_major_linestyle", "--")
    grid_minor_alpha = plot_style.get("grid_minor_alpha", 0.4)
    grid_minor_linestyle = plot_style.get("grid_minor_linestyle", ":")

    ax.grid(True, which='both', linestyle=grid_major_linestyle, alpha=grid_major_alpha)
    ax.grid(True, which='minor', linestyle=grid_minor_linestyle, alpha=grid_minor_alpha)

    # 设置刻度
    tick_labelsize = plot_style.get("tick_labelsize", 10)
    ax.tick_params(axis='both', which='major', labelsize=tick_labelsize)

    # 设置x轴为对数刻度
    ax.set_xscale('log')

    # 设置主要刻度位置
    ax.xaxis.set_major_formatter(ScalarFormatter())

    # 添加次要刻度
    ax.minorticks_on()

def create_wenz_curve_plot(curves, band_areas_data=None, output_path=None, style_config=None, use_interpolation=False):
    """
    创建完整的Wenz曲线图

    Args:
        curves: 曲线数据字典
        band_areas_data: 带状区域预处理数据字典
        output_path: 输出文件路径，如果为None则不保存
        style_config: 样式配置字典，如果为None则加载默认配置
        use_interpolation: 是否对数据进行插值（已废弃，保留参数是为了兼容性）

    Returns:
        fig, ax: matplotlib图表和轴对象
    """
    # 加载样式配置
    if style_config is None:
        style_config = load_style_config()

    # 获取绘图样式配置
    plot_style = style_config.get("plot_style", {})
    figsize = plot_style.get("figsize", [12, 8])
    dpi = plot_style.get("dpi", 300)

    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)

    # 设置图表样式
    setup_wenz_plot_style(ax, style_config)

    # 获取带状区域配置
    band_areas = style_config.get("band_areas", [])

    # 绘制带状区域
    for band in band_areas:
        band_name = band['name']

        # 优先使用预处理好的带状区域数据
        if band_areas_data and band_name in band_areas_data:
            print(f"使用预处理好的带状区域数据: {band_name}")
            plot_band_area(
                ax,
                band['top'],
                band['bottom'],
                [], [], [], [],  # 这些参数在使用预处理数据时不会被使用
                color=band['color'],
                alpha=band['alpha'],
                label=band['label'],
                band_data=band_areas_data[band_name]
            )
        # 如果没有预处理数据，则使用曲线数据进行插值
        elif band['top'] in curves and band['bottom'] in curves:
            print(f"使用曲线数据进行插值: {band_name}")
            top_data = curves[band['top']]
            bottom_data = curves[band['bottom']]

            plot_band_area(
                ax,
                band['top'],
                band['bottom'],
                top_data['frequencies'],
                top_data['power_levels'],
                bottom_data['frequencies'],
                bottom_data['power_levels'],
                color=band['color'],
                alpha=band['alpha'],
                label=band['label']
            )

    # 获取海况曲线配置
    sea_state_config = style_config.get("sea_state_curves", {})
    color_map_name = sea_state_config.get("color_map", "Blues")
    color_range = sea_state_config.get("color_range", [0.5, 1.0])
    label_prefix = sea_state_config.get("label_prefix", "海况 ")
    sea_state_linestyle = sea_state_config.get("linestyle", "-")
    sea_state_linewidth = sea_state_config.get("linewidth", 1.5)
    sea_state_alpha = sea_state_config.get("alpha", 1.0)

    # 绘制海况相关曲线
    sea_state_curves = [name for name in curves.keys() if name.startswith('wind') and not name.endswith('extrapolations')]
    color_map = plt.cm.get_cmap(color_map_name)

    for i, curve_name in enumerate(sorted(sea_state_curves)):
        data = curves[curve_name]
        # 使用配置的颜色映射
        color_factor = color_range[0] + (color_range[1] - color_range[0]) * i / max(1, len(sea_state_curves) - 1)
        color = color_map(color_factor)

        # 提取海况值
        sea_state = curve_name.split('-')[-1]
        label = f"{label_prefix}{sea_state}"

        plot_single_curve(
            ax,
            curve_name,
            data['frequencies'],
            data['power_levels'],
            color=color,
            linestyle=sea_state_linestyle,
            linewidth=sea_state_linewidth,
            alpha=sea_state_alpha,
            label=label,
            interpolate=use_interpolation
        )

    # 获取重要曲线配置
    important_curves_config = style_config.get("important_curves", {})

    # 创建一个集合来跟踪已经处理过的曲线
    processed_curves = set()

    # 绘制其他重要曲线
    for curve_name, props in important_curves_config.items():
        # 如果曲线已经处理过，则跳过
        if curve_name in processed_curves:
            continue

        if curve_name in curves:
            data = curves[curve_name]

            # 检查是否是限制对的上限曲线
            if props.get('is_limit_pair', False) and props.get('pair_with'):
                pair_curve_name = props.get('pair_with')
                if pair_curve_name in curves and pair_curve_name in important_curves_config:
                    # 将下限曲线标记为已处理
                    processed_curves.add(pair_curve_name)

                    # 绘制上限曲线
                    plot_single_curve(
                        ax,
                        curve_name,
                        data['frequencies'],
                        data['power_levels'],
                        color=props.get('color', 'black'),
                        linestyle=props.get('linestyle', '-'),
                        linewidth=props.get('linewidth', 1.5),
                        label=props.get('label', curve_name),
                        alpha=props.get('alpha', 1.0),
                        interpolate=use_interpolation
                    )

                    # 绘制下限曲线，但不添加到图例
                    pair_data = curves[pair_curve_name]
                    pair_props = important_curves_config[pair_curve_name]
                    # 注意：这里我们不使用 plot_single_curve 函数，而是直接使用 ax.semilogx
                    # 这样可以确保下限曲线不会出现在图例中
                    ax.semilogx(
                        pair_data['frequencies'],
                        pair_data['power_levels'],
                        color=pair_props.get('color', props.get('color', 'black')),
                        linestyle=pair_props.get('linestyle', props.get('linestyle', '-')),
                        linewidth=pair_props.get('linewidth', props.get('linewidth', 1.5)),
                        alpha=pair_props.get('alpha', props.get('alpha', 1.0))
                    )
            else:
                # 普通曲线
                # 检查是否应该显示在图例中
                label = props.get('label', curve_name) if props.get('show_in_legend', True) else None
                plot_single_curve(
                    ax,
                    curve_name,
                    data['frequencies'],
                    data['power_levels'],
                    color=props.get('color', 'black'),
                    linestyle=props.get('linestyle', '-'),
                    linewidth=props.get('linewidth', 1.5),
                    label=label,
                    alpha=props.get('alpha', 1.0),
                    interpolate=use_interpolation
                )

    # 添加图例
    legend_loc = plot_style.get("legend_loc", "upper right")
    legend_fontsize = plot_style.get("legend_fontsize", 10)
    ax.legend(loc=legend_loc, fontsize=legend_fontsize)

    # 保存图表
    if output_path:
        plt.tight_layout()
        plt.savefig(output_path, dpi=dpi)
        print(f"图表已保存到: {output_path}")

    return fig, ax

def main():
    """
    主函数 - 从JSON文件加载数据并绘制Wenz曲线图
    """
    # 设置中文字体
    set_chinese_font()

    # 加载样式配置
    style_config = load_style_config(STYLE_CONFIG_FILE)

    # 加载Wenz曲线数据（使用预处理的插值数据）
    curves, band_areas_data, metadata = load_wenz_data(JSON_FILE, use_interpolated=True)

    print(f"成功加载Wenz曲线数据，共 {len(curves)} 条曲线")
    if band_areas_data:
        print(f"成功加载带状区域预处理数据，共 {len(band_areas_data)} 个带状区域")
    print(f"数据单位: 频率 - {metadata['units']['frequency']}, 功率谱级 - {metadata['units']['power_level']}")

    # 创建输出目录
    output_dir = os.path.join('data', 'wenz_visualizations')
    os.makedirs(output_dir, exist_ok=True)

    # 创建完整的Wenz曲线图
    output_path = os.path.join(output_dir, 'wenz_curves_complete.png')
    create_wenz_curve_plot(curves, band_areas_data, output_path, style_config)
    print(f"完整Wenz曲线图已保存到: {output_path}")

    # 注意：create_wenz_curve_plot 函数已经包含了带状区域的定义和绘制
    # 如果需要单独绘制带状区域，可以取消下面的注释

    # # 定义带状区域
    # band_areas = [
    #     {
    #         'name': 'low-frequency-very-shallow-wind',
    #         'top': 'low-frequency-very-shallow-wind-top',
    #         'bottom': 'low-frequency-very-shallow-wind-bottom',
    #         'color': '#C7AD7D',  # 极浅海域风生噪声带的颜色
    #         'alpha': 0.5,
    #         'label': '极浅海域风生噪声'
    #     },
    #     {
    #         'name': 'usual-traffic-noise-shallow',
    #         'top': 'usual-traffic-noise-shallow-top',
    #         'bottom': 'usual-traffic-noise-shallow-bottom',
    #         'color': 'red',  # 浅海交通噪声带使用红色
    #         'alpha': 0.3,
    #         'label': '浅海交通噪声'
    #     },
    #     {
    #         'name': 'usual-traffic-noise-deep',
    #         'top': 'usual-traffic-noise-deep-top',
    #         'bottom': 'usual-traffic-noise-deep-bottom',
    #         'color': 'pink',  # 深海交通噪声带使用浅粉色
    #         'alpha': 0.3,
    #         'label': '深海交通噪声'
    #     }
    # ]

    print("可视化完成！")

if __name__ == "__main__":
    main()

