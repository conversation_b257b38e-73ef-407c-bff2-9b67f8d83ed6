<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Bokeh Plot</title>
    <style>
      html, body {
        box-sizing: border-box;
        display: flow-root;
        height: 100%;
        margin: 0;
        padding: 0;
      }
    </style>
    <script type="text/javascript" src="https://cdn.bokeh.org/bokeh/release/bokeh-3.7.2.min.js"></script>
    <script type="text/javascript">
        Bokeh.set_log_level("info");
    </script>
  </head>
  <body>
    <div id="dff8a664-9955-43b1-8c03-1f1d9d5c2e43" data-root-id="p1546" style="display: contents;"></div>
  
    <script type="application/json" id="e14fa927-bf11-4cfc-bcf5-dc26dd37d264">
      {"f40364e3-25cc-494b-b814-c7dee6098150":{"version":"3.7.2","title":"Bokeh Application","roots":[{"type":"object","name":"Figure","id":"p1546","attributes":{"width":900,"height":400,"x_range":{"type":"object","name":"DataRange1d","id":"p1548"},"y_range":{"type":"object","name":"Range1d","id":"p1555","attributes":{"start":-83.02960939353433,"end":-13.02960939353433}},"x_scale":{"type":"object","name":"LinearScale","id":"p1556"},"y_scale":{"type":"object","name":"LinearScale","id":"p1557"},"title":{"type":"object","name":"Title","id":"p1553"},"renderers":[{"type":"object","name":"GlyphRenderer","id":"p1586","attributes":{"data_source":{"type":"object","name":"ColumnDataSource","id":"p1580","attributes":{"selected":{"type":"object","name":"Selection","id":"p1581","attributes":{"indices":[],"line_indices":[]}},"selection_policy":{"type":"object","name":"UnionRenderers","id":"p1582"},"data":{"type":"map","entries":[["x",{"type":"ndarray","array":{"type":"bytes","data":"rNXVH4wI4T+s1dUfjAjhPw=="},"shape":[2],"dtype":"float64","order":"little"}],["y",{"type":"ndarray","array":{"type":"bytes","data":"mTjMHuXBVMCZOMwe5cFUwA=="},"shape":[2],"dtype":"float64","order":"little"}]]}}},"view":{"type":"object","name":"CDSView","id":"p1587","attributes":{"filter":{"type":"object","name":"AllIndices","id":"p1588"}}},"glyph":{"type":"object","name":"Line","id":"p1583","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue"}},"nonselection_glyph":{"type":"object","name":"Line","id":"p1584","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue","line_alpha":0.1}},"muted_glyph":{"type":"object","name":"Line","id":"p1585","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue","line_alpha":0.2}}}},{"type":"object","name":"GlyphRenderer","id":"p1595","attributes":{"data_source":{"type":"object","name":"ColumnDataSource","id":"p1589","attributes":{"selected":{"type":"object","name":"Selection","id":"p1590","attributes":{"indices":[],"line_indices":[]}},"selection_policy":{"type":"object","name":"UnionRenderers","id":"p1591"},"data":{"type":"map","entries":[["x",{"type":"ndarray","array":{"type":"bytes","data":"5xU2oDHF4D/nFTagMcXgPw=="},"shape":[2],"dtype":"float64","order":"little"}],["y",{"type":"ndarray","array":{"type":"bytes","data":"mTjMHuXBVMAbqiijtW9RwA=="},"shape":[2],"dtype":"float64","order":"little"}]]}}},"view":{"type":"object","name":"CDSView","id":"p1596","attributes":{"filter":{"type":"object","name":"AllIndices","id":"p1597"}}},"glyph":{"type":"object","name":"Line","id":"p1592","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue"}},"nonselection_glyph":{"type":"object","name":"Line","id":"p1593","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue","line_alpha":0.1}},"muted_glyph":{"type":"object","name":"Line","id":"p1594","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue","line_alpha":0.2}}}},{"type":"object","name":"GlyphRenderer","id":"p1604","attributes":{"data_source":{"type":"object","name":"ColumnDataSource","id":"p1598","attributes":{"selected":{"type":"object","name":"Selection","id":"p1599","attributes":{"indices":[],"line_indices":[]}},"selection_policy":{"type":"object","name":"UnionRenderers","id":"p1600"},"data":{"type":"map","entries":[["x",{"type":"ndarray","array":{"type":"bytes","data":"RynzH2q84D9HKfMfarzgPw=="},"shape":[2],"dtype":"float64","order":"little"}],["y",{"type":"ndarray","array":{"type":"bytes","data":"mTjMHuXBVMBHOUSPg3lNwA=="},"shape":[2],"dtype":"float64","order":"little"}]]}}},"view":{"type":"object","name":"CDSView","id":"p1605","attributes":{"filter":{"type":"object","name":"AllIndices","id":"p1606"}}},"glyph":{"type":"object","name":"Line","id":"p1601","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue"}},"nonselection_glyph":{"type":"object","name":"Line","id":"p1602","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue","line_alpha":0.1}},"muted_glyph":{"type":"object","name":"Line","id":"p1603","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue","line_alpha":0.2}}}},{"type":"object","name":"GlyphRenderer","id":"p1613","attributes":{"data_source":{"type":"object","name":"ColumnDataSource","id":"p1607","attributes":{"selected":{"type":"object","name":"Selection","id":"p1608","attributes":{"indices":[],"line_indices":[]}},"selection_policy":{"type":"object","name":"UnionRenderers","id":"p1609"},"data":{"type":"map","entries":[["x",{"type":"ndarray","array":{"type":"bytes","data":"3He+/4y74D/cd77/jLvgPw=="},"shape":[2],"dtype":"float64","order":"little"}],["y",{"type":"ndarray","array":{"type":"bytes","data":"mTjMHuXBVMBb4jB7lAc3wA=="},"shape":[2],"dtype":"float64","order":"little"}]]}}},"view":{"type":"object","name":"CDSView","id":"p1614","attributes":{"filter":{"type":"object","name":"AllIndices","id":"p1615"}}},"glyph":{"type":"object","name":"Line","id":"p1610","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue"}},"nonselection_glyph":{"type":"object","name":"Line","id":"p1611","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue","line_alpha":0.1}},"muted_glyph":{"type":"object","name":"Line","id":"p1612","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue","line_alpha":0.2}}}},{"type":"object","name":"GlyphRenderer","id":"p1622","attributes":{"data_source":{"type":"object","name":"ColumnDataSource","id":"p1616","attributes":{"selected":{"type":"object","name":"Selection","id":"p1617","attributes":{"indices":[],"line_indices":[]}},"selection_policy":{"type":"object","name":"UnionRenderers","id":"p1618"},"data":{"type":"map","entries":[["x",{"type":"ndarray","array":{"type":"bytes","data":"7JbifzLD4D/sluJ/MsPgPw=="},"shape":[2],"dtype":"float64","order":"little"}],["y",{"type":"ndarray","array":{"type":"bytes","data":"mTjMHuXBVMCotpePZQJSwA=="},"shape":[2],"dtype":"float64","order":"little"}]]}}},"view":{"type":"object","name":"CDSView","id":"p1623","attributes":{"filter":{"type":"object","name":"AllIndices","id":"p1624"}}},"glyph":{"type":"object","name":"Line","id":"p1619","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue"}},"nonselection_glyph":{"type":"object","name":"Line","id":"p1620","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue","line_alpha":0.1}},"muted_glyph":{"type":"object","name":"Line","id":"p1621","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue","line_alpha":0.2}}}},{"type":"object","name":"GlyphRenderer","id":"p1631","attributes":{"data_source":{"type":"object","name":"ColumnDataSource","id":"p1625","attributes":{"selected":{"type":"object","name":"Selection","id":"p1626","attributes":{"indices":[],"line_indices":[]}},"selection_policy":{"type":"object","name":"UnionRenderers","id":"p1627"},"data":{"type":"map","entries":[["x",{"type":"ndarray","array":{"type":"bytes","data":"pW3E32vS4D+lbcTfa9LgPw=="},"shape":[2],"dtype":"float64","order":"little"}],["y",{"type":"ndarray","array":{"type":"bytes","data":"mTjMHuXBVMA0pMWkwztSwA=="},"shape":[2],"dtype":"float64","order":"little"}]]}}},"view":{"type":"object","name":"CDSView","id":"p1632","attributes":{"filter":{"type":"object","name":"AllIndices","id":"p1633"}}},"glyph":{"type":"object","name":"Line","id":"p1628","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue"}},"nonselection_glyph":{"type":"object","name":"Line","id":"p1629","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue","line_alpha":0.1}},"muted_glyph":{"type":"object","name":"Line","id":"p1630","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue","line_alpha":0.2}}}},{"type":"object","name":"GlyphRenderer","id":"p1640","attributes":{"data_source":{"type":"object","name":"ColumnDataSource","id":"p1634","attributes":{"selected":{"type":"object","name":"Selection","id":"p1635","attributes":{"indices":[],"line_indices":[]}},"selection_policy":{"type":"object","name":"UnionRenderers","id":"p1636"},"data":{"type":"map","entries":[["x",{"type":"ndarray","array":{"type":"bytes","data":"W5I7IJTd4D9bkjsglN3gPw=="},"shape":[2],"dtype":"float64","order":"little"}],["y",{"type":"ndarray","array":{"type":"bytes","data":"mTjMHuXBVMCXhkqjmThUwA=="},"shape":[2],"dtype":"float64","order":"little"}]]}}},"view":{"type":"object","name":"CDSView","id":"p1641","attributes":{"filter":{"type":"object","name":"AllIndices","id":"p1642"}}},"glyph":{"type":"object","name":"Line","id":"p1637","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue"}},"nonselection_glyph":{"type":"object","name":"Line","id":"p1638","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue","line_alpha":0.1}},"muted_glyph":{"type":"object","name":"Line","id":"p1639","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue","line_alpha":0.2}}}},{"type":"object","name":"GlyphRenderer","id":"p1649","attributes":{"data_source":{"type":"object","name":"ColumnDataSource","id":"p1643","attributes":{"selected":{"type":"object","name":"Selection","id":"p1644","attributes":{"indices":[],"line_indices":[]}},"selection_policy":{"type":"object","name":"UnionRenderers","id":"p1645"},"data":{"type":"map","entries":[["x",{"type":"ndarray","array":{"type":"bytes","data":"UkHTf8fx4D9SQdN/x/HgPw=="},"shape":[2],"dtype":"float64","order":"little"}],["y",{"type":"ndarray","array":{"type":"bytes","data":"mTjMHuXBVMCZOMwe5cFUwA=="},"shape":[2],"dtype":"float64","order":"little"}]]}}},"view":{"type":"object","name":"CDSView","id":"p1650","attributes":{"filter":{"type":"object","name":"AllIndices","id":"p1651"}}},"glyph":{"type":"object","name":"Line","id":"p1646","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue"}},"nonselection_glyph":{"type":"object","name":"Line","id":"p1647","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue","line_alpha":0.1}},"muted_glyph":{"type":"object","name":"Line","id":"p1648","attributes":{"x":{"type":"field","field":"x"},"y":{"type":"field","field":"y"},"line_color":"blue","line_alpha":0.2}}}}],"toolbar":{"type":"object","name":"Toolbar","id":"p1554","attributes":{"tools":[{"type":"object","name":"PanTool","id":"p1568"},{"type":"object","name":"BoxZoomTool","id":"p1569","attributes":{"dimensions":"both","overlay":{"type":"object","name":"BoxAnnotation","id":"p1570","attributes":{"syncable":false,"line_color":"black","line_alpha":1.0,"line_width":2,"line_dash":[4,4],"fill_color":"lightgrey","fill_alpha":0.5,"level":"overlay","visible":false,"left":{"type":"number","value":"nan"},"right":{"type":"number","value":"nan"},"top":{"type":"number","value":"nan"},"bottom":{"type":"number","value":"nan"},"left_units":"canvas","right_units":"canvas","top_units":"canvas","bottom_units":"canvas","handles":{"type":"object","name":"BoxInteractionHandles","id":"p1576","attributes":{"all":{"type":"object","name":"AreaVisuals","id":"p1575","attributes":{"fill_color":"white","hover_fill_color":"lightgray"}}}}}}}},{"type":"object","name":"WheelZoomTool","id":"p1577","attributes":{"renderers":"auto"}},{"type":"object","name":"ResetTool","id":"p1578"},{"type":"object","name":"SaveTool","id":"p1579"}],"logo":null}},"left":[{"type":"object","name":"LinearAxis","id":"p1563","attributes":{"ticker":{"type":"object","name":"BasicTicker","id":"p1564","attributes":{"mantissas":[1,2,5]}},"formatter":{"type":"object","name":"BasicTickFormatter","id":"p1565"},"axis_label":"Amplitude (dB)","major_label_policy":{"type":"object","name":"AllLabels","id":"p1566"}}}],"below":[{"type":"object","name":"LinearAxis","id":"p1558","attributes":{"ticker":{"type":"object","name":"BasicTicker","id":"p1559","attributes":{"mantissas":[1,2,5]}},"formatter":{"type":"object","name":"BasicTickFormatter","id":"p1560"},"axis_label":"Arrival time (s)","major_label_policy":{"type":"object","name":"AllLabels","id":"p1561"}}}],"center":[{"type":"object","name":"Grid","id":"p1562","attributes":{"axis":{"id":"p1558"}}},{"type":"object","name":"Grid","id":"p1567","attributes":{"dimension":1,"axis":{"id":"p1563"}}}]}}]}}
    </script>
    <script type="text/javascript">
      (function() {
        const fn = function() {
          Bokeh.safely(function() {
            (function(root) {
              function embed_document(root) {
              const docs_json = document.getElementById('e14fa927-bf11-4cfc-bcf5-dc26dd37d264').textContent;
              const render_items = [{"docid":"f40364e3-25cc-494b-b814-c7dee6098150","roots":{"p1546":"dff8a664-9955-43b1-8c03-1f1d9d5c2e43"},"root_ids":["p1546"]}];
              root.Bokeh.embed.embed_items(docs_json, render_items);
              }
              if (root.Bokeh !== undefined) {
                embed_document(root);
              } else {
                let attempts = 0;
                const timer = setInterval(function(root) {
                  if (root.Bokeh !== undefined) {
                    clearInterval(timer);
                    embed_document(root);
                  } else {
                    attempts++;
                    if (attempts > 100) {
                      clearInterval(timer);
                      console.log("Bokeh: ERROR: Unable to run BokehJS code because BokehJS library is missing");
                    }
                  }
                }, 10, root)
              }
            })(window);
          });
        };
        if (document.readyState != "loading") fn();
        else document.addEventListener("DOMContentLoaded", fn);
      })();
    </script>
  </body>
</html>