# -*- coding: utf-8 -*-
"""
综合仿真模型

实现宽带源信号通过多阵元水声信道的接收信号模拟。
"""

import os
import json
import numpy as np
import pandas as pd
import scipy.fft
from scipy import signal
import concurrent.futures
from functools import partial
import time
import multiprocessing


class IntegratedSimulation:
    """
    综合仿真模型

    实现宽带源信号通过多阵元水声信道的接收信号模拟。
    """

    def __init__(self, fs):
        """
        初始化综合仿真模型

        Args:
            fs (int): 采样率 (Hz)
        """
        # 基本参数
        self.fs = fs  # 采样率 (Hz)

        # 信道数据
        self.channel_data = None  # 信道数据（从数据管理器获取）
        self.meta = None  # 元数据
        self.frequencies = None  # 频率列表
        self.array_elements = None  # 阵元位置列表
        self.t_first_global = None  # 全局最早到达时间
        self.t_last_global = None  # 全局最晚到达时间
        self.arrivals_data = None  # 到达结构数据

        # 信号处理参数
        self.use_all_arrivals = True  # 是否使用全部到达声线
        self.truncation_time = 0.0  # 截断时间 (s)
        self.source_signal = None  # 源信号
        self.source_signal_metadata = None  # 源信号元数据

        # 内部计算变量
        self._t_last_global_eff = None  # 有效的全局最晚到达时间
        self._max_relative_delay = None  # 最大相对时延
        self._N_h = None  # 冲击响应长度
        self._N_conv_len = None  # 卷积长度
        self._N_FFT = None  # FFT长度
        self._H_fft_cache = {}  # 传递函数缓存

    def set_channel_data(self, channel_data):
        """
        设置信道数据（从数据管理器获取）

        Args:
            channel_data (dict): 信道数据字典

        Returns:
            bool: 是否成功设置数据
        """
        try:
            if not channel_data:
                print("信道数据为空")
                return False

            # 保存信道数据
            self.channel_data = channel_data

            # 提取元数据、频率列表和阵元位置
            self.meta = channel_data.get('meta', {})
            self.frequencies = channel_data.get('frequencies', [])
            self.array_elements = channel_data.get('array_elements', [])
            self.t_first_global = channel_data.get('t_first_global', 0.0)
            self.t_last_global = channel_data.get('t_last_global', 0.0)
            self.arrivals_data = channel_data.get('arrivals_data', {})

            if not self.frequencies:
                print("频率列表为空")
                return False

            if not self.array_elements:
                print("阵元位置列表为空")
                return False

            if not self.arrivals_data:
                print("到达结构数据为空")
                return False

            return True

        except Exception as e:
            print(f"设置信道数据失败: {e}")
            return False

    def _calculate_global_arrival_times(self):
        """
        计算全局最早和最晚到达时间

        注意：现在我们从数据管理器获取全局最早和最晚到达时间，
        这个方法仅作为备用，当数据管理器中没有这些值时使用。
        """
        # 如果已经有全局最早和最晚到达时间，直接返回
        if self.t_first_global is not None and self.t_last_global is not None:
            return

        min_time = float('inf')
        max_time = float('-inf')

        for freq, elements in self.arrivals_data.items():
            for element_id, df in elements.items():
                # 检查不同的列名约定
                arrival_time_col = None
                if 'time_of_arrival' in df.columns:
                    arrival_time_col = 'time_of_arrival'
                elif 'arrival_time' in df.columns:
                    arrival_time_col = 'arrival_time'
                elif 'delay' in df.columns:
                    arrival_time_col = 'delay'

                if arrival_time_col is not None:
                    arrival_times = df[arrival_time_col].values
                    if len(arrival_times) > 0:
                        min_time = min(min_time, np.min(arrival_times))
                        max_time = max(max_time, np.max(arrival_times))

        if min_time == float('inf') or max_time == float('-inf'):
            print("警告: 无法计算全局到达时间，使用默认值")
            self.t_first_global = 0.0
            self.t_last_global = 1.0
        else:
            self.t_first_global = min_time
            self.t_last_global = max_time

    def set_source_signal(self, signal_data, metadata):
        """
        设置源信号

        Args:
            signal_data (numpy.ndarray): 源信号数据
            metadata (dict): 源信号元数据，包含fs、duration等信息
        """
        self.source_signal = signal_data
        self.source_signal_metadata = metadata

    def set_truncation_parameters(self, use_all_arrivals, truncation_time=0.0):
        """
        设置截断参数

        Args:
            use_all_arrivals (bool): 是否使用全部到达声线
            truncation_time (float): 截断时间 (s)，当use_all_arrivals为False时使用
        """
        self.use_all_arrivals = use_all_arrivals
        self.truncation_time = truncation_time

    def get_duration_suggestions(self):
        """
        获取源信号时长建议

        使用预计算的阈值映射表提高性能

        Returns:
            tuple: (min_duration, recommended_duration)
        """
        if self.t_first_global is None or self.t_last_global is None:
            return (0.0, 0.0)

        # 检查是否有预计算的阈值映射表
        if hasattr(self.channel_data, 'get') and callable(self.channel_data.get):
            duration_to_truncation_threshold = self.channel_data.get('duration_to_truncation_threshold', {})

            if duration_to_truncation_threshold and not self.use_all_arrivals:
                # 找出小于等于截断时间的最大建议时长
                min_duration = 1  # 默认最小值

                for duration, threshold in sorted(duration_to_truncation_threshold.items()):
                    if self.truncation_time >= threshold:
                        min_duration = duration

                # 计算推荐时长（通常是最小时长+4）
                recommended_duration = min_duration + 4

                return (min_duration, recommended_duration)

        # 如果没有预计算的映射表或使用全部到达声线，使用原来的方法
        # 计算有效的全局最晚到达时间
        if self.use_all_arrivals:
            t_last_global_eff = self.t_last_global
        else:
            # 确保截断时间大于全局最早到达时间
            if self.truncation_time <= self.t_first_global:
                t_last_global_eff = self.t_last_global
            else:
                # 找出所有小于等于截断时间的到达时间中的最大值
                max_time = self.t_first_global
                for freq, elements in self.arrivals_data.items():
                    for element_id, df in elements.items():
                        if 'time_of_arrival' in df.columns:
                            valid_times = df[df['time_of_arrival'] <= self.truncation_time]['time_of_arrival'].values
                            if len(valid_times) > 0:
                                max_time = max(max_time, np.max(valid_times))
                t_last_global_eff = max_time

        # 计算最大相对时延
        max_relative_delay = t_last_global_eff - self.t_first_global

        # 计算建议的最小源信号时长（最大相对时延 + 1秒）
        min_duration = np.ceil(max_relative_delay + 1.0)

        # 计算推荐的源信号时长（最大相对时延 + 5秒）
        recommended_duration = np.ceil(max_relative_delay + 5.0)

        return (min_duration, recommended_duration)

    def _prepare_processing(self):
        """
        准备信号处理

        计算有效的全局最晚到达时间、最大相对时延、冲击响应长度等参数

        Returns:
            bool: 是否成功准备
        """
        if self.t_first_global is None or self.t_last_global is None:
            print("错误: 未加载信道数据")
            return False

        if self.source_signal is None or self.source_signal_metadata is None:
            print("错误: 未设置源信号")
            return False

        # 计算有效的全局最晚到达时间
        if self.use_all_arrivals:
            self._t_last_global_eff = self.t_last_global
        else:
            # 确保截断时间大于全局最早到达时间
            if self.truncation_time <= self.t_first_global:
                self._t_last_global_eff = self.t_last_global
            else:
                # 找出所有小于等于截断时间的到达时间中的最大值
                max_time = self.t_first_global
                for freq, elements in self.arrivals_data.items():
                    for element_id, df in elements.items():
                        if 'time_of_arrival' in df.columns:
                            valid_times = df[df['time_of_arrival'] <= self.truncation_time]['time_of_arrival'].values
                            if len(valid_times) > 0:
                                max_time = max(max_time, np.max(valid_times))
                self._t_last_global_eff = max_time

        # 计算最大相对时延
        self._max_relative_delay = self._t_last_global_eff - self.t_first_global

        # 计算冲击响应长度
        self._N_h = int(np.ceil(self._max_relative_delay * self.fs)) + 1

        # 计算卷积长度
        N_s = len(self.source_signal)
        self._N_conv_len = N_s + self._N_h - 1

        # 计算FFT长度
        self._N_FFT = scipy.fft.next_fast_len(self._N_conv_len)

        # 清空传递函数缓存
        self._H_fft_cache = {}

        return True

    def _divide_subbands(self):
        """
        划分子带

        根据频率列表划分子带，返回子带边界
        标准：
        1. 第一个子带从0Hz开始
        2. 最后一个子带到Nyquist频率结束
        3. 中间子带的边界是相邻中心频率的中点
        4. 如果只有一个频率点，则覆盖整个频率范围(0-Nyquist)

        Returns:
            list: 子带列表，每个子带为 (f_center, f_start, f_end)
        """
        if not self.frequencies:
            return []

        # 排序频率列表
        sorted_freqs = sorted(self.frequencies)
        nyquist = self.fs / 2

        # 如果只有一个频率点，覆盖整个频率范围
        if len(sorted_freqs) == 1:
            f_center = sorted_freqs[0]
            f_start = 0  # 从0开始
            f_end = nyquist  # 到Nyquist结束
            return [(f_center, f_start, f_end)]

        # 多个频率点，划分子带
        subbands = []

        # 处理第一个频率点
        f_center = sorted_freqs[0]
        f_start = 0  # 从0开始
        f_mid = (f_center + sorted_freqs[1]) / 2
        subbands.append((f_center, f_start, f_mid))

        # 处理中间的频率点
        for i in range(1, len(sorted_freqs) - 1):
            f_center = sorted_freqs[i]
            f_start = (sorted_freqs[i-1] + f_center) / 2
            f_end = (f_center + sorted_freqs[i+1]) / 2
            subbands.append((f_center, f_start, f_end))

        # 处理最后一个频率点
        f_center = sorted_freqs[-1]
        f_start = (sorted_freqs[-2] + f_center) / 2
        f_end = nyquist  # 到Nyquist结束
        subbands.append((f_center, f_start, f_end))

        return subbands

    # 注意：此方法已不再使用，保留是为了兼容性
    def _calculate_H_val_for_freq(self, freq, subband_to_arrivals):
        """
        计算单个频率点的传递函数值（已弃用，使用向量化计算代替）

        Args:
            freq (float): 频率值
            subband_to_arrivals (dict): 子带到到达数据的映射

        Returns:
            complex: 传递函数值，如果没有找到对应子带则返回0
        """
        # 跳过负频率
        if freq < 0:
            return 0j

        # 找到该频率所属的子带
        for (f_start, f_end), (t_prime_m, A_m, _) in subband_to_arrivals.items():
            if (f_start <= freq < f_end) or (freq == f_end and f_end == self.fs / 2):  # 包含Nyquist频率
                # 对于当前频率点，使用该子带的到达数据，但使用当前频率计算相位
                return np.sum(A_m * np.exp(-1j * 2 * np.pi * freq * t_prime_m))

        # 如果没有找到对应的子带，返回0
        return 0j

    def _process_element(self, element_id, subbands):
        """
        处理单个阵元的传递函数计算

        Args:
            element_id (int): 阵元ID
            subbands (list): 子带列表

        Returns:
            tuple: (element_id, H_fft) 阵元ID和对应的传递函数
        """
        start_time = time.time()
        print(f"开始处理阵元 {element_id}...")

        # 创建全零复数数组，使用完整FFT长度
        H_fft = np.zeros(self._N_FFT, dtype=np.complex128)

        # 计算频率数组，使用完整FFT频率
        freqs = scipy.fft.fftfreq(self._N_FFT, 1/self.fs)

        # 创建子带到到达数据的映射
        subband_to_arrivals = {}
        for f_center, f_start, f_end in subbands:
            # 找到最接近中心频率的实际频率
            f_k_actual = min(self.frequencies, key=lambda x: abs(x - f_center))

            # 检查该阵元在该频率下是否有到达结构数据
            if f_k_actual not in self.arrivals_data or element_id not in self.arrivals_data[f_k_actual]:
                continue

            # 获取到达结构数据
            arrivals_df = self.arrivals_data[f_k_actual][element_id]

            # 检查不同的列名约定
            arrival_time_col = None
            if 'time_of_arrival' in arrivals_df.columns:
                arrival_time_col = 'time_of_arrival'
            elif 'arrival_time' in arrivals_df.columns:
                arrival_time_col = 'arrival_time'
            elif 'delay' in arrivals_df.columns:
                arrival_time_col = 'delay'

            if arrival_time_col is None:
                continue

            # 筛选有效的到达结构
            if not self.use_all_arrivals and self.truncation_time > self.t_first_global:
                arrivals_df = arrivals_df[arrivals_df[arrival_time_col] <= self.truncation_time]

            if arrivals_df.empty:
                continue

            # 计算相对延迟
            t_prime_m = arrivals_df[arrival_time_col].values - self.t_first_global

            # 获取振幅
            amplitude_col = None
            if 'arrival_amplitude' in arrivals_df.columns:
                amplitude_col = 'arrival_amplitude'
            elif 'amplitude' in arrivals_df.columns:
                amplitude_col = 'amplitude'

            if amplitude_col is not None:
                A_m_original = arrivals_df[amplitude_col].values
                # 检查振幅是否为复数
                if not np.issubdtype(A_m_original.dtype, np.complexfloating):
                    # 尝试转换为复数
                    try:
                        A_m_original = A_m_original.astype(np.complex128)
                    except:
                        # 如果转换失败，使用默认值1.0
                        A_m_original = np.ones(len(t_prime_m), dtype=np.complex128)
            else:
                # 如果没有振幅列，使用默认值1.0
                A_m_original = np.ones(len(t_prime_m), dtype=np.complex128)

            # 修正振幅，移除arlpy添加的时间相关相位
            #
            # arlpy返回的振幅表达式：
            # A_m_original = data[0] * exp(-j*Phase_rad_intrinsic) * exp(ω_c*t_imag) * exp(-j*ω_c*t_real)
            #
            # 我们需要提取不包含时间相关相位的基本振幅，以便在构建传递函数时
            # 可以正确地使用扫描频率和绝对到达时间重新构建相位

            # 1. 提取基本振幅（幅值）
            amplitude_base = np.abs(A_m_original)

            # 2. 计算绝对到达时间（t_real）
            t_real = t_prime_m + self.t_first_global

            # 3. 提取固有相位（不包含任何时间相关部分）
            # 从arlpy的振幅中减去时间相关的相位
            omega_c = 2 * np.pi * f_k_actual
            phase_intrinsic = np.angle(A_m_original) + omega_c * t_real

            # 4. 构建修正后的基本振幅（不包含任何时间相关相位）
            # A_m = amplitude_base * exp(j * phase_intrinsic)
            # 这样，在后续构建传递函数时，我们可以正确地添加与扫描频率相关的时间相位：
            # H(f_s) = Σ_paths [ A_m * exp(-j * ω_s * t_real) ]
            A_m = amplitude_base * np.exp(1j * phase_intrinsic)

            # 添加调试输出，验证修正是否有效
            if len(t_prime_m) > 0:
                print(f"阵元 {element_id}: 振幅修正示例 (第一个到达路径)")
                print(f"  原始振幅: {A_m_original[0]}")
                print(f"  幅值: {amplitude_base[0]}")
                print(f"  原始相位: {np.angle(A_m_original[0])}")
                print(f"  绝对到达时间: {t_real[0]}")
                print(f"  相对到达时间: {t_prime_m[0]}")
                print(f"  固有相位: {phase_intrinsic[0]}")
                print(f"  修正后振幅: {A_m[0]}")
                print(f"  频率: {f_k_actual} Hz")

            # 存储子带对应的到达数据和实际频率
            subband_to_arrivals[(f_start, f_end)] = (t_prime_m, A_m, f_k_actual)

        # 获取正频率部分
        positive_indices = np.where(freqs >= 0)[0]

        # 直接使用向量化计算处理所有正频率
        print(f"阵元 {element_id}: 处理 {len(positive_indices)} 个频率点")

        # 创建结果数组
        H_vals = np.zeros(len(positive_indices), dtype=np.complex128)

        # 对每个子带分别处理其覆盖的频率点
        for (f_start, f_end), (t_prime_m, A_m, _) in subband_to_arrivals.items():
            # 找出该子带覆盖的所有频率点
            subband_mask = np.logical_or(
                np.logical_and(freqs[positive_indices] >= f_start, freqs[positive_indices] < f_end),
                np.logical_and(freqs[positive_indices] == f_end, f_end == self.fs / 2)  # 包含Nyquist频率
            )
            if not np.any(subband_mask):
                continue

            # 获取子带内的频率点索引和对应的频率值
            subband_pos_indices = np.where(subband_mask)[0]  # 在positive_indices中的索引
            subband_freqs = freqs[positive_indices[subband_mask]]
            
            # 使用向量化计算所有频率点的H值
            print(f"阵元 {element_id}: 子带 [{f_start}, {f_end}) Hz 包含 {len(subband_freqs)} 个频率点")
            start_time_calc = time.time()

            # 对于每个扫描频率 f_s 和每个到达路径的相对时间 t_prime_m，
            # 计算相位项 exp(-j * ω_s * t_prime_m)，其中 ω_s = 2π * f_s
            phase_matrix = np.exp(-1j * 2 * np.pi * np.outer(subband_freqs, t_prime_m))

            # 对每个频率，计算与振幅的乘积并求和，使用矩阵乘法来加速计算
            # H(f_s) = Σ_paths [ A_m * exp(-j * ω_s * t_prime_m) ]
            H_subband = np.matmul(phase_matrix, A_m)

            # 将结果存入H_vals
            H_vals[subband_pos_indices] = H_subband

            calc_time = time.time() - start_time_calc
            print(f"阵元 {element_id}: 子带 [{f_start}, {f_end}) Hz 处理完成，计算耗时: {calc_time:.4f} 秒")

        # 将计算结果填充到H_fft中
        for i, idx in enumerate(positive_indices):
            H_fft[idx] = H_vals[i]

            # 处理负频率（共轭对称）
            if idx > 0:
                neg_idx = self._N_FFT - idx
                H_fft[neg_idx] = np.conj(H_vals[i])

        elapsed_time = time.time() - start_time
        print(f"阵元 {element_id} 处理完成，耗时: {elapsed_time:.2f} 秒")
        return element_id, H_fft

    def _build_transfer_functions(self, progress_callback=None):
        """
        构建传递函数

        为每个阵元构建传递函数，并缓存结果
        对每个频率点单独计算传递函数值，以保留时延信息
        使用并行计算提高效率

        Args:
            progress_callback (callable, optional): 进度回调函数，接受一个0-1之间的浮点数表示进度

        Returns:
            bool: 是否成功构建
        """
        total_start_time = time.time()
        print("\n开始构建传递函数...")

        if not self._prepare_processing():
            print("准备处理失败")
            return False

        # 划分子带
        subbands = self._divide_subbands()
        if not subbands:
            print("错误: 无法划分子带")
            return False

        print(f"划分了 {len(subbands)} 个子带")
        for i, (f_center, f_start, f_end) in enumerate(subbands):
            print(f"  子带 {i+1}: 中心频率={f_center} Hz, 范围=[{f_start}, {f_end}) Hz")

        # 使用并行计算处理多个阵元
        element_ids = list(range(len(self.array_elements)))
        total_elements = len(element_ids)
        print(f"需要处理 {total_elements} 个阵元")

        # 创建部分函数，固定subbands参数
        process_func = partial(self._process_element, subbands=subbands)

        # 使用ThreadPoolExecutor并行处理多个阵元
        results = []
        with concurrent.futures.ThreadPoolExecutor() as executor:
            # 提交所有任务
            future_to_element = {executor.submit(process_func, element_id): element_id for element_id in element_ids}

            # 收集结果并更新进度
            completed = 0
            for future in concurrent.futures.as_completed(future_to_element):
                element_id = future_to_element[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    print(f"处理阵元 {element_id} 时出错: {e}")

                # 更新进度
                completed += 1
                progress = completed / total_elements
                print(f"总进度: {progress*100:.1f}% ({completed}/{total_elements} 个阵元)")

                if progress_callback:
                    # 如果回调函数返回False，表示取消操作
                    if progress_callback(progress) is False:
                        print("传递函数计算已取消")
                        return False

        # 将结果存入缓存
        for element_id, H_fft in results:
            self._H_fft_cache[element_id] = H_fft

        total_elapsed_time = time.time() - total_start_time
        print(f"传递函数构建完成，总耗时: {total_elapsed_time:.2f} 秒")
        return True

    def process_signal(self, progress_callback=None):
        """
        处理信号

        执行频域卷积，计算接收信号

        Args:
            progress_callback (callable, optional): 进度回调函数，接受一个0-1之间的浮点数表示进度

        Returns:
            dict: 处理结果，包含各阵元的接收信号
        """
        print("\n开始处理信号...")
        process_start_time = time.time()

        # 构建传递函数（占总进度的70%）
        if progress_callback:
            build_progress_callback = lambda p: progress_callback(p * 0.7)
        else:
            build_progress_callback = None

        print("构建传递函数...")
        build_start_time = time.time()
        if not self._build_transfer_functions(progress_callback=build_progress_callback):
            print("构建传递函数失败")
            return None

        build_time = time.time() - build_start_time
        print(f"构建传递函数完成，耗时: {build_time:.2f} 秒")

        # 准备结果字典
        results = {
            'source_signal': {
                'metadata': self.source_signal_metadata,
                'time_data': np.linspace(0, self.source_signal_metadata.get('duration', 0), len(self.source_signal)),
                'signal': self.source_signal
            },
            't_first_global': self.t_first_global,
            't_last_global': self.t_last_global,
            't_last_global_eff': self._t_last_global_eff,
            'max_relative_delay': self._max_relative_delay,
            'impulse_response_length': self._N_h,
            'received_signals': {},
            'array_elements': self.array_elements  # 确保阵元位置数据被保存到结果中
        }

        # 打印阵元位置数据信息
        if self.array_elements:
            print(f"将阵元位置数据保存到结果中，共 {len(self.array_elements)} 个阵元")
        else:
            print("警告: 阵元位置数据为空")

        # 对源信号进行补零
        s_padded = np.zeros(self._N_FFT)
        s_padded[:len(self.source_signal)] = self.source_signal

        # 计算源信号的FFT，使用scipy.fft.fft以确保一致性
        S_fft = scipy.fft.fft(s_padded)

        # 更新进度到75%
        if progress_callback:
            progress_callback(0.75)

        # 为每个阵元执行频域卷积
        total_elements = len(self._H_fft_cache)
        for i, (element_id, H_fft) in enumerate(self._H_fft_cache.items()):
            # 频域乘法
            R_fft = S_fft * H_fft

            # 逆FFT
            r_padded = scipy.fft.ifft(R_fft).real  # 取实部，因为结果应该是实数

            # 取有效部分
            r_prime = r_padded[:self._N_conv_len]

            # 时间轴对齐
            # 创建物理时间轴，从t_first_global开始
            duration = len(r_prime) / self.fs
            time_data = np.linspace(self.t_first_global, self.t_first_global + duration, len(r_prime))

            # 保存接收信号
            results['received_signals'][element_id] = {
                'time_data': time_data,
                'signal': r_prime
            }

            # 更新进度（从75%到95%）
            if progress_callback:
                progress = 0.75 + (i + 1) / total_elements * 0.2
                # 如果回调函数返回False，表示取消操作
                if progress_callback(progress) is False:
                    print("信号处理已取消")
                    return None

        # 完成进度
        if progress_callback:
            # 忽略返回值，因为已经完成了
            progress_callback(1.0)

        total_process_time = time.time() - process_start_time
        print(f"信号处理完成，总耗时: {total_process_time:.2f} 秒")
        return results

    def calculate_psd(self, _signal, fs, nperseg=None):
        """
        计算功率谱密度

        Args:
            _signal (numpy.ndarray): 信号数据
            fs (int): 采样率 (Hz)
            nperseg (int, optional): 每段长度，默认为fs（1Hz分辨率）

        Returns:
            tuple: (freqs, psd, psd_db)，频率数组、功率谱密度和dB形式的功率谱密度
        """
        if nperseg is None:
            nperseg = fs  # 默认为1Hz分辨率

        # 使用Welch方法计算PSD
        freqs, psd = signal.welch(_signal, fs=fs, nperseg=nperseg)

        # 转换为dB
        psd_db = 10 * np.log10(psd + 1e-10)  # 添加小值避免log(0)

        return freqs, psd, psd_db

    def calculate_cross_spectral_density(self, signal1, signal2, fs, nperseg=None, noverlap=None,
                                         nfft=None, detrend='constant', scaling='density'):
        """
        计算互功率谱密度

        Args:
            signal1 (numpy.ndarray): 第一个信号数据
            signal2 (numpy.ndarray): 第二个信号数据
            fs (int): 采样率 (Hz)
            nperseg (int, optional): 每段长度，默认为fs（1Hz分辨率）
            noverlap (int, optional): 重叠长度，默认为nperseg//2
            nfft (int, optional): FFT长度，默认为nperseg
            detrend (str or function, optional): 去趋势方法，默认为'constant'
            scaling (str, optional): 缩放方式，'density'或'spectrum'，默认为'density'

        Returns:
            tuple: (freqs, csd)，频率数组和互功率谱密度（复数值）
        """
        if nperseg is None:
            nperseg = fs  # 默认为1Hz分辨率

        if noverlap is None:
            noverlap = nperseg // 2  # 默认50%重叠

        # 使用scipy.signal.csd计算互功率谱密度
        freqs, csd = signal.csd(signal1, signal2, fs=fs, nperseg=nperseg,
                               noverlap=noverlap, nfft=nfft, detrend=detrend,
                               scaling=scaling)

        # 打印调试信息
        print(f"互功率谱密度计算完成，频率点数: {len(freqs)}, 模值范围: [{np.min(np.abs(csd))}, {np.max(np.abs(csd))}]")

        return freqs, csd

    def calculate_coherence(self, signal1, signal2, fs, nperseg=None, noverlap=None,
                           nfft=None, detrend='constant'):
        """
        计算相干函数

        Args:
            signal1 (numpy.ndarray): 第一个信号数据
            signal2 (numpy.ndarray): 第二个信号数据
            fs (int): 采样率 (Hz)
            nperseg (int, optional): 每段长度，默认为fs（1Hz分辨率）
            noverlap (int, optional): 重叠长度，默认为nperseg//2
            nfft (int, optional): FFT长度，默认为nperseg
            detrend (str or function, optional): 去趋势方法，默认为'constant'

        Returns:
            tuple: (freqs, coherence)，频率数组和相干函数值（0到1之间）
        """
        if nperseg is None:
            nperseg = fs  # 默认为1Hz分辨率

        if noverlap is None:
            noverlap = nperseg // 2  # 默认50%重叠

        # 使用scipy.signal.coherence计算相干函数
        freqs, coherence = signal.coherence(signal1, signal2, fs=fs, nperseg=nperseg,
                                          noverlap=noverlap, nfft=nfft, detrend=detrend)

        return freqs, coherence

    def prepare_data_for_beamforming(self, signals, start_time, duration, freq_range=None, use_original_signal=False):
        """
        准备波束形成所需的数据

        Args:
            signals (dict): 各阵元接收信号字典
            start_time (float): 开始时间（秒）
            duration (float): 持续时间（秒）
            freq_range (list, optional): 频率范围，如[100, 1000]
            use_original_signal (bool, optional): 是否使用原始信号（未叠加噪声的信号），默认为False

        Returns:
            tuple: (signal_segments, element_positions)
                signal_segments: 截取的信号片段字典
                element_positions: 阵元位置数组
        """
        print(f"\n准备波束形成数据:")
        print(f"  开始时间: {start_time} 秒")
        print(f"  持续时间: {duration} 秒")
        if freq_range:
            print(f"  频率范围: [{freq_range[0]}, {freq_range[1]}] Hz")
        else:
            print(f"  频率范围: 全频段")
        print(f"  使用原始信号: {use_original_signal}")

        # 检查信号字典是否为空
        if not signals:
            raise ValueError("接收信号字典为空")

        # 获取阵元位置
        element_ids = sorted(list(signals.keys()))
        print(f"  阵元数量: {len(element_ids)}")

        # 检查阵元位置是否存在
        if not self.array_elements:
            # 打印更详细的错误信息
            print("错误: 阵元位置数据为空")
            print("当前对象状态:")
            print(f"  self.fs = {self.fs}")
            print(f"  self.channel_data 是否存在: {self.channel_data is not None}")
            print(f"  self.meta 是否存在: {self.meta is not None}")
            print(f"  self.frequencies 是否存在: {self.frequencies is not None}")
            print(f"  self.t_first_global = {self.t_first_global}")
            print(f"  self.t_last_global = {self.t_last_global}")
            print(f"  self.arrivals_data 是否存在: {self.arrivals_data is not None}")
            print(f"  self.source_signal 是否存在: {self.source_signal is not None}")
            print(f"  self.source_signal_metadata 是否存在: {self.source_signal_metadata is not None}")

            # 尝试从信号中提取阵元位置
            try:
                # 如果信号字典中有阵元ID，可以尝试构建一个简单的阵元位置数组
                if signals:
                    element_ids = sorted(list(signals.keys()))
                    print(f"  从信号字典中提取到 {len(element_ids)} 个阵元ID: {element_ids}")

                    # 创建一个简单的线性阵列作为临时解决方案
                    # 对于距离-深度平面，使用二维坐标 (x, z)
                    temp_array_elements = []
                    for i in range(max(element_ids) + 1):
                        # 创建一个简单的线性阵列，间距为0.5米
                        # 使用二维坐标 (x, z)，x=0表示垂直阵列
                        temp_array_elements.append([0.0, i * 0.5])

                    print(f"  创建临时阵元位置数组，共 {len(temp_array_elements)} 个阵元")
                    self.array_elements = temp_array_elements
                    print("  使用临时阵元位置数组继续处理")
                else:
                    raise ValueError("无法从信号字典中提取阵元ID")
            except Exception as e:
                print(f"  尝试创建临时阵元位置数组失败: {e}")
                raise ValueError("阵元位置数据为空，且无法创建临时数组")

        # 检查阵元ID是否在阵元位置列表中
        for element_id in element_ids:
            if element_id >= len(self.array_elements):
                raise ValueError(f"阵元ID {element_id} 超出阵元位置列表范围 (0-{len(self.array_elements)-1})")

        element_positions = np.array([self.array_elements[i] for i in element_ids])
        print(f"  阵元位置数组形状: {element_positions.shape}")

        # 获取采样率
        fs = self.fs
        print(f"  采样率: {fs} Hz")

        # 初始化信号片段字典
        signal_segments = {}

        for element_id in element_ids:
            # 检查信号数据是否完整
            if 'time_data' not in signals[element_id] or 'signal' not in signals[element_id]:
                raise ValueError(f"阵元 {element_id} 的信号数据不完整，缺少time_data或signal字段")

            time_data = signals[element_id]['time_data']

            # 根据use_original_signal参数选择使用原始信号或叠加噪声后的信号
            if use_original_signal and 'original_signal' in signals[element_id]:
                signal_data = signals[element_id]['original_signal']
                print(f"  阵元 {element_id} 使用原始信号（未叠加噪声）")
            else:
                signal_data = signals[element_id]['signal']
                if use_original_signal:
                    print(f"  阵元 {element_id} 未找到原始信号，使用当前信号")
                else:
                    print(f"  阵元 {element_id} 使用当前信号（可能已叠加噪声）")

            # 检查时间数据和信号数据长度是否一致
            if len(time_data) != len(signal_data):
                raise ValueError(f"阵元 {element_id} 的时间数据长度 ({len(time_data)}) 与信号数据长度 ({len(signal_data)}) 不一致")

            # 检查时间数据是否为空
            if len(time_data) == 0:
                raise ValueError(f"阵元 {element_id} 的时间数据为空")

            print(f"  阵元 {element_id} 信号时间范围: [{time_data[0]:.6f}, {time_data[-1]:.6f}] 秒，长度: {len(time_data)}")

            # 检查时间范围是否有效，如果超出范围则自动调整到有效范围内
            if start_time < time_data[0]:
                print(f"警告: 开始时间 {start_time} 小于信号最小时间 {time_data[0]}，将自动调整为信号最小时间")
                start_time = time_data[0]
            elif start_time >= time_data[-1]:
                print(f"警告: 开始时间 {start_time} 大于等于信号最大时间 {time_data[-1]}，将自动调整为信号最大时间前的一个时间点")
                start_time = time_data[-2] if len(time_data) > 1 else time_data[0]

            if start_time + duration > time_data[-1]:
                print(f"警告: 持续时间超出信号范围，将被截断至 {time_data[-1] - start_time} 秒")
                duration = time_data[-1] - start_time

            # 简化信号截取逻辑，直接使用最接近的索引
            start_idx = np.argmin(np.abs(time_data - start_time))
            end_time = start_time + duration
            end_idx = np.argmin(np.abs(time_data - end_time))

            # 确保索引有效
            start_idx = max(0, start_idx)
            end_idx = min(len(signal_data), end_idx)

            # 确保至少有一个样本
            if end_idx <= start_idx:
                end_idx = start_idx + 1

            # 添加调试输出
            print(f"  阵元 {element_id} 截取信号: 开始索引={start_idx}, 结束索引={end_idx}, 对应时间=[{time_data[start_idx]:.6f}, {time_data[end_idx-1]:.6f}]")

            # 截取信号
            try:
                signal_segment = signal_data[start_idx:end_idx].copy()
                print(f"  阵元 {element_id} 截取信号长度: {len(signal_segment)}")

                # 检查截取的信号是否为空
                if len(signal_segment) == 0:
                    raise ValueError(f"阵元 {element_id} 截取的信号段为空")

            except Exception as e:
                error_msg = f"阵元 {element_id} 截取信号失败: {str(e)}"
                print(f"错误: {error_msg}")
                raise ValueError(error_msg)

            # 如果指定了频率范围，进行带通滤波
            if freq_range is not None:
                try:
                    nyquist = fs / 2
                    low = max(1.0, freq_range[0]) / nyquist  # 确保低频不为0
                    high = min(nyquist - 1, freq_range[1]) / nyquist  # 确保高频不超过奈奎斯特频率

                    if low >= high:
                        raise ValueError(f"无效的频率范围: [{freq_range[0]}, {freq_range[1]}]")

                    # 使用巴特沃斯带通滤波器
                    b, a = signal.butter(4, [low, high], btype='band')
                    signal_segment = signal.filtfilt(b, a, signal_segment)
                    print(f"  阵元 {element_id} 带通滤波完成，频率范围: [{freq_range[0]}, {freq_range[1]}] Hz")
                except Exception as e:
                    error_msg = f"阵元 {element_id} 带通滤波失败: {str(e)}"
                    print(f"错误: {error_msg}")
                    raise ValueError(error_msg)

            signal_segments[element_id] = signal_segment

        # 确保所有阵元的信号长度相同（使用最短的长度）
        if not signal_segments:
            raise ValueError("没有有效的信号段")

        min_length = min(len(segment) for segment in signal_segments.values())
        print(f"  所有阵元信号段的最小长度: {min_length}")

        # 检查最小长度是否合理
        if min_length < 10:  # 设置一个合理的最小长度阈值
            raise ValueError(f"信号段长度过短 ({min_length})，无法进行有效的波束形成")

        for element_id in signal_segments:
            signal_segments[element_id] = signal_segments[element_id][:min_length]

        print(f"波束形成数据准备完成，共 {len(signal_segments)} 个阵元，信号长度: {min_length}")
        return signal_segments, element_positions

    def delay_and_sum_beamforming(self, signal_segments, element_positions, angles,
                                 sound_speed=1500, reference_position='center',
                                 weighting='uniform', delay_method='fractional'):
        """
        时延波束形成算法

        Args:
            signal_segments (dict): 各阵元信号片段字典
            element_positions (numpy.ndarray): 阵元位置数组，形状为(n_elements, 3)
            angles (numpy.ndarray): 扫描角度数组（度）
            sound_speed (float): 声速（m/s）
            reference_position (str or numpy.ndarray): 参考位置，'center'或具体坐标
            weighting (str): 加权方式，'uniform', 'hanning', 'hamming', 'blackman'
            delay_method (str): 时延补偿方法，'phase', 'integer', 'fractional'

        Returns:
            tuple: (angles, beam_pattern)
                angles: 扫描角度数组（度）
                beam_pattern: 波束方向图（线性功率值），形状为(len(angles),)
        """
        print(f"\n开始执行时延波束形成:")
        print(f"  扫描角度范围: [{angles[0]}, {angles[-1]}]°，共 {len(angles)} 个角度")
        print(f"  声速: {sound_speed} m/s")
        print(f"  参考位置: {reference_position}")
        print(f"  加权方式: {weighting}")
        print(f"  时延补偿方法: {delay_method}")

        # 检查输入参数
        if not signal_segments:
            raise ValueError("信号片段字典为空")

        if len(element_positions) == 0:
            raise ValueError("阵元位置数组为空")

        if len(angles) == 0:
            raise ValueError("扫描角度数组为空")

        # 获取信号参数
        element_ids = sorted(list(signal_segments.keys()))
        n_elements = len(element_ids)

        # 检查阵元数量
        if n_elements == 0:
            raise ValueError("没有有效的阵元")

        # 检查阵元数量与位置数量是否一致
        if n_elements != len(element_positions):
            raise ValueError(f"阵元数量 ({n_elements}) 与位置数量 ({len(element_positions)}) 不一致")

        # 检查信号长度
        try:
            signal_length = len(next(iter(signal_segments.values())))
            if signal_length == 0:
                raise ValueError("信号长度为0")
        except StopIteration:
            raise ValueError("信号片段字典为空")
        except Exception as e:
            raise ValueError(f"获取信号长度失败: {str(e)}")

        print(f"  阵元数量: {n_elements}")
        print(f"  信号长度: {signal_length}")

        fs = self.fs
        print(f"  采样率: {fs} Hz")

        # 确定参考位置
        try:
            if reference_position == 'center':
                ref_pos = np.mean(element_positions, axis=0)
                print(f"  使用中心点作为参考位置: {ref_pos}")
            elif reference_position == 'first':
                ref_pos = element_positions[0]
                print(f"  使用第一个阵元作为参考位置: {ref_pos}")
            elif reference_position == 'last':
                ref_pos = element_positions[-1]
                print(f"  使用最后一个阵元作为参考位置: {ref_pos}")
            else:
                ref_pos = np.array(reference_position)
                print(f"  使用自定义参考位置: {ref_pos}")
        except Exception as e:
            raise ValueError(f"确定参考位置失败: {str(e)}")

        # 计算各阵元相对于参考位置的位置
        try:
            relative_positions = element_positions - ref_pos
            print(f"  相对位置计算完成，形状: {relative_positions.shape}")
        except Exception as e:
            raise ValueError(f"计算相对位置失败: {str(e)}")

        # 确定加权系数
        try:
            if weighting == 'uniform':
                weights = np.ones(n_elements)
            elif weighting == 'hanning':
                weights = np.hanning(n_elements)
            elif weighting == 'hamming':
                weights = np.hamming(n_elements)
            elif weighting == 'blackman':
                weights = np.blackman(n_elements)
            else:
                weights = np.ones(n_elements)
            print(f"  加权系数计算完成，长度: {len(weights)}")
        except Exception as e:
            raise ValueError(f"计算加权系数失败: {str(e)}")

        # 将信号数据转换为数组形式，便于向量化处理
        try:
            signals_array = np.array([signal_segments[element_id] for element_id in element_ids])
            print(f"  信号数组转换完成，形状: {signals_array.shape}")
        except Exception as e:
            raise ValueError(f"转换信号数组失败: {str(e)}")

        # 初始化波束方向图
        beam_pattern = np.zeros(len(angles))

        # 对于相位补偿方法，预先计算FFT以提高效率
        if delay_method == 'phase' or delay_method == 'position_based':
            try:
                # 计算所有信号的FFT
                signals_fft = np.fft.rfft(signals_array, axis=1)
                freqs = np.fft.rfftfreq(signal_length, 1/fs)
                print(f"  FFT计算完成，形状: {signals_fft.shape}，频率点数: {len(freqs)}")
            except Exception as e:
                raise ValueError(f"计算FFT失败: {str(e)}")

        # 打印阵元位置信息，帮助调试
        print(f"\n阵元位置信息:")
        for i, pos in enumerate(element_positions):
            print(f"  阵元 {i}: 位置 = {pos}")

        # 对每个角度进行波束形成
        processed_angles = 0
        for i, angle in enumerate(angles):
            try:
                # 将角度转换为弧度
                theta = np.radians(angle)

                # 计算单位方向向量（假设在x-z平面内）
                # 注意：在水声学中，通常使用以下坐标系：
                # x轴：水平方向（距离）
                # y轴：横向（宽度）
                # z轴：垂直方向（深度）
                #
                # 角度定义：
                # theta是标准俯仰角：0°表示水平方向，正角度表示向下，负角度表示向上
                # 在二维坐标系(x,z)中，方向向量的x分量是cos(theta)，z分量是sin(theta)

                # 检查阵元位置的维度，确保方向向量维度匹配
                dim = element_positions.shape[1]
                print(f"  阵元位置维度: {dim}")

                if dim == 3:
                    # 三维坐标系 (x, y, z)，角度相对于x轴
                    # 在x-z平面内，y分量为0
                    direction = np.array([np.cos(theta), 0, np.sin(theta)])
                elif dim == 2:
                    # 二维坐标系 (x, z)，角度相对于x轴
                    # 这里的角度是标准俯仰角：0°表示水平方向，正角度表示向下，负角度表示向上
                    direction = np.array([np.cos(theta), np.sin(theta)])
                else:
                    # 其他情况，创建适当维度的方向向量
                    direction = np.zeros(dim)
                    if dim >= 1:
                        direction[0] = np.cos(theta)  # x方向
                    if dim >= 2:
                        direction[-1] = np.sin(theta)  # 最后一个维度作为深度

                print(f"  角度 {angle}°, 方向向量: {direction}, 形状: {direction.shape}")

                # 计算各阵元相对于参考位置的时延
                delays = np.dot(relative_positions, direction) / sound_speed
                print(f"  时延范围: [{np.min(delays)}, {np.max(delays)}] 秒")

                # 根据时延补偿方法处理
                if delay_method == 'phase':
                    # 频域相位补偿方法
                    try:
                        # 为每个阵元和每个频率计算相位移动，注意：与其他方法相同，同样要对delays取负数
                        phase_shifts = np.exp(-1j * 2 * np.pi * np.outer(-delays, freqs))

                        print(f"  相位补偿: delays形状={delays.shape}, freqs形状={freqs.shape}, phase_shifts形状={phase_shifts.shape}")
                        print(f"  signals_fft形状={signals_fft.shape}")

                        # 检查signals_fft的维度，确保维度匹配
                        if signals_fft.ndim == 2:  # (n_elements, n_freq_points)
                            # 应用相位移动到FFT结果
                            shifted_ffts = signals_fft * phase_shifts

                            # 应用权重
                            weighted_ffts = weights[:, np.newaxis] * shifted_ffts
                        elif signals_fft.ndim == 3:  # (n_elements, n_segments, n_freq_points)
                            # 应用相位移动到FFT结果
                            shifted_ffts = signals_fft * phase_shifts[:, np.newaxis, :]

                            # 应用权重
                            weighted_ffts = weights[:, np.newaxis, np.newaxis] * shifted_ffts
                        else:
                            raise ValueError(f"不支持的signals_fft维度: {signals_fft.ndim}")

                        print(f"  shifted_ffts形状={shifted_ffts.shape}, weighted_ffts形状={weighted_ffts.shape}")

                        # 求和并转回时域
                        beamformed_fft = np.sum(weighted_ffts, axis=0)
                        print(f"  beamformed_fft形状={beamformed_fft.shape}")

                        beamformed_signal = np.fft.irfft(beamformed_fft, signal_length)
                        print(f"  beamformed_signal形状={beamformed_signal.shape}")
                    except Exception as e:
                        import traceback
                        error_traceback = traceback.format_exc()
                        print(f"  相位补偿方法失败: {str(e)}")
                        print(f"  详细错误信息:\n{error_traceback}")
                        raise ValueError(f"相位补偿方法失败: {str(e)}")

                else:
                    # 时域时延补偿方法（整数延迟或分数延迟）
                    try:
                        # 初始化波束形成输出
                        beamformed_signal = np.zeros(signal_length)

                        # 对每个阵元进行时延补偿和加权求和
                        for j, element_id in enumerate(element_ids):
                            signal_j = signal_segments[element_id]
                            delay_j = delays[j]
                            weight_j = weights[j]

                            if delay_method == 'integer':
                                # 整数样本延迟（时域）
                                # 计算补偿时延（与几何时延相反）
                                # 在波束形成中，delay_j是几何路径差引起的时延
                                # 为了对齐信号，需要施加相反方向的补偿
                                compensated_delay_seconds = -delay_j
                                compensated_delay_samples = int(round(compensated_delay_seconds * fs))

                                # print(f"    阵元 {j}: 几何时延={delay_j:.6f}秒, 补偿时延={compensated_delay_seconds:.6f}秒, 补偿样本数={compensated_delay_samples}")

                                if compensated_delay_samples >= 0:  # 需要提前信号
                                    if compensated_delay_samples < signal_length:
                                        delayed_signal = np.pad(signal_j, (compensated_delay_samples, 0), 'constant')[:signal_length]
                                    else:
                                        delayed_signal = np.zeros(signal_length)
                                else:  # 需要延迟信号
                                    if -compensated_delay_samples < signal_length:
                                        delayed_signal = np.pad(signal_j, (0, -compensated_delay_samples), 'constant')[-signal_length:]
                                    else:
                                        delayed_signal = np.zeros(signal_length)

                            elif delay_method == 'fractional':
                                # 分数延迟（使用线性插值实现）
                                # 计算补偿时延（与几何时延相反）
                                compensated_delay_seconds = -delay_j
                                compensated_delay_samples = compensated_delay_seconds * fs

                                # print(f"    阵元 {j}: 几何时延={delay_j:.6f}秒, 补偿时延={compensated_delay_seconds:.6f}秒, 补偿样本数={compensated_delay_samples:.2f}")

                                # 使用线性插值实现分数延迟
                                indices = np.arange(signal_length)
                                indices_shifted = indices - compensated_delay_samples  # 注意这里使用补偿时延

                                # 只计算有效范围内的样本
                                valid_indices = (indices_shifted >= 0) & (indices_shifted < signal_length - 1)

                                if np.any(valid_indices):
                                    # 创建延迟信号
                                    delayed_signal = np.zeros(signal_length)

                                    # 获取有效索引
                                    valid_indices_int = np.where(valid_indices)[0]

                                    # 计算地板和天花板索引
                                    indices_floor = np.floor(indices_shifted[valid_indices]).astype(int)
                                    indices_ceil = indices_floor + 1

                                    # 计算小数部分
                                    frac = indices_shifted[valid_indices] - indices_floor

                                    # 线性插值
                                    delayed_signal[valid_indices_int] = (1 - frac) * signal_j[indices_floor] + frac * signal_j[indices_ceil]
                                else:
                                    delayed_signal = np.zeros(signal_length)
                            else:
                                # 默认使用整数延迟
                                # 计算补偿时延（与几何时延相反）
                                compensated_delay_seconds = -delay_j
                                compensated_delay_samples = int(round(compensated_delay_seconds * fs))

                                # print(f"    阵元 {j}: 几何时延={delay_j:.6f}秒, 补偿时延={compensated_delay_seconds:.6f}秒, 补偿样本数={compensated_delay_samples}")

                                if compensated_delay_samples >= 0:  # 需要提前信号
                                    if compensated_delay_samples < signal_length:
                                        delayed_signal = np.pad(signal_j, (compensated_delay_samples, 0), 'constant')[:signal_length]
                                    else:
                                        delayed_signal = np.zeros(signal_length)
                                else:  # 需要延迟信号
                                    if -compensated_delay_samples < signal_length:
                                        delayed_signal = np.pad(signal_j, (0, -compensated_delay_samples), 'constant')[-signal_length:]
                                    else:
                                        delayed_signal = np.zeros(signal_length)

                            # 加权求和
                            beamformed_signal += weight_j * delayed_signal
                    except Exception as e:
                        raise ValueError(f"时域时延补偿失败: {str(e)}")

                # 每处理10个角度打印一次进度
                processed_angles += 1
                if processed_angles % 10 == 0 or processed_angles == len(angles):
                    print(f"  已处理 {processed_angles}/{len(angles)} 个角度 ({processed_angles/len(angles)*100:.1f}%)")

                # 计算波束输出功率
                beam_pattern[i] = np.mean(beamformed_signal ** 2)

                # 添加调试输出
                if i % 10 == 0 or i == len(angles) - 1:
                    print(f"  角度 {angle}°: 波束输出功率 = {beam_pattern[i]:.6e}")

            except Exception as e:
                print(f"  处理角度 {angle}° 失败: {str(e)}")
                # 继续处理下一个角度，而不是中断整个计算
                continue

        # 检查是否有有效的波束方向图
        valid_points = np.sum(~np.isnan(beam_pattern))
        if valid_points == 0:
            print("警告: 没有计算出有效的波束方向图点，返回零数组")
            # 返回零数组而不是抛出异常，以便UI能够显示
            return angles, np.zeros_like(angles)
        elif valid_points < len(angles):
            print(f"警告: 只有 {valid_points}/{len(angles)} 个角度计算成功")
            # 将NaN值替换为0，以便后续处理
            beam_pattern = np.where(np.isnan(beam_pattern), 0, beam_pattern)

        # 检查波束方向图是否全为0
        if np.all(beam_pattern == 0):
            print("警告: 波束方向图全为0，可能是计算过程中出现问题")
            # 返回零数组而不是抛出异常，以便UI能够显示
            return angles, np.zeros_like(angles)

        try:
            # 在转换为dB前，确保所有值都是正的
            print(f"波束方向图值范围: [{np.min(beam_pattern)}, {np.max(beam_pattern)}]")

            # 检查是否有负值或非正值
            if np.any(beam_pattern <= 0):
                print(f"警告: 波束方向图包含 {np.sum(beam_pattern <= 0)} 个非正值，将被替换为小正数")
                # 将非正值替换为一个很小的正数
                min_positive = np.min(beam_pattern[beam_pattern > 0]) if np.any(beam_pattern > 0) else 1e-10
                safe_min = min(min_positive * 0.01, 1e-10)  # 使用比最小正值小100倍的值，但不小于1e-10
                beam_pattern = np.where(beam_pattern > 0, beam_pattern, safe_min)
                print(f"替换非正值后的波束方向图值范围: [{np.min(beam_pattern)}, {np.max(beam_pattern)}]")

            # 确保所有值都是有限的
            if not np.all(np.isfinite(beam_pattern)):
                print(f"警告: 波束方向图包含 {np.sum(~np.isfinite(beam_pattern))} 个无穷大或NaN值，将被替换为小正数")
                beam_pattern = np.where(np.isfinite(beam_pattern), beam_pattern, 1e-10)
                print(f"替换无穷大或NaN值后的波束方向图值范围: [{np.min(beam_pattern)}, {np.max(beam_pattern)}]")

            # 最后检查一次，确保所有值都是正的且有限的
            if np.any(beam_pattern <= 0) or not np.all(np.isfinite(beam_pattern)):
                print("警告: 经过处理后仍有非正值或无穷大/NaN值，强制替换为1e-10")
                beam_pattern = np.where((beam_pattern <= 0) | ~np.isfinite(beam_pattern), 1e-10, beam_pattern)

            # 打印波束方向图的关键信息
            print("\n波束方向图结果摘要:")
            print(f"  角度范围: [{angles[0]}, {angles[-1]}]°，共 {len(angles)} 个角度")
            print(f"  最大值位置: {angles[np.argmax(beam_pattern)]}°，值: {np.max(beam_pattern):.6e}")

            # 打印前10个和后10个值，帮助调试
            print(f"\n前10个角度的波束方向图值:")
            for j in range(min(10, len(angles))):
                print(f"  角度 {angles[j]}°: {beam_pattern[j]:.6e}")

            print(f"\n后10个角度的波束方向图值:")
            for j in range(max(0, len(angles)-10), len(angles)):
                print(f"  角度 {angles[j]}°: {beam_pattern[j]:.6e}")

            # 返回原始的平均功率数据，而不是归一化的dB指向性图
            return angles, beam_pattern

        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            print(f"转换波束方向图为dB失败: {str(e)}")
            print(f"详细错误信息:\n{error_traceback}")

            # 返回零数组而不是抛出异常，以便UI能够显示
            print("返回零数组作为波束方向图")
            return angles, np.zeros_like(angles)

    def _process_angle_batch(self, angles_batch, signal_segments, element_positions,
                           sound_speed, reference_position, weighting, delay_method):
        """
        处理一批角度的波束形成计算（用于并行计算）

        Args:
            angles_batch (numpy.ndarray): 一批角度
            signal_segments (dict): 各阵元信号片段字典
            element_positions (numpy.ndarray): 阵元位置数组
            sound_speed (float): 声速
            reference_position (str or numpy.ndarray): 参考位置
            weighting (str): 加权方式
            delay_method (str): 时延补偿方法

        Returns:
            tuple: (angles_batch, beam_pattern_batch) 角度批次和对应的波束方向图（线性功率值）
        """
        try:
            # 检查输入参数
            if len(angles_batch) == 0:
                raise ValueError("角度批次为空")

            # 创建临时对象以复用代码
            temp_obj = IntegratedSimulation(self.fs)
            temp_obj.array_elements = self.array_elements

            # 调用单线程波束形成方法
            _, beam_pattern_batch = temp_obj.delay_and_sum_beamforming(
                signal_segments, element_positions, angles_batch,
                sound_speed, reference_position, weighting, delay_method
            )

            return angles_batch, beam_pattern_batch

        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            print(f"处理角度批次 [{angles_batch[0]}, {angles_batch[-1]}] 失败: {str(e)}")
            print(f"详细错误信息:\n{error_traceback}")

            # 返回全为0的结果，而不是NaN，避免后续处理出现问题
            print(f"  批次处理失败，返回全为0的结果而不是NaN")
            beam_pattern_batch = np.zeros(len(angles_batch))
            return angles_batch, beam_pattern_batch

    def _calculate_beam_width(self, angles, beam_pattern, threshold_db=-3):
        """
        计算波束宽度

        Args:
            angles (numpy.ndarray): 角度数组
            beam_pattern (numpy.ndarray): 波束方向图（线性功率值）
            threshold_db (float): 阈值（dB），默认为-3dB

        Returns:
            float: 波束宽度（度）
        """
        try:
            # 将线性功率值转换为dB
            beam_pattern_db = 10 * np.log10(np.where(beam_pattern > 0, beam_pattern, 1e-10))

            # 归一化dB值
            max_db = np.max(beam_pattern_db)
            beam_pattern_db = beam_pattern_db - max_db

            # 找出最大值位置
            max_idx = np.argmax(beam_pattern_db)
            max_angle = angles[max_idx]

            # 找出左侧和右侧第一个低于阈值的点
            left_idx = max_idx
            while left_idx > 0 and beam_pattern_db[left_idx] > threshold_db:
                left_idx -= 1

            right_idx = max_idx
            while right_idx < len(angles) - 1 and beam_pattern_db[right_idx] > threshold_db:
                right_idx += 1

            # 计算波束宽度
            left_angle = angles[left_idx]
            right_angle = angles[right_idx]
            beam_width = right_angle - left_angle

            return beam_width
        except Exception as e:
            print(f"计算波束宽度失败: {str(e)}")
            return 0.0

    def parallel_beamforming(self, signal_segments, element_positions, angles,
                           sound_speed=1500, reference_position='center',
                           weighting='uniform', delay_method='fractional',
                           n_jobs=None):
        """
        并行时延波束形成算法

        Args:
            signal_segments (dict): 各阵元信号片段字典
            element_positions (numpy.ndarray): 阵元位置数组，形状为(n_elements, 3)
            angles (numpy.ndarray): 扫描角度数组（度）
            sound_speed (float): 声速（m/s）
            reference_position (str or numpy.ndarray): 参考位置，'center'或具体坐标
            weighting (str): 加权方式，'uniform', 'hanning', 'hamming', 'blackman'
            delay_method (str): 时延补偿方法，'phase', 'integer', 'fractional'
            n_jobs (int, optional): 并行任务数，默认为CPU核心数

        Returns:
            tuple: (angles, beam_pattern)
                angles: 扫描角度数组（度）
                beam_pattern: 波束方向图（线性功率值），形状为(len(angles),)
        """
        print(f"\n开始执行并行时延波束形成:")
        print(f"  扫描角度范围: [{angles[0]}, {angles[-1]}]°，共 {len(angles)} 个角度")

        # 如果角度数量较少，使用单线程计算
        if len(angles) < 10:
            print(f"  角度数量较少 ({len(angles)} < 10)，使用单线程计算")
            return self.delay_and_sum_beamforming(
                signal_segments, element_positions, angles,
                sound_speed, reference_position, weighting, delay_method
            )

        # 确定并行任务数
        if n_jobs is None:
            n_jobs = max(1, multiprocessing.cpu_count() - 1)  # 留一个核心给系统
        print(f"  使用 {n_jobs} 个线程进行并行计算")

        # 将角度划分为多个批次
        batch_size = max(1, len(angles) // n_jobs)
        angle_batches = [angles[i:i+batch_size] for i in range(0, len(angles), batch_size)]
        print(f"  将 {len(angles)} 个角度划分为 {len(angle_batches)} 个批次，每批次约 {batch_size} 个角度")

        # 创建部分函数，固定其他参数
        process_batch = partial(
            self._process_angle_batch,
            signal_segments=signal_segments,
            element_positions=element_positions,
            sound_speed=sound_speed,
            reference_position=reference_position,
            weighting=weighting,
            delay_method=delay_method
        )

        # 使用线程池并行计算
        beam_pattern = np.zeros(len(angles))

        # 记录成功处理的批次数
        successful_batches = 0
        total_batches = len(angle_batches)

        with concurrent.futures.ThreadPoolExecutor(max_workers=n_jobs) as executor:
            # 提交所有批次的计算任务
            future_to_batch = {
                executor.submit(process_batch, batch): i
                for i, batch in enumerate(angle_batches)
            }

            # 使用字典存储结果，以角度值为键
            angle_to_pattern = {}

            # 收集结果
            for future in concurrent.futures.as_completed(future_to_batch):
                batch_idx = future_to_batch[future]
                try:
                    batch_angles, batch_pattern = future.result()

                    # 添加调试输出
                    print(f"  批次 {batch_idx+1} 角度范围: [{batch_angles[0]}, {batch_angles[-1]}], 波束方向图值范围: [{np.min(batch_pattern)}, {np.max(batch_pattern)}]")

                    # 检查批次结果中是否有NaN或无穷大值
                    if np.any(~np.isfinite(batch_pattern)):
                        print(f"  警告: 批次 {batch_idx+1} 结果包含 {np.sum(~np.isfinite(batch_pattern))} 个NaN或无穷大值")
                        # 将NaN或无穷大值替换为0
                        batch_pattern = np.where(np.isfinite(batch_pattern), batch_pattern, 0.0)

                    # 检查批次结果中是否有异常大或异常小的值
                    if np.any(batch_pattern):  # 确保不是全零数组
                        max_val = np.max(np.abs(batch_pattern[np.isfinite(batch_pattern)]))
                        if max_val > 1e20:
                            print(f"  警告: 批次 {batch_idx+1} 结果包含异常大的值: {max_val}")
                            # 将异常大的值替换为0
                            batch_pattern = np.where(np.abs(batch_pattern) < 1e20, batch_pattern, 0.0)

                    # 将角度和对应的波束方向图值存储到字典中
                    for i, angle in enumerate(batch_angles):
                        angle_to_pattern[angle] = batch_pattern[i]

                    # 更新成功批次计数
                    successful_batches += 1
                    print(f"  批次 {batch_idx+1}/{total_batches} 处理完成 ({successful_batches/total_batches*100:.1f}%)")

                except Exception as e:
                    import traceback
                    error_traceback = traceback.format_exc()
                    print(f"  批次 {batch_idx+1}/{total_batches} 处理失败: {e}")
                    print(f"  详细错误信息:\n{error_traceback}")

        # 检查是否有成功处理的批次
        if successful_batches == 0:
            print("错误: 所有批次处理均失败，无法计算波束方向图")
            # 返回零数组而不是抛出异常，以便UI能够显示
            return angles, np.zeros_like(angles)

        # 重建完整的波束方向图
        print("重建完整的波束方向图...")
        beam_pattern = np.zeros(len(angles))
        for i, angle in enumerate(angles):
            if angle in angle_to_pattern:
                beam_pattern[i] = angle_to_pattern[angle]
            else:
                print(f"  警告: 角度 {angle}° 没有对应的波束方向图值，使用0代替")

        # 检查重建后的波束方向图
        print(f"重建后的波束方向图: 长度={len(beam_pattern)}, 非零元素数量={np.sum(beam_pattern != 0)}, 值范围=[{np.min(beam_pattern)}, {np.max(beam_pattern)}]")

        # 打印波束方向图的基本统计信息
        print(f"\n波束方向图统计信息:")
        print(f"  形状: {beam_pattern.shape}")
        print(f"  非零元素数量: {np.sum(beam_pattern != 0)}/{len(beam_pattern)}")
        print(f"  NaN元素数量: {np.sum(np.isnan(beam_pattern))}/{len(beam_pattern)}")
        print(f"  无穷大元素数量: {np.sum(np.isinf(beam_pattern))}/{len(beam_pattern)}")

        # 如果有有限值，打印其范围
        finite_values = beam_pattern[np.isfinite(beam_pattern)]
        if len(finite_values) > 0:
            print(f"  有限值范围: [{np.min(finite_values)}, {np.max(finite_values)}]")

        # 打印前几个和后几个元素，帮助调试
        print(f"  前5个元素: {beam_pattern[:5]}")
        print(f"  后5个元素: {beam_pattern[-5:]}")

        # 检查波束方向图是否全为0
        if np.all(beam_pattern == 0):
            print("警告: 波束方向图全为0，可能是计算过程中出现问题")
            # 返回零数组而不是抛出异常，以便UI能够显示
            return angles, np.zeros_like(angles)

        # 检查是否有异常大的值
        if np.any(np.abs(beam_pattern) > 1e20):
            print(f"警告: 波束方向图包含异常大的值，最大绝对值: {np.max(np.abs(beam_pattern[np.isfinite(beam_pattern)]))}")
            # 将异常大的值替换为0
            beam_pattern = np.where(np.abs(beam_pattern) < 1e20, beam_pattern, 0.0)
            print(f"  替换异常值后的波束方向图值范围: [{np.min(beam_pattern)}, {np.max(beam_pattern)}]")

        try:
            # 在转换为dB前，确保所有值都是正的
            print(f"波束方向图值范围: [{np.min(beam_pattern)}, {np.max(beam_pattern)}]")

            # 检查是否有负值或NaN
            if np.any(beam_pattern < 0) or np.any(np.isnan(beam_pattern)):
                print(f"警告: 波束方向图包含 {np.sum(beam_pattern < 0)} 个负值和 {np.sum(np.isnan(beam_pattern))} 个NaN值")
                # 将负值和NaN替换为一个很小的正数
                beam_pattern = np.where(np.isnan(beam_pattern) | (beam_pattern <= 0), 1e-10, beam_pattern)
                print(f"替换后的波束方向图值范围: [{np.min(beam_pattern)}, {np.max(beam_pattern)}]")

            # 最后检查一次，确保所有值都是正的且有限的
            if np.any(beam_pattern <= 0) or not np.all(np.isfinite(beam_pattern)):
                print("警告: 经过处理后仍有非正值或无穷大/NaN值，强制替换为1e-10")
                beam_pattern = np.where((beam_pattern <= 0) | ~np.isfinite(beam_pattern), 1e-10, beam_pattern)

            # 转换为dB并归一化，使用更安全的方法
            try:
                beam_pattern_db = 10 * np.log10(beam_pattern)  # 现在应该安全了，因为所有值都是正的
            except Exception as e:
                print(f"警告: 计算对数时出错: {str(e)}，使用更安全的方法")
                # 如果仍然出错，使用更安全的方法
                beam_pattern_db = 10 * np.log10(beam_pattern + 1e-10)

            # 检查是否有有效的dB值
            if np.all(np.isnan(beam_pattern_db)):
                print("错误: 波束方向图dB值全为NaN，返回零数组")
                # 返回零数组而不是抛出异常，以便UI能够显示
                return angles, np.zeros_like(angles)

            # 检查是否有有限的dB值
            if not np.any(np.isfinite(beam_pattern_db)):
                print("错误: 波束方向图dB值全为无穷大或NaN，返回零数组")
                # 返回零数组而不是抛出异常，以便UI能够显示
                return angles, np.zeros_like(angles)

            # 替换任何NaN或无穷大值
            beam_pattern_db = np.where(np.isfinite(beam_pattern_db), beam_pattern_db, -100)

            # 找出最大的有限值用于归一化
            valid_values = beam_pattern_db[np.isfinite(beam_pattern_db)]
            if len(valid_values) > 0:
                max_db = np.max(valid_values)
                beam_pattern_db -= max_db  # 归一化
                print(f"并行波束形成完成，最大功率: {max_db:.2f} dB")
            else:
                print("警告: 没有有效的dB值用于归一化")

            # 打印波束方向图的关键信息
            print("\n波束方向图结果摘要:")
            print(f"  角度范围: [{angles[0]}, {angles[-1]}]°，共 {len(angles)} 个角度")
            print(f"  最大值位置: {angles[np.argmax(beam_pattern_db)]}°，值: 0.0 dB")
            print(f"  -3dB宽度: {self._calculate_beam_width(angles, beam_pattern_db, -3):.2f}°")
            print(f"  -6dB宽度: {self._calculate_beam_width(angles, beam_pattern_db, -6):.2f}°")
            print(f"  -10dB宽度: {self._calculate_beam_width(angles, beam_pattern_db, -10):.2f}°")

            # 打印前10个和后10个值，帮助调试
            print(f"\n前10个角度的波束方向图值:")
            for j in range(min(10, len(angles))):
                print(f"  角度 {angles[j]}°: {beam_pattern_db[j]:.2f} dB")

            print(f"\n后10个角度的波束方向图值:")
            for j in range(max(0, len(angles)-10), len(angles)):
                print(f"  角度 {angles[j]}°: {beam_pattern_db[j]:.2f} dB")

            return angles, beam_pattern_db

        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            print(f"转换波束方向图为dB失败: {str(e)}")
            print(f"详细错误信息:\n{error_traceback}")

            # 返回零数组而不是抛出异常，以便UI能够显示
            print("返回零数组作为波束方向图")
            return angles, np.zeros_like(angles)

