# -*- coding: utf-8 -*-
"""
测试新的船舶辐射噪声模型

测试重构后的船舶辐射噪声模型的功能
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import numpy as np
import matplotlib.pyplot as plt
import matplotlib as mpl
from scipy import signal as scipy_signal
from src.models.noise_sources.ship_radiated import ShipRadiatedNoise

# 设置中文字体支持
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号
except:
    print("警告: 无法设置中文字体，图形中的中文可能无法正确显示")


def test_continuous_spectrum():
    """测试连续谱模型"""
    print("=" * 50)
    print("测试船舶辐射噪声连续谱模型")
    print("=" * 50)

    # 创建船舶辐射噪声模型实例
    ship_model = ShipRadiatedNoise(name="测试船舶辐射噪声")

    # 设置连续谱参数
    ship_model.set_continuous_params(
        f0=250.0,
        sl0=130.0,
        a1_oct=3.0,
        a2_oct=-4.2
    )

    # 设置仿真参数
    ship_model.set_simulation_params(
        fs=10000,
        duration=5.0,
        filter_order=257
    )

    # 生成连续谱信号（已缩放）
    continuous_signal, scaling_factor = ship_model.simulate_continuous_spectrum()

    # 计算功率谱密度
    freqs, psd, psd_db = ship_model.calculate_psd(continuous_signal)

    # 打印信息
    print(f"生成的信号长度: {len(continuous_signal)}")
    print(f"采样频率: {ship_model.fs} Hz")
    print(f"信号持续时间: {ship_model.duration} 秒")
    print(f"峰值频率: {ship_model.f0} Hz")
    print(f"峰值声级: {ship_model.sl0} dB")
    print(f"上升斜率: {ship_model.a1_oct} dB/倍频程")
    print(f"下降斜率: {ship_model.a2_oct} dB/倍频程")

    # 计算理论频谱
    # 1Hz到2500Hz的对数间隔
    freq_range = np.logspace(0, np.log10(2500), 1000)
    spectrum = ship_model.calculate_sl(freq_range)

    # 绘制结果
    plt.figure(figsize=(12, 12))

    # 绘制理论频谱
    plt.subplot(3, 1, 1)
    plt.plot(freq_range, spectrum, 'b-', label='理论频谱')
    plt.title('船舶辐射噪声连续谱 - 理论频谱')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('声级 (dB re 1μPa)')
    plt.xlim(0, 2500)
    plt.grid(True)
    plt.legend()

    # 绘制信号的功率谱密度
    plt.subplot(3, 1, 2)
    # 将小于0的值设为0
    psd_db_positive = np.maximum(psd_db, 0)
    plt.plot(freqs, psd_db_positive, 'm-', label='信号PSD')
    plt.title('船舶辐射噪声连续谱 - 功率谱密度')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('功率谱密度 (dB/Hz)')
    plt.xlim(0, 2500)
    plt.grid(True)
    plt.legend()

    # 绘制时域信号
    plt.subplot(3, 1, 3)
    t = np.linspace(0, ship_model.duration, len(continuous_signal), endpoint=False)
    # 计算显示1秒所需的点数
    points_per_second = int(ship_model.fs)
    # 跳过前面的零值部分，从滤波器阶数开始显示
    skip_samples = min(ship_model.filter_order, points_per_second // 10)  # 跳过的样本数
    plt.plot(t[skip_samples:points_per_second], continuous_signal[skip_samples:points_per_second], 'r-', label='信号')
    plt.title('船舶辐射噪声连续谱 - 时域信号')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    # 调整x轴范围，从跳过的时间开始
    plt.xlim(t[skip_samples], t[skip_samples] + 1.0)
    plt.grid(True)
    plt.legend()

    plt.tight_layout()
    plt.savefig('continuous_spectrum_test_new.png')
    plt.close()

    print("连续谱测试完成，结果已保存为 continuous_spectrum_test_new.png")
    return ship_model


def test_line_spectrum():
    """测试线谱模型"""
    print("\n" + "=" * 50)
    print("测试船舶辐射噪声线谱模型")
    print("=" * 50)

    # 创建船舶辐射噪声模型实例
    ship_model = ShipRadiatedNoise(name="测试船舶辐射噪声")

    # 设置连续谱参数（作为线谱的参考）
    ship_model.set_continuous_params(
        f0=250.0,
        sl0=130.0,
        a1_oct=3.0,
        a2_oct=-4.2
    )

    # 设置仿真参数
    ship_model.set_simulation_params(
        fs=10000,
        duration=5.0,
        filter_order=257
    )

    # 设置线谱参数
    line_frequencies = np.array([16.0, 32.0, 48.0, 65.0, 350.0, 800.0])
    line_levels_diff = np.array([25.0, 23.0, 21.0, 19.0, 18.0, 18.0])
    ship_model.set_line_params(line_frequencies, line_levels_diff)

    # 生成线谱信号
    line_signal = ship_model.simulate_line_spectrum()

    # 生成总信号（连续谱+线谱）
    total_signal = ship_model.simulate_radiated_noise(include_line=True, include_modulation=False)

    # 计算总信号的功率谱密度
    total_freqs, total_psd, total_psd_db = ship_model.calculate_psd(total_signal)

    # 打印信息
    print(f"生成的信号长度: {len(line_signal)}")
    print(f"采样频率: {ship_model.fs} Hz")
    print(f"信号持续时间: {ship_model.duration} 秒")
    print("\n线谱参数:")
    for i, (freq, level_diff) in enumerate(zip(line_frequencies, line_levels_diff)):
        sl_continuous = ship_model.calculate_sl(freq)
        sl_line = sl_continuous + level_diff
        print(f"{freq:.2f} Hz: 连续谱电平 {sl_continuous:.2f} dB, 差值 {level_diff:.2f} dB, 线谱电平 {sl_line:.2f} dB")

    # 绘制结果
    plt.figure(figsize=(12, 15))

    # 绘制线谱时域信号
    plt.subplot(3, 1, 1)
    t = np.linspace(0, ship_model.duration, len(line_signal), endpoint=False)
    # 计算显示1秒所需的点数
    points_per_second = int(ship_model.fs)
    # 跳过前面的零值部分，从滤波器阶数开始显示
    skip_samples = min(ship_model.filter_order, points_per_second // 10)  # 跳过的样本数
    plt.plot(t[skip_samples:points_per_second], line_signal[skip_samples:points_per_second], 'r-', label='线谱信号')
    plt.title('船舶辐射噪声 - 线谱时域信号')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    # 调整x轴范围，从跳过的时间开始
    plt.xlim(t[skip_samples], t[skip_samples] + 1.0)
    plt.grid(True)
    plt.legend()

    # 绘制总信号的功率谱密度
    plt.subplot(3, 1, 2)
    # 将小于0的值设为0
    total_psd_db_positive = np.maximum(total_psd_db, 0)
    plt.plot(total_freqs, total_psd_db_positive, 'm-', label='总信号PSD')
    plt.title('船舶辐射噪声 - 总信号功率谱密度')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('功率谱密度 (dB/Hz)')
    plt.xlim(0, 2500)
    plt.grid(True)
    plt.legend()

    # 绘制时域信号
    plt.subplot(3, 1, 3)
    t = np.linspace(0, ship_model.duration, len(total_signal), endpoint=False)
    # 计算显示1秒所需的点数
    points_per_second = int(ship_model.fs)
    # 跳过前面的零值部分，从滤波器阶数开始显示
    skip_samples = min(ship_model.filter_order, points_per_second // 10)  # 跳过的样本数
    plt.plot(t[skip_samples:points_per_second], total_signal[skip_samples:points_per_second], 'r-', label='总信号')
    plt.title('船舶辐射噪声 - 时域信号')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    # 调整x轴范围，从跳过的时间开始
    plt.xlim(t[skip_samples], t[skip_samples] + 1.0)
    plt.grid(True)
    plt.legend()

    plt.tight_layout()
    plt.savefig('line_spectrum_test_new.png')
    plt.close()

    print("线谱测试完成，结果已保存为 line_spectrum_test_new.png")
    return ship_model


if __name__ == "__main__":
    # 测试连续谱模型
    ship_model_continuous = test_continuous_spectrum()

    # 测试线谱模型
    ship_model_line = test_line_spectrum()

    print("\n所有测试完成！")
