# -*- coding: utf-8 -*-
"""
综合仿真视图更新器

负责更新综合仿真视图，处理数据管理器的数据变更信号。
"""

from PyQt5.QtCore import QObject, pyqtSlot
import numpy as np


class IntegratedViewUpdater(QObject):
    """
    综合仿真视图更新器

    负责更新综合仿真视图，处理数据管理器的数据变更信号。
    """

    def __init__(self, view, data_manager):
        """
        初始化综合仿真视图更新器

        Args:
            view: 综合仿真视图实例
            data_manager: 数据管理器实例
        """
        super().__init__()

        # 保存视图和数据管理器引用
        self.view = view
        self.data_manager = data_manager

        # 连接数据管理器的信号
        self.data_manager.parameters_changed.connect(self.on_parameters_changed)
        self.data_manager.results_changed.connect(self.on_results_changed)

    @pyqtSlot(str)
    def on_parameters_changed(self, module):
        """
        当参数更新时的槽函数

        Args:
            module: 更新的模块名称
        """
        if module != 'integrated':
            return

        # 获取参数
        params = self.data_manager.get_parameters('integrated')
        if not params:
            return

        # 获取上一次的参数，用于比较变化
        old_params = getattr(self, '_last_params', {})

        # 保存当前参数，用于下次比较
        self._last_params = params.copy()

        # 检查哪些参数发生了变化，只更新相关视图
        changed_params = set()
        for key in ['selected_channel_element_index', 'selected_signal_element_index',
                   'selected_spectrum_element_index', 'selected_frequency']:
            if key in params and (key not in old_params or params[key] != old_params.get(key)):
                changed_params.add(key)

        # 如果没有参数变化，不需要更新视图
        if not changed_params:
            return

        # 根据变化的参数更新相应的视图
        self.update_view_from_params(params, changed_params)

    @pyqtSlot(str)
    def on_results_changed(self, module):
        """
        当结果更新时的槽函数

        Args:
            module: 更新的模块名称
        """
        if module != 'integrated':
            return

        # 获取结果
        results = self.data_manager.get_results('integrated')

        # 如果结果为空，重置所有视图
        if not results:
            self.reset_all_views()
            return

        # 获取上一次的结果，用于比较变化
        old_results = getattr(self, '_last_results', {})

        # 保存当前结果，用于下次比较
        self._last_results = results.copy()

        # 检查哪些部分发生了变化
        changed_parts = set()

        # 检查信道数据是否变化
        if 'channel_data' in results:
            if 'channel_data' not in old_results:
                changed_parts.add('channel_data')
            else:
                # 安全地比较信道数据，避免DataFrame比较问题
                try:
                    # 比较基本信息而不是整个数据结构
                    current_meta = results['channel_data'].get('meta', {})
                    old_meta = old_results['channel_data'].get('meta', {})
                    current_dir = current_meta.get('channel_data_dir', '')
                    old_dir = old_meta.get('channel_data_dir', '')

                    if current_dir != old_dir:
                        changed_parts.add('channel_data')
                except Exception as e:
                    print(f"比较信道数据时出错: {e}")
                    # 如果比较失败，假设数据已变化
                    changed_parts.add('channel_data')

        # 检查信号处理结果是否变化
        if 'signal_processing' in results:
            if 'signal_processing' not in old_results:
                changed_parts.add('signal_processing')
            else:
                # 检查接收信号是否变化
                if 'received_signals' in results['signal_processing']:
                    if 'received_signals' not in old_results['signal_processing'] or \
                       results['signal_processing']['received_signals'] != old_results['signal_processing'].get('received_signals'):
                        changed_parts.add('received_signals')

                # 检查频谱是否变化
                if 'spectrum' in results['signal_processing']:
                    if 'spectrum' not in old_results['signal_processing'] or \
                       results['signal_processing']['spectrum'] != old_results['signal_processing'].get('spectrum'):
                        changed_parts.add('spectrum')

        # 检查阵列处理结果是否变化
        if 'array_processing' in results:
            if 'array_processing' not in old_results or results['array_processing'] != old_results.get('array_processing'):
                changed_parts.add('array_processing')

        # 如果没有变化，不需要更新视图
        if not changed_parts:
            return

        # 更新视图，只更新变化的部分
        self.update_view_from_results(results, changed_parts)

    def update_view_from_params(self, params, changed_params=None):
        """
        根据参数更新视图

        Args:
            params: 参数字典
            changed_params: 变化的参数集合，如果为None则更新所有视图
        """
        # 如果没有指定变化的参数，则更新所有视图
        if changed_params is None:
            changed_params = {'selected_channel_element_index', 'selected_signal_element_index',
                             'selected_spectrum_element_index', 'selected_frequency'}
        # 更新信道数据目录显示
        channel_data_dir = params.get('channel_data_dir', '')
        if hasattr(self.view, 'update_channel_data_dir'):
            self.view.update_channel_data_dir(channel_data_dir)

        # 获取参数值
        selected_channel_element_index = params.get('selected_channel_element_index', 0)
        selected_signal_element_index = params.get('selected_signal_element_index', 0)
        selected_spectrum_element_index = params.get('selected_spectrum_element_index', 0)
        selected_frequency = params.get('selected_frequency', 0)

        # 根据变化的参数有选择地更新视图
        if 'selected_channel_element_index' in changed_params:
            # 更新信道数据分析选项卡的选中阵元
            if hasattr(self.view, 'update_selected_channel_element'):
                self.view.update_selected_channel_element(selected_channel_element_index)

            # 兼容旧接口
            if hasattr(self.view, 'update_selected_element'):
                self.view.update_selected_element(selected_channel_element_index)

            # 更新到达结构图
            self._update_arrivals_plot(selected_channel_element_index, selected_frequency)

        if 'selected_signal_element_index' in changed_params:
            # 更新时域波形选项卡的选中阵元
            if hasattr(self.view, 'update_selected_signal_element'):
                self.view.update_selected_signal_element(selected_signal_element_index)

            # 更新接收信号时域波形
            self._update_received_signal_plot(selected_signal_element_index)

        if 'selected_spectrum_element_index' in changed_params:
            # 更新频谱图选项卡的选中阵元
            if hasattr(self.view, 'update_selected_spectrum_element'):
                self.view.update_selected_spectrum_element(selected_spectrum_element_index)

            # 更新频谱图
            self._update_spectrum_plot(selected_spectrum_element_index)

        if 'selected_frequency' in changed_params:
            # 更新选中的频率
            if hasattr(self.view, 'update_selected_frequency'):
                self.view.update_selected_frequency(selected_frequency)

            # 更新到达结构图
            self._update_arrivals_plot(selected_channel_element_index, selected_frequency)

    def _update_arrivals_plot(self, element_index, frequency):
        """
        更新到达结构图

        Args:
            element_index: 阵元索引
            frequency: 频率
        """
        # 获取结果
        results = self.data_manager.get_results('integrated')
        if not results or 'channel_data' not in results:
            return

        channel_data = results['channel_data']
        arrivals_data = channel_data.get('arrivals_data', {})

        # 检查选中的频率是否在数据中
        if frequency not in arrivals_data:
            return

        # 获取阵元ID列表
        array_elements = channel_data.get('array_elements', [])
        if element_index >= len(array_elements):
            return

        # 获取阵元ID
        element_id = element_index  # 假设阵元ID就是索引

        # 检查阵元ID是否在数据中
        if element_id not in arrivals_data[frequency]:
            return

        # 获取到达结构数据
        arrivals = arrivals_data[frequency][element_id]

        # 更新到达结构图
        if hasattr(self.view, 'update_arrivals_plot'):
            self.view.update_arrivals_plot(
                arrivals=arrivals,
                frequency=frequency,
                element_id=element_id,
                element_position=array_elements[element_index]
            )

    def _update_received_signal_plot(self, element_index):
        """
        更新接收信号时域波形

        Args:
            element_index: 阵元索引
        """
        # 获取结果
        results = self.data_manager.get_results('integrated')
        if not results or 'signal_processing' not in results:
            return

        signal_processing = results['signal_processing']
        received_signals = signal_processing.get('received_signals', {})

        # 检查选中的阵元是否在数据中
        if element_index not in received_signals:
            return

        # 获取接收信号数据
        received_signal = received_signals[element_index]

        # 获取源信号数据
        source_signal = signal_processing.get('source_signal', {})

        # 更新接收信号时域波形
        if hasattr(self.view, 'update_received_signal_plot'):
            self.view.update_received_signal_plot(
                source_signal=source_signal,
                received_signal=received_signal,
                element_index=element_index
            )

    def _update_spectrum_plot(self, element_index):
        """
        更新频谱图

        Args:
            element_index: 阵元索引
        """
        # 获取结果
        results = self.data_manager.get_results('integrated')
        if not results or 'signal_processing' not in results:
            return

        signal_processing = results['signal_processing']
        if 'spectrum' not in signal_processing:
            return

        spectrum_data = signal_processing['spectrum']

        # 检查选中的阵元是否在数据中
        if element_index not in spectrum_data:
            # 如果选中的阵元不在频谱数据中，清除频谱图并显示提示信息
            if hasattr(self.view, 'update_spectrum_plot_empty'):
                self.view.update_spectrum_plot_empty(element_index)
            return

        # 获取频谱数据
        element_spectrum = spectrum_data[element_index]

        # 更新频谱图
        if hasattr(self.view, 'update_spectrum_plot'):
            freqs = element_spectrum.get('freqs', [])
            psd_db = element_spectrum.get('psd_db', [])
            signal_type = element_spectrum.get('signal_type', '叠加噪声后')

            self.view.update_spectrum_plot(
                freqs=freqs,
                psd_db=psd_db,
                element_index=element_index,
                signal_type=signal_type
            )

    def update_view_from_results(self, results, changed_parts=None):
        """
        根据结果更新视图

        Args:
            results: 结果字典
            changed_parts: 变化的部分集合，如果为None则更新所有视图
        """
        # 如果没有指定变化的部分，则更新所有视图
        if changed_parts is None:
            changed_parts = {'channel_data', 'signal_processing', 'received_signals', 'spectrum', 'array_processing'}
        # 检查是否有信道数据结果，且信道数据发生了变化
        if 'channel_data' in results and 'channel_data' in changed_parts:
            channel_data = results['channel_data']

            # 更新信道数据基本信息
            if hasattr(self.view, 'update_channel_data_info'):
                # 提取基本信息
                meta = channel_data.get('meta', {})
                frequencies = channel_data.get('frequencies', [])
                array_elements = channel_data.get('array_elements', [])
                t_first_global = channel_data.get('t_first_global', 0.0)
                t_last_global = channel_data.get('t_last_global', 0.0)

                self.view.update_channel_data_info(
                    meta=meta,
                    frequencies=frequencies,
                    array_elements=array_elements,
                    t_first_global=t_first_global,
                    t_last_global=t_last_global
                )

            # 更新到达结构图
            if hasattr(self.view, 'update_arrivals_plot') and ('channel_data' in changed_parts or 'selected_channel_element_index' in changed_parts or 'selected_frequency' in changed_parts):
                # 获取参数中选中的阵元和频率
                params = self.data_manager.get_parameters('integrated')
                selected_channel_element_index = params.get('selected_channel_element_index', 0)
                selected_frequency = params.get('selected_frequency', 0)

                # 使用辅助方法更新到达结构图
                self._update_arrivals_plot(selected_channel_element_index, selected_frequency)

        # 检查是否有信号处理结果，且信号处理结果发生了变化
        if 'signal_processing' in results and ('signal_processing' in changed_parts or 'received_signals' in changed_parts or 'selected_signal_element_index' in changed_parts):
            signal_processing = results['signal_processing']

            # 更新接收信号时域波形
            if hasattr(self.view, 'update_received_signal_plot'):
                # 获取参数中选中的阵元
                params = self.data_manager.get_parameters('integrated')
                selected_signal_element_index = params.get('selected_signal_element_index', 0)

                # 获取接收信号数据
                received_signals = signal_processing.get('received_signals', {})

                # 检查选中的阵元是否在数据中
                if selected_signal_element_index in received_signals:
                    # 使用辅助方法更新接收信号时域波形
                    self._update_received_signal_plot(selected_signal_element_index)
                else:
                    # 如果选中的阵元不在数据中，但有其他阵元数据，使用第一个可用的阵元
                    if received_signals:
                        # 获取第一个可用的阵元ID
                        first_element_id = list(received_signals.keys())[0]

                        # 更新接收信号时域波形，并显示警告信息
                        print(f"警告: 选中的阵元 #{selected_signal_element_index} 不在数据中，使用阵元 #{first_element_id} 代替")

                        # 使用辅助方法更新接收信号时域波形
                        self._update_received_signal_plot(first_element_id)

                        # 更新参数中的选中阵元
                        self.data_manager.update_parameter('integrated', 'selected_signal_element_index', first_element_id)

            # 不再需要更新信号处理信息表格，因为我们已经简化了视图

            # 检查是否有频谱计算结果，且频谱计算结果发生了变化
            if 'spectrum' in signal_processing and ('spectrum' in changed_parts or 'selected_spectrum_element_index' in changed_parts):
                # 获取参数中选中的阵元
                params = self.data_manager.get_parameters('integrated')
                selected_spectrum_element_index = params.get('selected_spectrum_element_index', 0)

                # 使用辅助方法更新频谱图
                self._update_spectrum_plot(selected_spectrum_element_index)

        # 检查是否有阵列处理结果，且阵列处理结果发生了变化
        if 'array_processing' in results and ('array_processing' in changed_parts or 'computation_type' in changed_parts):
            array_processing = results['array_processing']

            # 检查是否有指向性结果
            if 'directivity' in array_processing:
                print("视图更新器: 发现指向性结果")
                directivity = array_processing['directivity']
                angles = np.array(directivity.get('angles', []))
                pattern = np.array(directivity.get('pattern', []))  # 这里获取的是原始的平均功率数据
                params = directivity.get('params', {})

                print(f"视图更新器: 指向性数据 - 角度点数: {len(angles)}, 波束方向图点数: {len(pattern)}")
                if len(pattern) > 0:
                    print(f"视图更新器: 波束方向图值范围: [{np.min(pattern)}, {np.max(pattern)}]")

                # 将原始的平均功率数据转换为归一化的dB指向性图
                if len(pattern) > 0:
                    # 确保所有值都是正的
                    pattern_safe = np.where(pattern > 0, pattern, 1e-10)

                    # 转换为dB
                    pattern_db = 10 * np.log10(pattern_safe)

                    # 归一化
                    if np.any(np.isfinite(pattern_db)):
                        max_db = np.max(pattern_db[np.isfinite(pattern_db)])
                        pattern_db = pattern_db - max_db

                    # 替换任何NaN或无穷大值
                    pattern_db = np.where(np.isfinite(pattern_db), pattern_db, -100)

                    print(f"视图更新器: 将原始功率数据转换为dB并归一化，范围: [{np.min(pattern_db)}, {np.max(pattern_db)}] dB")
                else:
                    pattern_db = np.array([])
                    print("视图更新器: 指向性数据为空，无法转换为dB")

                # 更新指向性图表
                if hasattr(self.view, 'update_directivity_plot'):
                    print("视图更新器: 调用update_directivity_plot方法")
                    self.view.update_directivity_plot(angles, pattern_db, params)
                else:
                    print("视图更新器: 视图对象没有update_directivity_plot方法")

            # 检查是否有互功率谱结果
            if 'cross_spectral_density' in array_processing:
                print("视图更新器: 发现互功率谱结果")
                csd_result = array_processing['cross_spectral_density']
                element_pair = csd_result.get('element_pair', [0, 0])
                freqs = np.array(csd_result.get('freqs', []))

                # 优先使用dB形式的模值，如果没有则使用线性模值
                if 'csd_magnitude_db' in csd_result:
                    csd_magnitude = np.array(csd_result.get('csd_magnitude_db', []))
                    print(f"视图更新器: 使用dB形式的互功率谱密度模值，范围: [{np.min(csd_magnitude)}, {np.max(csd_magnitude)}] dB")
                else:
                    # 如果没有dB形式的模值，使用线性模值并转换为dB
                    csd_magnitude_linear = np.array(csd_result.get('csd_magnitude', []))
                    csd_magnitude = 10 * np.log10(np.where(csd_magnitude_linear > 0, csd_magnitude_linear, 1e-10))
                    print(f"视图更新器: 将线性互功率谱密度模值转换为dB，范围: [{np.min(csd_magnitude)}, {np.max(csd_magnitude)}] dB")

                csd_phase_unwrapped = np.array(csd_result.get('csd_phase_unwrapped', []))
                coherence = np.array(csd_result.get('coherence', []))
                params = csd_result.get('params', {})

                print(f"视图更新器: 互功率谱数据 - 频率点数: {len(freqs)}, 模值点数: {len(csd_magnitude)}, 相位点数: {len(csd_phase_unwrapped)}, 相干函数点数: {len(coherence)}")

                # 更新互功率谱图表
                if hasattr(self.view, 'update_cross_spectrum_plot'):
                    print("视图更新器: 调用update_cross_spectrum_plot方法")
                    self.view.update_cross_spectrum_plot(
                        freqs=freqs,
                        csd_magnitude=csd_magnitude,
                        csd_phase_unwrapped=csd_phase_unwrapped,
                        coherence=coherence if len(coherence) > 0 else None,
                        element_pair=element_pair,
                        params=params
                    )
                else:
                    print("视图更新器: 视图对象没有update_cross_spectrum_plot方法")

    def reset_all_views(self):
        """
        重置所有视图到初始状态

        在新建项目或结果被清空时调用，使用空数据更新而不是清空重绘
        """
        print("综合仿真视图更新器: 重置所有视图")

        # 清空信道数据信息表格
        if hasattr(self.view, 'clear_channel_data_info'):
            self.view.clear_channel_data_info()

        # 使用空数据更新图表，而不是清空重绘
        self.reset_plots_with_empty_data()

        # 重置上一次的结果记录
        self._last_results = {}
        self._last_params = {}

    def reset_plots_with_empty_data(self):
        """
        使用空数据重置图表，避免清空重绘的性能开销
        """
        import numpy as np

        # 创建空数据
        empty_time = np.array([0, 1])
        empty_signal = np.zeros_like(empty_time)
        empty_freq = np.array([0, 1])
        empty_psd = np.zeros_like(empty_freq)

        # 使用空数据更新各个图表
        try:
            # 更新到达结构图
            if hasattr(self.view, 'update_arrivals_plot'):
                self.view.update_arrivals_plot([], [])  # 空的到达数据

            # 更新时域信号图
            if hasattr(self.view, 'update_time_domain_plot'):
                self.view.update_time_domain_plot(empty_time, empty_signal, 0)

            # 更新频域图
            if hasattr(self.view, 'update_freq_domain_plot'):
                self.view.update_freq_domain_plot(empty_freq, empty_psd, 0)

            # 更新指向性图
            if hasattr(self.view, 'update_directivity_plot'):
                angles = np.linspace(-90, 90, 181)
                empty_directivity = np.zeros_like(angles)
                self.view.update_directivity_plot(angles, empty_directivity, 1000)

            # 更新互功率谱图
            if hasattr(self.view, 'update_cross_spectrum_plot'):
                empty_magnitude = np.zeros_like(empty_freq)
                empty_phase = np.zeros_like(empty_freq)
                empty_coherence = np.zeros_like(empty_freq)
                self.view.update_cross_spectrum_plot(
                    empty_freq, empty_magnitude, empty_phase, empty_coherence, 0, 1
                )

        except Exception as e:
            print(f"使用空数据重置图表时出错: {e}")
            # 如果空数据更新失败，回退到清空方法
            if hasattr(self.view, 'clear_all_plots_and_redraw'):
                self.view.clear_all_plots_and_redraw()
