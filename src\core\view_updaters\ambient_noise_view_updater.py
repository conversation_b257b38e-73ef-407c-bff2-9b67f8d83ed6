# -*- coding: utf-8 -*-
"""
海洋环境噪声视图更新器

负责更新海洋环境噪声视图，将数据管理器中的数据更新到视图中。
"""

from PyQt5.QtCore import QObject


class AmbientNoiseViewUpdater(QObject):
    """
    海洋环境噪声视图更新器

    负责更新海洋环境噪声视图，将数据管理器中的数据更新到视图中。
    """

    def __init__(self, data_manager, ambient_noise_view):
        """
        初始化海洋环境噪声视图更新器

        Args:
            data_manager: 数据管理器实例
            ambient_noise_view: 海洋环境噪声视图实例
        """
        super().__init__()

        # 保存数据管理器和视图引用
        self.data_manager = data_manager
        self.ambient_noise_view = ambient_noise_view

        # 连接数据管理器的信号
        self.data_manager.parameters_changed.connect(self.on_parameters_changed)
        self.data_manager.results_changed.connect(self.on_results_changed)

    def on_parameters_changed(self, module):
        """
        参数变更事件处理

        Args:
            module (str): 模块名称
        """
        # 只处理海洋环境噪声模块的参数变更
        if module != 'ambient_noise':
            return

        # 获取参数
        params = self.data_manager.get_parameters('ambient_noise')

        # 更新用户定义的点
        user_points = params.get('user_defined_points', [])
        self.ambient_noise_view.set_user_points(user_points)

        # 更新外插点
        extrapolated_points = params.get('extrapolated_points', [])
        self.ambient_noise_view.update_extrapolated_curve(extrapolated_points)

    def on_results_changed(self, module):
        """
        结果变更事件处理

        Args:
            module (str): 模块名称
        """
        # 只处理海洋环境噪声模块的结果变更
        if module != 'ambient_noise':
            return

        # 获取结果
        results = self.data_manager.get_results('ambient_noise')

        # 检查结果是否存在
        if not results:
            # 如果结果为空，重置仿真结果图表
            self.reset_simulation_plots()
            return

        # 获取时间数组和信号
        time_data = results.get('time_data')
        ambient_signal = results.get('ambient_signal')

        # 获取频率和功率谱密度
        ambient_freqs = results.get('ambient_freqs')
        ambient_psd_db = results.get('ambient_psd_db')

        # 检查数据是否完整
        if time_data is None or ambient_signal is None or ambient_freqs is None or ambient_psd_db is None:
            # 如果数据不完整，重置仿真结果图表
            self.reset_simulation_plots()
            return

        # 更新信号图表
        self.ambient_noise_view.update_signal_plot(time_data, ambient_signal, ambient_freqs, ambient_psd_db)

    def update_view(self):
        """
        更新视图

        手动触发视图更新，用于初始化或重置视图
        """
        # 更新参数
        self.on_parameters_changed('ambient_noise')

        # 更新结果
        self.on_results_changed('ambient_noise')

    def reset_simulation_plots(self):
        """
        重置仿真结果图表

        当仿真结果为空或不完整时调用
        """
        # 调用视图的重置方法
        self.ambient_noise_view.reset_simulation_plots()