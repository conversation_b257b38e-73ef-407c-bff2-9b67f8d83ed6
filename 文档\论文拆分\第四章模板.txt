4   XXX系统的设计
XXXXXXXXXXXXXXXXXXXXXXXXXX（章导引段）。
4.1 XXX系统的概要设计
该章论述XXX系统的设计。篇幅要求：8到12页，最好为10页。
一般地，设计划分为两个阶段：概要设计与详细设计。概要设计主要论述XXX系统的软件体系结构和功能模块结构。详细设计则主要论述XXX系统的类设计（含关键算法设计）、数据库设计等。
4.1.1 XXX系统的软件体系结构
设计XXX系统的软件体系结构时，不要大篇幅地描述已有的软件体系结构方面的理论，而应结合XXX系统的功能性需求遴选合适的体系结构风格或者组合几种简单的体系结构风格。当然，确需设计全新的软件体系结构时，也应明确设计原则。
如3.3节所述，设计XXX系统的软件体系结构时，往往还必须考虑某些非功能需求。此时，应彰显这种呼应关系。
……
4.1.2 XXX系统的功能模块结构
在该小节中，使用树形结构描述各个功能模块之间的层次关系。
当采用树形结构描述功能模块结构时，不要在相邻层的连线上使用任何形状的箭头，因为箭头没有任何实质性的语义。
……
4.2 XXX系统的详细设计
所谓XXX系统的详细设计，是指考虑了计算机空间（或解空间）的理论和技术之后对分析模型的细化或精化。相应地，分析阶段所获得的功能模型、静态模型和动态模型都会被进一步诠释。
相对而言，分析阶段功能模型的进一步诠释在概要设计阶段已基本完成。因此，该节应主要诠释分析阶段的静态模型和动态模型。
4.2.1 XXX模块的详细设计
注意：静态模型的核心是类图，分析阶段的类图仅包括问题空间的类和类间关系，而设计阶段的类图将加入更多的解空间类和相应的类间关系。与此同时，设计阶段的类设计还应完善问题空间类的属性和操作。
分析阶段的动态模型一般基于活动图进行表达，亦可基于顺序图进行表达。无论分析阶段动态模型模型采取哪种表达，设计阶段都应把参与动态交互的对象分为三类：实体对象、控制对象、界面对象。
顺序图适合描述解空间中多个对象之间的消息交互序列，反映的是事件驱动风格的算法过程。典型地，一个顺序图只描述单个主要操作的算法。顺序图通过每名参加者下方的垂线（即生命线）以及各个消息依次向下的顺序来描述交互，顺序图中的参加者往往就是对象。顺序图的数量要求：至少3幅，至多5幅。顺序图描述的篇幅要求：4页足矣！
关于顺序图的注意事项，请对照图4-1中的顺序图示例。
 
图4 1  顺序图示例
1）各个参与者的名字最好有下划线，以表明它是对象实例。而且，应与设计类图中的相应类名有显著的对应关系。
2）各个参与者下方的生命线均必须为虚线。
3）最左边参与者的生命线上不能出现向左的消息传递。
4）表示“请求”的消息传递应采用带实心箭头的实线，表示同步语义。
5）表示“完成/返回”的消息传递应采用带开放箭头的虚线，表示异步语义。
6）各个参与者之间的消息传递应有语义标注，且应与设计类图中的相应操作名对应。具体地，哪个参与者接收消息，就用它所对应的类上的合适操作名标注消息。
7）在各个参与者的生命线上，激活条的开始与结束位置应与“请求”与“完成/返回”消息的箭头位置对齐，不可错位。
8）顺序图周围不要画任何边框。
类设计的核心任务是为若干关键类的主要操作设计具体的算法，包括数据结构的设计。注意：应提供算法的伪码描述。
在论述关键算法设计时，应配合使用UML中的状态图、顺序图和活动图等。状态图适合描述单个对象的生命周期，顺序图适合描述多个对象之间的消息传递序列，活动图适合描述多个对象或多个功能模块之间的交互或依赖。
关于状态机图的注意事项，请对照图4-2中的状态图示例。
1）状态机图的“初始伪态”与“终态”节点必须使用各自的图符，即实心圆与牛眼图符。
2）状态节点之间的转换连线必须使用带开放箭头的实线。
3）转换标注必须是正体，若有警戒条件则必须置于中括号内。
4）状态机图周围不要画任何边框。
 
图4 2  状态图示例
注意：仅仅给出图描述远远不够，必须给出相应的文字段。
4.2.2 XXX模块的详细设计
……
4.2.3 XXX模块的详细设计
……
4.2.4 XXX模块的详细设计
……
4.2.5 XXX模块的详细设计（可选）
……
