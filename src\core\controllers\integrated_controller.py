# -*- coding: utf-8 -*-
"""
综合仿真控制器

负责执行综合仿真任务，包括信道数据读取、信号处理等。
"""

import os
import json
import time
import numpy as np
import pandas as pd
from PyQt5.QtCore import QThread, pyqtSignal
from scipy import signal as sig
import concurrent.futures
import multiprocessing

from src.models.integrated_simulation import IntegratedSimulation
import numpy as np
from scipy import signal as sig

# 定义模块级别的函数，用于ProcessPoolExecutor
def generate_ambient_noise_for_element_wrapper(args):
    """
    为单个阵元生成独立的海洋环境噪声并叠加

    Args:
        args: 包含所有参数的元组 (element_id, signal_length, element_duration, fs, fir_coeffs, scaling_factor)

    Returns:
        dict: 包含处理结果的字典
    """
    element_id, signal_length, element_duration, fs, fir_coeffs, scaling_factor = args

    # 获取滤波器阶数
    filter_order = len(fir_coeffs)

    # 计算需要的样本数
    num_samples = int(fs * element_duration)

    # 生成高斯白噪声 - 为每个阵元生成独立的噪声
    # 使用简单的随机数种子，确保每个阵元的噪声不同
    # 确保种子在有效范围内 (0 到 2**32 - 1)
    seed = (int(time.time() * 1000) + element_id) % (2**32 - 1)
    rng = np.random.RandomState(seed)  # 使用RandomState而不是全局种子
    white_noise = rng.randn(num_samples + filter_order - 1)

    # 应用滤波器
    filtered_signal = sig.lfilter(fir_coeffs, 1.0, white_noise)

    # 去除滤波器延迟
    filtered_signal = filtered_signal[filter_order-1:]

    # 截取所需长度
    ambient_signal = filtered_signal[:num_samples]

    # 应用缩放因子
    if scaling_factor is not None:
        ambient_signal = ambient_signal * scaling_factor

    # 确保噪声信号长度与接收信号相同
    if len(ambient_signal) > signal_length:
        # 如果噪声信号较长，截取
        ambient_signal = ambient_signal[:signal_length]
    elif len(ambient_signal) < signal_length:
        # 如果噪声信号较短，补零
        padding = np.zeros(signal_length - len(ambient_signal))
        ambient_signal = np.concatenate([ambient_signal, padding])

    # 只返回必要的数据，减少进程间通信开销
    return {
        'element_id': element_id,
        'ambient_noise': ambient_signal
    }


class IntegratedController(QThread):
    """
    综合仿真控制器

    负责执行综合仿真任务，在后台线程中运行以保持UI响应性。
    """

    # 信号定义
    simulation_started = pyqtSignal(str)  # 仿真开始信号，参数为模块名称
    simulation_progress = pyqtSignal(str, int)  # 仿真进度信号，参数为模块名称和进度百分比
    simulation_completed = pyqtSignal(str)  # 仿真完成信号，参数为模块名称
    simulation_error = pyqtSignal(str, str)  # 仿真错误信号，参数为模块名称和错误信息

    def __init__(self, data_manager):
        """
        初始化综合仿真控制器

        Args:
            data_manager: 数据管理器实例
        """
        super().__init__()

        # 保存数据管理器引用
        self.data_manager = data_manager

        # 仿真状态
        self.is_running = False
        self.is_cancelled = False

    def run(self):
        """
        线程执行函数，执行综合仿真
        """
        try:
            # 设置运行状态
            self.is_running = True
            self.is_cancelled = False

            # 设置仿真状态并发送仿真开始信号
            self.data_manager.set_module_simulating('integrated', True)
            self.simulation_started.emit('integrated')
            self.simulation_progress.emit('integrated', 0)

            # 从数据管理器获取参数
            params = self.data_manager.get_parameters('integrated')

            # 获取计算类型
            computation_type = params.get('computation_type', 'channel_data_analysis')

            # 根据计算类型执行不同的计算
            if computation_type == 'channel_data_analysis':
                self._analyze_channel_data(params)
            elif computation_type == 'signal_processing':
                self._process_signal(params)
            elif computation_type == 'directivity_calculation':
                self.calculate_directivity(params)
            elif computation_type == 'cross_spectral_density':
                # 获取阵元对
                element_pair = params.get('element_pair', [0, 0])
                start_time = params.get('start_time', 0.0)
                end_time = params.get('end_time', 0.0)
                show_coherence = params.get('show_coherence', False)

                # 创建参数字典
                csd_params = {
                    'element_pair': element_pair,
                    'start_time': start_time,
                    'end_time': end_time,
                    'show_coherence': show_coherence
                }

                # 计算互功率谱
                success = self.calculate_cross_spectral_density(csd_params)

                if not success:
                    self.simulation_error.emit('integrated', "计算互功率谱失败")
                    self.is_running = False
                    return
            else:
                self.simulation_error.emit('integrated', f"未知的计算类型: {computation_type}")
                self.is_running = False
                return

            # 发送仿真完成信号
            self.simulation_completed.emit('integrated')

        except Exception as e:
            # 发送错误信号，包含计算类型信息
            try:
                params = self.data_manager.get_parameters('integrated')
                computation_type = params.get('computation_type', 'unknown')
                error_msg = f"计算类型: {computation_type}, 错误: {str(e)}"
            except:
                error_msg = str(e)

            self.simulation_error.emit('integrated', error_msg)
        finally:
            # 重置运行状态和仿真状态
            self.is_running = False
            self.data_manager.set_module_simulating('integrated', False)

    def _analyze_channel_data(self, params):
        """
        分析信道数据

        Args:
            params (dict): 参数字典
        """
        # 获取信道数据目录
        channel_data_dir = params.get('channel_data_dir', '')
        if not channel_data_dir or not os.path.exists(channel_data_dir):
            self.simulation_error.emit('integrated', f"信道数据目录不存在: {channel_data_dir}")
            return

        # 发送进度信号
        self.simulation_progress.emit('integrated', 10)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 读取meta.json文件
        meta_file = os.path.join(channel_data_dir, 'meta.json')
        if not os.path.exists(meta_file):
            self.simulation_error.emit('integrated', f"meta.json文件不存在: {meta_file}")
            return

        try:
            with open(meta_file, 'r') as f:
                meta_data = json.load(f)
        except Exception as e:
            self.simulation_error.emit('integrated', f"读取meta.json文件失败: {e}")
            return

        # 发送进度信号
        self.simulation_progress.emit('integrated', 20)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 获取频率列表和阵元位置
        frequencies = meta_data.get('frequencies', [])
        array_elements = meta_data.get('array_elements', [])

        if not frequencies:
            self.simulation_error.emit('integrated', "meta.json中未找到频率列表")
            return

        if not array_elements:
            self.simulation_error.emit('integrated', "meta.json中未找到阵元位置数据")
            return

        # 发送进度信号
        self.simulation_progress.emit('integrated', 30)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 读取所有CSV文件并解析到达结构数据
        arrivals_data = {}
        t_first_global = float('inf')  # 全局最早到达时间
        t_last_global = 0.0  # 全局最晚到达时间

        # 发送进度信号
        self.simulation_progress.emit('integrated', 40)

        # 计算总文件数用于进度计算
        csv_files = [f for f in os.listdir(channel_data_dir) if f.endswith('.csv')]
        total_files = len(csv_files)
        files_processed = 0

        for freq in frequencies:
            # 构建CSV文件名
            csv_filename = f"fc_{int(freq)}Hz.csv"
            csv_path = os.path.join(channel_data_dir, csv_filename)

            if not os.path.exists(csv_path):
                print(f"警告: 频率 {freq} Hz 的CSV文件不存在: {csv_path}")
                continue

            try:
                # 读取CSV文件
                arrivals_df = pd.read_csv(csv_path)

                # 根据提供的CSV格式调整列名映射
                # 检查必要的列是否存在
                required_columns = ['time_of_arrival', 'arrival_amplitude']
                if not all(col in arrivals_df.columns for col in required_columns):
                    print(f"警告: CSV文件 {csv_filename} 缺少必要的列: {required_columns}")
                    print(f"可用列: {arrivals_df.columns.tolist()}")
                    continue

                # 将复数振幅从字符串转换为复数
                if 'arrival_amplitude' in arrivals_df.columns:
                    # 检查是否已经是复数类型
                    if not np.issubdtype(arrivals_df['arrival_amplitude'].dtype, np.complexfloating):
                        # 尝试将字符串转换为复数
                        try:
                            arrivals_df['arrival_amplitude'] = arrivals_df['arrival_amplitude'].apply(
                                lambda x: complex(x.replace('(', '').replace(')', ''))
                                if isinstance(x, str) else x
                            )
                        except Exception as e:
                            print(f"警告: 无法将振幅转换为复数: {e}")

                # 根据阵列类型使用不同的阵元ID
                # 检查元数据中的阵列类型
                array_type = meta_data.get('array_type', 'vertical')

                if 'rx_range_ndx' in arrivals_df.columns and 'rx_depth_ndx' in arrivals_df.columns:
                    # 创建阵元ID列
                    if array_type.lower() == 'horizontal':
                        # 水平线列阵，使用rx_range_ndx作为阵元ID
                        arrivals_df['receiver_id'] = arrivals_df.apply(
                            lambda row: int(row['rx_range_ndx']), axis=1
                        )
                    else:
                        # 垂直线列阵或其他类型，使用rx_depth_ndx作为阵元ID
                        arrivals_df['receiver_id'] = arrivals_df.apply(
                            lambda row: int(row['rx_depth_ndx']), axis=1
                        )

                    # 按阵元ID组织数据
                    for rx_id in arrivals_df['receiver_id'].unique():
                        rx_arrivals = arrivals_df[arrivals_df['receiver_id'] == rx_id]

                        # 更新全局最早和最晚到达时间
                        if not rx_arrivals.empty:
                            # 使用time_of_arrival列作为到达时间
                            t_first = rx_arrivals['time_of_arrival'].min()
                            t_last = rx_arrivals['time_of_arrival'].max()

                            t_first_global = min(t_first_global, t_first)
                            t_last_global = max(t_last_global, t_last)

                            # 存储到达结构数据
                            if freq not in arrivals_data:
                                arrivals_data[freq] = {}
                            arrivals_data[freq][rx_id] = rx_arrivals
                else:
                    # 如果没有rx_range_ndx和rx_depth_ndx列，使用其他方式组织数据
                    print(f"警告: CSV文件 {csv_filename} 没有rx_range_ndx和rx_depth_ndx列，使用默认阵元ID")

                    # 假设只有一个阵元，使用0作为阵元ID
                    rx_id = 0

                    # 更新全局最早和最晚到达时间
                    if not arrivals_df.empty:
                        t_first = arrivals_df['time_of_arrival'].min()
                        t_last = arrivals_df['time_of_arrival'].max()

                        t_first_global = min(t_first_global, t_first)
                        t_last_global = max(t_last_global, t_last)

                        # 存储到达结构数据
                        if freq not in arrivals_data:
                            arrivals_data[freq] = {}
                        arrivals_data[freq][rx_id] = arrivals_df

            except Exception as e:
                print(f"读取CSV文件 {csv_filename} 时出错: {e}")

            # 更新进度
            files_processed += 1
            progress = 40 + int(50 * files_processed / total_files)
            self.simulation_progress.emit('integrated', progress)

            # 检查是否取消
            if self.is_cancelled:
                return

        # 如果没有找到任何到达时间，设置默认值
        if t_first_global == float('inf'):
            t_first_global = 0.0

        # 发送进度信号
        self.simulation_progress.emit('integrated', 90)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 计算最大相对时延
        max_relative_delay = t_last_global - t_first_global

        # 计算建议的源信号时长
        min_duration = np.ceil(max_relative_delay + 1.0)
        recommended_duration = np.ceil(max_relative_delay + 5.0)

        # 更新参数
        self.data_manager.update_parameter('integrated', 'min_duration_suggestion', min_duration)
        self.data_manager.update_parameter('integrated', 'recommended_duration', recommended_duration)

        # 预计算不同截断时间对应的建议时长
        print("开始预计算建议时长阈值...")
        start_time = time.time()

        # 收集所有到达时间并排序
        all_arrival_times = set()
        for freq, elements in arrivals_data.items():
            for element_id, df in elements.items():
                if 'time_of_arrival' in df.columns:
                    all_arrival_times.update(df['time_of_arrival'].values)

        sorted_arrival_times = sorted(all_arrival_times)
        print(f"收集到 {len(sorted_arrival_times)} 个不同的到达时间点")

        # 对于每个整数秒的建议时长，找出对应的截断时间阈值
        duration_to_truncation_threshold = {}

        # 计算最大可能的建议时长（向上取整）
        max_min_duration = int(np.ceil(max_relative_delay + 1.0))

        # 对于每个可能的建议时长值
        for duration in range(1, max_min_duration + 1):
            # 计算对应的相对时延阈值
            # 对于公式 min_duration = np.ceil(max_time - t_first_global + 1.0)
            # 当 duration = n 时，需要 max_time - t_first_global + 1.0 > n-1
            # 即 max_time > t_first_global + n - 2
            threshold = t_first_global + duration - 2

            # 找出大于阈值的最小到达时间
            next_times = [t for t in sorted_arrival_times if t > threshold]

            if next_times:
                # 这个时间点是使建议时长变为duration的最小截断时间
                min_truncation_time = next_times[0]
                duration_to_truncation_threshold[duration] = min_truncation_time
                print(f"建议时长 {duration}s 的最小截断时间: {min_truncation_time:.4f}s")

        end_time = time.time()
        print(f"预计算建议时长阈值完成，耗时: {end_time - start_time:.4f}s")

        # 准备结果数据
        channel_data_results = {
            'meta': meta_data,
            'frequencies': frequencies,
            'array_elements': array_elements,
            't_first_global': t_first_global,
            't_last_global': t_last_global,
            'max_relative_delay': max_relative_delay,
            'arrivals_data': arrivals_data,
            'duration_to_truncation_threshold': duration_to_truncation_threshold
        }

        # 获取当前结果
        try:
            current_results = self.data_manager.get_results('integrated')
        except Exception as e:
            print(f"获取结果失败: {e}")
            current_results = {}

        # 更新结果字典，添加信道数据分析相关的结果
        current_results['channel_data'] = channel_data_results

        # 将更新后的结果存储到数据管理器
        try:
            self.data_manager.set_results('integrated', current_results)
        except Exception as e:
            print(f"设置结果失败: {e}")
            self.simulation_error.emit('integrated', f"设置结果失败: {e}")
            return

        # 发送进度信号
        self.simulation_progress.emit('integrated', 100)

    def cancel(self):
        """
        取消仿真
        """
        if self.is_running:
            self.is_cancelled = True

    def is_simulation_running(self):
        """
        检查仿真是否正在运行

        Returns:
            bool: 仿真是否正在运行
        """
        return self.is_running

    def _process_signal(self, params):
        """
        处理信号

        Args:
            params (dict): 参数字典
        """
        # 获取信道数据目录
        channel_data_dir = params.get('channel_data_dir', '')
        if not channel_data_dir or not os.path.exists(channel_data_dir):
            self.simulation_error.emit('integrated', f"信道数据目录不存在: {channel_data_dir}")
            return

        # 发送进度信号
        self.simulation_progress.emit('integrated', 10)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 获取全局参数
        try:
            fs = self.data_manager.get_global_param('fs')
        except Exception as e:
            self.simulation_error.emit('integrated', f"获取全局参数失败: {e}")
            return

        # 创建综合仿真模型
        model = IntegratedSimulation(fs)

        # 从数据管理器获取信道数据
        try:
            channel_data = self.data_manager.get_results('integrated').get('channel_data', {})
            if not channel_data:
                self.simulation_error.emit('integrated', "未找到信道数据")
                return

            # 设置信道数据
            if not model.set_channel_data(channel_data):
                self.simulation_error.emit('integrated', "设置信道数据失败")
                return
        except Exception as e:
            self.simulation_error.emit('integrated', f"获取信道数据失败: {e}")
            return

        # 发送进度信号
        self.simulation_progress.emit('integrated', 20)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 设置截断参数
        use_all_arrivals = params.get('use_all_arrivals', True)
        truncation_time = params.get('truncation_time', 0.0)
        model.set_truncation_parameters(use_all_arrivals, truncation_time)

        # 获取源信号 - 不再要求用户选择，直接使用船舶辐射噪声结果
        # 只是保留参数以便记录

        # 从数据管理器获取船舶辐射噪声结果
        try:
            ship_noise_results = self.data_manager.get_results('ship_noise')
            if not ship_noise_results:
                self.simulation_error.emit('integrated', "未找到船舶辐射噪声结果")
                return

            # 获取源信号数据
            source_signal = ship_noise_results.get('total_signal')
            if source_signal is None:
                self.simulation_error.emit('integrated', "未找到船舶辐射噪声总信号")
                return

            # 获取源信号元数据
            metadata = ship_noise_results.get('metadata', {})
            if not metadata:
                self.simulation_error.emit('integrated', "未找到船舶辐射噪声元数据")
                return

            # 设置源信号
            model.set_source_signal(source_signal, metadata)
        except Exception as e:
            self.simulation_error.emit('integrated', f"获取船舶辐射噪声结果失败: {e}")
            return

        # 发送进度信号
        self.simulation_progress.emit('integrated', 30)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 获取源信号时长建议
        min_duration, recommended_duration = model.get_duration_suggestions()

        # 更新参数
        self.data_manager.update_parameter('integrated', 'min_duration_suggestion', min_duration)
        self.data_manager.update_parameter('integrated', 'recommended_duration', recommended_duration)

        # 检查源信号时长是否满足要求
        source_duration = metadata.get('duration', 0.0)
        if source_duration < min_duration:
            # 这里只是警告，不中断处理
            print(f"警告: 源信号时长 ({source_duration} s) 小于建议的最小时长 ({min_duration} s)")

        # 发送进度信号
        self.simulation_progress.emit('integrated', 40)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 创建进度回调函数
        def progress_callback(progress):
            # 将进度从0-1映射到40-80
            mapped_progress = 40 + int(progress * 40)
            self.simulation_progress.emit('integrated', mapped_progress)

            # 检查是否取消
            return not self.is_cancelled

        # 处理信号
        results = model.process_signal(progress_callback=progress_callback)
        if results is None:
            self.simulation_error.emit('integrated', "处理信号失败")
            return

        # 发送进度信号
        self.simulation_progress.emit('integrated', 80)

        # 检查是否取消
        if self.is_cancelled:
            return

        # 检查是否需要叠加海洋环境噪声
        add_ambient_noise = params.get('add_ambient_noise', False)
        if add_ambient_noise:
            # 发送进度信号
            self.simulation_progress.emit('integrated', 85)

            try:
                # 获取海洋环境噪声结果
                ambient_noise_results = self.data_manager.get_results('ambient_noise')
                if ambient_noise_results and 'ambient_signal' in ambient_noise_results:
                    # 获取海洋环境噪声模型
                    from src.models.noise_sources.ocean_ambient import OceanAmbientNoise
                    ambient_model = OceanAmbientNoise()

                    # 获取海洋环境噪声参数
                    ambient_params = self.data_manager.get_parameters('ambient_noise')

                    # 设置仿真参数 - 使用相对时间轴，计算实际需要的持续时间
                    # 获取接收信号的相对时间范围
                    t_min = float('inf')
                    t_max = float('-inf')

                    for element_id, received_signal_data in results['received_signals'].items():
                        time_data = received_signal_data['time_data']
                        t_min = min(t_min, time_data[0])
                        t_max = max(t_max, time_data[-1])

                    # 计算实际需要的持续时间
                    actual_duration = t_max - t_min
                    print(f"接收信号时间范围: [{t_min:.4f}, {t_max:.4f}] 秒，持续时间: {actual_duration:.4f} 秒")

                    # 尝试从海洋环境噪声结果中获取滤波器系数和缩放因子
                    if 'fir_coeffs' in ambient_noise_results and 'scaling_factor' in ambient_noise_results:
                        # 直接使用保存的滤波器系数和缩放因子
                        fir_coeffs = ambient_noise_results['fir_coeffs']
                        scaling_factor = ambient_noise_results['scaling_factor']
                        print(f"从海洋环境噪声结果中获取滤波器系数，长度: {len(fir_coeffs)}，缩放因子: {scaling_factor}")

                        # 获取滤波器阶数
                        filter_order = len(fir_coeffs)
                        print(f"滤波器阶数: {filter_order}")
                    else:
                        # 如果没有保存滤波器系数和缩放因子，则重新设计滤波器
                        print("未找到保存的滤波器系数和缩放因子，将重新设计滤波器...")

                        # 尝试从海洋环境噪声结果的元数据中获取滤波器阶数
                        filter_order = 16385  # 默认值

                        # 首先尝试从结果元数据中获取
                        if 'metadata' in ambient_noise_results:
                            metadata = ambient_noise_results['metadata']
                            if 'filter_order' in metadata:
                                filter_order = metadata['filter_order']
                                print(f"从海洋环境噪声结果元数据中获取滤波器阶数: {filter_order}")

                        # 如果元数据中没有，再尝试从参数中获取
                        if filter_order == 16385 and 'filter_order' in ambient_params:
                            filter_order = ambient_params.get('filter_order')
                            print(f"从海洋环境噪声参数中获取滤波器阶数: {filter_order}")

                        print(f"最终使用的滤波器阶数: {filter_order}")

                        # 使用前面已经计算好的实际持续时间

                        # 设置仿真参数
                        ambient_model.set_simulation_params(fs, actual_duration, filter_order)

                        # 打印调试信息
                        print(f"海洋环境噪声模型参数: fs={fs}, duration={actual_duration}, filter_order={filter_order}")

                        # 加载用户定义的点
                        user_defined_points = ambient_params.get('user_defined_points', [])
                        for freq, level in user_defined_points:
                            ambient_model.add_point(freq, level)

                        # 加载外插点
                        extrapolated_points = ambient_params.get('extrapolated_points', [])
                        for freq, level in extrapolated_points:
                            ambient_model.add_extrapolated_point(freq, level)

                        # 创建共享的FIR滤波器和缩放因子，避免重复计算
                        print("设计FIR滤波器...")
                        start_time = time.time()
                        fir_coeffs = ambient_model.design_filter()
                        scaling_factor = ambient_model.get_scaling_factor()
                        print(f"FIR滤波器设计完成，耗时: {time.time() - start_time:.4f}秒")

                    # 获取CPU核心数
                    try:
                        cpu_cores = self.data_manager.get_global_param('cpu_cores')
                    except KeyError:
                        # 如果全局参数中没有cpu_cores，使用系统CPU核心数
                        cpu_cores = multiprocessing.cpu_count()
                        # 设置全局参数，以便下次使用
                        self.data_manager.set_global_param('cpu_cores', cpu_cores)

                    # 为每个阵元生成独立的海洋环境噪声
                    print(f"准备为每个阵元生成独立的海洋环境噪声，持续时间: {actual_duration:.4f} 秒...")

                    # 打印各阵元时间轴的起始和结束时间，检查是否一致
                    print("\n各阵元时间轴信息:")
                    for element_id, received_signal_data in results['received_signals'].items():
                        time_data = received_signal_data['time_data']
                        print(f"阵元 {element_id}: 起始时间={time_data[0]:.6f}, 结束时间={time_data[-1]:.6f}, 长度={len(time_data)}")

                    # 开始计时
                    start_time = time.time()

                    # 获取所有需要处理的阵元
                    elements_to_process = list(results['received_signals'].items())

                    # 准备进程池的输入参数 - 只传递必要的数据
                    process_args = []
                    for element_id, received_signal_data in elements_to_process:
                        time_data = received_signal_data['time_data']
                        signal = received_signal_data['signal']
                        signal_length = len(signal)
                        element_duration = time_data[-1] - time_data[0]
                        # 将必要参数打包成一个元组，减少数据传输量
                        args = (element_id, signal_length, element_duration, fs, fir_coeffs, scaling_factor)
                        process_args.append(args)

                    # 获取阵元数量
                    num_elements = len(process_args)

                    # 使用串行处理 - 基于性能测试结果，串行处理对于所有阵元数量都更快
                    print(f"使用串行处理生成海洋环境噪声，共 {num_elements} 个阵元...")

                    # 直接在主进程中串行处理
                    completed = 0
                    for args in process_args:
                        element_id = args[0]

                        # 获取参数
                        _, signal_length, element_duration, fs, fir_coeffs, scaling_factor = args

                        # 获取接收信号数据
                        received_signal_data = results['received_signals'][element_id]

                        # 保存原始信号
                        if 'original_signal' not in received_signal_data:
                            received_signal_data['original_signal'] = received_signal_data['signal'].copy()

                        # 获取滤波器阶数
                        filter_order = len(fir_coeffs)

                        # 计算需要的样本数
                        num_samples = int(fs * element_duration)

                        # 生成高斯白噪声
                        np.random.seed((int(time.time() * 1000) + element_id) % (2**32 - 1))
                        white_noise = np.random.randn(num_samples)

                        # 使用FFT卷积代替lfilter，性能提升显著
                        ambient_signal = sig.fftconvolve(white_noise, fir_coeffs, mode='same')

                        # 应用缩放因子
                        if scaling_factor is not None:
                            ambient_signal = ambient_signal * scaling_factor

                        # 确保噪声信号长度与接收信号相同
                        if len(ambient_signal) > signal_length:
                            ambient_signal = ambient_signal[:signal_length]
                        elif len(ambient_signal) < signal_length:
                            padding = np.zeros(signal_length - len(ambient_signal))
                            ambient_signal = np.concatenate([ambient_signal, padding])

                        # 保存环境噪声
                        received_signal_data['ambient_noise'] = ambient_signal

                        # 叠加噪声到原始信号
                        received_signal_data['signal'] = received_signal_data['original_signal'] + ambient_signal

                        # 更新进度
                        completed += 1
                        progress = completed / num_elements * 100
                        print(f"环境噪声叠加进度: {progress:.1f}% ({completed}/{num_elements})")

                    # 简化的结果验证，只打印总体信息
                    print("\n噪声生成和叠加完成:")
                    print(f"处理了 {completed} 个阵元的噪声生成和叠加")

                    # 随机抽取一个阵元打印详细信息作为示例
                    if completed > 0:
                        sample_element_id = list(results['received_signals'].keys())[0]
                        sample_data = results['received_signals'][sample_element_id]
                        if 'ambient_noise' in sample_data:
                            noise = sample_data['ambient_noise']
                            noise_rms = np.sqrt(np.mean(np.square(noise)))
                            print(f"示例 - 阵元 {sample_element_id}: 噪声信号 RMS = {noise_rms:.6f}, 最大值 = {np.max(np.abs(noise)):.6f}")

                    print(f"环境噪声生成完成，总耗时: {time.time() - start_time:.4f}秒")

                    # 添加标记，表示已叠加海洋环境噪声
                    results['ambient_noise_added'] = True

                    print("已为各阵元接收信号叠加海洋环境噪声")
                else:
                    print("未找到海洋环境噪声结果，跳过叠加")
            except Exception as e:
                print(f"叠加海洋环境噪声失败: {e}")
                # 不中断处理，继续执行

        # 发送进度信号
        self.simulation_progress.emit('integrated', 90)

        # 获取当前结果
        try:
            current_results = self.data_manager.get_results('integrated')
        except Exception as e:
            print(f"获取结果失败: {e}")
            current_results = {}

        # 更新结果字典，添加信号处理相关的结果
        current_results['signal_processing'] = results

        # 将更新后的结果存储到数据管理器
        try:
            self.data_manager.set_results('integrated', current_results)
        except Exception as e:
            print(f"设置结果失败: {e}")
            self.simulation_error.emit('integrated', f"设置结果失败: {e}")
            return

        # 发送进度信号
        self.simulation_progress.emit('integrated', 100)

    def calculate_directivity(self, params):
        """
        计算阵列指向性

        Args:
            params (dict): 参数字典，包含：
                - start_time: 信号开始时间
                - duration: 信号持续时间
                - angle_start: 扫描角度起始值
                - angle_end: 扫描角度结束值
                - angle_step: 扫描角度步长
                - sound_speed: 声速
                - reference_position: 参考位置
                - weighting: 加权方式
                - delay_method: 时延补偿方法
                - freq_range: 频率范围（可选）
                - display_mode: 显示方式

        Returns:
            bool: 是否成功计算
        """
        try:
            # 获取采样率
            fs = self.data_manager.get_global_param('fs')

            # 创建综合仿真模型
            model = IntegratedSimulation(fs)

            # 从数据管理器获取信号处理结果
            signal_results = self.data_manager.get_results('integrated').get('signal_processing', {})
            if not signal_results or 'received_signals' not in signal_results:
                self.simulation_error.emit('integrated', "未找到接收信号数据")
                return False

            received_signals = signal_results['received_signals']

            # 设置模型参数
            # 首先尝试从信号处理结果中获取阵元位置数据
            array_elements = signal_results.get('array_elements', None)

            # 如果信号处理结果中没有阵元位置数据，尝试从信道数据中获取
            if array_elements is None or not array_elements:
                print("信号处理结果中未找到阵元位置数据，尝试从信道数据中获取...")
                try:
                    channel_data = self.data_manager.get_results('integrated').get('channel_data', {})
                    array_elements = channel_data.get('array_elements', None)
                    if array_elements:
                        print(f"从信道数据中获取到阵元位置数据，共 {len(array_elements)} 个阵元")
                    else:
                        print("信道数据中也未找到阵元位置数据")
                except Exception as e:
                    print(f"从信道数据中获取阵元位置数据失败: {e}")

            # 设置模型的阵元位置数据
            if array_elements:
                model.array_elements = array_elements
                print(f"设置模型阵元位置数据，共 {len(array_elements)} 个阵元")
            else:
                print("警告: 未找到阵元位置数据，模型将使用默认值")

            # 准备波束形成数据
            start_time = params.get('start_time', 0.0)
            duration = params.get('duration', 1.0)
            freq_range = None
            if params.get('freq_range_enabled', False):
                freq_min = params.get('freq_min', 100)
                freq_max = params.get('freq_max', 1000)
                freq_range = [freq_min, freq_max]

            # 发送进度信号
            self.simulation_progress.emit('integrated', 20)

            # 检查是否取消
            if self.is_cancelled:
                return False

            # 检查是否需要使用原始信号（未叠加噪声的信号）
            use_original_signal = params.get('use_original_signal', False)

            # 如果没有明确指定，则根据add_ambient_noise参数决定
            if 'use_original_signal' not in params and 'add_ambient_noise' in params:
                use_original_signal = not params.get('add_ambient_noise', False)

            print(f"指向性计算使用原始信号: {use_original_signal}")

            signal_segments, element_positions = model.prepare_data_for_beamforming(
                received_signals, start_time, duration, freq_range, use_original_signal
            )

            # 设置扫描角度 - 对于距离-深度平面，使用-90到90度的俯仰角范围
            angle_start = params.get('angle_start', -90)
            angle_end = params.get('angle_end', 90)

            # 确保角度范围在-90到90度之间
            angle_start = max(-90, min(90, angle_start))
            angle_end = max(-90, min(90, angle_end))

            # 确保起始角度小于结束角度
            if angle_start > angle_end:
                angle_start, angle_end = angle_end, angle_start

            angle_step = params.get('angle_step', 1)

            # 确保步长为正值且合理
            angle_step = max(0.1, min(10, abs(angle_step)))

            print(f"扫描角度范围: [{angle_start}, {angle_end}]°, 步长: {angle_step}°")
            angles = np.arange(angle_start, angle_end + angle_step/2, angle_step)  # 加上angle_step/2确保包含end值

            # 发送进度信号
            self.simulation_progress.emit('integrated', 40)

            # 检查是否取消
            if self.is_cancelled:
                return False

            # 执行波束形成
            sound_speed = params.get('sound_speed', 1500)
            reference_position = params.get('reference_position', 'center')
            weighting = params.get('weighting', 'uniform')
            delay_method = params.get('delay_method', 'position_based')

            # 暂时禁用并行计算，始终使用单线程计算，便于调试
            print(f"使用单线程波束形成，角度数量: {len(angles)}")
            angles, beam_pattern = model.delay_and_sum_beamforming(
                signal_segments, element_positions, angles,
                sound_speed, reference_position, weighting, delay_method
            )

            # 发送进度信号
            self.simulation_progress.emit('integrated', 80)

            # 检查是否取消
            if self.is_cancelled:
                return False

            # 获取当前结果
            current_results = self.data_manager.get_results('integrated')

            # 更新结果
            if 'array_processing' not in current_results:
                current_results['array_processing'] = {}

            current_results['array_processing']['directivity'] = {
                'angles': angles.tolist(),
                'pattern': beam_pattern.tolist(),  # 保存原始的平均功率数据，而不是归一化的dB指向性图
                'params': {
                    'start_time': start_time,
                    'duration': duration,
                    'sound_speed': sound_speed,
                    'reference_position': reference_position,
                    'weighting': weighting,
                    'delay_method': delay_method,
                    'freq_range': freq_range,
                    'display_mode': 'linear'  # 固定使用线性图
                }
            }

            # 添加调试输出
            print(f"指向性计算结果已保存到数据管理器: {len(angles)} 个角度, 波束方向图值范围: [{np.min(beam_pattern)}, {np.max(beam_pattern)}]")

            # 打印调试信息
            print(f"指向性计算完成，数据已保存到结果中")
            print(f"角度点数: {len(angles)}, 波束方向图点数: {len(beam_pattern)}")
            print(f"波束方向图值范围: [{np.min(beam_pattern)}, {np.max(beam_pattern)}]")

            # 将结果存储到数据管理器
            self.data_manager.set_results('integrated', current_results)

            # 通知结果已更改
            self.data_manager.results_changed.emit('integrated')
            print("已发送结果更改信号")

            # 发送进度信号
            self.simulation_progress.emit('integrated', 100)

            return True

        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            print(f"计算指向性失败，详细错误信息:\n{error_traceback}")

            # 提供更详细的错误信息
            error_message = f"计算指向性失败: {str(e)}"
            if str(e) == "0":
                error_message = "计算指向性失败: 可能是信号段截取或波束形成过程中出现问题，请检查信号时间范围和阵元位置"

            self.simulation_error.emit('integrated', error_message)
            return False

    def calculate_cross_spectral_density(self, params):
        """
        计算互功率谱密度

        Args:
            params (dict): 参数字典，包含：
                - element_pair: 阵元对 [element_id1, element_id2]
                - start_time: 信号开始时间
                - end_time: 信号结束时间
                - show_coherence: 是否显示相干函数

        Returns:
            bool: 是否成功计算
        """
        try:
            # 获取采样率
            fs = self.data_manager.get_global_param('fs')

            # 创建综合仿真模型
            model = IntegratedSimulation(fs)

            # 从数据管理器获取信号处理结果
            signal_results = self.data_manager.get_results('integrated').get('signal_processing', {})
            if not signal_results or 'received_signals' not in signal_results:
                self.simulation_error.emit('integrated', "未找到接收信号数据")
                return False

            received_signals = signal_results['received_signals']

            # 获取阵元对
            element_pair = params.get('element_pair', [0, 0])
            element_id1, element_id2 = element_pair

            # 检查阵元是否存在
            if element_id1 not in received_signals or element_id2 not in received_signals:
                self.simulation_error.emit('integrated', f"阵元 {element_id1} 或 {element_id2} 不存在")
                return False

            # 获取信号时间范围
            start_time = params.get('start_time', None)
            end_time = params.get('end_time', None)

            # 处理特殊值
            if start_time == 0.0 and end_time == 0.0:
                # 如果都是0.0，表示用户没有输入，使用全部信号
                start_time = None
                end_time = None
            elif end_time == 0.0:
                # 如果只有end_time是0.0，表示用户只输入了start_time
                end_time = None

            # 发送进度信号
            self.simulation_progress.emit('integrated', 20)

            # 检查是否取消
            if self.is_cancelled:
                return False

            # 获取两个阵元的信号
            signal1_data = received_signals[element_id1]
            signal2_data = received_signals[element_id2]

            # 检查是否需要使用原始信号（未叠加噪声的信号）
            use_original_signal = params.get('use_original_signal', False)

            # 如果没有明确指定，则根据add_ambient_noise参数决定
            if 'use_original_signal' not in params and 'add_ambient_noise' in params:
                use_original_signal = not params.get('add_ambient_noise', False)

            print(f"互功率谱计算使用原始信号: {use_original_signal}")

            # 获取信号和时间数据
            if use_original_signal and 'original_signal' in signal1_data:
                signal1 = signal1_data['original_signal']
                print(f"阵元 {element_id1} 使用原始信号（未叠加噪声）")
            else:
                signal1 = signal1_data['signal']
                if use_original_signal:
                    print(f"阵元 {element_id1} 未找到原始信号，使用当前信号")
                else:
                    print(f"阵元 {element_id1} 使用当前信号（可能已叠加噪声）")

            if use_original_signal and 'original_signal' in signal2_data:
                signal2 = signal2_data['original_signal']
                print(f"阵元 {element_id2} 使用原始信号（未叠加噪声）")
            else:
                signal2 = signal2_data['signal']
                if use_original_signal:
                    print(f"阵元 {element_id2} 未找到原始信号，使用当前信号")
                else:
                    print(f"阵元 {element_id2} 使用当前信号（可能已叠加噪声）")

            time_data1 = signal1_data['time_data']
            time_data2 = signal2_data['time_data']

            # 如果指定了时间范围，截取信号
            if start_time is not None or end_time is not None:
                # 确定实际的起始和结束时间
                actual_start_time = max(time_data1[0], time_data2[0]) if start_time is None else max(start_time, time_data1[0], time_data2[0])
                actual_end_time = min(time_data1[-1], time_data2[-1]) if end_time is None else min(end_time, time_data1[-1], time_data2[-1])

                # 找出对应的索引
                start_idx1 = np.argmin(np.abs(time_data1 - actual_start_time))
                end_idx1 = np.argmin(np.abs(time_data1 - actual_end_time))
                start_idx2 = np.argmin(np.abs(time_data2 - actual_start_time))
                end_idx2 = np.argmin(np.abs(time_data2 - actual_end_time))

                # 截取信号
                signal1 = signal1[start_idx1:end_idx1+1]
                signal2 = signal2[start_idx2:end_idx2+1]
                time_data1 = time_data1[start_idx1:end_idx1+1]
                time_data2 = time_data2[start_idx2:end_idx2+1]

            # 确保两个信号长度相同
            min_length = min(len(signal1), len(signal2))
            signal1 = signal1[:min_length]
            signal2 = signal2[:min_length]

            # 发送进度信号
            self.simulation_progress.emit('integrated', 40)

            # 检查是否取消
            if self.is_cancelled:
                return False

            # 计算互功率谱密度
            freqs, csd = model.calculate_cross_spectral_density(signal1, signal2, fs)

            # 计算互功率谱密度的模值和相位
            csd_magnitude = np.abs(csd)
            csd_phase = np.angle(csd)
            csd_phase_unwrapped = np.unwrap(csd_phase)  # 相位解缠绕

            # 计算dB形式的模值
            csd_magnitude_db = 10 * np.log10(np.where(csd_magnitude > 0, csd_magnitude, 1e-10))

            # 打印调试信息
            print(f"互功率谱密度计算完成，频率点数: {len(freqs)}, 模值范围: [{np.min(csd_magnitude)}, {np.max(csd_magnitude)}]")
            print(f"dB形式的模值范围: [{np.min(csd_magnitude_db)}, {np.max(csd_magnitude_db)}] dB")

            # 发送进度信号
            self.simulation_progress.emit('integrated', 60)

            # 检查是否取消
            if self.is_cancelled:
                return False

            # 如果需要，计算相干函数
            coherence = None
            if params.get('show_coherence', False):
                _, coherence = model.calculate_coherence(signal1, signal2, fs)

            # 发送进度信号
            self.simulation_progress.emit('integrated', 80)

            # 检查是否取消
            if self.is_cancelled:
                return False

            # 获取当前结果
            current_results = self.data_manager.get_results('integrated')

            # 更新结果
            if 'array_processing' not in current_results:
                current_results['array_processing'] = {}

            # 创建互功率谱结果
            csd_result = {
                'element_pair': element_pair,
                'freqs': freqs.tolist(),
                'csd_magnitude': csd_magnitude.tolist(),
                'csd_magnitude_db': csd_magnitude_db.tolist(),  # 添加dB形式的模值
                'csd_phase': csd_phase.tolist(),
                'csd_phase_unwrapped': csd_phase_unwrapped.tolist(),
                'time_range': [time_data1[0], time_data1[-1]],
                'params': {
                    'start_time': start_time if start_time is not None else time_data1[0],
                    'end_time': end_time if end_time is not None else time_data1[-1],
                    'show_coherence': params.get('show_coherence', False)
                }
            }

            # 如果计算了相干函数，添加到结果中
            if coherence is not None:
                csd_result['coherence'] = coherence.tolist()

            # 更新结果
            current_results['array_processing']['cross_spectral_density'] = csd_result

            # 打印调试信息
            print(f"互功率谱计算完成，数据已保存到结果中")
            print(f"频率点数: {len(freqs)}, 模值点数: {len(csd_magnitude)}, 相位点数: {len(csd_phase_unwrapped)}")
            if coherence is not None:
                print(f"相干函数点数: {len(coherence)}")
            else:
                print("未计算相干函数")

            # 将结果存储到数据管理器
            self.data_manager.set_results('integrated', current_results)

            # 通知结果已更改
            self.data_manager.results_changed.emit('integrated')
            print("已发送结果更改信号")

            # 发送进度信号
            self.simulation_progress.emit('integrated', 100)

            return True

        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            print(f"计算互功率谱密度失败，详细错误信息:\n{error_traceback}")
            self.simulation_error.emit('integrated', f"计算互功率谱密度失败: {str(e)}")
            return False

    def on_calculate_spectrum_requested(self, element_index, start_time, end_time):
        """
        处理计算频谱请求

        Args:
            element_index: 阵元索引
            start_time: 起始时间
            end_time: 结束时间
        """
        # 更新数据管理器中的选中阵元
        self.data_manager.update_parameter('integrated', 'selected_spectrum_element_index', element_index)

        # 检查是否正在仿真
        if self.is_simulation_running():
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(None, "错误", "综合仿真正在进行中，请等待完成后再计算频谱")
            return

        # 处理特殊值
        if start_time == 0.0 and end_time == 0.0:
            # 如果都是0.0，表示用户没有输入，使用全部信号
            start_time = None
            end_time = None
        elif end_time == 0.0:
            # 如果只有end_time是0.0，表示用户只输入了start_time
            end_time = None

        # 创建并启动异步计算线程
        from PyQt5.QtCore import QThread, pyqtSignal, QObject
        from PyQt5.QtWidgets import QApplication

        # 创建进度信号发射器
        class ProgressEmitter(QObject):
            """进度信号发射器"""
            progress_updated = pyqtSignal(str)  # 进度更新信号
            calculation_started = pyqtSignal()  # 计算开始信号
            calculation_done = pyqtSignal(dict)  # 计算完成信号，参数为计算结果
            calculation_error = pyqtSignal(str)  # 计算错误信号，参数为错误信息

        # 创建进度发射器实例
        if not hasattr(self, 'progress_emitter'):
            self.progress_emitter = ProgressEmitter()

        # 更新状态栏的函数
        def update_status_bar(message):
            from PyQt5.QtWidgets import QApplication
            main_window = QApplication.activeWindow()
            if main_window and hasattr(main_window, 'statusBar'):
                main_window.statusBar().showMessage(message)

        # 连接进度信号
        self.progress_emitter.progress_updated.connect(update_status_bar)

        # 计算开始时通知UI
        self.progress_emitter.calculation_started.emit()
        update_status_bar(f"正在计算阵元 #{element_index} 的频谱...")

        class SpectrumCalculator(QThread):
            """频谱计算线程"""
            def __init__(self, controller, element_index, start_time, end_time, progress_emitter, freq_range=None):
                super().__init__()
                self.controller = controller
                self.element_index = element_index
                self.start_time = start_time
                self.end_time = end_time
                self.progress_emitter = progress_emitter
                self.freq_range = freq_range
                # 设置线程优先级为低，避免阻塞UI
                self.setPriority(QThread.LowPriority)

            def run(self):
                try:
                    # 检查是否已经计算过该时间范围的频谱
                    results = self.controller.data_manager.get_results('integrated')
                    if 'spectrum' in results.get('signal_processing', {}) and self.element_index in results['signal_processing']['spectrum']:
                        # 已经计算过，直接返回结果
                        spectrum_result = results['signal_processing']['spectrum'][self.element_index]
                        self.progress_emitter.progress_updated.emit(f"使用缓存的频谱结果 (阵元 #{self.element_index})")
                        self.progress_emitter.calculation_done.emit(spectrum_result)
                        return

                    # 如果没有计算过，计算所有阵元的频谱
                    self.progress_emitter.progress_updated.emit(f"计算所有阵元在时间范围 [{self.start_time}, {self.end_time}] 的频谱...")

                    # 处理事件循环，保持UI响应
                    QApplication.processEvents()

                    result = self.controller.calculate_all_spectrums(
                        self.start_time,
                        self.end_time,
                        self.freq_range
                    )

                    if result and self.element_index in result:
                        self.progress_emitter.calculation_done.emit(result[self.element_index])
                    else:
                        self.progress_emitter.calculation_error.emit("频谱计算失败，请查看控制台输出")
                except Exception as e:
                    import traceback
                    traceback.print_exc()
                    self.progress_emitter.calculation_error.emit(str(e))

        # 创建计算线程
        self.spectrum_calculator = SpectrumCalculator(self, element_index, start_time, end_time, self.progress_emitter, freq_range=None)

        # 连接信号
        self.progress_emitter.calculation_done.connect(
            lambda result: self._on_spectrum_calculation_done(element_index, result)
        )
        self.progress_emitter.calculation_error.connect(
            lambda error: self._show_error_message("频谱计算失败", error)
        )

        # 启动线程
        self.spectrum_calculator.start()

    def _on_spectrum_calculation_done(self, element_index, spectrum_result):
        """
        频谱计算完成处理

        Args:
            element_index: 阵元索引
            spectrum_result: 频谱计算结果
        """
        # 更新状态栏
        from PyQt5.QtWidgets import QApplication
        main_window = QApplication.activeWindow()
        if main_window and hasattr(main_window, 'statusBar'):
            main_window.statusBar().showMessage(f"频谱计算完成 (阵元 #{element_index})")

        # 确保结果被正确保存到数据管理器
        try:
            # 获取当前结果
            results = self.data_manager.get_results('integrated')
            if 'signal_processing' not in results:
                results['signal_processing'] = {}

            if 'spectrum' not in results['signal_processing']:
                results['signal_processing']['spectrum'] = {}

            # 保存频谱结果
            results['signal_processing']['spectrum'][element_index] = spectrum_result

            # 更新数据管理器
            self.data_manager.set_results('integrated', results)

            print(f"频谱计算结果已保存到数据管理器 (阵元 #{element_index})")
        except Exception as e:
            print(f"保存频谱计算结果失败: {e}")
            import traceback
            traceback.print_exc()

        # 通知视图更新器
        self.data_manager.results_changed.emit('integrated')

    def _show_error_message(self, title, message):
        """显示错误消息对话框"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.warning(None, title, message)

    def calculate_all_spectrums(self, start_time=None, end_time=None, freq_range=None):
        """
        计算所有阵元接收信号的频谱

        Args:
            start_time (float, optional): 信号起始时间，默认为None（使用全部信号）
            end_time (float, optional): 信号结束时间，默认为None（使用全部信号）
            freq_range (tuple, optional): 频率范围，格式为(min_freq, max_freq)，默认为None（使用全部频率）

        Returns:
            dict: 所有阵元的频谱计算结果，格式为 {element_index: spectrum_result}
        """
        try:
            # 获取信号处理结果
            results = self.data_manager.get_results('integrated')
            if not results or 'signal_processing' not in results:
                print("未找到信号处理结果")
                return None

            signal_processing = results['signal_processing']
            received_signals = signal_processing.get('received_signals', {})

            if not received_signals:
                print("未找到接收信号数据")
                return None

            # 获取参数
            params = self.data_manager.get_parameters('integrated')
            if not params:
                params = {}

            # 检查是否需要使用原始信号（未叠加噪声的信号）
            # 如果用户选择不叠加海洋环境噪声，则使用原始信号
            # 如果用户选择叠加海洋环境噪声，则使用叠加噪声后的信号
            use_original_signal = not params.get('add_ambient_noise', False)
            params['use_original_signal'] = use_original_signal

            # 获取采样率
            source_signal = signal_processing.get('source_signal', {})
            fs = source_signal.get('metadata', {}).get('fs', 44100)

            # 获取CPU核心数
            try:
                cpu_cores = self.data_manager.get_global_param('cpu_cores')
            except KeyError:
                # 如果全局参数中没有cpu_cores，使用系统CPU核心数
                cpu_cores = multiprocessing.cpu_count()
                # 设置全局参数，以便下次使用
                self.data_manager.set_global_param('cpu_cores', cpu_cores)

            # 定义单个阵元频谱计算函数 - 使其与ProcessPoolExecutor兼容
            def calculate_element_spectrum(element_data):
                element_index, received_signal = element_data

                # 导入必要的模块（在进程中需要重新导入）
                import numpy as np
                from scipy import signal

                # 获取接收信号数据
                time_data = received_signal['time_data']

                # 检查是否需要使用原始信号（未叠加噪声的信号）
                use_original_signal = params.get('use_original_signal', False)

                # 根据用户选择决定使用哪个信号
                if use_original_signal and 'original_signal' in received_signal:
                    # 使用原始信号（未叠加噪声的信号）
                    signal_data = received_signal['original_signal']
                    signal_type = '原始信号'
                else:
                    # 使用当前信号（可能已叠加噪声）
                    signal_data = received_signal['signal']
                    signal_type = '叠加噪声后' if 'ambient_noise_added' in signal_processing and signal_processing['ambient_noise_added'] else '原始信号'

                # 获取频率范围（如果有）
                local_freq_range = freq_range

                # 如果指定了时间范围，截取相应的信号段
                if start_time is not None or end_time is not None:
                    # 默认值
                    local_start_time = time_data[0] if start_time is None else start_time
                    local_end_time = time_data[-1] if end_time is None else end_time

                    # 确保时间范围有效
                    if local_start_time >= local_end_time:
                        print(f"阵元 #{element_index}: 无效的时间范围: [{local_start_time}, {local_end_time}]")
                        return element_index, None

                    # 找出时间范围内的索引
                    start_idx = np.argmin(np.abs(time_data - local_start_time))
                    end_idx = np.argmin(np.abs(time_data - local_end_time))

                    # 截取信号
                    time_data_segment = time_data[start_idx:end_idx+1]
                    signal_data_segment = signal_data[start_idx:end_idx+1]

                    # 如果有原始信号和环境噪声，也截取
                    original_signal_segment = None
                    ambient_noise_segment = None

                    if 'original_signal' in received_signal and not use_original_signal:
                        original_signal = received_signal['original_signal']
                        if len(original_signal) >= end_idx + 1:
                            original_signal_segment = original_signal[start_idx:end_idx+1]

                    if 'ambient_noise' in received_signal:
                        ambient_noise = received_signal['ambient_noise']
                        if len(ambient_noise) >= end_idx + 1:
                            ambient_noise_segment = ambient_noise[start_idx:end_idx+1]
                else:
                    # 使用全部信号
                    time_data_segment = time_data
                    signal_data_segment = signal_data
                    original_signal_segment = None if use_original_signal else received_signal.get('original_signal', None)
                    ambient_noise_segment = received_signal.get('ambient_noise', None)

                # 计算功率谱密度
                nperseg = fs  # 1Hz分辨率

                # 计算信号频谱
                freqs, psd = signal.welch(signal_data_segment, fs=fs, nperseg=nperseg)
                psd_db = 10 * np.log10(psd + 1e-20)  # 避免log10(0)

                # 如果指定了频率范围，只保留该范围内的频率
                if local_freq_range is not None:
                    freq_min, freq_max = local_freq_range
                    # 找出频率范围内的索引
                    freq_mask = (freqs >= freq_min) & (freqs <= freq_max)
                    freqs = freqs[freq_mask]
                    psd_db = psd_db[freq_mask]

                # 准备结果
                spectrum_result = {
                    'freqs': freqs,
                    'psd_db': psd_db,
                    'signal_type': signal_type,
                    'time_range': [time_data_segment[0], time_data_segment[-1]],
                    'freq_range': local_freq_range
                }

                # 如果有原始信号，也计算其频谱（仅当使用叠加噪声后的信号时）
                if original_signal_segment is not None:
                    orig_freqs, orig_psd = signal.welch(original_signal_segment, fs=fs, nperseg=nperseg)
                    orig_psd_db = 10 * np.log10(orig_psd + 1e-20)

                    # 如果指定了频率范围，只保留该范围内的频率
                    if local_freq_range is not None:
                        freq_min, freq_max = local_freq_range
                        # 找出频率范围内的索引
                        freq_mask = (orig_freqs >= freq_min) & (orig_freqs <= freq_max)
                        orig_freqs = orig_freqs[freq_mask]
                        orig_psd_db = orig_psd_db[freq_mask]

                    spectrum_result['original_freqs'] = orig_freqs
                    spectrum_result['original_psd_db'] = orig_psd_db

                # 如果有环境噪声，也计算其频谱
                if ambient_noise_segment is not None:
                    noise_freqs, noise_psd = signal.welch(ambient_noise_segment, fs=fs, nperseg=nperseg)
                    noise_psd_db = 10 * np.log10(noise_psd + 1e-20)

                    # 如果指定了频率范围，只保留该范围内的频率
                    if local_freq_range is not None:
                        freq_min, freq_max = local_freq_range
                        # 找出频率范围内的索引
                        freq_mask = (noise_freqs >= freq_min) & (noise_freqs <= freq_max)
                        noise_freqs = noise_freqs[freq_mask]
                        noise_psd_db = noise_psd_db[freq_mask]

                    spectrum_result['noise_freqs'] = noise_freqs
                    spectrum_result['noise_psd_db'] = noise_psd_db

                return element_index, spectrum_result

            # 不再根据时间范围决定是否只计算一个阵元的频谱
            # 始终计算所有阵元的频谱，以确保用户可以在不同阵元之间切换查看频谱
            # 这样可以提高用户体验，避免每次切换阵元都需要重新计算频谱

            # 并行计算所有阵元的频谱
            print(f"使用 {cpu_cores} 个线程并行计算频谱...")
            start_time_calc = time.time()

            all_spectrum_results = {}

            # 使用分批处理方式，避免一次性创建太多线程
            batch_size = min(cpu_cores, 4)  # 每批最多4个线程
            element_items = list(received_signals.items())
            total_elements = len(element_items)

            for batch_start in range(0, total_elements, batch_size):
                batch_end = min(batch_start + batch_size, total_elements)
                batch_items = element_items[batch_start:batch_end]

                print(f"处理批次 {batch_start//batch_size + 1}/{(total_elements + batch_size - 1)//batch_size}, "
                      f"阵元 {batch_start} 到 {batch_end-1}")

                with concurrent.futures.ThreadPoolExecutor(max_workers=batch_size) as executor:
                    # 提交批次任务
                    future_to_element = {
                        executor.submit(calculate_element_spectrum, (element_index, received_signal)): element_index
                        for element_index, received_signal in batch_items
                    }

                    # 收集结果
                    for future in concurrent.futures.as_completed(future_to_element):
                        element_index = future_to_element[future]
                        try:
                            element_index, spectrum_result = future.result()
                            if spectrum_result is not None:
                                all_spectrum_results[element_index] = spectrum_result
                        except Exception as e:
                            print(f"计算阵元 #{element_index} 的频谱失败: {e}")
                            import traceback
                            traceback.print_exc()

                # 处理事件循环，保持UI响应
                from PyQt5.QtWidgets import QApplication
                QApplication.processEvents()

            print(f"频谱计算完成，耗时: {time.time() - start_time_calc:.4f}秒")

            # 更新结果
            if 'spectrum' not in signal_processing:
                signal_processing['spectrum'] = {}

            signal_processing['spectrum'] = all_spectrum_results

            # 更新数据管理器
            self.data_manager.set_results('integrated', results)

            return all_spectrum_results

        except Exception as e:
            print(f"计算频谱失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def calculate_spectrum(self, element_index, start_time=None, end_time=None, freq_range=None):
        """
        计算指定阵元接收信号的频谱

        此方法现在是calculate_all_spectrums的包装器，用于向后兼容

        Args:
            element_index (int): 阵元索引
            start_time (float, optional): 信号起始时间，默认为None（使用全部信号）
            end_time (float, optional): 信号结束时间，默认为None（使用全部信号）
            freq_range (tuple, optional): 频率范围，格式为(min_freq, max_freq)，默认为None（使用全部频率）

        Returns:
            dict: 频谱计算结果，包含频率和功率谱密度
        """
        # 调用计算所有阵元频谱的方法
        all_spectrums = self.calculate_all_spectrums(start_time, end_time, freq_range)

        # 返回指定阵元的结果
        if all_spectrums and element_index in all_spectrums:
            return all_spectrums[element_index]
        else:
            print(f"未找到阵元 #{element_index} 的频谱结果")
            return None

    def on_simulation_requested(self, computation_type):
        """
        处理仿真请求

        Args:
            computation_type: 计算类型
        """
        # 检查是否正在仿真
        if self.is_simulation_running():
            self.simulation_error.emit('integrated', "综合仿真正在进行中，请等待完成后再进行新的仿真")
            return

        # 重置取消标志
        self.is_cancelled = False

        # 获取参数
        params = self.data_manager.get_parameters('integrated')
        if not params:
            self.simulation_error.emit('integrated', "未找到仿真参数")
            return

        # 根据计算类型分发到不同的处理方法
        if computation_type == 'channel_data':
            # 处理信道数据计算
            self.process_channel_data(params)
        elif computation_type == 'signal_processing':
            # 处理信号处理计算
            self.process_signal(params)
        elif computation_type == 'spectrum_calculation':
            # 处理频谱计算
            element_index = params.get('selected_spectrum_element_index', 0)
            start_time = params.get('start_time', 0.0)
            end_time = params.get('end_time', 0.0)
            self.calculate_spectrum(element_index, start_time, end_time)
        elif computation_type == 'directivity_calculation':
            # 处理指向性计算
            self.calculate_directivity(params)
        elif computation_type == 'cross_spectral_density':
            # 处理互功率谱计算
            element_pair = params.get('element_pair', [0, 0])
            start_time = params.get('start_time', 0.0)
            end_time = params.get('end_time', 0.0)
            show_coherence = params.get('show_coherence', False)

            # 创建参数字典
            csd_params = {
                'element_pair': element_pair,
                'start_time': start_time,
                'end_time': end_time,
                'show_coherence': show_coherence
            }

            # 计算互功率谱
            success = self.calculate_cross_spectral_density(csd_params)

            if success:
                self.simulation_completed.emit('integrated')
            else:
                self.simulation_error.emit('integrated', "计算互功率谱失败")
        else:
            self.simulation_error.emit('integrated', f"未知的计算类型: {computation_type}")
            return

    def load_channel_data(self, channel_data_dir):
        """
        加载信道数据（非线程方式）

        Args:
            channel_data_dir (str): 信道数据目录

        Returns:
            bool: 是否成功加载数据
        """
        try:
            # 检查目录是否存在
            if not os.path.exists(channel_data_dir):
                print(f"信道数据目录不存在: {channel_data_dir}")
                return False

            # 更新参数
            self.data_manager.update_parameter('integrated', 'channel_data_dir', channel_data_dir)
            self.data_manager.update_parameter('integrated', 'computation_type', 'channel_data_analysis')

            # 执行数据分析
            params = self.data_manager.get_parameters('integrated')
            self._analyze_channel_data(params)

            return True
        except Exception as e:
            print(f"加载信道数据失败: {e}")
            return False
