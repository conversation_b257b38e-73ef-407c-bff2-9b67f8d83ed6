# Bellhop并行计算工具

这个目录包含了用于并行计算多个频率的声传播信息的脚本，使用arlpy的uwapm模块调用Bellhop模型。

## 文件说明

- `parallel_bellhop.py`: 基础版并行计算脚本
- `advanced_parallel_bellhop.py`: 高级版并行计算脚本，提供更多功能和灵活性
- `bellhop_config_example.json`: 示例配置文件

## 依赖项

- Python 3.6+
- numpy
- pandas
- matplotlib
- arlpy
- Bellhop声学工具箱（需要在PATH中）

## 基础版使用方法

```bash
python parallel_bellhop.py [选项]
```

### 选项

- `--freq_start`: 起始频率 (Hz)，默认为100
- `--freq_end`: 结束频率 (Hz)，默认为1000
- `--freq_step`: 频率步长 (Hz)，默认为100
- `--freq_list`: 频率列表，以逗号分隔，优先于范围参数
- `--max_workers`: 最大并行进程数，默认使用CPU核心数
- `--output_dir`: 输出目录，默认为"results"
- `--debug`: 输出调试信息

### 示例

计算100Hz到1000Hz，步长为100Hz的频率：

```bash
python parallel_bellhop.py
```

计算指定频率列表：

```bash
python parallel_bellhop.py --freq_list 100,200,500,1000,2000
```

## 高级版使用方法

```bash
python advanced_parallel_bellhop.py [选项]
```

### 选项

除了基础版的所有选项外，还包括：

- `--config`: 配置文件路径
- `--no_plot`: 不绘制图表

### 示例

使用配置文件计算：

```bash
python advanced_parallel_bellhop.py --config bellhop_config_example.json
```

计算指定频率并不绘制图表：

```bash
python advanced_parallel_bellhop.py --freq_list 100,200,500,1000 --no_plot
```

## 配置文件格式

配置文件使用JSON格式，包含以下可选参数：

```json
{
  "depth": 100,                      // 水深 (m)
  "soundspeed": 1500,                // 声速 (m/s)，也可以是声速剖面数组
  "soundspeed_interp": "spline",     // 声速插值方法，"spline"或"linear"
  "tx_depth": 50,                    // 发射深度 (m)
  "rx_depth": [0, 10, 20, ..., 100], // 接收深度数组 (m)
  "rx_range": [100, 200, ..., 5000], // 接收距离数组 (m)
  "bottom_soundspeed": 1600,         // 海底声速 (m/s)
  "bottom_density": 1800,            // 海底密度 (kg/m^3)
  "bottom_absorption": 0.5,          // 海底吸收系数 (dB/wavelength)
  "min_angle": -80,                  // 最小发射角度 (度)
  "max_angle": 80,                   // 最大发射角度 (度)
  "nbeams": 0,                       // 光线数量 (0表示自动)
  "fs": 44100,                       // 采样率 (Hz)，用于元数据
  "duration": 5.0,                   // 持续时间 (s)，用于元数据
  "filter_order": 8192               // 滤波器阶数，用于元数据
}
```

## 输出结果

脚本将在指定的输出目录中生成以下文件：

- 每个频率的arrivals数据（CSV格式）
- 每个频率的元数据（JSON格式，仅高级版）
- 所有频率的合并arrivals数据（CSV格式）
- 每个频率的到达信息图表（PNG格式，仅高级版）
- 到达时间与频率关系图表（PNG格式，仅高级版）

## 注意事项

1. Bellhop可能在相同目录下创建临时文件，脚本使用UUID生成唯一的临时文件名以避免并行运行时的冲突。
2. 计算结果包含元数据，便于后续信号处理和叠加操作。
3. 高级版脚本提供了面向对象的API，可以在其他Python代码中导入使用。
