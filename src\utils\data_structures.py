# -*- coding: utf-8 -*-
"""
数据结构文档

本模块记录项目中使用的主要数据结构，包括：
1. 数据管理器存储的数据结构
2. Wenz曲线数据结构
3. Wenz曲线样式配置
"""

import numpy as np
import pandas as pd

# 数据结构定义仅用于文档目的，不会在实际代码中使用
# 所有类和函数都是用来说明数据结构的，不是实际的实现

class DataManagerStructures:
    """
    数据管理器存储的数据结构

    记录SimulationDataManager中使用的数据结构
    """

    @staticmethod
    def global_params():
        """
        全局参数结构

        存储在SimulationDataManager._global_params中

        Returns:
            dict: 全局参数字典示例
        """
        return {
            'fs': 44100,        # 采样率 (Hz)
            'cpu_cores': 4      # CPU核心数，用于并行计算的默认值
        }

    @staticmethod
    def parameters():
        """
        参数结构

        存储在SimulationDataManager._parameters中

        Returns:
            dict: 参数字典示例
        """
        return {
            'ship_noise': {
                # 连续谱参数
                'f0': 250,                  # 连续谱峰值频率 (Hz)
                'sl0': 130.0,               # 连续谱峰值电平 (dB)
                'a1': 3.0,                  # 连续谱上升斜率 (dB/倍频程)
                'a2': -4.2,                 # 连续谱下降斜率 (dB/倍频程)

                # 线谱参数
                'line_frequencies': [16, 32, 48, 65, 350, 800],  # 线谱频率列表 (Hz)
                'line_levels_diff': [25.0, 23.0, 21.0, 19.0, 18.0, 18.0],  # 线谱电平差值列表 (dB)
                'include_line_spectrum': True,  # 是否包含线谱

                # 调制谱参数
                'mod_f1': 8.0,              # 轴频 (Hz)
                'mod_f2': 32.0,             # 叶频 (Hz)
                'mod_A1': 0.05,             # 轴频分量振幅
                'mod_A2': 0.3,              # 叶频分量振幅
                'mod_p': 0.1,               # 轴频衰减系数
                'mod_q': 0.1,               # 叶频衰减系数
                'mod_N': 5,                 # 轴频谐波数量
                'mod_M': 5,                 # 叶频谐波数量
                'include_modulation': True,  # 是否包含调制谱

                # 工况参数计算
                'propeller_diameter': 1.6,  # 螺旋桨直径 (m)
                'advance_coefficient': 0.47, # 进速系数
                'wake_fraction': 0.21,      # 伴流分数
                'cavitation_number': 3.6,   # 空泡评价
                'depth': 30.0,              # 航行深度 (m)
                'speed': 7.5,               # 航速 (kn)
                'linear_speed': 20.6,       # 线速度 (m/s)
                'rotation_speed': 245,      # 转速 (r/min)

                # 仿真参数
                'duration': 5.0,            # 信号持续时间 (s)
                'filter_order': 16385       # 滤波器阶数
            },
            'ambient_noise': {
                # 用户定义点和外插点
                'user_defined_points': [
                    (10, 80),    # 频率(Hz), 噪声级(dB re 1μPa²/Hz)
                    (100, 70),
                    (1000, 60),
                    (10000, 50)
                ],
                'extrapolated_points': [
                    (1, 85),     # 外插点
                    (20000, 45)
                ],

                # 外插设置
                'low_freq_extrapolation_method': 'auto',  # 低频外插方式：'auto'(自动)或'manual'(手动指定斜率)
                'high_freq_extrapolation_method': 'auto',  # 高频外插方式：'auto'(自动)或'manual'(手动指定斜率)
                'low_freq_slope': -9.0,     # 低频用户指定斜率 (dB/倍频程)
                'high_freq_slope': -5.0,    # 高频用户指定斜率 (dB/倍频程)

                # 频段划分
                'frequency_bands': {
                    'low': (1, 20),      # 低频段：1-20Hz
                    'mid': (20, 500),    # 中频段：20-500Hz
                    'high': (500, 20000) # 高频段：500-20000Hz
                },

                # 各频段斜率因子（dB/倍频程）
                'slope_factors': {
                    'low': -9.0,    # 低频段斜率：-9dB/倍频程
                    'mid': -3.0,    # 中频段斜率：-3dB/倍频程
                    'high': -5.0    # 高频段斜率：-5dB/倍频程
                },

                # 仿真参数
                'duration': 5.0,            # 信号持续时间 (s)
                'filter_order': 16385       # 滤波器阶数
            },
            'propagation': {
                # 基本参数
                'name': 'default_simulation',      # 仿真名称
                'type': '2D',                      # 仿真类型，固定为2D

                # 环境参数
                'soundspeed': 1500,                # 恒定声速 (m/s)，当ssp_type为constant时使用
                'soundspeed_interp': 'spline',     # 声速插值方法: 'spline'或'linear'
                'bottom_soundspeed': 1600,         # 海底声速 (m/s)
                'bottom_density': 1600,            # 海底密度 (kg/m^3)
                'bottom_absorption': 0.1,          # 海底吸收系数 (dB/wavelength)
                'bottom_roughness': 0,             # 海底粗糙度 (m rms)
                'depth': 100,                      # 水深 (m)，当depth_type为flat时使用
                'depth_interp': 'linear',          # 水深插值方法: 'linear'或'curvilinear'

                # 声源和接收器参数
                'tx_depth': 50,                    # 声源深度 (m)
                'tx_directionality': None,         # 声源指向性数据 [(deg, dB)...]
                'rx_depth': 20,                    # 接收器深度 (m)
                'rx_range': 1000,                  # 接收器距离 (m)

                # 声线计算参数
                'min_angle': -80,                  # 最小发射角度 (deg)
                'max_angle': 80,                   # 最大发射角度 (deg)
                'nbeams': 0,                       # 声束数量，0表示自动设置
                'frequency': 1000,                 # 探测频率 (Hz)

                # 计算类型和模式
                'computation_type': 'environment', # 计算类型: 'environment', 'rays', 'eigenrays', 'arrivals', 'transmission_loss', 'broadband'
                'ssp_type': 'constant',            # 声速类型: 'constant'或'depth_dependent'
                'depth_type': 'flat',              # 水深类型: 'flat'或'range_dependent'
                'tx_directivity_enabled': False,   # 是否启用声源指向性
                'tl_mode': 'coherent',             # 传播损失计算模式: 'coherent', 'incoherent', 'semicoherent'

                # 数据集合
                'ssp_data': [],                    # 声速剖面数据 [(depth, speed), ...]
                'bathymetry_data': [],             # 水深数据 [(range, depth), ...]
                'directivity_data': [],            # 声源指向性数据 [(angle, gain), ...]

                # 网格设置
                'depth_grid': {                    # 深度网格设置（用于传播损失计算）
                    'start': 0,                    # 起始深度 (m)
                    'end': 100,                    # 结束深度 (m)
                    'step': 5                      # 深度步长 (m)
                },
                'range_grid': {                    # 距离网格设置（用于传播损失计算）
                    'start': 0,                    # 起始距离 (m)
                    'end': 1000,                   # 结束距离 (m)
                    'step': 50                     # 距离步长 (m)
                },
                'tl_range': {                      # 传播损失显示范围设置
                    'vmin': 40,                    # 最小值 (dB)
                    'vmax': 100                    # 最大值 (dB)
                },

                # 频率设置
                'freq_mode': 'range',              # 频率设置模式: 'range'或'list'
                'freq_start': 250,                 # 起始频率 (Hz)
                'freq_end': 4750,                  # 结束频率 (Hz)
                'freq_step': 500,                  # 频率步长 (Hz)
                'freq_list': [250, 750, 1250, 1750, 2250, 2750, 3250, 3750, 4250, 4750],  # 自定义频率列表 (Hz)

                # 系统设置
                'max_workers': 4,                  # 最大并行进程数
                'output_dir': './channel_data',    # 输出目录
                'debug_mode': False,               # 调试模式（保留临时文件）

                # 阵列设置
                'array_elements_data': [           # 阵元位置数据 [(距离, 深度), ...]
                    (0, 10),
                    (0, 20),
                    (0, 30),
                    (0, 40),
                    (0, 50),
                    (0, 60),
                    (0, 70),
                    (0, 80)
                ]
            },
            'integrated': {
                # 信道数据参数
                'channel_data_dir': '',           # 信道数据目录路径
                'selected_element_index': 0,      # 选中的阵元索引
                'selected_frequency': 0,          # 选中的频率

                # 计算类型
                'computation_type': 'channel_data_analysis',  # 计算类型：'channel_data_analysis', 'signal_processing', 'array_processing'

                # 信号处理参数
                'use_all_arrivals': True,         # 是否使用全部到达声线
                'truncation_time': 0.0,           # 截断时间 (s)，当use_all_arrivals为False时使用
                'selected_ship_noise_result': '', # 选中的船舶辐射噪声结果
                'min_duration_suggestion': 0.0,   # 建议的最小源信号时长 (s)
                'recommended_duration': 0.0,      # 推荐的源信号时长 (s)
                'add_ambient_noise': False,       # 是否叠加海洋环境噪声

                # 信号区间选择
                'signal_selection': {
                    'start_time': 0.0,            # 信号选择起始时间 (s)
                    'end_time': 5.0,              # 信号选择结束时间 (s)
                },

                # 频谱分析参数
                'spectrum_analysis': {
                    'nperseg': 44100,             # 每段长度，默认为fs以获得1Hz分辨率
                    'noverlap': 0,                # 重叠长度
                    'window': 'hann',             # 窗函数类型
                    'scaling': 'density'          # 缩放类型：'density'或'spectrum'
                },

                # 指向性计算参数
                'beamforming': {
                    'frequency_range': [100, 1000],  # 频率范围 (Hz)
                    'angle_range': [-90, 90],     # 角度范围 (度)
                    'angle_step': 1,              # 角度步长 (度)
                    'sound_speed': 1500           # 声速 (m/s)
                },

                # 互功率谱计算参数
                'cross_spectral': {
                    'element_pair': [0, 1],       # 阵元对索引
                    'nperseg': 44100,             # 每段长度
                    'noverlap': 0,                # 重叠长度
                    'window': 'hann',             # 窗函数类型
                    'scaling': 'density',         # 缩放类型
                    'calculate_coherence': True   # 是否计算相干函数
                }
            }
        }

    @staticmethod
    def results():
        """
        结果结构

        存储在SimulationDataManager._results中

        Returns:
            dict: 结果字典示例
        """
        import numpy as np

        # 创建示例数组
        n_samples = 1000
        t = np.linspace(0, 5, n_samples)
        f = np.linspace(0, 5000, n_samples//2)

        return {
            'ship_noise': {
                # 时间数据
                'time_data': t,                      # 时间数组 (s)

                # 连续谱数据
                'continuous_signal': np.sin(2*np.pi*100*t),  # 连续谱时域信号
                'continuous_freqs': f,               # 连续谱频率数组 (Hz)
                'continuous_psd_db': np.random.rand(n_samples//2) * 20,  # 连续谱功率谱密度 (dB/Hz)

                # 线谱数据
                'line_signal': np.sin(2*np.pi*200*t),  # 线谱时域信号
                'line_freqs': f,                     # 线谱频率数组 (Hz)
                'line_psd_db': np.random.rand(n_samples//2) * 20,  # 线谱功率谱密度 (dB/Hz)

                # 调制谱数据
                'modulation_signal': np.sin(2*np.pi*8*t) + np.sin(2*np.pi*32*t),  # 调制信号
                'modulated_continuous_signal': np.sin(2*np.pi*100*t) * (1 + 0.05*np.sin(2*np.pi*8*t) + 0.3*np.sin(2*np.pi*32*t)),  # 调制后的连续谱信号
                'modulated_continuous_psd_db': np.random.rand(n_samples//2) * 20,  # 调制后的连续谱功率谱密度 (dB/Hz)

                # 总信号数据
                'total_signal': np.sin(2*np.pi*100*t) + np.sin(2*np.pi*200*t),  # 总信号时域信号
                'total_freqs': f,                    # 总信号频率数组 (Hz)
                'total_psd_db': np.random.rand(n_samples//2) * 20,   # 总信号功率谱密度 (dB/Hz)

                # 元数据
                'metadata': {
                    'fs': 44100,                     # 采样率 (Hz)
                    'duration': 5.0,                 # 持续时间 (s)
                    'filter_order': 16385,           # 滤波器阶数
                    'generation_time': '2023-05-15T10:30:45.123456',  # 生成时间
                    'parameters': {                  # 生成参数的副本
                        'f0': 250,                   # 连续谱峰值频率 (Hz)
                        'sl0': 130.0,                # 连续谱峰值电平 (dB)
                        'a1': 3.0,                   # 连续谱上升斜率 (dB/倍频程)
                        'a2': -4.2,                  # 连续谱下降斜率 (dB/倍频程)
                        'include_line_spectrum': True,  # 是否包含线谱
                        'include_modulation': True,  # 是否包含调制谱
                    }
                }
            },
            'ambient_noise': {
                # 时间和信号数据
                'time_data': t,                      # 时间数组 (s)
                'ambient_signal': np.random.randn(n_samples),  # 环境噪声时域信号
                'ambient_freqs': f,                  # 环境噪声频率数组 (Hz)
                'ambient_psd_db': np.random.rand(n_samples//2) * 20,  # 环境噪声功率谱密度 (dB/Hz)

                # 滤波器数据
                'fir_coeffs': np.random.rand(16385),  # FIR滤波器系数
                'scaling_factor': 0.0123,            # 缩放因子

                # 元数据
                'metadata': {
                    'fs': 44100,                     # 采样率 (Hz)
                    'duration': 5.0,                 # 持续时间 (s)
                    'filter_order': 16385,           # 滤波器阶数
                    'generation_time': '2023-05-15T10:30:45.123456',  # 生成时间
                    'parameters': {                  # 生成参数的副本
                        'user_defined_points': [
                            (10, 80),                # 频率(Hz), 噪声级(dB re 1μPa²/Hz)
                            (100, 70),
                            (1000, 60),
                            (10000, 50)
                        ],
                        'low_freq_slope': -9.0,      # 低频用户指定斜率 (dB/倍频程)
                        'high_freq_slope': -5.0,     # 高频用户指定斜率 (dB/倍频程)
                    }
                }
            },
            'propagation': {
                # 声传播结果
                'environment': {
                    # 环境参数，包括声速剖面、水深等
                    'sound_speed_profile': [(0, 1500), (100, 1480)],  # 声速剖面 [(深度, 声速), ...]
                    'water_depth': 100,  # 水深 (m)
                    'bottom_properties': {
                        'sound_speed': 1600,  # 海底声速 (m/s)
                        'density': 1.8,  # 海底密度 (g/cm^3)
                        'attenuation': 0.5  # 海底衰减系数 (dB/wavelength)
                    },
                    'surface_properties': {
                        'sound_speed': 340,  # 空气声速 (m/s)
                        'density': 0.001,  # 空气密度 (g/cm^3)
                        'attenuation': 0.0  # 空气衰减系数 (dB/wavelength)
                    }
                },
                'transmission_loss': pd.DataFrame(np.random.rand(21, 21)),  # 传播损失数据，pandas.DataFrame
                'rays': np.random.rand(10, 100, 2),  # 声线数据，numpy.ndarray
                'arrivals': {
                    # 到达结构数据
                    'arrival_times': np.random.rand(10),  # 到达时间 (s)
                    'amplitudes': np.random.rand(10),  # 振幅
                    'angles': np.random.rand(10),  # 到达角度 (度)
                    'num_surface_bounces': np.random.randint(0, 5, 10),  # 表面反射次数
                    'num_bottom_bounces': np.random.randint(0, 5, 10)  # 底部反射次数
                },
                'channel_preparation': {
                    # 信道数据制备结果
                    'output_folder': './channel_data/20230515_103045',  # 输出文件夹路径
                    'frequencies': [250, 750, 1250, 1750, 2250, 2750, 3250, 3750, 4250, 4750],  # 计算的频率列表 (Hz)
                    'array_elements': [  # 阵元位置数据 [(距离, 深度), ...]
                        (0, 10),
                        (0, 20),
                        (0, 30),
                        (0, 40),
                        (0, 50),
                        (0, 60),
                        (0, 70),
                        (0, 80)
                    ],
                    'timestamp': '2023-05-15T10:30:45.123456'  # 生成时间
                },
                'broadband_frequencies': [250, 750, 1250, 1750, 2250, 2750, 3250, 3750, 4250, 4750],  # 宽带计算的频率列表 (Hz)
                'broadband_arrivals': {  # 宽带计算的到达结构，键为频率，值为对应的到达结构DataFrame
                    250: pd.DataFrame({
                        'arrival_time': [0.1, 0.2, 0.3],
                        'arrival_amplitude': [0.5+0.1j, 0.4+0.2j, 0.3+0.3j],
                        'arrival_phase': [0.1, 0.2, 0.3],
                        'surface_bounces': [0, 1, 2],
                        'bottom_bounces': [0, 1, 2]
                    }),
                    # 其他频率的到达结构...
                },
                # 元数据
                'metadata': {
                    'fs': 44100,  # 采样率 (Hz)
                    'duration': 5.0,  # 持续时间 (s)
                    'computation_type': 'transmission_loss',  # 计算类型
                    'tl_mode': 'coherent',  # 传播损失计算模式
                    'tl_range': {
                        'vmin': 40,  # 最小值 (dB)
                        'vmax': 100  # 最大值 (dB)
                    },
                    'generation_time': '2023-05-15T10:30:45.123456'  # 生成时间
                }
            },
            'integrated': {
                # 综合仿真结果
                'channel_data': {
                    # 信道数据分析结果
                    'meta': {
                        # meta.json的内容，包含环境参数等
                        'environment': {
                            'tx_depth': 50.0,  # 声源深度 (m)
                            'soundspeed': 1500.0,  # 声速 (m/s)
                            'depth': 100.0,  # 水深 (m)
                            'bottom_soundspeed': 1600.0,  # 海底声速 (m/s)
                            'bottom_density': 1600.0,  # 海底密度 (kg/m^3)
                            'bottom_absorption': 0.1,  # 海底吸收系数 (dB/wavelength)
                            # 其他环境参数...
                        },
                        'generation_time': '2023-05-15T10:30:45.123456'  # 生成时间
                    },
                    'frequencies': [250, 750, 1250, 1750, 2250, 2750, 3250, 3750, 4250, 4750],  # 频率列表 (Hz)
                    'array_elements': [  # 阵元位置列表 [(距离, 深度), ...]
                        (0, 10),
                        (0, 20),
                        (0, 30),
                        (0, 40),
                        (0, 50),
                        (0, 60),
                        (0, 70),
                        (0, 80)
                    ],
                    't_first_global': 0.6723,  # 全局最早到达时间 (s)
                    't_last_global': 0.8945,  # 全局最晚到达时间 (s)
                    'arrivals_data': {  # 按频率和阵元索引组织的到达结构数据
                        250: {  # 频率 (Hz)
                            0: pd.DataFrame({  # 阵元索引
                                'time_of_arrival': [0.6723, 0.7123, 0.7523],  # 到达时间 (s)
                                'arrival_amplitude': [0.5+0.1j, 0.4+0.2j, 0.3+0.3j],  # 振幅
                                'angle_of_departure': [-10.5, -5.2, 0.0],  # 出射角 (°)
                                'angle_of_arrival': [15.3, 10.1, 5.0],  # 到达角 (°)
                                'surface_bounces': [0, 1, 2],  # 海面反射次数
                                'bottom_bounces': [0, 1, 2]  # 海底反射次数
                            }),
                            # 其他阵元的到达结构...
                        },
                        # 其他频率的到达结构...
                    }
                },
                'signal_processing': {
                    # 信号处理结果
                    'source_signal': {
                        'metadata': {
                            'fs': 44100,                     # 采样率 (Hz)
                            'duration': 5.0,                 # 持续时间 (s)
                            'name': 'ship_noise_result_1',   # 源信号名称
                            'generation_time': '2023-05-15T10:30:45.123456'  # 生成时间
                        },
                        'time_data': np.linspace(0, 5, n_samples),  # 时间数组 (s)
                        'signal': np.sin(2*np.pi*100*t),     # 源信号
                    },
                    't_first_global': 0.6723,                # 全局最早到达时间 (s)
                    't_last_global': 0.8945,                 # 全局最晚到达时间 (s)
                    't_last_global_eff': 0.7945,             # 有效的全局最晚到达时间 (s)，考虑截断时间
                    'max_relative_delay': 0.2222,            # 最大相对时延 (s)
                    'impulse_response_length': 9801,         # 冲击响应长度 (samples)

                    # 接收信号（无海洋环境噪声）
                    'received_signals': {
                        # 按阵元索引组织的接收信号
                        0: {
                            'time_data': np.linspace(0.6723, 5.6723, n_samples),  # 物理时间数组 (s)
                            'signal': np.sin(2*np.pi*100*t),  # 接收信号
                        },
                        # 其他阵元的接收信号...
                    },

                    # 接收信号（有海洋环境噪声）
                    'received_signals_with_noise': {
                        # 按阵元索引组织的接收信号（含噪声）
                        0: {
                            'time_data': np.linspace(0.6723, 5.6723, n_samples),  # 物理时间数组 (s)
                            'signal': np.sin(2*np.pi*100*t) + 0.1*np.random.randn(n_samples),  # 接收信号（含噪声）
                        },
                        # 其他阵元的接收信号...
                    },

                    # 是否已叠加海洋环境噪声
                    'ambient_noise_added': True,

                    # 频谱分析结果
                    'spectrum': {
                        0: {  # 阵元索引
                            'freqs': f,  # 频率数组 (Hz)
                            'psd_db': np.random.rand(n_samples//2) * 20,  # 功率谱密度 (dB/Hz)
                            'with_noise': False  # 是否包含噪声
                        },
                        1: {  # 阵元索引
                            'freqs': f,  # 频率数组 (Hz)
                            'psd_db': np.random.rand(n_samples//2) * 20,  # 功率谱密度 (dB/Hz)
                            'with_noise': True  # 是否包含噪声
                        },
                        # 其他阵元的频谱分析结果...
                    }
                },
                'array_processing': {
                    # 指向性计算结果
                    'beamforming': {
                        'angles': np.linspace(-90, 90, 181),  # 角度数组 (°)
                        'beam_pattern': np.random.rand(181),  # 波束方向图
                        'beam_pattern_db': 10*np.log10(np.random.rand(181)),  # 波束方向图 (dB)
                        'normalized_beam_pattern': np.random.rand(181),  # 归一化波束方向图
                        'normalized_beam_pattern_db': 10*np.log10(np.random.rand(181)),  # 归一化波束方向图 (dB)
                        'with_noise': False,  # 是否包含噪声
                        'frequency_range': [100, 1000],  # 频率范围 (Hz)
                        'selected_elements': [0, 1, 2, 3, 4, 5, 6, 7]  # 选中的阵元索引
                    },

                    # 互功率谱计算结果
                    'cross_spectral_density': {
                        'element_pair': [0, 1],  # 阵元对索引
                        'freqs': f,  # 频率数组 (Hz)
                        'csd_magnitude': np.random.rand(n_samples//2),  # 互功率谱密度模值
                        'csd_magnitude_db': 10*np.log10(np.random.rand(n_samples//2)),  # 互功率谱密度模值 (dB)
                        'csd_phase': np.random.rand(n_samples//2) * 2*np.pi - np.pi,  # 互功率谱密度相位 (rad)
                        'csd_phase_unwrapped': np.random.rand(n_samples//2) * 10*np.pi,  # 展开的互功率谱密度相位 (rad)
                        'coherence': np.random.rand(n_samples//2),  # 相干函数
                        'with_noise': True  # 是否包含噪声
                    }
                }
            }
        }


class WenzCurveStructures:
    """
    Wenz曲线数据结构

    记录Wenz曲线相关的数据结构
    """

    @staticmethod
    def wenz_curves_json():
        """
        Wenz曲线JSON文件结构

        存储在data/wenz_curves.json中

        Returns:
            dict: Wenz曲线JSON文件结构示例
        """
        return {
            "metadata": {
                "description": "Wenz curves data with interpolation",
                "units": {
                    "frequency": "Hz",
                    "power_level": "dB re 1 μPa^2/Hz"
                },
                "processing": {
                    "decimal_places": 4,
                    "interpolation_points": 1000,
                    "processing_time": 0.115
                },
                "stats": {
                    "original": {
                        "wind-dependent-0.5": {
                            "point_count": 33,
                            "freq_min": 244.9139,
                            "freq_max": 18842.9094,
                            "power_min": 22.539,
                            "power_max": 47.1431,
                            "freq_range": 18597.9955,
                            "power_range": 24.6041
                        },
                        # 其他曲线的统计信息...
                    },
                    "interpolated": {
                        "wind-dependent-0.5": {
                            "point_count": 944,
                            "freq_min": 244.9139,
                            "freq_max": 18842.9094,
                            "power_min": 22.539,
                            "power_max": 47.1431,
                            "freq_range": 18597.9955,
                            "power_range": 24.6041
                        },
                        # 其他曲线的统计信息...
                    }
                }
            },
            "curves": {
                "wind-dependent-0.5": {
                    "original": {
                        "frequencies": [244.9139, 279.6783, 317.7539, 361.0132, 410.1618, 466.0015, 529.4433],
                        "power_levels": [44.6027, 45.301, 45.8805, 46.3193, 46.7053, 46.9858, 47.1431]
                    },
                    "interpolated": {
                        "frequencies": [244.9139, 246.0374, 247.1661, 248.3, 249.4391, 250.5834, 251.7329],
                        "power_levels": [44.6027, 44.6206, 44.6385, 44.6564, 44.6743, 44.6922, 44.7101]
                    }
                },
                # 其他曲线数据...
            },
            "band_areas": {
                "usual-traffic-noise-shallow": {
                    "common_frequencies": [7.061, 7.1234, 7.1863, 7.2497, 7.3136, 7.3781, 7.4431],
                    "top_powers": [80.9372, 80.8954, 80.8535, 80.8115, 80.7694, 80.7272, 80.6849],
                    "bottom_powers": [71.5524, 71.5106, 71.4687, 71.4267, 71.3846, 71.3424, 71.3001]
                },
                # 其他带状区域数据...
            }
        }

    @staticmethod
    def wenz_style_config():
        """
        Wenz曲线样式配置结构

        存储在data/wenz_style_config.json中

        Returns:
            dict: Wenz曲线样式配置结构示例
        """
        return {
            "plot_style": {
                "title": "Wenz Curves",
                "title_fontsize": 14,
                "title_fontweight": "bold",
                "xlabel": "频率 (Hz)",
                "xlabel_fontsize": 12,
                "ylabel": "噪声谱级 (dB re 1 μPa^2/Hz)",
                "ylabel_fontsize": 12,
                "min_freq": 1,
                "max_freq": 100000,
                "min_power": 0,
                "max_power": 140,
                "grid_major_alpha": 0.7,
                "grid_major_linestyle": "--",
                "grid_minor_alpha": 0.4,
                "grid_minor_linestyle": ":",
                "tick_labelsize": 10,
                "legend_loc": "upper right",
                "legend_fontsize": 10,
                "figsize": [12, 8],
                "dpi": 300
            },
            "band_areas": [
                {
                    "name": "low-frequency-very-shallow-wind",
                    "top": "low-frequency-very-shallow-wind-top",
                    "bottom": "low-frequency-very-shallow-wind-bottom",
                    "color": "#C7AD7D",
                    "alpha": 0.5,
                    "label": "极浅海域风生噪声"
                },
                {
                    "name": "usual-traffic-noise-shallow",
                    "top": "usual-traffic-noise-shallow-top",
                    "bottom": "usual-traffic-noise-shallow-bottom",
                    "color": "red",
                    "alpha": 0.5,
                    "label": "浅海交通噪声"
                },
                {
                    "name": "usual-traffic-noise-deep",
                    "top": "usual-traffic-noise-deep-top",
                    "bottom": "usual-traffic-noise-deep-bottom",
                    "color": "#FF69B4",
                    "alpha": 0.7,
                    "label": "深海交通噪声"
                }
            ],
            "important_curves": {
                "thermal-noise": {
                    "color": "gold",
                    "label": "热噪声",
                    "linestyle": "--",
                    "linewidth": 2.0,
                    "alpha": 1.0
                },
                "heavy-precipitation": {
                    "color": "purple",
                    "label": "强降水",
                    "linestyle": "--",
                    "linewidth": 2.0,
                    "alpha": 1.0
                },
                "earth-quakes-and-explosions": {
                    "color": "blue",
                    "label": "地震和爆炸",
                    "linestyle": "-.",
                    "linewidth": 2.0,
                    "alpha": 1.0
                },
                "limits-of-prevailing-noise-top": {
                    "color": "black",
                    "label": "主要噪声范围",
                    "linestyle": "-",
                    "linewidth": 1.5,
                    "alpha": 1.0,
                    "is_limit_pair": "true",
                    "pair_with": "limits-of-prevailing-noise-bottom"
                },
                "limits-of-prevailing-noise-bottom": {
                    "color": "black",
                    "linestyle": "-",
                    "linewidth": 1.5,
                    "alpha": 1.0,
                    "is_limit_pair": "true",
                    "show_in_legend": "false"
                }
            },
            "sea_state_curves": {
                "color_map": "Blues",
                "color_range": [0.5, 1.0],
                "label_prefix": "海况 ",
                "linestyle": "-",
                "linewidth": 1.5,
                "alpha": 1.0
            }
        }

    @staticmethod
    def ambient_noise_user_points():
        """
        环境噪声用户定义点结构

        用于OceanAmbientNoise类中的user_defined_points属性

        Returns:
            list: 用户定义点列表示例
        """
        return [
            (10, 80),    # 频率(Hz), 噪声级(dB re 1μPa²/Hz)
            (100, 70),
            (1000, 60),
            (10000, 50)
        ]


class ProjectStructures:
    """
    项目文件结构

    记录项目文件相关的数据结构
    """

    @staticmethod
    def project_file():
        """
        项目文件结构

        项目使用JSON+NPZ混合格式保存：
        - JSON文件存储元数据和参数
        - NPZ文件存储大型数组数据

        Returns:
            dict: 项目文件结构示例
        """
        return {
            'global_params': {
                'fs': 44100,        # 采样率 (Hz)
            },
            'parameters': {
                'ship_noise': {
                    # 船舶辐射噪声参数
                    'f0': 250,
                    'sl0': 130.0,
                    # 其他参数...
                },
                'ambient_noise': {
                    # 海洋环境噪声参数
                    'user_defined_points': [
                        (10, 80),
                        (100, 70),
                        # 其他点...
                    ],
                    # 其他参数...
                },
                'propagation': {
                    # 声传播参数
                    'ssp_type': 'constant',
                    'sound_speed': 1500,
                    'ssp_data': [],
                    'depth_type': 'flat',
                    'water_depth': 100,
                    'bathymetry_data': [],
                    'tx_depth': 50,
                    'rx_depth': 50,
                    'rx_range': 1000,
                    'tx_directivity_enabled': False,
                    'directivity_data': [],
                    'tl_mode': 'coherent',
                    'depth_grid': {
                        'start': 0,
                        'end': 100,
                        'step': 5
                    },
                    'range_grid': {
                        'start': 0,
                        'end': 1000,
                        'step': 50
                    },
                    'tl_range': {
                        'vmin': 40,
                        'vmax': 100
                    },
                    'freq_mode': 'range',
                    'freq_start': 250,
                    'freq_end': 4750,
                    'freq_step': 500,
                    'freq_list': '250 750 1250 1750 2250 2750 3250 3750 4250 4750',
                    'array_elements_data': [
                        [0, 10],
                        [0, 20],
                        [0, 30],
                        [0, 40],
                        [0, 50],
                        [0, 60],
                        [0, 70],
                        [0, 80]
                    ]
                },
                'integrated': {
                    # 信道数据参数
                    'channel_data_dir': './channel_data/20230515_103045',  # 信道数据目录路径
                    'selected_element_index': 0,  # 选中的阵元索引
                    'selected_frequency': 250,  # 选中的频率

                    # 计算类型
                    'computation_type': 'channel_data_analysis',  # 计算类型：'channel_data_analysis', 'signal_processing', 'array_processing'

                    # 信号处理参数
                    'use_all_arrivals': True,  # 是否使用全部到达声线
                    'truncation_time': 0.0,  # 截断时间 (s)，当use_all_arrivals为False时使用
                    'add_ambient_noise': True,  # 是否叠加海洋环境噪声

                    # 信号区间选择
                    'signal_selection': {
                        'start_time': 0.0,  # 信号选择起始时间 (s)
                        'end_time': 5.0,  # 信号选择结束时间 (s)
                    },

                    # 频谱分析参数
                    'spectrum_analysis': {
                        'nperseg': 44100,  # 每段长度，默认为fs以获得1Hz分辨率
                        'noverlap': 0,  # 重叠长度
                        'window': 'hann',  # 窗函数类型
                        'scaling': 'density'  # 缩放类型：'density'或'spectrum'
                    },

                    # 指向性计算参数
                    'beamforming': {
                        'frequency_range': [100, 1000],  # 频率范围 (Hz)
                        'angle_range': [-90, 90],  # 角度范围 (度)
                        'angle_step': 1,  # 角度步长 (度)
                        'sound_speed': 1500  # 声速 (m/s)
                    },

                    # 互功率谱计算参数
                    'cross_spectral': {
                        'element_pair': [0, 1],  # 阵元对索引
                        'nperseg': 44100,  # 每段长度
                        'noverlap': 0,  # 重叠长度
                        'window': 'hann',  # 窗函数类型
                        'scaling': 'density',  # 缩放类型
                        'calculate_coherence': True  # 是否计算相干函数
                    }
                },
                # 其他模块参数...
            },
            'results_metadata': {
                'ship_noise': {
                    'time_data': {
                        'is_array': True,
                        'shape': [220500],
                        'dtype': 'float32',
                        'array_key': 'ship_noise_time_data'
                    },
                    'total_signal': {
                        'is_array': True,
                        'shape': [220500],
                        'dtype': 'float32',
                        'array_key': 'ship_noise_total_signal'
                    },
                    'small_data': {
                        'is_array': False,
                        'value': 42.0  # 小型数据直接存储在JSON中
                    },
                    'metadata': {
                        'is_array': False,
                        'value': {
                            'fs': 44100,
                            'duration': 5.0,
                            'filter_order': 16385,
                            'generation_time': '2023-05-15T10:30:45.123456'
                        }
                    }
                    # 其他结果元数据...
                },
                'ambient_noise': {
                    'time_data': {
                        'is_array': True,
                        'shape': [220500],
                        'dtype': 'float32',
                        'array_key': 'ambient_noise_time_data'
                    },
                    'ambient_signal': {
                        'is_array': True,
                        'shape': [220500],
                        'dtype': 'float32',
                        'array_key': 'ambient_noise_ambient_signal'
                    },
                    'metadata': {
                        'is_array': False,
                        'value': {
                            'fs': 44100,
                            'duration': 5.0,
                            'filter_order': 16385,
                            'generation_time': '2023-05-15T10:30:45.123456'
                        }
                    }
                },
                'propagation': {
                    'environment': {
                        'is_array': False,
                        'value': {
                            # 环境参数，包括声速剖面、水深等
                        }
                    },
                    'transmission_loss': {
                        'is_array': True,
                        'shape': [21, 21],  # 示例形状，实际取决于网格设置
                        'dtype': 'complex128',
                        'array_key': 'propagation_transmission_loss'
                    },
                    'rays': {
                        'is_array': True,
                        'shape': [10, 100, 2],  # 示例形状，实际取决于声线数量和点数
                        'dtype': 'float32',
                        'array_key': 'propagation_rays'
                    },
                    'arrivals': {
                        'is_array': False,
                        'value': {
                            # 到达结构数据
                        }
                    },
                    'channel_preparation': {
                        'is_array': False,
                        'value': {
                            'output_folder': './channel_data/20230515_103045',
                            'frequencies': [250, 750, 1250, 1750, 2250, 2750, 3250, 3750, 4250, 4750],
                            'array_elements': [
                                [0, 10],
                                [0, 20],
                                [0, 30],
                                [0, 40],
                                [0, 50],
                                [0, 60],
                                [0, 70],
                                [0, 80]
                            ],
                            'timestamp': '2023-05-15T10:30:45.123456'
                        }
                    },
                    'broadband_frequencies': {
                        'is_array': True,
                        'shape': [10],
                        'dtype': 'float64',
                        'array_key': 'propagation_broadband_frequencies'
                    },
                    'metadata': {
                        'is_array': False,
                        'value': {
                            'fs': 44100,
                            'duration': 5.0,
                            'computation_type': 'transmission_loss',
                            'tl_mode': 'coherent',
                            'tl_range': {
                                'vmin': 40,
                                'vmax': 100
                            },
                            'generation_time': '2023-05-15T10:30:45.123456'
                        }
                    }
                },
                'integrated': {
                    'channel_data_dir': {
                        'is_array': False,
                        'value': './channel_data/20230515_103045'  # 信道数据目录路径
                    },
                    # 注意：信道数据不会被保存到项目文件中，只保存目录路径
                    # 在项目加载时，会根据目录路径重新加载信道数据

                    # 信号处理结果
                    'signal_processing': {
                        'is_array': False,
                        'value': {
                            'source_signal': {
                                'metadata': {
                                    'fs': 44100,
                                    'duration': 5.0,
                                    'name': 'ship_noise_result_1'
                                }
                            },
                            't_first_global': 0.6723,
                            't_last_global': 0.8945,
                            't_last_global_eff': 0.7945,
                            'ambient_noise_added': True
                        }
                    },

                    # 接收信号（无噪声）
                    'received_signals': {
                        'is_array': True,
                        'shape': [8, 220500],  # 8个阵元，每个阵元220500个采样点
                        'dtype': 'float32',
                        'array_key': 'integrated_received_signals'
                    },

                    # 接收信号（有噪声）
                    'received_signals_with_noise': {
                        'is_array': True,
                        'shape': [8, 220500],  # 8个阵元，每个阵元220500个采样点
                        'dtype': 'float32',
                        'array_key': 'integrated_received_signals_with_noise'
                    },

                    # 指向性计算结果
                    'beamforming': {
                        'is_array': False,
                        'value': {
                            'angles': [-90, -89, ..., 89, 90],
                            'beam_pattern_db': [-40, -38, ..., -35, -40],
                            'frequency_range': [100, 1000],
                            'with_noise': False
                        }
                    },

                    # 互功率谱计算结果
                    'cross_spectral_density': {
                        'is_array': False,
                        'value': {
                            'element_pair': [0, 1],
                            'csd_magnitude_db': [-80, -75, ..., -70],
                            'csd_phase': [0.1, 0.2, ..., 0.3],
                            'coherence': [0.8, 0.85, ..., 0.9],
                            'with_noise': True
                        }
                    }
                },
                # 其他模块结果元数据...
            },
            'npz_file': 'project_name_arrays.npz'  # NPZ文件名
        }


def get_data_structure_summary():
    """
    获取数据结构摘要

    Returns:
        str: 数据结构摘要
    """
    return """
数据结构摘要：

1. 数据管理器存储的数据结构
   - 全局参数 (_global_params)：存储项目级别的参数，如采样率(fs)
   - 模块参数 (_parameters)：按模块分类存储的参数字典，包含模块特定参数如持续时间(duration)
   - 模块结果 (_results)：按模块分类存储的结果字典，包含时域信号和频域分析结果

2. 各模块数据结构
   - 船舶辐射噪声：包含连续谱、线谱、调制谱参数和结果，以及工况参数计算
   - 海洋环境噪声：包含用户定义点、外插设置、频段划分和滤波器参数
   - 声传播：包含环境参数、声源和接收器参数、计算类型和模式、网格设置和频率设置
   - 综合仿真：包含信道数据、信号处理、指向性计算和互功率谱计算相关参数和结果

3. Wenz曲线数据结构
   - Wenz曲线JSON文件：存储原始和插值后的曲线数据，以及带状区域数据
   - Wenz曲线样式配置：定义曲线绘制的样式，包括颜色、线型、标签等
   - 环境噪声用户定义点：用户在Wenz曲线图上选择的点，用于定义自定义噪声谱

4. 项目文件结构
   - 全局参数：项目级别的参数
   - 模块参数：各模块的参数
   - 结果元数据：描述结果数据的结构和存储位置
   - 大型数组数据：存储在单独的NPZ文件中

注意：
- 全局参数fs应统一使用，确保不同模块生成的信号具有相同的采样率
- 持续时间(duration)参数在各模块中单独设置，允许不同模块生成不同长度的信号
- 大型数组数据（如时域信号）在内存中使用float64计算，但存储时转换为float32以节省空间
- 项目文件采用JSON+NPZ混合格式，JSON存储参数和元数据，NPZ存储大型数组
- Wenz曲线数据使用预处理的JSON文件，避免在运行时进行大量插值计算
- 综合仿真模块管理两组信号（有/无海洋环境噪声），后处理（频谱计算、指向性计算、互功率谱计算）根据用户选择使用相应的信号组
- 信道数据不会被保存到项目文件中，只保存目录路径，在项目加载时根据目录路径重新加载
"""