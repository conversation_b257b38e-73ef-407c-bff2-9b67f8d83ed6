# 项目架构更新文档

本文档描述了对水声噪声仿真系统架构的最新更新，特别是关于项目状态管理、数据流和组件关系的改进。

## 1. 项目状态管理优化

### 1.1 简化修改状态判断逻辑

**原有实现**：
- 使用复杂的状态比较逻辑判断项目是否被修改
- 通过`_create_state_snapshot()`创建状态快照，然后与保存时的状态进行比较
- 比较过程复杂，容易出错

**优化后**：
- 简化了`ProjectManager.is_modified()`方法，直接返回内部状态标志
- 移除了不再需要的`_create_state_snapshot()`和`_has_any_data()`等复杂函数
- 添加了`reset_modified_state()`方法，用于重置修改状态
- 统一了数据变更处理函数，使用同一个`_on_data_changed()`方法处理所有类型的数据变更

### 1.2 优化保存状态管理

**原有实现**：
- 保存项目时创建完整的状态快照
- 状态快照包含所有参数和结果键的副本

**优化后**：
- 不再创建和存储状态快照，简化了内存使用
- 使用简单的布尔标志跟踪修改状态
- 在关键操作（保存、加载、新建项目）后重置修改状态

## 2. 数据流优化

### 2.1 细粒度参数更新

**原有实现**：
- 每次参数变化都需要获取和设置整个参数字典
- UI控件值变化时，通过信号通知MainWindow，再由MainWindow同步到DataManager

**优化后**：
- 添加了`update_parameter(module, param_key, value)`方法，支持单个参数的更新
- 添加了`update_nested_parameter(module, param_path, value)`方法，支持嵌套参数的更新
- UI控件直接持有DataManager引用，参数变化时直接更新DataManager

### 2.2 数据流向简化

**原有实现**：
```
UI控件 -> MainWindow -> DataManager -> ProjectManager -> UI
```

**优化后**：
```
UI控件 -> DataManager -> ProjectManager -> UI
```

- UI控件直接更新DataManager，移除了MainWindow作为中间层
- 减少了不必要的数据复制和传递
- 提高了参数更新的效率

### 2.3 仿真请求流程简化

**原有实现**：
- UI标签页（如ShipNoiseTab）在用户点击"开始仿真"按钮时，收集所有参数
- 通过simulation_requested信号传递完整的参数字典
- SimulationUIManager接收信号，将参数设置到DataManager
- 然后调用SimulationController执行仿真

**优化后**：
- 参数已通过UI控件值变化事件实时更新到DataManager
- UI标签页在用户点击"开始仿真"按钮时，只进行参数验证
- 通过simulation_requested信号（不再携带参数）请求仿真
- SimulationUIManager接收信号，直接调用SimulationController执行仿真
- SimulationController从DataManager获取最新参数

**优势**：
- 简化了仿真请求流程，移除了不必要的参数传递
- 确保了DataManager作为唯一的数据源
- 减少了潜在的数据不一致问题
- 使代码更加清晰和一致

## 3. 组件关系调整

### 3.1 组件创建顺序和依赖注入

**原有实现**：
- MainWindow创建SimulationController
- SimulationController创建DataManager和ProjectManager
- ProjectUIManager通过SimulationController间接访问ProjectManager

**优化后**：
- MainWindow直接创建DataManager和ProjectManager
- MainWindow将DataManager和ProjectManager注入给SimulationController和UI组件
- ProjectUIManager直接持有ProjectManager引用，不再通过SimulationController间接调用

### 3.2 SimulationController职责简化

**原有实现**：
- SimulationController负责仿真控制和项目管理
- 包含大量项目管理相关的转发方法

**优化后**：
- SimulationController只负责仿真控制
- 移除了项目管理相关的转发方法
- 接受DataManager作为构造参数，不再创建DataManager

## 4. 功能增强

### 4.1 添加"另存为"功能

- 在工具栏中添加了"另存为"按钮
- 实现了完整的"另存为"功能，允许用户将项目保存为新文件
- 确保"另存为"操作后正确更新项目文件路径和修改状态

### 4.2 参数双向绑定

- 实现了UI控件和DataManager的双向绑定
- 控件值变化时自动更新DataManager
- DataManager数据变化时通知ProjectManager更新状态

## 5. 代码优化

### 5.1 移除冗余代码

- 删除了不再需要的函数和方法
- 简化了状态管理相关的代码
- 移除了重复的信号连接和处理逻辑

### 5.2 改进错误处理

- 在关键操作中添加了更好的错误处理
- 确保在异常情况下系统状态保持一致

## 6. 总结

通过这次架构优化，系统的数据流向更加清晰，状态管理更加简单，组件间的关系更加合理。主要改进包括：

1. **简化数据流向**：UI控件直接更新DataManager，减少了中间层
2. **实现双向数据绑定**：控件值变化时自动同步到DataManager
3. **简化修改状态判断**：使用简单的布尔标志跟踪修改状态
4. **简化仿真请求流程**：移除了不必要的参数传递，确保DataManager作为唯一数据源
5. **调整组件关系**：更合理的依赖注入和组件创建顺序
6. **添加新功能**：实现"另存为"功能，增强用户体验
7. **优化代码结构**：移除冗余代码，提高代码质量

这些改进使得代码更加清晰、高效，同时也解决了原有架构中的状态同步问题。特别是参数更新机制和仿真请求流程的改进，使得数据流向更加直接，减少了不必要的数据复制和传递，确保了系统中数据的一致性。
