# -*- coding: utf-8 -*-
"""
海洋环境噪声视图

用于显示海洋环境噪声的Wenz曲线、用户定义的噪声谱和生成的信号
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSizePolicy, QTabWidget
from PyQt5.QtCore import pyqtSignal, Qt
from matplotlib.figure import Figure
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
import numpy as np
import os
import sys
import json
import matplotlib.pyplot as plt
from matplotlib.cm import get_cmap


class AmbientNoiseView(QWidget):
    """
    海洋环境噪声视图

    用于显示海洋环境噪声的Wenz曲线、用户定义的噪声谱和生成的信号
    """

    # 点选择范围常量（像素）
    PICK_RADIUS_X = 15  # x方向选点范围（像素）
    PICK_RADIUS_Y = 15  # y方向选点范围（像素）

    # 自定义信号
    point_added = pyqtSignal(float, float)  # 添加了新点，参数为频率和电平
    point_selected = pyqtSignal(int)        # 选中了已有点，参数为点的索引
    point_updated = pyqtSignal(int, float, float)  # 更新了点的位置，参数为索引、新频率和新电平
    point_removed = pyqtSignal(int)         # 删除了点，参数为点的索引

    def __init__(self, parent=None):
        """
        初始化海洋环境噪声视图

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 保存父窗口引用
        self.parent = parent

        # 初始化属性
        self.user_points = []  # 用户定义的点，每个元素为(x, y, artist)元组
        self.extrapolated_points = []  # 外插的点，每个元素为(x, y, artist)元组
        self.user_curve = None  # 用户曲线对象
        self.extrapolated_curve = None  # 外插曲线对象
        self.selected_point_index = -1  # 当前选中的点索引，-1表示未选中
        self.mode = 'add'  # 当前操作模式：'add', 'adjust', 'delete', 'locked'
        self.previous_mode = 'add'  # 锁定前的操作模式
        self.is_refreshing = False  # 是否正在刷新点显示，避免重复刷新

        # Wenz曲线相关属性
        self.wenz_curves_artists = []  # Wenz曲线图形对象列表
        self.wenz_data = None  # Wenz曲线数据
        self.wenz_band_areas_data = None  # Wenz曲线带状区域数据
        self.wenz_metadata = None  # Wenz曲线元数据
        self.wenz_style_config = None  # Wenz曲线样式配置

        # 加载Wenz曲线数据（只加载一次）
        self.load_wenz_data()
        self.load_wenz_style_config()

        # 创建布局
        self.init_ui()

        # 保存当前坐标轴尺度，用于检测尺度变化
        self.current_xscale = self.ax.get_xscale()  # 'log' 或 'linear'
        self.current_yscale = self.ax.get_yscale()  # 'log' 或 'linear'

        # 连接事件
        self.connect_events()

    def init_ui(self):
        """
        初始化UI
        """
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)  # 减少控件之间的间距

        # 创建标签页控件
        self.tabs = QTabWidget()

        # ===== 创建Wenz曲线标签页 =====
        self.wenz_tab = QWidget()
        wenz_layout = QVBoxLayout(self.wenz_tab)
        wenz_layout.setContentsMargins(0, 0, 0, 0)
        wenz_layout.setSpacing(0)

        # 创建Wenz曲线Matplotlib画布
        self.fig = Figure(figsize=(8, 6), dpi=100)
        self.canvas = FigureCanvas(self.fig)
        self.canvas.setFocusPolicy(Qt.StrongFocus)  # 允许接收键盘焦点

        # 设置画布的大小策略，使其能够自适应填充窗口
        self.canvas.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 创建坐标显示标签
        self.coord_label = QLabel("频率: 0.00 Hz, 噪声级: 0.00 dB")
        self.coord_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.coord_label.setStyleSheet("background-color: rgba(255, 255, 255, 180); padding: 5px; border-radius: 5px;")
        self.coord_label.setFixedHeight(30)
        self.coord_label.setMinimumWidth(300)  # 增加最小宽度，避免文本被截断

        # 创建工具栏
        self.toolbar = NavigationToolbar(self.canvas, self)

        # 添加到Wenz曲线标签页布局
        wenz_layout.addWidget(self.toolbar)
        wenz_layout.addWidget(self.canvas)

        # 创建坐标轴
        self.ax = self.fig.add_subplot(111)
        self.ax.set_title('海洋环境噪声频谱')
        self.ax.set_xlabel('频率 (Hz)')
        self.ax.set_ylabel('噪声级 (dB re 1μPa²/Hz)')
        self.ax.set_xscale('log')  # 使用对数X轴
        self.ax.grid(True, which='both', linestyle='--', alpha=0.7)

        # 设置坐标轴范围
        self.ax.set_xlim(1, 20000)
        self.ax.set_ylim(0, 150)

        # 绘制Wenz曲线背景
        self.draw_wenz_curves()

        # 添加坐标显示标签到图表
        self.fig.tight_layout()

        # 创建坐标显示的布局容器
        coord_container = QWidget(self.wenz_tab)
        coord_layout = QHBoxLayout(coord_container)
        coord_layout.addWidget(self.coord_label)
        coord_layout.setContentsMargins(0, 0, 10, 10)

        # 将坐标显示添加到Wenz曲线标签页右上角
        coord_container.setGeometry(self.width() - 320, 50, 300, 30)  # 调整位置和大小，避免与Matplotlib坐标显示重叠
        coord_container.raise_()  # 确保显示在最上层

        # ===== 创建仿真结果标签页 =====
        self.simulation_tab = QWidget()
        simulation_layout = QVBoxLayout(self.simulation_tab)

        # 创建仿真结果画布
        self.simulation_fig = Figure(figsize=(8, 8), dpi=100)
        self.simulation_canvas = FigureCanvas(self.simulation_fig)
        self.simulation_canvas.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 创建仿真结果工具栏
        self.simulation_toolbar = NavigationToolbar(self.simulation_canvas, self)

        # 添加到仿真结果标签页布局
        simulation_layout.addWidget(self.simulation_toolbar)
        simulation_layout.addWidget(self.simulation_canvas)

        # 创建时域信号图
        self.time_ax = self.simulation_fig.add_subplot(211)
        self.time_ax.set_title('海洋环境噪声 - 时域信号')
        self.time_ax.set_xlabel('时间 (s)')
        self.time_ax.set_ylabel('幅度')
        self.time_ax.set_xlim(0, 1.0)  # 显示1秒的时域信号
        self.time_ax.grid(True)

        # 创建频域信号图
        self.freq_ax = self.simulation_fig.add_subplot(212)
        self.freq_ax.set_title('海洋环境噪声 - 功率谱密度')
        self.freq_ax.set_xlabel('频率 (Hz)')
        self.freq_ax.set_ylabel('功率谱密度 (dB re 1μPa²/Hz)')
        self.freq_ax.set_xscale('log')
        self.freq_ax.grid(True, which='both', linestyle='--', alpha=0.7)

        self.simulation_fig.tight_layout()

        # 添加标签页到标签页控件
        self.tabs.addTab(self.wenz_tab, "Wenz曲线")
        self.tabs.addTab(self.simulation_tab, "仿真结果")

        # 添加标签页控件到主布局
        main_layout.addWidget(self.tabs)

        # 设置主布局
        self.setLayout(main_layout)

    def load_wenz_data(self, json_file=None, use_interpolated=True):
        """
        从JSON文件加载Wenz曲线数据

        Args:
            json_file: JSON文件路径，如果为None则使用默认路径
            use_interpolated: 是否使用插值后的数据

        Returns:
            包含所有曲线数据的字典、带状区域数据和元数据
        """
        try:
            if json_file is None:
                # 尝试使用绝对路径
                base_dir = os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
                # 首先尝试src/data目录
                json_file = os.path.join(base_dir, 'data', 'wenz_curves.json')

                # 如果文件不存在，尝试项目根目录下的data目录
                if not os.path.exists(json_file):
                    # 向上一级到项目根目录
                    project_root = os.path.dirname(base_dir)
                    json_file = os.path.join(project_root, 'data', 'wenz_curves.json')

                # print(f"使用绝对路径: {json_file}")

                # 检查文件是否存在
                if not os.path.exists(json_file):
                    print(f"警告: 文件 {json_file} 不存在")
                    # 尝试查找data目录下的所有json文件
                    data_dir = os.path.join(os.path.dirname(base_dir), 'data')
                    if os.path.exists(data_dir):
                        json_files = [f for f in os.listdir(data_dir) if f.endswith('.json')]
                        # print(f"data目录下的JSON文件: {json_files}")
                    else:
                        print(f"警告: data目录 {data_dir} 不存在")

            # print(f"尝试加载Wenz曲线数据: {os.path.abspath(json_file)}")

            with open(json_file, 'r') as f:
                data = json.load(f)

            raw_curves = data.get('curves', {})
            if not raw_curves:
                print("警告: 'curves' 键在JSON数据中不存在或为空")

            metadata = data.get('metadata', {})

            # 加载带状区域数据（如果存在）
            band_areas = data.get('band_areas', {})

            # 提取原始数据或插值数据
            curves = {}
            for curve_name, curve_data in raw_curves.items():
                try:
                    if use_interpolated and 'interpolated' in curve_data:
                        curves[curve_name] = curve_data['interpolated']
                    else:
                        curves[curve_name] = curve_data['original']
                except Exception as e:
                    print(f"处理曲线 {curve_name} 时出错: {e}")

            # print(f"成功加载了 {len(curves)} 条曲线数据")

            # 保存数据到类属性中
            self.wenz_data = curves
            self.wenz_band_areas_data = band_areas
            self.wenz_metadata = metadata

            return curves, band_areas, metadata

        except FileNotFoundError:
            print(f"错误: 找不到文件 {json_file}")
            return {}, {}, {}
        except json.JSONDecodeError:
            print(f"错误: {json_file} 不是有效的JSON文件")
            return {}, {}, {}
        except Exception as e:
            print(f"加载Wenz曲线数据时出错: {e}")
            return {}, {}, {}

    def load_wenz_style_config(self, config_file=None):
        """
        从JSON文件加载Wenz曲线样式配置

        Args:
            config_file: 配置文件路径，如果为None则使用默认路径

        Returns:
            包含样式配置的字典，如果文件不存在则返回默认配置
        """
        if config_file is None:
            # 尝试使用绝对路径
            base_dir = os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            # 首先尝试src/data目录
            config_file = os.path.join(base_dir, 'data', 'wenz_style_config.json')

            # 如果文件不存在，尝试项目根目录下的data目录
            if not os.path.exists(config_file):
                # 向上一级到项目根目录
                project_root = os.path.dirname(base_dir)
                config_file = os.path.join(project_root, 'data', 'wenz_style_config.json')

            # print(f"使用样式配置绝对路径: {config_file}")

            # 检查文件是否存在
            if not os.path.exists(config_file):
                print(f"警告: 样式配置文件 {config_file} 不存在，将使用默认配置")

        # 默认配置
        default_config = {
            "plot_style": {
                "title": "Wenz Curves",
                "xlabel": "频率 (Hz)",
                "ylabel": "噪声谱级 (dB re 1 μPa^2/Hz)",
                "min_freq": 1,
                "max_freq": 100000,
                "min_power": 0,
                "max_power": 140,
                "grid_major_alpha": 0.7,
                "grid_major_linestyle": "--",
                "grid_minor_alpha": 0.4,
                "grid_minor_linestyle": ":",
                "legend_loc": "upper right"
            },
            "band_areas": [
                {
                    "name": "low-frequency-very-shallow-wind",
                    "top": "low-frequency-very-shallow-wind-top",
                    "bottom": "low-frequency-very-shallow-wind-bottom",
                    "color": "#C7AD7D",
                    "alpha": 0.5,
                    "label": "极浅海域风生噪声"
                },
                {
                    "name": "usual-traffic-noise-shallow",
                    "top": "usual-traffic-noise-shallow-top",
                    "bottom": "usual-traffic-noise-shallow-bottom",
                    "color": "red",
                    "alpha": 0.5,
                    "label": "浅海交通噪声"
                },
                {
                    "name": "usual-traffic-noise-deep",
                    "top": "usual-traffic-noise-deep-top",
                    "bottom": "usual-traffic-noise-deep-bottom",
                    "color": "#FF69B4",
                    "alpha": 0.7,
                    "label": "深海交通噪声"
                }
            ],
            "important_curves": {
                "thermal-noise": {
                    "color": "gold",
                    "label": "热噪声",
                    "linestyle": "--",
                    "linewidth": 2.0,
                    "alpha": 1.0
                },
                "heavy-precipitation": {
                    "color": "purple",
                    "label": "强降水",
                    "linestyle": "--",
                    "linewidth": 2.0,
                    "alpha": 1.0
                },
                "earth-quakes-and-explosions": {
                    "color": "blue",
                    "label": "地震和爆炸",
                    "linestyle": "-.",
                    "linewidth": 2.0,
                    "alpha": 1.0
                },
                "heavy-traffic-noise": {
                    "color": "red",
                    "label": "重度交通噪声",
                    "linestyle": "--",
                    "linewidth": 2.0,
                    "alpha": 1.0
                },
                "limits-of-prevailing-noise-top": {
                    "color": "black",
                    "label": "主要噪声范围",
                    "linestyle": "-",
                    "linewidth": 1.5,
                    "alpha": 1.0,
                    "is_limit_pair": "true",
                    "pair_with": "limits-of-prevailing-noise-bottom"
                },
                "limits-of-prevailing-noise-bottom": {
                    "color": "black",
                    "linestyle": "-",
                    "linewidth": 1.5,
                    "alpha": 1.0,
                    "is_limit_pair": "true",
                    "show_in_legend": "false"
                }
            },
            "sea_state_curves": {
                "color_map": "Blues",
                "color_range": [0.5, 1.0],
                "label_prefix": "海况 ",
                "linestyle": "-",
                "linewidth": 1.5,
                "alpha": 1.0
            }
        }

        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.wenz_style_config = config
                return config
            else:
                self.wenz_style_config = default_config
                return default_config
        except Exception as e:
            print(f"加载样式配置出错: {e}，使用默认配置")
            self.wenz_style_config = default_config
            return default_config

    def get_main_window(self):
        """
        获取主窗口引用

        Returns:
            MainWindow: 主窗口引用
        """
        return self.parent

    def connect_events(self):
        """
        连接事件处理函数
        """
        # 鼠标点击事件
        self.canvas.mpl_connect('button_press_event', self.on_mouse_click)

        # 鼠标移动事件
        self.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)

        # 键盘事件
        self.canvas.mpl_connect('key_press_event', self.on_key_press)

        # 窗口大小变化事件
        self.canvas.mpl_connect('resize_event', self.on_resize)

        # 监听坐标轴变化事件，用于检测尺度变化
        # 我们使用xlim_changed和ylim_changed事件，但在处理函数中检查尺度是否变化
        self.ax.callbacks.connect('xlim_changed', self.on_axis_changed)
        self.ax.callbacks.connect('ylim_changed', self.on_axis_changed)

    def draw_wenz_curves(self):
        """
        绘制Wenz曲线背景

        使用数字化的Wenz曲线数据绘制背景
        """
        try:
            # print("开始绘制Wenz曲线...")

            # 确保已加载数据
            if self.wenz_data is None or self.wenz_style_config is None:
                # print("Wenz曲线数据或样式配置未加载，重新加载...")
                self.load_wenz_data()
                self.load_wenz_style_config()

            # 如果数据仍然为空，使用空字典避免错误
            if self.wenz_data is None:
                self.wenz_data = {}
            if self.wenz_band_areas_data is None:
                self.wenz_band_areas_data = {}

            # 获取样式配置
            style_config = self.wenz_style_config
            plot_style = style_config.get("plot_style", {})
            band_areas_config = style_config.get("band_areas", [])
            important_curves_config = style_config.get("important_curves", {})
            sea_state_config = style_config.get("sea_state_curves", {})

            # print(f"已加载样式配置: {len(band_areas_config)} 个带状区域, {len(important_curves_config)} 条重要曲线")

            # 设置坐标轴范围（保留当前范围）
            current_xlim = self.ax.get_xlim()
            current_ylim = self.ax.get_ylim()

            # 绘制带状区域
            for band_config in band_areas_config:
                band_name = band_config["name"]
                top_curve_name = band_config["top"]
                bottom_curve_name = band_config["bottom"]
                color = band_config["color"]
                alpha = band_config["alpha"]
                label = band_config["label"]

                # 确保曲线数据存在
                if top_curve_name in self.wenz_data and bottom_curve_name in self.wenz_data:
                    top_data = self.wenz_data[top_curve_name]
                    bottom_data = self.wenz_data[bottom_curve_name]

                    # 获取频率和功率谱级数据
                    top_freqs = top_data["frequencies"]
                    top_powers = top_data["power_levels"]
                    bottom_freqs = bottom_data["frequencies"]
                    bottom_powers = bottom_data["power_levels"]

                    # 如果有预处理的带状区域数据，使用它
                    if self.wenz_band_areas_data and band_name in self.wenz_band_areas_data:
                        try:
                            # print(f"使用预处理好的带状区域数据: {band_name}")
                            band_data = self.wenz_band_areas_data[band_name]
                            # 使用common_frequencies, top_powers, bottom_powers键
                            if all(key in band_data for key in ["common_frequencies", "top_powers", "bottom_powers"]):
                                common_freqs = np.array(band_data["common_frequencies"])
                                top_interp = np.array(band_data["top_powers"])
                                bottom_interp = np.array(band_data["bottom_powers"])

                                # 绘制填充区域
                                self.ax.fill_between(common_freqs, bottom_interp, top_interp,
                                                  color=color, alpha=alpha, label=label)
                            else:
                                print(f"警告: 带状区域 {band_name} 数据缺少必要的键")
                                raise KeyError("缺少必要的键")
                        except Exception as e:
                            print(f"处理带状区域 {band_name} 时出错: {e}，使用备用方法")
                            # 使用备用方法
                            raise
                    else:
                        # print(f"使用曲线数据进行插值: {band_name}")
                        # 确定共同的频率范围
                        min_freq = max(min(top_freqs), min(bottom_freqs))
                        max_freq = min(max(top_freqs), max(bottom_freqs))

                        # 创建共同的频率点，增加点数以提高分辨率
                        common_freqs = np.logspace(np.log10(min_freq), np.log10(max_freq), 500)

                        # 在共同频率点上插值上下边界曲线
                        top_interp = np.interp(np.log10(common_freqs), np.log10(top_freqs), top_powers)
                        bottom_interp = np.interp(np.log10(common_freqs), np.log10(bottom_freqs), bottom_powers)

                        # 绘制填充区域
                        self.ax.fill_between(common_freqs, bottom_interp, top_interp,
                                          color=color, alpha=alpha, label=label)

            # 绘制重要曲线
            processed_curves = set()  # 跟踪已处理的曲线
            for curve_name, curve_config in important_curves_config.items():
                # 跳过已处理的曲线
                if curve_name in processed_curves:
                    continue

                # 检查是否是限制对曲线
                is_limit_pair = curve_config.get("is_limit_pair", False)
                # 处理字符串形式的布尔值
                if isinstance(is_limit_pair, str):
                    is_limit_pair = is_limit_pair.lower() == "true"
                if is_limit_pair and curve_config.get("pair_with"):
                    pair_with = curve_config.get("pair_with")
                    if pair_with in self.wenz_data and curve_name in self.wenz_data and pair_with in important_curves_config:
                        # 将下限曲线标记为已处理
                        processed_curves.add(pair_with)

                        # 绘制上限曲线
                        top_data = self.wenz_data[curve_name]
                        top_freqs = top_data["frequencies"]
                        top_powers = top_data["power_levels"]

                        # 绘制上限曲线，添加到图例
                        color = curve_config.get("color", "black")
                        linestyle = curve_config.get("linestyle", "-")
                        linewidth = curve_config.get("linewidth", 1.5)
                        alpha = curve_config.get("alpha", 1.0)
                        label = curve_config.get("label", curve_name)

                        self.ax.plot(top_freqs, top_powers, color=color, linestyle=linestyle,
                                    linewidth=linewidth, alpha=alpha, label=label)

                        # 绘制下限曲线，但不添加到图例
                        pair_data = self.wenz_data[pair_with]
                        pair_freqs = pair_data["frequencies"]
                        pair_powers = pair_data["power_levels"]
                        pair_config = important_curves_config[pair_with]

                        # 使用下限曲线的样式，或者默认使用上限曲线的样式
                        pair_color = pair_config.get("color", color)
                        pair_linestyle = pair_config.get("linestyle", linestyle)
                        pair_linewidth = pair_config.get("linewidth", linewidth)
                        pair_alpha = pair_config.get("alpha", alpha)

                        # 直接使用ax.plot，不添加到图例
                        self.ax.plot(pair_freqs, pair_powers, color=pair_color, linestyle=pair_linestyle,
                                    linewidth=pair_linewidth, alpha=pair_alpha)

                        # 标记为已处理
                        processed_curves.add(curve_name)
                    continue

                # 普通曲线
                if curve_name in self.wenz_data:
                    data = self.wenz_data[curve_name]
                    freqs = data["frequencies"]
                    powers = data["power_levels"]

                    # 获取样式
                    color = curve_config.get("color", "black")
                    linestyle = curve_config.get("linestyle", "-")
                    linewidth = curve_config.get("linewidth", 1.5)
                    alpha = curve_config.get("alpha", 1.0)
                    label = curve_config.get("label", curve_name)
                    show_in_legend = curve_config.get("show_in_legend", True)
                    # 处理字符串形式的布尔值
                    if isinstance(show_in_legend, str):
                        show_in_legend = show_in_legend.lower() == "true"

                    # 检查是否应该显示在图例中
                    if not show_in_legend:
                        label = None

                    # 绘制曲线
                    self.ax.plot(freqs, powers, color=color, linestyle=linestyle,
                                linewidth=linewidth, alpha=alpha, label=label)

                    # 标记为已处理
                    processed_curves.add(curve_name)

            # 绘制海况曲线
            sea_state_curves = [name for name in self.wenz_data.keys() if name.startswith("wind-dependent-") and not name.endswith("-extrapolations")]

            if sea_state_curves:
                # 获取海况曲线配置
                color_map_name = sea_state_config.get("color_map", "Blues")
                color_range = sea_state_config.get("color_range", [0.5, 1.0])
                label_prefix = sea_state_config.get("label_prefix", "海况 ")
                linestyle = sea_state_config.get("linestyle", "-")
                linewidth = sea_state_config.get("linewidth", 1.5)
                alpha = sea_state_config.get("alpha", 1.0)

                # 创建颜色映射
                color_map = get_cmap(color_map_name)

                # 按海况值排序
                sea_state_curves.sort(key=lambda x: float(x.split("-")[-1]))

                # 绘制每条海况曲线
                for i, curve_name in enumerate(sea_state_curves):
                    data = self.wenz_data[curve_name]
                    freqs = data["frequencies"]
                    powers = data["power_levels"]

                    # 使用配置的颜色映射
                    color_factor = color_range[0] + (color_range[1] - color_range[0]) * i / max(1, len(sea_state_curves) - 1)
                    color = color_map(color_factor)

                    # 提取海况值
                    sea_state = curve_name.split("-")[-1]
                    label = f"{label_prefix}{sea_state}"

                    # 绘制曲线
                    self.ax.semilogx(freqs, powers, color=color, linestyle=linestyle,
                                  linewidth=linewidth, alpha=alpha, label=label)

            # 添加图例
            legend_loc = plot_style.get("legend_loc", "upper right")
            self.ax.legend(loc=legend_loc)

            # 恢复坐标轴范围
            self.ax.set_xlim(current_xlim)
            self.ax.set_ylim(current_ylim)

            # 刷新画布
            self.canvas.draw()

            # print("Wenz曲线绘制完成")
        except Exception as e:
            print(f"绘制Wenz曲线时出错: {e}")
            # 直接报错，不使用替代方案
            raise

    def on_mouse_move(self, event):
        """
        鼠标移动事件处理

        Args:
            event: 鼠标事件对象
        """
        if event.inaxes != self.ax:
            return

        # 如果在调整模式下且有选中点，不更新坐标显示
        if self.mode == 'adjust' and self.selected_point_index != -1:
            return

        # 更新坐标显示
        freq = event.xdata
        level = event.ydata

        if freq is not None and level is not None:
            self.coord_label.setText(f"频率: {freq:.2f} Hz, 噪声级: {level:.2f} dB")

    def on_mouse_click(self, event):
        """
        鼠标点击事件处理

        Args:
            event: 鼠标事件对象
        """
        if event.inaxes != self.ax or event.button != 1:  # 只处理左键点击
            return

        # 检查matplotlib工具栏是否处于活动状态
        if self.toolbar.mode:  # 如果工具栏有活动的工具（如缩放、平移等）
            return  # 不处理点击事件

        # 如果处于锁定模式，不处理点击事件
        if self.mode == 'locked':
            return

        # 获取点击位置
        freq = event.xdata
        level = event.ydata

        if freq is None or level is None:
            return

        # 根据当前模式处理点击事件
        if self.mode == 'add':
            # 添加新点
            self.add_point(freq, level)
        elif self.mode == 'adjust':
            # 选择点
            self.select_point(event)
        elif self.mode == 'delete':
            # 删除点
            self.delete_point(event)

    def on_key_press(self, event):
        """
        键盘事件处理

        Args:
            event: 键盘事件对象
        """
        # 如果处于锁定模式，不处理键盘事件
        if self.mode == 'locked':
            return

        # 只有在调整模式下且有选中点时才处理方向键
        if self.mode != 'adjust' or self.selected_point_index == -1:
            return

        # 获取当前选中点
        if 0 <= self.selected_point_index < len(self.user_points):
            freq, level, artist = self.user_points[self.selected_point_index]

            # 检查是否按下了Shift键（用于更大幅度的调整）
            is_shift_pressed = 'shift' in event.key

            # 提取基本按键（去除修饰符）
            base_key = event.key.replace('shift+', '')

            # 设置像素调整幅度
            pixel_step = 1  # 默认像素微调步长

            # 如果按下Shift键，使用更大的调整幅度
            if is_shift_pressed:
                pixel_step = 5  # Shift+方向键时的像素调整步长

            # 将当前数据点转换为屏幕坐标（像素）
            screen_coords = self.ax.transData.transform([freq, level])

            # 根据按键调整屏幕坐标
            if base_key == 'up':
                screen_coords[1] += pixel_step
            elif base_key == 'down':
                screen_coords[1] -= pixel_step
            elif base_key == 'right':
                screen_coords[0] += pixel_step
            elif base_key == 'left':
                screen_coords[0] -= pixel_step
            else:
                return

            # 将屏幕坐标转换回数据坐标
            new_data_coords = self.ax.transData.inverted().transform(screen_coords)
            new_freq, new_level = new_data_coords

            # 确保频率不会变为负数
            if new_freq <= 0:
                new_freq = 0.01  # 设置一个很小的正值

            # 保存当前选中的点索引
            current_selected_index = self.selected_point_index

            # 更新点的位置
            self.update_point(self.selected_point_index, new_freq, new_level)

            # 更新坐标显示标签，显示当前选中点的位置
            self.coord_label.setText(f"频率: {new_freq:.2f} Hz, 噪声级: {new_level:.2f} dB")

            # 确保点仍然处于选中状态
            if current_selected_index != self.selected_point_index:
                self.selected_point_index = current_selected_index
                # 重新设置点的颜色为绿色
                _, _, artist = self.user_points[current_selected_index]
                artist.set_color('g')
                artist.set_markersize(10)  # 选中点变大
                self.canvas.draw_idle()

    def on_resize(self, event):
        """
        窗口大小变化事件处理

        Args:
            event: 窗口大小变化事件对象
        """
        # 更新坐标显示标签的位置
        for child in self.wenz_tab.children():
            if isinstance(child, QWidget) and child != self.canvas and child != self.toolbar:
                child.setGeometry(self.wenz_tab.width() - 320, 50, 300, 30)  # 调整位置和大小，避免与Matplotlib坐标显示重叠

    def on_axis_changed(self, ax):
        """
        坐标轴变化事件处理

        检查坐标轴尺度是否发生变化，如果是，则只刷新点的显示状态
        Matplotlib会自动处理坐标轴和网格的重绘

        Args:
            ax: 发生变化的坐标轴对象
        """
        # 获取当前坐标轴尺度
        current_xscale = self.ax.get_xscale()
        current_yscale = self.ax.get_yscale()

        # 检查尺度是否发生变化
        if current_xscale != self.current_xscale or current_yscale != self.current_yscale:
            # print(f"坐标轴尺度变化: {self.current_xscale} -> {current_xscale}, {self.current_yscale} -> {current_yscale}")

            # 更新保存的尺度
            self.current_xscale = current_xscale
            self.current_yscale = current_yscale

            # 刷新点的显示状态
            self.refresh_points_display()

    def refresh_points_display(self):
        """
        刷新所有点的显示状态

        在坐标轴变化（如尺度变化）后调用，确保点的颜色状态正确
        使用blitting技术优化性能，避免重绘整个图表
        """
        # 如果没有点或者正在刷新中，直接返回
        if not self.user_points or self.is_refreshing:
            return

        # 设置刷新标志，避免重复刷新
        self.is_refreshing = True

        try:
            # 保存当前点的数据和选中状态
            points_data = [(freq, level) for freq, level, _ in self.user_points]
            selected_idx = self.selected_point_index

            # 移除所有现有点
            for _, _, artist in self.user_points:
                artist.remove()
            self.user_points = []

            # 重新创建所有点
            for i, (freq, level) in enumerate(points_data):
                color = 'g' if i == selected_idx else 'r'
                artist = self.ax.plot([freq], [level], color+'o', markersize=8, picker=5)[0]
                self.user_points.append((freq, level, artist))

            # 更新用户曲线
            self.update_user_curve()

            # 使用draw_idle而不是draw，避免不必要的重绘
            self.canvas.draw_idle()

            # # 打印调试信息
            # print(f"刷新点显示完成，选中点索引: {selected_idx}")
            # if selected_idx != -1:
            #     _, _, artist = self.user_points[selected_idx]
            #     print(f"选中点颜色: {artist.get_color()}")
        finally:
            # 无论成功与否，都重置刷新标志
            self.is_refreshing = False

    def add_point(self, freq, level):
        """
        添加新点

        Args:
            freq (float): 频率
            level (float): 噪声级
        """
        # 创建点的图形对象
        artist = self.ax.plot([freq], [level], 'ro', markersize=8, picker=5)[0]

        # 添加到用户点列表
        self.user_points.append((freq, level, artist))

        # 更新用户曲线
        self.update_user_curve()

        # 使用draw_idle代替draw，避免不必要的重绘
        self.canvas.draw_idle()

        # 发送信号
        self.point_added.emit(freq, level)



    def select_point(self, event):
        """
        选择点

        Args:
            event: 鼠标事件对象
        """
        # 获取鼠标点击的屏幕坐标
        click_screen_coords = self.ax.transData.transform([event.xdata, event.ydata])

        # 查找最近的点
        min_dist_sum = float('inf')
        selected_idx = -1
        candidates = []

        # 首先筛选出在矩形区域内的候选点
        for i, (freq, level, _) in enumerate(self.user_points):
            # 将数据点转换为屏幕坐标
            point_screen_coords = self.ax.transData.transform([freq, level])

            # 计算x和y方向的距离（像素）
            x_dist = abs(click_screen_coords[0] - point_screen_coords[0])
            y_dist = abs(click_screen_coords[1] - point_screen_coords[1])

            # 如果点在矩形选择区域内，加入候选列表
            if x_dist <= self.PICK_RADIUS_X and y_dist <= self.PICK_RADIUS_Y:
                candidates.append((i, x_dist, y_dist))

        # 从候选点中选择x和y偏差之和最小的点
        for i, x_dist, y_dist in candidates:
            dist_sum = x_dist + y_dist
            if dist_sum < min_dist_sum:
                min_dist_sum = dist_sum
                selected_idx = i

        # 如果找到了点
        if selected_idx != -1:
            # 取消之前选中的点
            if self.selected_point_index != -1 and self.selected_point_index < len(self.user_points):
                _, _, artist = self.user_points[self.selected_point_index]
                artist.set_color('r')
                artist.set_markersize(8)  # 重置点大小为默认值

            # 设置新选中的点
            self.selected_point_index = selected_idx
            _, _, artist = self.user_points[selected_idx]
            artist.set_color('g')  # 选中点变为绿色

            # 增加点大小以提高视觉反馈
            artist.set_markersize(10)  # 选中点变大

            # 强制立即重绘以确保颜色变化可见
            self.canvas.draw()  # 使用draw而不是draw_idle，确保立即更新

            # 获取选中点的信息
            freq, level, _ = self.user_points[selected_idx]

            # 更新坐标显示标签，显示当前选中点的位置
            self.coord_label.setText(f"频率: {freq:.2f} Hz, 噪声级: {level:.2f} dB")

            # 不在这里更新状态栏，让tab的on_point_selected处理

            # 发送信号
            self.point_selected.emit(selected_idx)

            # 设置键盘焦点
            self.canvas.setFocus()

    def delete_point(self, event):
        """
        删除点

        Args:
            event: 鼠标事件对象
        """
        # 获取鼠标点击的屏幕坐标
        click_screen_coords = self.ax.transData.transform([event.xdata, event.ydata])

        # 查找最近的点
        min_dist_sum = float('inf')
        selected_idx = -1
        candidates = []

        # 首先筛选出在矩形区域内的候选点
        for i, (freq, level, _) in enumerate(self.user_points):
            # 将数据点转换为屏幕坐标
            point_screen_coords = self.ax.transData.transform([freq, level])

            # 计算x和y方向的距离（像素）
            x_dist = abs(click_screen_coords[0] - point_screen_coords[0])
            y_dist = abs(click_screen_coords[1] - point_screen_coords[1])

            # 如果点在矩形选择区域内，加入候选列表
            if x_dist <= self.PICK_RADIUS_X and y_dist <= self.PICK_RADIUS_Y:
                candidates.append((i, x_dist, y_dist))

        # 从候选点中选择x和y偏差之和最小的点
        for i, x_dist, y_dist in candidates:
            dist_sum = x_dist + y_dist
            if dist_sum < min_dist_sum:
                min_dist_sum = dist_sum
                selected_idx = i

        # 如果找到了点
        if selected_idx != -1:
            # 移除点的图形对象
            _, _, artist = self.user_points[selected_idx]
            artist.remove()

            # 从列表中移除
            self.user_points.pop(selected_idx)

            # 如果删除的是当前选中的点，重置选中状态
            if selected_idx == self.selected_point_index:
                self.selected_point_index = -1

            # 更新用户曲线
            self.update_user_curve()

            # 使用draw_idle代替draw，避免不必要的重绘
            self.canvas.draw_idle()

            # 发送信号
            self.point_removed.emit(selected_idx)



    def update_point(self, index, freq, level):
        """
        更新点的位置

        Args:
            index (int): 点的索引
            freq (float): 新频率
            level (float): 新噪声级
        """
        if 0 <= index < len(self.user_points):
            # 获取点的图形对象
            _, _, artist = self.user_points[index]

            # # 保存当前颜色和大小，确保选中状态不丢失
            # current_color = artist.get_color()
            # current_size = artist.get_markersize()

            # 更新点的位置
            artist.set_xdata([freq])  # 使用列表包装单个值
            artist.set_ydata([level])  # 使用列表包装单个值

            # # 确保点的颜色和大小保持不变（特别是选中状态的绿色）
            # artist.set_color(current_color)
            # artist.set_markersize(current_size)

            # 更新列表中的数据
            self.user_points[index] = (freq, level, artist)

            # 更新用户曲线
            self.update_user_curve()

            # 如果当前是选中的点，更新坐标显示标签
            if index == self.selected_point_index:
                self.coord_label.setText(f"频率: {freq:.2f} Hz, 噪声级: {level:.2f} dB")

            # 使用draw_idle代替draw，避免不必要的重绘
            self.canvas.draw_idle()

            # # 确保canvas保持键盘焦点，以便继续接收键盘事件
            # self.canvas.setFocus()

            # 发送信号
            self.point_updated.emit(index, freq, level)



    def update_user_curve(self):
        """
        更新用户曲线
        """
        # 如果没有点或只有一个点，不绘制曲线
        if len(self.user_points) < 2:
            if self.user_curve:
                self.user_curve.remove()
                self.user_curve = None
            return

        # 提取频率和电平
        freqs = [point[0] for point in self.user_points]
        levels = [point[1] for point in self.user_points]

        # 按频率排序
        sorted_points = sorted(zip(freqs, levels))
        freqs = [point[0] for point in sorted_points]
        levels = [point[1] for point in sorted_points]

        # 更新或创建用户曲线
        if self.user_curve:
            self.user_curve.set_xdata(freqs)
            self.user_curve.set_ydata(levels)
        else:
            self.user_curve = self.ax.plot(freqs, levels, 'r-', linewidth=2)[0]

    def update_extrapolated_curve(self, extrapolated_points):
        """
        更新外插曲线

        Args:
            extrapolated_points (list): 外插点列表，每个元素为(freq, level)元组
        """
        # 清除之前的外插点
        for _, _, artist in self.extrapolated_points:
            artist.remove()
        self.extrapolated_points = []

        # 如果没有外插点，移除外插曲线
        if not extrapolated_points:
            if self.extrapolated_curve:
                self.extrapolated_curve.remove()
                self.extrapolated_curve = None
            return

        # 添加新的外插点
        for freq, level in extrapolated_points:
            artist = self.ax.plot([freq], [level], 'bo', markersize=6)[0]
            self.extrapolated_points.append((freq, level, artist))

        # 提取所有点（用户点和外插点）
        all_points = self.user_points + self.extrapolated_points
        all_points.sort(key=lambda x: x[0])  # 按频率排序

        freqs = [point[0] for point in all_points]
        levels = [point[1] for point in all_points]

        # 更新或创建外插曲线
        if self.extrapolated_curve:
            self.extrapolated_curve.set_xdata(freqs)
            self.extrapolated_curve.set_ydata(levels)
        else:
            self.extrapolated_curve = self.ax.plot(freqs, levels, 'b--', linewidth=1.5)[0]

        # 刷新画布
        self.canvas.draw()

    def set_mode(self, mode):
        """
        设置操作模式

        Args:
            mode (str): 操作模式，'add', 'adjust' 或 'delete'
        """
        if mode in ['add', 'adjust', 'delete', 'locked']:
            # 如果切换到锁定模式，保存当前模式
            if mode == 'locked' and self.mode != 'locked':
                self.previous_mode = self.mode

            # 如果从调整模式切换到其他模式，取消当前选中点的状态
            if self.mode == 'adjust' and mode != 'adjust' and self.selected_point_index != -1:
                # 取消选中状态
                if 0 <= self.selected_point_index < len(self.user_points):
                    _, _, artist = self.user_points[self.selected_point_index]
                    artist.set_color('r')  # 恢复为红色
                    artist.set_markersize(8)  # 恢复默认大小
                    self.canvas.draw_idle()  # 刷新画布

                # 重置选中点索引
                self.selected_point_index = -1

            # 设置新模式
            self.mode = mode

    def set_interaction_mode(self, mode):
        """
        设置交互模式

        Args:
            mode (str): 交互模式，'add', 'adjust', 'delete' 或 'locked'
        """
        # 调用现有的set_mode方法
        self.set_mode(mode)

    def restore_previous_mode(self):
        """
        恢复锁定前的操作模式
        """
        if self.mode == 'locked':
            # 使用set_mode方法恢复到之前的模式
            # set_mode方法会处理选中点的状态
            self.set_mode(self.previous_mode)

    def reset_view(self):
        """
        重置视图到初始状态
        """
        # 清除当前图表内容
        self.ax.clear()

        # 重新设置坐标轴标题和网格
        self.ax.set_title('海洋环境噪声频谱')
        self.ax.set_xlabel('频率 (Hz)')
        self.ax.set_ylabel('噪声级 (dB re 1μPa²/Hz)')
        self.ax.set_xscale('log')  # 使用对数X轴
        self.ax.grid(True, which='both', linestyle='--', alpha=0.7)

        # 重置坐标轴范围
        self.ax.set_xlim(1, 20000)
        self.ax.set_ylim(0, 150)

        # 更新保存的尺度
        self.current_xscale = self.ax.get_xscale()
        self.current_yscale = self.ax.get_yscale()

        # 重新绘制Wenz曲线背景
        self.draw_wenz_curves()

        # 如果有用户点，重新绘制
        if self.user_points:
            self.refresh_points_display()
        else:
            # 刷新画布
            self.canvas.draw()

    def clear_points(self):
        """
        清除所有点
        """
        # 清除用户点
        for _, _, artist in self.user_points:
            artist.remove()
        self.user_points = []

        # 清除外插点
        for _, _, artist in self.extrapolated_points:
            artist.remove()
        self.extrapolated_points = []

        # 清除曲线
        if self.user_curve:
            self.user_curve.remove()
            self.user_curve = None

        if self.extrapolated_curve:
            self.extrapolated_curve.remove()
            self.extrapolated_curve = None

        # 重置选中状态
        self.selected_point_index = -1

        # 刷新画布
        self.canvas.draw()

    def reset_simulation_plots(self):
        """
        重置仿真结果图表
        """
        # 清除时域信号图
        self.time_ax.clear()
        self.time_ax.set_title('海洋环境噪声 - 时域信号')
        self.time_ax.set_xlabel('时间 (s)')
        self.time_ax.set_ylabel('幅度')
        self.time_ax.set_xlim(0, 1.0)
        self.time_ax.grid(True)

        # 清除频域信号图
        self.freq_ax.clear()
        self.freq_ax.set_title('海洋环境噪声 - 功率谱密度')
        self.freq_ax.set_xlabel('频率 (Hz)')
        self.freq_ax.set_ylabel('功率谱密度 (dB re 1μPa²/Hz)')
        self.freq_ax.set_xscale('log')
        self.freq_ax.grid(True, which='both', linestyle='--', alpha=0.7)

        # 刷新画布
        self.simulation_fig.tight_layout()
        self.simulation_canvas.draw()



    def get_user_points(self):
        """
        获取用户定义的点

        Returns:
            list: 用户定义的点列表，每个元素为(freq, level)元组
        """
        return [(freq, level) for freq, level, _ in self.user_points]

    def set_user_points(self, points):
        """
        设置用户定义的点

        Args:
            points (list): 用户定义的点列表，每个元素为(freq, level)元组
        """
        # 清除当前点
        self.clear_points()

        # 如果没有点，直接返回
        if not points:
            return

        # 批量添加所有点，避免多次重绘
        for freq, level in points:
            # 创建点的图形对象，但不触发重绘
            artist = self.ax.plot([freq], [level], 'ro', markersize=8, picker=5)[0]

            # 添加到用户点列表
            self.user_points.append((freq, level, artist))

            # 不发送信号，因为这是批量操作

        # 更新用户曲线
        self.update_user_curve()

        # 一次性重绘
        self.canvas.draw()

    def update_signal_plot(self, time_data, signal_data, freq_data, psd_db):
        """
        更新信号图表

        Args:
            time_data (ndarray): 时间数组
            signal_data (ndarray): 时域信号
            freq_data (ndarray): 频率数组
            psd_db (ndarray): 功率谱密度（dB）
        """
        # 清除当前图表
        self.time_ax.clear()
        self.freq_ax.clear()

        # 更新时域信号图
        self.time_ax.plot(time_data, signal_data, 'b-')
        self.time_ax.set_title('海洋环境噪声 - 时域信号')
        self.time_ax.set_xlabel('时间 (s)')
        self.time_ax.set_ylabel('幅度')
        self.time_ax.set_xlim(0, 1.0)  # 显示1秒的时域信号
        self.time_ax.grid(True)

        # 更新频域信号图
        self.freq_ax.plot(freq_data, psd_db, 'b-', label='仿真结果')

        # 绘制目标PSD曲线
        # 获取用户定义点和外插点
        all_points = self.get_user_points() + [(freq, level) for freq, level, _ in self.extrapolated_points]
        if all_points:
            # 按频率排序
            all_points.sort(key=lambda x: x[0])
            target_freqs = [point[0] for point in all_points]
            target_levels = [point[1] for point in all_points]

            # 绘制目标PSD曲线
            self.freq_ax.plot(target_freqs, target_levels, 'r--', linewidth=1.5, label='目标谱级')

            # 计算并显示差异范围
            if len(freq_data) > 0 and len(target_freqs) > 0:
                from scipy.interpolate import interp1d
                import numpy as np

                # 在对数频率轴上对dB值进行线性插值
                try:
                    # 确保目标频率是严格递增的
                    if len(target_freqs) > 1 and all(target_freqs[i] < target_freqs[i+1] for i in range(len(target_freqs)-1)):
                        interp_func_db = interp1d(
                            np.log10(target_freqs),
                            target_levels,
                            kind='linear',
                            bounds_error=False,
                            fill_value=np.nan
                        )

                        # 确定有效频率范围
                        min_target_f = min(target_freqs)
                        max_target_f = max(target_freqs)
                        valid_indices = (freq_data >= min_target_f) & (freq_data <= max_target_f)

                        if np.any(valid_indices):
                            # 计算目标PSD在仿真频率点上的值
                            target_psd_db_interp = interp_func_db(np.log10(freq_data[valid_indices]))

                            # 计算差异
                            diff = psd_db[valid_indices] - target_psd_db_interp

                            # 计算差异统计量
                            max_diff = np.nanmax(np.abs(diff))
                            mean_diff = np.nanmean(np.abs(diff))

                            # 在图表上显示差异信息
                            diff_text = f"差异范围: 最大 {max_diff:.2f} dB, 平均 {mean_diff:.2f} dB"
                            self.freq_ax.text(0.05, 0.05, diff_text, transform=self.freq_ax.transAxes,
                                           bbox=dict(facecolor='white', alpha=0.7))
                except Exception as e:
                    print(f"计算差异时出错: {e}")

        self.freq_ax.set_title('海洋环境噪声 - 功率谱密度')
        self.freq_ax.set_xlabel('频率 (Hz)')
        self.freq_ax.set_ylabel('功率谱密度 (dB re 1μPa²/Hz)')
        self.freq_ax.set_xscale('log')
        self.freq_ax.grid(True, which='both', linestyle='--', alpha=0.7)
        self.freq_ax.legend()

        # 调整布局
        self.simulation_fig.tight_layout()

        # 刷新画布
        self.simulation_canvas.draw()

        # 切换到仿真结果标签页
        self.tabs.setCurrentIndex(1)