3-1 全局活动图

@startuml

|用户|
start
:启动系统;
if () then ([在已有的工作上继续])
  |用户|
  :选择项目文件;
  |仿真系统|
  :加载项目数据;
  else ([开始新的工作])
endif
'
' 隐含新建或加载项目
'
|用户|
:查看噪声信号与信道数据;
if () then ([数据缺失或不满意])
:配置噪声/声传播环境仿真参数;
|仿真系统|
:执行噪声/声传播环境仿真;
|用户|
:查看与分析仿真结果;
else ([数据完备且满意])
endif
'
' 数据准备完毕，执行综合仿真
'
|用户|
:配置综合仿真计算参数;
'
' 用户使用已备好的噪声信号和信道数据
' 配置阵列处理等综合分析参数
'
|仿真系统|
:执行综合仿真与分析计算;
|用户|
:查看与分析综合仿真结果;
'
' 仿真完成，项目收尾
'
|用户|
:保存项目文件/导出信号数据;
|仿真系统|
:执行项目文件保存/信号数据导出;
|用户|
:关闭系统;
stop
@enduml

========================================

3-2 整体用例图

@startuml
!theme plain
left to right direction

actor "用户" as User

rectangle "面向复杂水声环境的噪声仿真系统" {
  usecase "项目文件管理与信号数据导出" as UC4
  usecase "综合声场仿真" as UC3
  usecase "声传播环境仿真" as UC2
  usecase "噪声信号仿真" as UC1
}

User -- UC1
User -- UC2
User -- UC3
User -- UC4

@enduml

========================================

3-3 噪声信号仿真用例图

@startuml
!theme plain
left to right direction

actor "用户" as User

rectangle "面向复杂水声环境的噪声仿真系统" {
  usecase "查看仿真结果" as UC3
  usecase "执行仿真计算" as UC2
  usecase "配置噪声参数" as UC1
}

User -- UC1
User -- UC2
User -- UC3

@enduml

========================================

3-4 声传播环境仿真用例图

@startuml
!theme plain
left to right direction

actor "用户" as User

rectangle "面向复杂水声环境的噪声仿真系统" {
  usecase "制备信道数据" as UC5
  usecase "配置阵列与频率参数" as UC4
  usecase "查看声传播计算结果" as UC3
  usecase "执行声传播计算" as UC2
  usecase "配置声传播环境参数" as UC1
}

User -- UC1
User -- UC2
User -- UC3
User -- UC4
User -- UC5

@enduml

========================================

3-5 综合声场仿真用例图

@startuml
!theme plain
left to right direction

actor "用户" as User

rectangle "面向复杂水声环境的噪声仿真系统" {
  usecase "查看声场特性" as UC4
  usecase "进行阵列信号处理" as UC3
  usecase "生成阵元接收信号" as UC2
  usecase "选择信道数据" as UC1

}

User -- UC1
User -- UC2
User -- UC3
User -- UC4

@enduml

========================================

3-6 项目文件管理与信号数据导出用例图

@startuml
!theme plain
left to right direction

actor "用户" as User

rectangle "面向复杂水声环境的噪声仿真系统" {
  usecase "保存项目文件" as UC1_2
  usecase "打开项目文件" as UC1_1
  usecase "导出信号时域数据" as UC2
  usecase "管理项目文件" as UC1
}

User -- UC1
User -- UC2
UC1 ..> UC1_1 : 《include》
UC1 ..> UC1_2 : 《include》

@enduml

========================================

3-7 分析类图

@startuml
!theme plain
hide empty members
skinparam linetype ortho


class 仿真项目 {
  项目名称
  --
  管理项目()
  导出信号()
}

class 噪声 {
  参数
  信号
  --
  设置参数()
  生成信号()
}

class 船舶辐射噪声 {}

class 海洋环境噪声 {}

class 声传播环境 {
  参数
  --
  设置参数()
  声传播环境仿真()
  生成信道数据()
}

class 信道数据 {
  阵列几何
  频率列表
  声线到达结构
}

class 综合声场 {
  阵列信号
  声场特性
  --
  模拟阵列信号()
  生成声场特性()
}

' 继承关系
噪声 <|-- 船舶辐射噪声
噪声 <|-- 海洋环境噪声

' 聚合关系
仿真项目 o-- 噪声
仿真项目 o-- 声传播环境
仿真项目 o-- 综合声场
仿真项目 o-- 信道数据

' 关联关系
信道数据 <-- 综合声场 : 使用
噪声 <-- 综合声场 : 使用

@enduml

========================================

3-8 船舶辐射噪声仿真活动图（分析阶段）

@startuml

|用户|
start
:进行船舶辐射噪声仿真;

if () then ([直接输入连续谱参数])
  :输入连续谱参数;
else ([根据工况估算连续谱参数])
  :提供船舶工况信息;
  |仿真系统|
  :根据工况计算连续谱参数;
  |用户|
endif
if () then ([包含线谱])
  :设置线谱参数;
  else ([不包含线谱])
endif
if () then ([包含调制谱])
  :设置调制谱参数;
  else ([不包含调制谱])
endif
:启动仿真计算;
|仿真系统|
:生成船舶辐射噪声信号;
:计算信号特性;
:展示仿真结果;
|用户|
:查看和分析结果;
stop

@enduml

========================================

3-9 海洋环境噪声仿真活动图（分析阶段）

@startuml

|用户|
start
:进行海洋环境噪声仿真;
|仿真系统|
:显示Wenz曲线参考背景;
|用户|
:开始定义噪声谱级曲线;
if () then ([通过图形直接交互定义])
  :在Wenz曲线图上点击添加点;
else ([通过输入方式定义])
  :设置频率-谱级点具体数字;
endif
:完成噪声谱级曲线定义;
:启动噪声仿真计算;
|仿真系统|
:生成环境噪声信号;
:计算信号特性;
:展示仿真结果;
|用户|
:查看和分析结果;
stop

@enduml

========================================

3-10 声传播环境仿真活动图（分析阶段）

@startuml

|用户|
start
:进行声传播环境仿真;
:配置声传播环境参数;
:启动声传播计算;
|仿真系统|
:调用声传播模型进行计算;
:展示声线轨迹、传播损失和到达结构等结果;
|用户|
:查看和分析结果;
if () then ([制备信道数据])
  :设置接收阵列几何与计算频率列表;
  :启动信道数据制备;
  |仿真系统|
  :调用声传播模型进行计算;
  :保存信道数据到目标目录;
  |用户|
  else ([不制备信道数据])
endif
stop

@enduml

========================================

3-11 综合声场仿真活动图（分析阶段）

@startuml

|用户|
start
:进行综合声场仿真;
:选择信道数据目录;
|仿真系统|
:加载并显示信道信息;
|用户|
:配置噪声信号;
:启动信号仿真;
|仿真系统|
:进行信道建模并模拟噪声信号传播;
:展示各阵元时域信号;
|用户|
:进行信号分析;
|仿真系统|
:进行阵列信号处理;
:可视化展示分析结果;
|用户|
:查看信号处理结果;
stop

@enduml

========================================

更接近实际的类图

@startuml
!theme plain

' 用户界面层
class 主窗口 {
  窗口标题
  窗口大小
  窗口状态
  --
  初始化UI组件()
  处理窗口事件()
}

class 视图区域 {
  堆叠部件
  --
  切换视图()
}

class 控制面板 {
  标签页控件
  --
  管理标签页()
}

class 控制标签页 {
  输入控件
  操作按钮
  --
  配置参数()
  处理用户交互()
}

class 视图组件 {
  图表组件
  数据表格
  --
  显示结果()
  可视化数据()
}

' 数据管理层
class 数据管理器 {
  全局参数字典
  模块参数字典
  模块结果字典
  --
  设置参数()
  获取参数()
  存储结果()
  发送变更通知()
}

class 项目管理器 {
  项目文件路径
  修改状态
  --
  保存项目()
  加载项目()
  管理状态()
}

' 业务逻辑层
class 仿真控制器 {
  专门控制器引用
  --
  分发仿真任务()
  协调流程()
}

class 专门控制器 {
  计算状态
  进度信息
  --
  验证参数()
  调用模型()
  处理结果()
}

' 模型层
class 噪声源模型 {
  采样频率
  信号持续时间
  --
  生成噪声信号()
}

class 船舶辐射噪声模型 {
  连续谱参数
  线谱参数
  调制谱参数
  工况参数
}

class 海洋环境噪声模型 {
  Wenz曲线参数
  用户定义点
  插值方法
}

class 声传播模型 {
  环境参数
  计算方法
  --
  创建环境()
  计算声线()
  计算传播损失()
}

class 综合仿真模型 {
  信道数据
  信号处理参数
  --
  信号传播建模()
  阵列信号处理()
  计算声场特性()
}

' 视图更新器
class 视图更新器 {
  数据管理器引用
  视图组件引用
  --
  监听数据变更()
  更新视图内容()
}

' 组合关系
主窗口 *-- 视图区域 : 包含
主窗口 *-- 控制面板 : 包含
视图区域 o-- 视图组件 : 管理
控制面板 o-- 控制标签页 : 管理

' 聚合关系
仿真控制器 o-- 专门控制器 : 协调
数据管理器 o-- 项目管理器 : 使用

' 关联关系
专门控制器 --> 数据管理器 : 使用
视图更新器 --> 视图组件 : 更新
视图更新器 --> 数据管理器 : 监听
专门控制器 --> 噪声源模型 : 调用
专门控制器 --> 声传播模型 : 调用
专门控制器 --> 综合仿真模型 : 调用

' 继承关系
噪声源模型 <|-- 船舶辐射噪声模型
噪声源模型 <|-- 海洋环境噪声模型

note top of 主窗口 : {应用程序入口和容器}
note right of 数据管理器 : {系统核心数据中心}
note bottom of 仿真控制器 : {业务逻辑协调中心}

@enduml
