# -*- coding: utf-8 -*-
"""
仿真数据管理器

作为系统的核心组件，负责集中管理所有仿真参数和结果数据。
提供线程安全的数据访问接口。
"""

import threading
import numpy as np
from PyQt5.QtCore import QObject, pyqtSignal


class SimulationDataManager(QObject):
    """
    仿真数据管理器

    作为中央数据存储和管理中心，负责集中管理所有仿真参数和结果数据。
    提供线程安全的数据访问接口。
    """

    # 数据变更信号
    parameters_changed = pyqtSignal(str)  # 参数变更信号，参数为模块名称
    results_changed = pyqtSignal(str)     # 结果变更信号，参数为模块名称
    global_params_changed = pyqtSignal(str)  # 全局参数变更信号，参数为参数名

    # 仿真状态信号
    simulation_state_changed = pyqtSignal(str, bool)  # 仿真状态变更信号，参数为模块名称和是否在仿真

    def __init__(self):
        """
        初始化数据管理器
        """
        super().__init__()

        # 创建可重入锁
        self._lock = threading.RLock()

        # 初始化全局参数字典
        self._global_params = {
            'fs': 22050,        # 默认采样率 (Hz)
        }

        # 初始化参数和结果字典
        self._parameters = {
            'ship_noise': {
                'duration': 5.0,     # 默认持续时间 (s)
                # 'filter_order': 16385  # 滤波器阶数
            },      # 船舶辐射噪声参数
            'ambient_noise': {
                'duration': 5.0,     # 默认持续时间 (s)
                # 'filter_order': 16385  # 滤波器阶数
            },   # 海洋环境噪声参数
            'propagation': {},     # 声传播参数
            'integrated': {
                # 初始化综合仿真参数，为不同的选择框创建独立的参数
                'selected_channel_element_index': 0,  # 信道数据分析选项卡中的选择阵元
                'selected_signal_element_index': 0,   # 时域波形选项卡中的选择阵元
                'selected_spectrum_element_index': 0, # 频谱图选项卡中的选择阵元
                'selected_frequency': 0,              # 选中的频率
            }       # 综合仿真参数
        }

        self._results = {
            'ship_noise': {},      # 船舶辐射噪声结果
            'ambient_noise': {},   # 海洋环境噪声结果
            'propagation': {},     # 声传播结果
            'integrated': {}       # 综合仿真结果
        }

        # 初始化仿真状态字典
        self._simulation_states = {
            'ship_noise': False,      # 船舶辐射噪声仿真状态
            'ambient_noise': False,   # 海洋环境噪声仿真状态
            'propagation': False,     # 声传播仿真状态
            'integrated': False       # 综合仿真状态
        }

    def set_parameters(self, module, params):
        """
        设置模块参数

        注意：调用者应避免在调用后修改params字典。

        Args:
            module (str): 模块名称，如'ship_noise'
            params (dict): 参数字典
        """
        with self._lock:
            # 检查模块是否存在
            if module not in self._parameters:
                raise ValueError(f"未知模块: {module}")

            # 更新参数，对于大型数组数据，避免不必要的复制
            # 注意：这意味着调用者应避免在调用后修改params字典
            self._parameters[module] = params

        # 发送参数变更信号
        self.parameters_changed.emit(module)

    def update_parameter(self, module, param_key, value):
        """
        更新单个参数值

        提供细粒度的参数更新，避免每次更新单个参数时都要获取和设置整个参数字典

        Args:
            module (str): 模块名称，如'ship_noise'
            param_key (str): 参数键名
            value: 参数值
        """
        with self._lock:
            # 检查模块是否存在
            if module not in self._parameters:
                raise ValueError(f"未知模块: {module}")

            # 更新单个参数
            self._parameters[module][param_key] = value

        # 发送参数变更信号
        self.parameters_changed.emit(module)

    def update_nested_parameter(self, module, param_path, value):
        """
        更新嵌套参数值

        提供对嵌套字典结构的参数的细粒度更新

        Args:
            module (str): 模块名称，如'ship_noise'
            param_path (list): 参数路径，如['line_spectrum', 'frequencies', 0]表示第一个线谱频率
            value: 参数值
        """
        with self._lock:
            # 检查模块是否存在
            if module not in self._parameters:
                raise ValueError(f"未知模块: {module}")

            # 获取参数字典
            params = self._parameters[module]

            # 遍历路径，直到倒数第二个元素
            for key in param_path[:-1]:
                # 如果键不存在或不是字典/列表，创建一个新的字典
                if isinstance(key, int):
                    # 如果键是整数，确保当前节点是列表
                    if not isinstance(params, list):
                        params = []
                    # 确保列表长度足够
                    while len(params) <= key:
                        params.append({})
                    params = params[key]
                else:
                    # 如果键是字符串，确保当前节点是字典
                    if key not in params or not isinstance(params[key], (dict, list)):
                        params[key] = {}
                    params = params[key]

            # 设置最后一个键的值
            last_key = param_path[-1]
            if isinstance(last_key, int):
                # 如果最后的键是整数，确保当前节点是列表
                if not isinstance(params, list):
                    params = []
                # 确保列表长度足够
                while len(params) <= last_key:
                    params.append(None)
                params[last_key] = value
            else:
                # 如果最后的键是字符串，直接设置值
                params[last_key] = value

        # 发送参数变更信号
        self.parameters_changed.emit(module)

    def get_parameters(self, module):
        """
        获取模块参数

        注意：返回的是原始数据的引用，不是副本。
        调用者不应修改返回的数据，否则可能导致意外的副作用。
        如需修改数据，请使用set_parameters方法。

        Args:
            module (str): 模块名称，如'ship_noise'

        Returns:
            dict: 参数字典（直接引用，不是副本）
        """
        with self._lock:
            # 检查模块是否存在
            if module not in self._parameters:
                raise ValueError(f"未知模块: {module}")

            # 直接返回参数引用，避免不必要的复制
            # 注意：调用者应避免修改返回的字典
            return self._parameters[module]

    def get_all_parameters(self):
        """
        获取所有模块的参数

        Returns:
            dict: 包含所有模块参数的字典（直接引用，不是副本）
        """
        with self._lock:
            # 直接返回参数引用，避免不必要的复制
            return self._parameters

    def set_all_parameters(self, parameters):
        """
        设置所有模块的参数

        Args:
            parameters (dict): 包含所有模块参数的字典
        """
        with self._lock:
            # 更新参数
            for module, params in parameters.items():
                if module in self._parameters:
                    self._parameters[module] = params

        # 发送参数变更信号
        for module in parameters:
            if module in self._parameters:
                self.parameters_changed.emit(module)

    def set_result(self, module, key, value):
        """
        设置单个结果

        Args:
            module (str): 模块名称，如'ship_noise'
            key (str): 结果键名
            value: 结果值
        """
        with self._lock:
            # 检查模块是否存在
            if module not in self._results:
                raise ValueError(f"未知模块: {module}")

            # 更新结果
            self._results[module][key] = value

        # 发送结果变更信号
        self.results_changed.emit(module)

    def set_results(self, module, results):
        """
        设置模块所有结果

        注意：调用者应避免在调用后修改results字典。

        Args:
            module (str): 模块名称，如'ship_noise'
            results (dict): 结果字典
        """
        with self._lock:
            # 检查模块是否存在
            if module not in self._results:
                raise ValueError(f"未知模块: {module}")

            # 更新结果，对于大型数组数据，避免不必要的复制
            # 注意：这意味着调用者应避免在调用后修改results字典
            self._results[module] = results

        # 发送结果变更信号
        self.results_changed.emit(module)

    def get_result(self, module, key, default=None):
        """
        获取单个结果

        Args:
            module (str): 模块名称，如'ship_noise'
            key (str): 结果键名
            default: 如果结果不存在，返回的默认值

        Returns:
            结果值或默认值
        """
        with self._lock:
            # 检查模块是否存在
            if module not in self._results:
                raise ValueError(f"未知模块: {module}")

            # 返回结果或默认值
            return self._results[module].get(key, default)

    def get_results(self, module):
        """
        获取模块所有结果

        注意：返回的是原始数据的引用，不是副本。
        调用者不应修改返回的数据，否则可能导致意外的副作用。
        如需修改数据，请使用set_result或set_results方法。

        Args:
            module (str): 模块名称，如'ship_noise'

        Returns:
            dict: 结果字典（直接引用，不是副本）
        """
        with self._lock:
            # 检查模块是否存在
            if module not in self._results:
                raise ValueError(f"未知模块: {module}")

            # 直接返回结果引用，避免不必要的复制
            # 注意：调用者应避免修改返回的字典
            return self._results[module]

    def get_all_results(self):
        """
        获取所有模块的结果

        Returns:
            dict: 包含所有模块结果的字典（直接引用，不是副本）
        """
        with self._lock:
            # 直接返回结果引用，避免不必要的复制
            return self._results

    def has_result(self, module, key):
        """
        检查是否存在指定结果

        Args:
            module (str): 模块名称，如'ship_noise'
            key (str): 结果键名

        Returns:
            bool: 是否存在结果
        """
        with self._lock:
            # 检查模块是否存在
            if module not in self._results:
                raise ValueError(f"未知模块: {module}")

            # 检查结果是否存在
            return key in self._results[module]

    def clear_results(self, module=None):
        """
        清除结果

        Args:
            module (str, optional): 模块名称，如'ship_noise'。如果为None，清除所有模块的结果。
        """
        with self._lock:
            if module is None:
                # 清除所有模块的结果
                for mod in self._results:
                    self._results[mod] = {}
                    # 发送结果变更信号
                    self.results_changed.emit(mod)
            else:
                # 检查模块是否存在
                if module not in self._results:
                    raise ValueError(f"未知模块: {module}")

                # 清除指定模块的结果
                self._results[module] = {}
                # 发送结果变更信号
                self.results_changed.emit(module)

    def get_global_param(self, key):
        """
        获取全局参数

        如果参数不存在，将抛出KeyError异常

        Args:
            key (str): 参数名

        Returns:
            参数值
        """
        with self._lock:
            return self._global_params[key]

    def set_global_param(self, key, value):
        """
        设置全局参数

        Args:
            key (str): 参数名
            value: 参数值
        """
        with self._lock:
            self._global_params[key] = value

        # 发送参数变更信号
        self.global_params_changed.emit(key)

    def get_all_global_params(self):
        """
        获取所有全局参数

        Returns:
            dict: 全局参数字典（直接引用，不是副本）
        """
        with self._lock:
            return self._global_params

    def clear_all_data(self):
        """
        清除所有数据（参数和结果）
        """
        with self._lock:
            # 清除所有参数
            for module in self._parameters:
                self._parameters[module] = {}
                # 发送参数变更信号
                self.parameters_changed.emit(module)

            # 清除所有结果
            for module in self._results:
                self._results[module] = {}
                # 发送结果变更信号
                self.results_changed.emit(module)

    def set_module_simulating(self, module, is_simulating):
        """
        设置模块的仿真状态

        Args:
            module (str): 模块名称，如'ship_noise'
            is_simulating (bool): 是否正在仿真
        """
        with self._lock:
            # 检查模块是否存在
            if module not in self._simulation_states:
                raise ValueError(f"未知模块: {module}")

            # 如果状态没有变化，不做任何操作
            if self._simulation_states[module] == is_simulating:
                return

            # 更新仿真状态
            self._simulation_states[module] = is_simulating

        # 发送仿真状态变更信号
        self.simulation_state_changed.emit(module, is_simulating)

    def is_module_simulating(self, module):
        """
        检查模块是否正在仿真

        Args:
            module (str): 模块名称，如'ship_noise'

        Returns:
            bool: 是否正在仿真
        """
        with self._lock:
            # 检查模块是否存在
            if module not in self._simulation_states:
                raise ValueError(f"未知模块: {module}")

            # 返回仿真状态
            return self._simulation_states[module]

    def any_module_simulating(self):
        """
        检查是否有任何模块正在仿真

        Returns:
            bool: 是否有任何模块正在仿真
        """
        with self._lock:
            # 检查是否有任何模块正在仿真
            return any(self._simulation_states.values())