# -*- coding: utf-8 -*-
"""
菜单管理器

负责创建和管理主窗口的菜单和工具栏。
"""

from PyQt5.QtWidgets import QAction


class MenuManager:
    """
    菜单管理器

    负责创建和管理主窗口的菜单和工具栏。
    """

    def __init__(self, main_window, project_ui_manager, simulation_ui_manager):
        """
        初始化菜单管理器

        Args:
            main_window: 主窗口实例
            project_ui_manager: 项目UI管理器实例
            simulation_ui_manager: 仿真UI管理器实例
        """
        self.main_window = main_window
        self.project_ui_manager = project_ui_manager
        self.simulation_ui_manager = simulation_ui_manager

        # 创建菜单和工具栏
        self.create_menus()
        self.create_toolbars()

    def on_system_settings(self):
        """
        打开系统参数设置对话框
        """
        from src.ui.dialogs.system_settings_dialog import SystemSettingsDialog
        dialog = SystemSettingsDialog(self.main_window)
        dialog.exec_()

    def create_menus(self):
        """
        创建菜单栏
        """
        # 创建文件菜单
        file_menu = self.main_window.menuBar().addMenu("文件")

        # 新建项目动作
        new_action = QAction("新建项目", self.main_window)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.project_ui_manager.on_new_project)
        file_menu.addAction(new_action)

        # 打开项目动作
        open_action = QAction("打开项目", self.main_window)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self.project_ui_manager.on_open_project)
        file_menu.addAction(open_action)

        # 保存项目动作
        save_action = QAction("保存项目", self.main_window)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.project_ui_manager.on_save_project)
        file_menu.addAction(save_action)

        # 另存为动作
        save_as_action = QAction("另存为...", self.main_window)
        save_as_action.setShortcut("Ctrl+Shift+S")
        save_as_action.triggered.connect(self.project_ui_manager.on_save_project_as)
        file_menu.addAction(save_as_action)

        file_menu.addSeparator()

        # 导出信号动作
        export_action = QAction("导出信号", self.main_window)
        export_action.triggered.connect(self.project_ui_manager.on_export_signals)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        # 退出动作
        exit_action = QAction("退出", self.main_window)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.main_window.close)
        file_menu.addAction(exit_action)

        # 创建编辑菜单
        edit_menu = self.main_window.menuBar().addMenu("编辑")

        # 复制动作
        copy_action = QAction("复制", self.main_window)
        copy_action.setShortcut("Ctrl+C")
        copy_action.triggered.connect(self.main_window.on_copy)
        edit_menu.addAction(copy_action)

        # 粘贴动作
        paste_action = QAction("粘贴", self.main_window)
        paste_action.setShortcut("Ctrl+V")
        paste_action.triggered.connect(self.main_window.on_paste)
        edit_menu.addAction(paste_action)

        edit_menu.addSeparator()

        # 系统参数设置动作
        self.preferences_action = QAction("系统参数设置", self.main_window)
        self.preferences_action.triggered.connect(self.on_system_settings)
        edit_menu.addAction(self.preferences_action)

        # 创建视图菜单
        view_menu = self.main_window.menuBar().addMenu("视图")

        # 全屏动作
        fullscreen_action = QAction("全屏", self.main_window)
        fullscreen_action.setShortcut("F11")
        fullscreen_action.setCheckable(True)
        fullscreen_action.triggered.connect(self.main_window.on_toggle_fullscreen)
        view_menu.addAction(fullscreen_action)

        # 创建帮助菜单
        help_menu = self.main_window.menuBar().addMenu("帮助")

        # 关于动作
        about_action = QAction("关于", self.main_window)
        about_action.triggered.connect(self.main_window.on_about)
        help_menu.addAction(about_action)

    def create_toolbars(self):
        """
        创建工具栏
        """
        # 创建主工具栏
        main_toolbar = self.main_window.addToolBar("主工具栏")
        main_toolbar.setMovable(False)

        # 新建项目动作
        new_action = QAction("新建", self.main_window)
        new_action.triggered.connect(self.project_ui_manager.on_new_project)
        main_toolbar.addAction(new_action)

        # 打开项目动作
        open_action = QAction("打开", self.main_window)
        open_action.triggered.connect(self.project_ui_manager.on_open_project)
        main_toolbar.addAction(open_action)

        # 保存项目动作
        save_action = QAction("保存", self.main_window)
        save_action.triggered.connect(self.project_ui_manager.on_save_project)
        main_toolbar.addAction(save_action)

        # 另存为动作
        save_as_action = QAction("另存为", self.main_window)
        save_as_action.triggered.connect(self.project_ui_manager.on_save_project_as)
        main_toolbar.addAction(save_as_action)

        main_toolbar.addSeparator()

        # 开始仿真动作
        start_action = QAction("开始仿真", self.main_window)
        start_action.triggered.connect(self.simulation_ui_manager.on_start_simulation)
        main_toolbar.addAction(start_action)

        # 停止仿真动作
        stop_action = QAction("停止仿真", self.main_window)
        stop_action.triggered.connect(self.simulation_ui_manager.on_stop_simulation)
        main_toolbar.addAction(stop_action)

        # 添加分隔符
        main_toolbar.addSeparator()

        # 导出信号动作
        export_signals_action = QAction("导出信号", self.main_window)
        export_signals_action.triggered.connect(self.project_ui_manager.on_export_signals)
        main_toolbar.addAction(export_signals_action)

        # 系统参数设置动作
        self.system_settings_action = QAction("系统参数", self.main_window)
        self.system_settings_action.triggered.connect(self.on_system_settings)
        main_toolbar.addAction(self.system_settings_action)

    def set_system_settings_enabled(self, enabled):
        """
        设置系统参数设置动作的启用/禁用状态

        Args:
            enabled (bool): 是否启用
        """
        self.preferences_action.setEnabled(enabled)
        self.system_settings_action.setEnabled(enabled)
