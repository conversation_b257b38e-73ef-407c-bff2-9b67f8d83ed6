# -*- coding: utf-8 -*-
"""
综合仿真视图

用于显示综合仿真结果，包括信道数据分析、信号处理结果等。
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QTabWidget, QLabel, QTextBrowser, QSplitter,
    QTableWidget, QTableWidgetItem, QHeaderView, QHBoxLayout,
    QLineEdit, QPushButton, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from matplotlib.figure import Figure
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
import numpy as np
import pandas as pd
from scipy import signal


class IntegratedView(QWidget):
    """
    综合仿真视图

    用于显示综合仿真结果，包括信道数据分析、信号处理结果等。
    """

    # 自定义信号
    calculate_spectrum_requested = pyqtSignal(int, float, float)  # 计算频谱请求信号，参数为阵元索引、起始时间和结束时间

    def __init__(self, parent=None):
        """
        初始化综合仿真视图

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建选项卡用于切换不同视图
        self.tabs = QTabWidget()

        # 创建信道数据信息标签页
        self.channel_info_widget = QWidget()
        channel_info_layout = QVBoxLayout(self.channel_info_widget)
        channel_info_layout.setContentsMargins(0, 0, 0, 0)

        # 创建信道数据表格
        self.channel_info_table = QTableWidget()
        self.channel_info_table.setColumnCount(2)
        self.channel_info_table.setHorizontalHeaderLabels(['参数', '值'])
        self.channel_info_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.channel_info_table.setAlternatingRowColors(True)
        self.channel_info_table.setEditTriggers(QTableWidget.NoEditTriggers)  # 设置为只读

        # 创建频率列表表格
        self.frequency_table = QTableWidget()
        self.frequency_table.setColumnCount(1)  # 只显示频率值
        self.frequency_table.setHorizontalHeaderLabels(['频率 (Hz)'])
        self.frequency_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.frequency_table.setAlternatingRowColors(True)
        self.frequency_table.setEditTriggers(QTableWidget.NoEditTriggers)  # 设置为只读

        # 创建阵元位置表格
        self.array_table = QTableWidget()
        self.array_table.setColumnCount(3)
        self.array_table.setHorizontalHeaderLabels(['阵元ID', '距离 (m)', '深度 (m)'])
        self.array_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.array_table.setAlternatingRowColors(True)
        self.array_table.setEditTriggers(QTableWidget.NoEditTriggers)  # 设置为只读

        # 创建上部水平分割器，包含三个表格
        top_splitter = QSplitter(Qt.Horizontal)
        top_splitter.addWidget(self.channel_info_table)

        # 创建右侧垂直分割器，包含频率表格和阵元表格
        right_splitter = QSplitter(Qt.Vertical)
        right_splitter.addWidget(self.frequency_table)
        right_splitter.addWidget(self.array_table)
        right_splitter.setSizes([int(self.height() * 0.5), int(self.height() * 0.5)])

        top_splitter.addWidget(right_splitter)
        top_splitter.setSizes([int(self.width() * 0.4), int(self.width() * 0.6)])

        # 创建到达结构部分
        self.arrivals_splitter = QSplitter(Qt.Vertical)

        # 到达结构图
        arrivals_chart_widget = QWidget()
        arrivals_chart_layout = QVBoxLayout(arrivals_chart_widget)
        arrivals_chart_layout.setContentsMargins(0, 0, 0, 0)

        self.arrivals_canvas = FigureCanvas(Figure(figsize=(8, 4)))
        self.arrivals_fig = self.arrivals_canvas.figure
        self.arrivals_ax = self.arrivals_fig.add_subplot(111)
        self.arrivals_ax.set_title('到达结构')
        self.arrivals_ax.set_xlabel('到达时间 (s)')
        self.arrivals_ax.set_ylabel('振幅')
        self.arrivals_ax.grid(True)

        self.arrivals_toolbar = NavigationToolbar(self.arrivals_canvas, self)
        arrivals_chart_layout.addWidget(self.arrivals_toolbar)
        arrivals_chart_layout.addWidget(self.arrivals_canvas)

        # 到达结构表格
        self.arrivals_table = QTableWidget()
        self.arrivals_table.setColumnCount(7)
        self.arrivals_table.setHorizontalHeaderLabels([
            '到达序号', '到达时间 (s)', '幅度', '出射角 (°)', '到达角 (°)',
            '海面反射次数', '海底反射次数'
        ])
        self.arrivals_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.arrivals_table.setAlternatingRowColors(True)
        self.arrivals_table.setEditTriggers(QTableWidget.NoEditTriggers)  # 设置为只读

        # 将图表和表格添加到分割器
        self.arrivals_splitter.addWidget(arrivals_chart_widget)
        self.arrivals_splitter.addWidget(self.arrivals_table)
        self.arrivals_splitter.setSizes([int(self.height() * 0.6), int(self.height() * 0.4)])

        # 创建主垂直分割器，包含上部表格和下部到达结构
        main_splitter = QSplitter(Qt.Vertical)
        main_splitter.addWidget(top_splitter)
        main_splitter.addWidget(self.arrivals_splitter)
        main_splitter.setSizes([int(self.height() * 0.4), int(self.height() * 0.6)])

        # 添加到布局
        channel_info_layout.addWidget(main_splitter)

        # 创建时域波形标签页
        self.time_domain_widget = QWidget()
        time_domain_layout = QVBoxLayout(self.time_domain_widget)
        time_domain_layout.setContentsMargins(0, 0, 0, 0)

        # 时域信号图表
        self.time_domain_canvas = FigureCanvas(Figure(figsize=(8, 4)))
        self.time_domain_fig = self.time_domain_canvas.figure
        self.time_domain_ax = self.time_domain_fig.add_subplot(111)
        self.time_domain_ax.set_title('接收信号 (时域)')
        self.time_domain_ax.set_xlabel('时间 (s)')
        self.time_domain_ax.set_ylabel('幅度')
        self.time_domain_ax.grid(True)

        self.time_domain_toolbar = NavigationToolbar(self.time_domain_canvas, self)
        time_domain_layout.addWidget(self.time_domain_toolbar)
        time_domain_layout.addWidget(self.time_domain_canvas)



        # 创建频谱图标签页
        self.spectrum_widget = QWidget()
        spectrum_layout = QVBoxLayout(self.spectrum_widget)
        spectrum_layout.setContentsMargins(0, 0, 0, 0)

        # 频域信号图表
        self.freq_domain_canvas = FigureCanvas(Figure(figsize=(8, 4)))
        self.freq_domain_fig = self.freq_domain_canvas.figure
        self.freq_domain_ax = self.freq_domain_fig.add_subplot(111)
        self.freq_domain_ax.set_title('接收信号 (频域)')
        self.freq_domain_ax.set_xlabel('频率 (Hz)')
        self.freq_domain_ax.set_ylabel('功率谱密度 (dB/Hz)')
        self.freq_domain_ax.grid(True)

        self.freq_domain_toolbar = NavigationToolbar(self.freq_domain_canvas, self)
        spectrum_layout.addWidget(self.freq_domain_toolbar)
        spectrum_layout.addWidget(self.freq_domain_canvas)

        # 创建指向性标签页
        self.directivity_widget = QWidget()
        directivity_layout = QVBoxLayout(self.directivity_widget)
        directivity_layout.setContentsMargins(0, 0, 0, 0)

        # 创建指向性图表
        self.directivity_fig = Figure(figsize=(8, 6))
        self.directivity_canvas = FigureCanvas(self.directivity_fig)

        # 只创建线性图
        self.directivity_linear_ax = self.directivity_fig.add_subplot(111)
        self.directivity_linear_ax.set_title('指向性')
        self.directivity_linear_ax.set_xlabel('角度 (°)')
        self.directivity_linear_ax.set_ylabel('相对增益 (dB)')
        self.directivity_linear_ax.grid(True)

        # 添加工具栏
        self.directivity_toolbar = NavigationToolbar(self.directivity_canvas, self)

        # 添加到布局
        directivity_layout.addWidget(self.directivity_toolbar)
        directivity_layout.addWidget(self.directivity_canvas)

        # 创建互功率谱标签页
        self.cross_spectrum_widget = QWidget()
        cross_spectrum_layout = QVBoxLayout(self.cross_spectrum_widget)
        cross_spectrum_layout.setContentsMargins(0, 0, 0, 0)

        # 创建互功率谱图表
        self.cross_spectrum_fig = Figure(figsize=(8, 8))
        self.cross_spectrum_canvas = FigureCanvas(self.cross_spectrum_fig)

        # 创建两个子图：模值和相位
        self.csd_magnitude_ax = self.cross_spectrum_fig.add_subplot(211)  # 模值
        self.csd_phase_ax = self.cross_spectrum_fig.add_subplot(212)      # 相位

        # 设置标题和标签
        self.csd_magnitude_ax.set_title('互功率谱密度 - 模值')
        self.csd_magnitude_ax.set_xlabel('频率 (Hz)')
        self.csd_magnitude_ax.set_ylabel('|S_kl(f)|')
        self.csd_magnitude_ax.grid(True)

        self.csd_phase_ax.set_title('互功率谱密度 - 相位')
        self.csd_phase_ax.set_xlabel('频率 (Hz)')
        self.csd_phase_ax.set_ylabel('相位 (弧度)')
        self.csd_phase_ax.grid(True)

        # 添加工具栏
        self.cross_spectrum_toolbar = NavigationToolbar(self.cross_spectrum_canvas, self)

        # 添加到布局
        cross_spectrum_layout.addWidget(self.cross_spectrum_toolbar)
        cross_spectrum_layout.addWidget(self.cross_spectrum_canvas)

        # 创建相干函数标签页
        self.coherence_widget = QWidget()
        coherence_layout = QVBoxLayout(self.coherence_widget)
        coherence_layout.setContentsMargins(0, 0, 0, 0)

        # 创建相干函数图表
        self.coherence_fig = Figure(figsize=(8, 6))
        self.coherence_canvas = FigureCanvas(self.coherence_fig)

        # 创建相干函数子图
        self.coherence_only_ax = self.coherence_fig.add_subplot(111)

        # 设置标题和标签
        self.coherence_only_ax.set_title('相干函数')
        self.coherence_only_ax.set_xlabel('频率 (Hz)')
        self.coherence_only_ax.set_ylabel('相干函数值')
        self.coherence_only_ax.set_ylim([0, 1])
        self.coherence_only_ax.grid(True)

        # 添加工具栏
        self.coherence_toolbar = NavigationToolbar(self.coherence_canvas, self)

        # 添加到布局
        coherence_layout.addWidget(self.coherence_toolbar)
        coherence_layout.addWidget(self.coherence_canvas)

        # 添加到选项卡
        self.tabs.addTab(self.channel_info_widget, "信道数据分析")
        self.tabs.addTab(self.time_domain_widget, "时域波形")
        self.tabs.addTab(self.spectrum_widget, "频谱图")
        self.tabs.addTab(self.directivity_widget, "指向性")
        self.tabs.addTab(self.cross_spectrum_widget, "互功率谱")
        self.tabs.addTab(self.coherence_widget, "相干函数")

        # 添加到布局
        layout.addWidget(self.tabs)

        # 初始化当前选中的阵元和频率
        self.current_channel_element_index = 0  # 信道数据分析选项卡中的选中阵元
        self.current_signal_element_index = 0   # 时域波形选项卡中的选中阵元
        self.current_spectrum_element_index = 0  # 频谱图选项卡中的选中阵元
        self.current_element_index = 0  # 兼容旧接口
        self.current_frequency = 0  # 选中的频率
        self.current_fs = 44100  # 当前采样率

    def update_channel_data_dir(self, channel_data_dir):
        """
        更新信道数据目录显示

        Args:
            channel_data_dir: 信道数据目录
        """
        # 不再显示信道数据目录
        pass

    def update_channel_data_info(self, meta, frequencies, array_elements, t_first_global, t_last_global):
        """
        更新信道数据基本信息

        Args:
            meta: 元数据字典
            frequencies: 频率列表
            array_elements: 阵元位置列表
            t_first_global: 全局最早到达时间
            t_last_global: 全局最晚到达时间
        """
        # 清空表格
        self.channel_info_table.setRowCount(0)
        self.frequency_table.setRowCount(0)
        self.array_table.setRowCount(0)

        # 添加基本信息到信道数据表格
        self._add_table_row(self.channel_info_table, "全局最早到达时间", f"{t_first_global:.6f} 秒")
        self._add_table_row(self.channel_info_table, "全局最晚到达时间", f"{t_last_global:.6f} 秒")
        self._add_table_row(self.channel_info_table, "全局传播时延差", f"{t_last_global - t_first_global:.6f} 秒")
        self._add_table_row(self.channel_info_table, "频率数量", str(len(frequencies)))
        self._add_table_row(self.channel_info_table, "阵元数量", str(len(array_elements)))

        # 添加环境信息
        if 'environment' in meta:
            env = meta['environment']

            # 添加声源深度
            if 'tx_depth' in env:
                self._add_table_row(self.channel_info_table, "声源深度", f"{env['tx_depth']} 米")

            # 添加声速信息
            if 'ssp_type' in env:
                if env['ssp_type'] == 'constant':
                    if 'soundspeed' in env:
                        self._add_table_row(self.channel_info_table, "声速 (恒定)", f"{env['soundspeed']} 米/秒")
                else:
                    self._add_table_row(self.channel_info_table, "声速剖面类型", "深度依赖")

            # 添加水深信息
            if 'depth_type' in env:
                if env['depth_type'] == 'flat':
                    if 'depth' in env:
                        self._add_table_row(self.channel_info_table, "水深 (平坦)", f"{env['depth']} 米")
                else:
                    self._add_table_row(self.channel_info_table, "水深类型", "距离依赖")

            # 添加海底参数
            if 'bottom_soundspeed' in env:
                self._add_table_row(self.channel_info_table, "海底声速", f"{env['bottom_soundspeed']} 米/秒")
            if 'bottom_density' in env:
                self._add_table_row(self.channel_info_table, "海底密度", f"{env['bottom_density']} 千克/立方米")
            if 'bottom_absorption' in env:
                self._add_table_row(self.channel_info_table, "海底吸收系数", f"{env['bottom_absorption']} dB/波长")

            # 添加声源指向性信息
            if 'tx_directivity_enabled' in env:
                if env['tx_directivity_enabled']:
                    if 'directivity_data' in env and env['directivity_data']:
                        self._add_table_row(self.channel_info_table, "声源指向性", "已启用")
                        self._add_table_row(self.channel_info_table, "指向性数据点数", str(len(env['directivity_data'])))
                    else:
                        self._add_table_row(self.channel_info_table, "声源指向性", "已启用，但无数据")
                else:
                    self._add_table_row(self.channel_info_table, "声源指向性", "未启用")

        # 添加生成时间
        if 'generation_time' in meta:
            self._add_table_row(self.channel_info_table, "生成时间", meta['generation_time'])

        # 添加频率信息到频率表格
        for i, freq in enumerate(frequencies):
            self.frequency_table.insertRow(i)
            self.frequency_table.setItem(i, 0, QTableWidgetItem(str(freq)))

        # 添加阵元信息到阵元表格
        for i, element in enumerate(array_elements):
            self.array_table.insertRow(i)
            self.array_table.setItem(i, 0, QTableWidgetItem(str(i)))

            if isinstance(element, (list, tuple)) and len(element) >= 2:
                self.array_table.setItem(i, 1, QTableWidgetItem(str(element[0])))
                self.array_table.setItem(i, 2, QTableWidgetItem(str(element[1])))
            else:
                self.array_table.setItem(i, 1, QTableWidgetItem("N/A"))
                self.array_table.setItem(i, 2, QTableWidgetItem("N/A"))

    def _add_table_row(self, table, param, value):
        """
        向表格添加一行

        Args:
            table: 表格控件
            param: 参数名
            value: 参数值
        """
        row = table.rowCount()
        table.insertRow(row)
        table.setItem(row, 0, QTableWidgetItem(param))
        table.setItem(row, 1, QTableWidgetItem(value))

    def update_selected_channel_element(self, element_index):
        """
        更新信道数据分析选项卡中选中的阵元

        Args:
            element_index: 阵元索引
        """
        # 记录当前选中的信道数据分析阵元
        self.current_channel_element_index = element_index

    def update_selected_signal_element(self, element_index):
        """
        更新时域波形选项卡中选中的阵元

        Args:
            element_index: 阵元索引
        """
        # 记录当前选中的时域波形阵元
        self.current_signal_element_index = element_index

    def update_selected_spectrum_element(self, element_index):
        """
        更新频谱图选项卡中选中的阵元

        Args:
            element_index: 阵元索引
        """
        # 记录当前选中的频谱图阵元
        self.current_spectrum_element_index = element_index

    def update_selected_element(self, element_index):
        """
        更新选中的阵元（兼容旧接口）

        Args:
            element_index: 阵元索引
        """
        # 这个方法在视图中不需要做什么，因为选中的阵元是在控制面板中设置的
        # 但是我们可以在这里记录当前选中的阵元，以便在更新到达结构图时使用
        self.current_element_index = element_index

    def update_selected_frequency(self, frequency):
        """
        更新选中的频率

        Args:
            frequency: 频率
        """
        # 这个方法在视图中不需要做什么，因为选中的频率是在控制面板中设置的
        # 但是我们可以在这里记录当前选中的频率，以便在更新到达结构图时使用
        self.current_frequency = frequency

    def update_arrivals_plot(self, arrivals, frequency, element_id, element_position):
        """
        更新到达结构图和表格

        Args:
            arrivals: 到达结构数据（DataFrame）
            frequency: 频率
            element_id: 阵元ID
            element_position: 阵元位置
        """
        # 清除当前图表
        self.arrivals_ax.clear()

        # 清空表格
        self.arrivals_table.setRowCount(0)

        # 检查数据是否为空
        if arrivals is None or arrivals.empty:
            self.arrivals_ax.set_title(f"无到达结构数据 (频率: {frequency} Hz, 阵元: #{element_id})")
            self.arrivals_ax.grid(True)
            self.arrivals_canvas.draw()
            return

        # 提取到达时间和振幅
        arrival_times = arrivals['time_of_arrival'].values if 'time_of_arrival' in arrivals.columns else arrivals['arrival_time'].values if 'arrival_time' in arrivals.columns else np.array([])

        # 检查振幅是否为复数
        if 'arrival_amplitude' in arrivals.columns:
            amplitudes = arrivals['arrival_amplitude'].values
            # 如果振幅是复数，取绝对值
            if np.issubdtype(amplitudes.dtype, np.complexfloating):
                amplitudes = np.abs(amplitudes)
            else:
                # 尝试转换为复数并取绝对值
                try:
                    amplitudes = np.abs(amplitudes.astype(complex))
                except:
                    # 如果转换失败，直接使用原始值
                    pass
        else:
            # 如果没有振幅列，使用默认值
            amplitudes = np.ones_like(arrival_times)

        # 绘制到达结构
        self.arrivals_ax.stem(arrival_times, amplitudes, linefmt='b-', markerfmt='bo', basefmt='r-')

        # 设置标题和标签
        element_pos_str = f"({element_position[0]}, {element_position[1]})" if isinstance(element_position, (list, tuple)) and len(element_position) >= 2 else str(element_position)
        self.arrivals_ax.set_title(f"到达结构 (频率: {frequency} Hz, 阵元: #{element_id} {element_pos_str})")
        self.arrivals_ax.set_xlabel("到达时间 (s)")
        self.arrivals_ax.set_ylabel("振幅")
        self.arrivals_ax.grid(True)

        # 如果有表面和底部反射次数列，添加注释
        if 'surface_bounces' in arrivals.columns and 'bottom_bounces' in arrivals.columns:
            # 限制注释数量，避免过多注释导致图表混乱
            max_annotations = 20
            if len(arrival_times) > max_annotations:
                # 如果到达次数太多，只标注振幅最大的几个
                indices = np.argsort(amplitudes)[-max_annotations:]
                for i in indices:
                    t = arrival_times[i]
                    a = amplitudes[i]
                    sb = arrivals['surface_bounces'].values[i]
                    bb = arrivals['bottom_bounces'].values[i]
                    self.arrivals_ax.annotate(f"S{sb}B{bb}", (t, a),
                                            textcoords="offset points",
                                            xytext=(0, 10),
                                            ha='center',
                                            fontsize=8)
            else:
                # 如果到达次数不多，标注所有的
                for i, (t, a, sb, bb) in enumerate(zip(arrival_times, amplitudes,
                                                    arrivals['surface_bounces'].values,
                                                    arrivals['bottom_bounces'].values)):
                    self.arrivals_ax.annotate(f"S{sb}B{bb}", (t, a),
                                            textcoords="offset points",
                                            xytext=(0, 10),
                                            ha='center',
                                            fontsize=8)

        # 更新图表
        self.arrivals_canvas.draw()

        # 更新表格
        self.arrivals_table.setRowCount(len(arrivals))

        for i, (_, row) in enumerate(arrivals.iterrows()):
            # 到达序号
            arrival_number = i
            if 'arrival_number' in row:
                if hasattr(row.arrival_number, 'real'):
                    arrival_number = int(row.arrival_number.real)
                else:
                    arrival_number = int(row.arrival_number)
            self.arrivals_table.setItem(i, 0, QTableWidgetItem(str(arrival_number)))

            # 到达时间
            arrival_time = 0.0
            if 'time_of_arrival' in row:
                if hasattr(row.time_of_arrival, 'real'):
                    arrival_time = row.time_of_arrival.real
                else:
                    arrival_time = row.time_of_arrival
            elif 'arrival_time' in row:
                if hasattr(row.arrival_time, 'real'):
                    arrival_time = row.arrival_time.real
                else:
                    arrival_time = row.arrival_time
            self.arrivals_table.setItem(i, 1, QTableWidgetItem(f"{arrival_time:.6f}"))

            # 幅度
            amp_complex = 0.0
            if 'arrival_amplitude' in row:
                amp_complex = row.arrival_amplitude

            # 检查是否为复数
            if hasattr(amp_complex, 'real') and hasattr(amp_complex, 'imag'):
                # 使用科学计数法格式化实部和虚部
                real_part = f"{amp_complex.real:.2e}"
                # 处理虚部的符号，避免出现 "+-" 的情况
                if amp_complex.imag >= 0:
                    imag_part = f"+{amp_complex.imag:.2e}"
                else:
                    imag_part = f"{amp_complex.imag:.2e}"  # 负数会自带负号
                amp_str = f"{real_part}{imag_part}j"
            else:
                # 如果不是复数，直接使用科学计数法格式化
                amp_str = f"{amp_complex:.2e}"

            self.arrivals_table.setItem(i, 2, QTableWidgetItem(amp_str))

            # 出射角
            departure_angle = 0.0
            if 'angle_of_departure' in row:
                if hasattr(row.angle_of_departure, 'real'):
                    departure_angle = row.angle_of_departure.real
                else:
                    departure_angle = row.angle_of_departure
            self.arrivals_table.setItem(i, 3, QTableWidgetItem(f"{departure_angle:.2f}"))

            # 到达角
            arrival_angle = 0.0
            if 'angle_of_arrival' in row:
                if hasattr(row.angle_of_arrival, 'real'):
                    arrival_angle = row.angle_of_arrival.real
                else:
                    arrival_angle = row.angle_of_arrival
            self.arrivals_table.setItem(i, 4, QTableWidgetItem(f"{arrival_angle:.2f}"))

            # 海面反射次数
            surface_bounces = 0
            if 'surface_bounces' in row:
                if hasattr(row.surface_bounces, 'real'):
                    surface_bounces = int(row.surface_bounces.real)
                else:
                    surface_bounces = int(row.surface_bounces)
            self.arrivals_table.setItem(i, 5, QTableWidgetItem(str(surface_bounces)))

            # 海底反射次数
            bottom_bounces = 0
            if 'bottom_bounces' in row:
                if hasattr(row.bottom_bounces, 'real'):
                    bottom_bounces = int(row.bottom_bounces.real)
                else:
                    bottom_bounces = int(row.bottom_bounces)
            self.arrivals_table.setItem(i, 6, QTableWidgetItem(str(bottom_bounces)))

    def on_calculate_spectrum_clicked(self, element_index, start_time, end_time):
        """
        计算频谱按钮点击事件处理

        Args:
            element_index: 阵元索引
            start_time: 起始时间
            end_time: 结束时间
        """
        # 发送计算频谱信号
        print(f"计算频谱: 阵元 #{element_index}, 起始时间: {start_time}, 结束时间: {end_time if end_time != 0.0 else '全部'}")

        # 如果end_time为0.0，表示用户想使用全部信号
        self.calculate_spectrum_requested.emit(
            element_index,
            start_time,
            end_time
        )

        # 切换到频谱图标签页
        self.tabs.setCurrentWidget(self.spectrum_widget)

    def update_received_signal_plot(self, source_signal, received_signal, element_index):
        """
        更新接收信号时域波形

        Args:
            source_signal: 源信号数据字典，包含time_data和signal
            received_signal: 接收信号数据字典，包含time_data和signal
            element_index: 阵元索引
        """
        # 清除当前图表
        self.time_domain_ax.clear()

        # 绘制接收信号时域波形
        if received_signal and 'time_data' in received_signal and 'signal' in received_signal:
            time_data = received_signal['time_data']
            signal = received_signal['signal']

            # 检查是否有原始信号（未叠加噪声的信号）
            has_original_signal = 'original_signal' in received_signal

            # 绘制接收信号
            if has_original_signal:
                # 如果有原始信号，绘制原始信号和叠加噪声后的信号
                original_signal = received_signal['original_signal']
                self.time_domain_ax.plot(time_data, original_signal, 'b-', alpha=0.5, label='原始信号')
                self.time_domain_ax.plot(time_data, signal, 'r-', label='叠加噪声后的信号')
                self.time_domain_ax.legend()
            else:
                # 如果没有原始信号，只绘制接收信号
                self.time_domain_ax.plot(time_data, signal)

            self.time_domain_ax.set_title(f'接收信号 (阵元 #{element_index}, 时域)')
            self.time_domain_ax.set_xlabel('时间 (s)')
            self.time_domain_ax.set_ylabel('幅度')
            self.time_domain_ax.grid(True)

            # 设置合适的y轴范围
            max_amp = np.max(np.abs(signal))
            self.time_domain_ax.set_ylim([-max_amp * 1.1, max_amp * 1.1])

            # 更新图表
            self.time_domain_fig.tight_layout()
            self.time_domain_canvas.draw()

            # 保存当前信号数据的采样率，以便后续设置频谱图的x轴范围
            self.current_fs = source_signal.get('metadata', {}).get('fs', 44100)

            # 保存信号时间范围，供其他组件使用
            self.signal_time_range = {
                'start_time': time_data[0],
                'end_time': time_data[-1]
            }

        # 切换到时域波形标签页
        self.tabs.setCurrentWidget(self.time_domain_widget)

    def update_spectrum_plot(self, freqs, psd_db, element_index, signal_type='叠加噪声后'):
        """
        更新频谱图

        Args:
            freqs: 频率数组
            psd_db: 功率谱密度数组（dB）
            element_index: 阵元索引
            signal_type: 信号类型描述
        """
        # 清除当前图表
        self.freq_domain_ax.clear()

        # 绘制频谱
        self.freq_domain_ax.plot(freqs, psd_db)
        self.freq_domain_ax.set_title(f'接收信号 (阵元 #{element_index}, {signal_type})')
        self.freq_domain_ax.set_xlabel('频率 (Hz)')
        self.freq_domain_ax.set_ylabel('功率谱密度 (dB/Hz)')
        self.freq_domain_ax.grid(True)

        # 设置x轴范围
        fs = getattr(self, 'current_fs', 44100)
        self.freq_domain_ax.set_xlim([0, min(fs/2, 2500)])  # 限制到2500Hz或Nyquist频率

        # 更新图表
        self.freq_domain_fig.tight_layout()
        self.freq_domain_canvas.draw()

        # 保存当前显示的阵元索引
        self.current_spectrum_element_index = element_index

    def update_spectrum_plot_empty(self, element_index):
        """
        显示空的频谱图，用于提示用户计算频谱

        Args:
            element_index: 阵元索引
        """
        # 清除当前图表
        self.freq_domain_ax.clear()

        # 设置标题和提示信息
        self.freq_domain_ax.set_title(f'接收信号 (阵元 #{element_index})')
        self.freq_domain_ax.text(0.5, 0.5, f'阵元 #{element_index} 的频谱尚未计算\n请点击"计算频谱"按钮计算所有阵元的频谱',
                                horizontalalignment='center', verticalalignment='center',
                                transform=self.freq_domain_ax.transAxes, fontsize=12)

        # 设置坐标轴标签
        self.freq_domain_ax.set_xlabel('频率 (Hz)')
        self.freq_domain_ax.set_ylabel('功率谱密度 (dB/Hz)')
        self.freq_domain_ax.grid(True)

        # 更新图表
        self.freq_domain_fig.tight_layout()
        self.freq_domain_canvas.draw()

        # 保存当前显示的阵元索引
        self.current_spectrum_element_index = element_index

    def update_directivity_plot(self, angles, pattern, params=None):
        """
        更新指向性图表

        Args:
            angles: 角度数组（度）
            pattern: 波束方向图（dB）
            params: 参数字典，包含计算参数
        """
        # 添加调试输出
        print(f"从数据管理器读取指向性计算结果: {len(angles)} 个角度, 波束方向图值范围: [{np.min(pattern) if len(pattern) > 0 else 'N/A'}, {np.max(pattern) if len(pattern) > 0 else 'N/A'}]")

        # 清除当前图表
        self.directivity_linear_ax.clear()

        # 检查数据是否有效
        if angles is None or pattern is None or len(angles) == 0 or len(pattern) == 0:
            print("警告: 指向性数据无效，无法绘制图表")
            self.directivity_linear_ax.set_title('指向性计算失败 - 无有效数据')
            self.directivity_linear_ax.set_xlabel('角度 (°)')
            self.directivity_linear_ax.set_ylabel('相对增益 (dB)')
            self.directivity_linear_ax.grid(True)
            self.directivity_fig.tight_layout()
            self.directivity_canvas.draw()
            return

        # 检查数据中是否有NaN或无穷大值
        if np.any(~np.isfinite(pattern)):
            print(f"警告: 波束方向图包含 {np.sum(~np.isfinite(pattern))} 个NaN或无穷大值，将被替换为-100")
            pattern = np.where(np.isfinite(pattern), pattern, -100)

        # 检查是否全为零
        if np.all(pattern == 0):
            print("警告: 波束方向图全为零，可能是计算过程中出现问题")
            self.directivity_linear_ax.set_title('指向性计算结果异常 - 全为零')
            self.directivity_linear_ax.plot(angles, pattern, 'b-', linewidth=2)
            self.directivity_linear_ax.set_xlabel('角度 (°)')
            self.directivity_linear_ax.set_ylabel('相对增益 (dB)')
            self.directivity_linear_ax.set_ylim([-40, 0])
            self.directivity_linear_ax.grid(True)
            self.directivity_fig.tight_layout()
            self.directivity_canvas.draw()
            return

        # 设置y轴范围，限制最小值为-40dB
        min_val = max(np.min(pattern), -40)

        # 绘制线性图
        self.directivity_linear_ax.plot(angles, pattern, 'b-', linewidth=2)

        # 添加0dB参考线
        self.directivity_linear_ax.axhline(y=0, color='r', linestyle='--', alpha=0.7)

        # 添加-3dB参考线（半功率点）
        self.directivity_linear_ax.axhline(y=-3, color='g', linestyle='--', alpha=0.7)
        self.directivity_linear_ax.text(angles[-1], -3, "-3dB", va='center', ha='left', color='g')

        # 标记主瓣方向
        if np.any(np.isfinite(pattern)):
            max_idx = np.argmax(pattern)
            max_angle = angles[max_idx]
            self.directivity_linear_ax.plot(max_angle, 0, 'ro')
            self.directivity_linear_ax.text(max_angle, 0, f" 主瓣: {max_angle:.1f}°", va='bottom')

        # 计算-3dB波束宽度
        try:
            # 确保pattern中有有限值
            if not np.any(np.isfinite(pattern)):
                print("警告: 波束方向图中没有有限值，无法计算波束宽度")
                return

            # 使用有限值的最大值
            valid_pattern = pattern[np.isfinite(pattern)]
            if len(valid_pattern) == 0:
                print("警告: 波束方向图中没有有效值，无法计算波束宽度")
                return

            half_power_level = np.max(valid_pattern) - 3

            # 确保half_power_level是有限值
            if not np.isfinite(half_power_level):
                print(f"警告: 半功率点电平 {half_power_level} 不是有限值，无法计算波束宽度")
                return

            above_half_power = pattern >= half_power_level

            # 检查above_half_power是否包含True值
            if not np.any(above_half_power):
                print("警告: 没有点高于半功率点电平，无法计算波束宽度")
                return

            regions = np.where(np.diff(above_half_power.astype(int)))[0]

            print(f"半功率点电平: {half_power_level:.2f} dB")
            print(f"高于半功率点的点数: {np.sum(above_half_power)}")
            print(f"区域边界数量: {len(regions)}")

            if len(regions) >= 2:
                # 找到主瓣区域
                main_lobe_start = None
                main_lobe_end = None

                for i in range(0, len(regions), 2):
                    if i+1 < len(regions):
                        if max_idx >= regions[i] and max_idx <= regions[i+1]:
                            main_lobe_start = regions[i]
                            main_lobe_end = regions[i+1]
                            break

                # 如果没有找到包含最大值的区域，使用第一个区域
                if main_lobe_start is None and len(regions) >= 2:
                    print("警告: 未找到包含最大值的区域，使用第一个区域")
                    main_lobe_start = regions[0]
                    main_lobe_end = regions[1]
        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            print(f"计算波束宽度时出错: {str(e)}")
            print(f"详细错误信息:\n{error_traceback}")
            return

        # 如果找到了主瓣区域，计算波束宽度
        if 'main_lobe_start' in locals() and 'main_lobe_end' in locals() and main_lobe_start is not None and main_lobe_end is not None:
            try:
                # 计算精确的-3dB点（线性插值）
                left_idx = main_lobe_start
                right_idx = main_lobe_end + 1

                # 左侧-3dB点
                if left_idx > 0:
                    # 确保插值点是有限值
                    if np.isfinite(pattern[left_idx]) and np.isfinite(pattern[left_idx+1]):
                        left_angle = np.interp(half_power_level,
                                              [pattern[left_idx], pattern[left_idx+1]],
                                              [angles[left_idx], angles[left_idx+1]])
                    else:
                        print(f"警告: 左侧插值点包含无限值: [{pattern[left_idx]}, {pattern[left_idx+1]}]")
                        left_angle = angles[left_idx]
                else:
                    left_angle = angles[0]

                # 右侧-3dB点
                if right_idx < len(pattern):
                    # 确保插值点是有限值
                    if np.isfinite(pattern[right_idx-1]) and np.isfinite(pattern[right_idx]):
                        right_angle = np.interp(half_power_level,
                                               [pattern[right_idx-1], pattern[right_idx]],
                                               [angles[right_idx-1], angles[right_idx]])
                    else:
                        print(f"警告: 右侧插值点包含无限值: [{pattern[right_idx-1]}, {pattern[right_idx]}]")
                        right_angle = angles[right_idx-1]
                else:
                    right_angle = angles[-1]

                # 计算波束宽度
                beamwidth = right_angle - left_angle

                # 确保波束宽度是有限值且为正值
                if np.isfinite(beamwidth) and beamwidth > 0:
                    # 标记-3dB点和波束宽度
                    self.directivity_linear_ax.plot([left_angle, right_angle], [half_power_level, half_power_level], 'g-', linewidth=2)
                    self.directivity_linear_ax.plot([left_angle, left_angle], [min_val, half_power_level], 'g--', alpha=0.5)
                    self.directivity_linear_ax.plot([right_angle, right_angle], [min_val, half_power_level], 'g--', alpha=0.5)

                    # 添加波束宽度标注
                    mid_angle = (left_angle + right_angle) / 2
                    self.directivity_linear_ax.text(mid_angle, half_power_level - 2,
                                                  f"波束宽度: {beamwidth:.1f}°",
                                                  ha='center', va='top',
                                                  bbox=dict(boxstyle='round', facecolor='white', alpha=0.7))
                else:
                    print(f"警告: 计算的波束宽度 {beamwidth} 无效")
            except Exception as e:
                print(f"计算波束宽度细节时出错: {str(e)}")

        # 设置标题和标签
        self.directivity_linear_ax.set_title('指向性')
        self.directivity_linear_ax.set_xlabel('角度 (°)')
        self.directivity_linear_ax.set_ylabel('相对增益 (dB)')
        self.directivity_linear_ax.grid(True)
        self.directivity_linear_ax.set_ylim([min_val, 0])

        # 添加参数信息
        if params:
            # 格式化参数文本
            reference_position = params.get('reference_position', 'center')
            if reference_position == 'center':
                ref_pos_text = "阵列中心"
            elif reference_position == 'first':
                ref_pos_text = "阵列第一个元素"
            elif reference_position == 'last':
                ref_pos_text = "阵列最后一个元素"
            else:
                ref_pos_text = str(reference_position)

            weighting = params.get('weighting', 'uniform')
            if weighting == 'uniform':
                weight_text = "均匀加权"
            elif weighting == 'hanning':
                weight_text = "汉宁窗"
            elif weighting == 'hamming':
                weight_text = "汉明窗"
            elif weighting == 'blackman':
                weight_text = "布莱克曼窗"
            else:
                weight_text = str(weighting)

            delay_method = params.get('delay_method', 'position_based')
            if delay_method == 'position_based':
                delay_text = "基于位置关系"
            elif delay_method == 'phase':
                delay_text = "相位补偿"
            elif delay_method == 'integer':
                delay_text = "整数样本延迟"
            elif delay_method == 'fractional':
                delay_text = "分数延迟"
            else:
                delay_text = str(delay_method)

            param_text = f"声速: {params.get('sound_speed', 1500)} m/s\n"
            param_text += f"参考位置: {ref_pos_text}\n"
            param_text += f"加权方式: {weight_text}\n"
            param_text += f"时延补偿: {delay_text}"

            # 在线性图上添加参数文本
            self.directivity_linear_ax.text(
                0.02, 0.02, param_text,
                transform=self.directivity_linear_ax.transAxes,
                fontsize=8, verticalalignment='bottom',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.7)
            )

        # 更新图表
        self.directivity_fig.tight_layout()
        self.directivity_canvas.draw()

        # 切换到指向性标签页
        self.tabs.setCurrentWidget(self.directivity_widget)

    def update_cross_spectrum_plot(self, freqs, csd_magnitude, csd_phase_unwrapped, coherence=None, element_pair=None, params=None):
        """
        更新互功率谱图表

        Args:
            freqs: 频率数组
            csd_magnitude: 互功率谱密度的模值
            csd_phase_unwrapped: 互功率谱密度的解缠绕相位
            coherence: 相干函数值（可选，用于相干函数标签页）
            element_pair: 阵元对 [element_id1, element_id2]
            params: 参数字典
        """
        print(f"update_cross_spectrum_plot被调用")
        print(f"频率点数: {len(freqs)}, 模值点数: {len(csd_magnitude)}, 相位点数: {len(csd_phase_unwrapped)}")
        if coherence is not None:
            print(f"相干函数点数: {len(coherence)}")
        else:
            print("未提供相干函数数据")

        # 清除当前图表
        self.csd_magnitude_ax.clear()
        self.csd_phase_ax.clear()

        # 绘制互功率谱密度的模值和相位
        if len(freqs) > 0 and len(csd_magnitude) > 0:
            print("开始绘制互功率谱密度图表")
            # 将模值转换为dB
            csd_magnitude_db = 10 * np.log10(np.where(csd_magnitude > 0, csd_magnitude, 1e-10))
            print(f"互功率谱密度模值转换为dB，范围: [{np.min(csd_magnitude_db)}, {np.max(csd_magnitude_db)}] dB")

            # 绘制模值(dB)
            self.csd_magnitude_ax.plot(freqs, csd_magnitude_db)

            # 设置标题和标签
            title = "互功率谱密度 - 模值"
            if element_pair:
                title += f" (阵元 {element_pair[0]} - 阵元 {element_pair[1]})"
            self.csd_magnitude_ax.set_title(title)
            self.csd_magnitude_ax.set_xlabel("频率 (Hz)")
            self.csd_magnitude_ax.set_ylabel("|S_kl(f)| (dB re 1/Hz)")

            # 添加网格
            self.csd_magnitude_ax.grid(True)

            # 绘制相位
            if len(csd_phase_unwrapped) > 0:
                self.csd_phase_ax.plot(freqs, csd_phase_unwrapped)

                # 设置标题和标签
                title = "互功率谱密度 - 相位"
                if element_pair:
                    title += f" (阵元 {element_pair[0]} - 阵元 {element_pair[1]})"
                self.csd_phase_ax.set_title(title)
                self.csd_phase_ax.set_xlabel("频率 (Hz)")
                self.csd_phase_ax.set_ylabel("相位 (弧度)")

                # 添加网格
                self.csd_phase_ax.grid(True)
        else:
            print("没有互功率谱密度数据，显示提示信息")
            # 如果没有数据，显示提示信息
            self.csd_magnitude_ax.text(0.5, 0.5, "没有互功率谱密度数据",
                                     horizontalalignment='center',
                                     verticalalignment='center',
                                     transform=self.csd_magnitude_ax.transAxes)
            self.csd_phase_ax.text(0.5, 0.5, "没有互功率谱密度数据",
                                 horizontalalignment='center',
                                 verticalalignment='center',
                                 transform=self.csd_phase_ax.transAxes)

        # 调整子图布局
        self.cross_spectrum_fig.tight_layout()

        # 刷新图表
        self.cross_spectrum_canvas.draw()
        print("互功率谱图表已刷新")

        # 切换到互功率谱标签页
        self.tabs.setCurrentWidget(self.cross_spectrum_widget)
        print("已切换到互功率谱标签页")

        # 如果有相干函数数据，也更新相干函数图表
        if coherence is not None and len(coherence) > 0:
            print("调用update_coherence_plot更新相干函数图表")
            self.update_coherence_plot(freqs, coherence, element_pair, params)

    def update_coherence_plot(self, freqs, coherence, element_pair=None, params=None):
        """
        更新相干函数图表

        Args:
            freqs: 频率数组
            coherence: 相干函数值
            element_pair: 阵元对 [element_id1, element_id2]
            params: 参数字典
        """
        # 清除当前图表
        self.coherence_only_ax.clear()

        # 绘制相干函数
        if len(freqs) > 0 and len(coherence) > 0:
            # 绘制相干函数
            self.coherence_only_ax.plot(freqs, coherence)

            # 设置标题和标签
            title = "相干函数"
            if element_pair:
                title += f" (阵元 {element_pair[0]} - 阵元 {element_pair[1]})"
            if params:
                # 添加参数信息到标题
                start_time = params.get('start_time', 0.0)
                end_time = params.get('end_time', 0.0)
                title += f"\n时间范围: [{start_time:.4f}, {end_time:.4f}] s"

            self.coherence_only_ax.set_title(title)
            self.coherence_only_ax.set_xlabel("频率 (Hz)")
            self.coherence_only_ax.set_ylabel("相干函数值")

            # 设置y轴范围
            self.coherence_only_ax.set_ylim([0, 1])

            # 添加网格
            self.coherence_only_ax.grid(True)

            # 添加0.5和0.9参考线
            self.coherence_only_ax.axhline(y=0.5, color='g', linestyle='--', alpha=0.3)
            self.coherence_only_ax.axhline(y=0.9, color='r', linestyle='--', alpha=0.3)
        else:
            # 如果没有数据，显示提示信息
            self.coherence_only_ax.text(0.5, 0.5, "没有相干函数数据",
                                      horizontalalignment='center',
                                      verticalalignment='center',
                                      transform=self.coherence_only_ax.transAxes)

        # 调整子图布局
        self.coherence_fig.tight_layout()

        # 刷新图表
        self.coherence_canvas.draw()

        # 切换到相干函数标签页
        self.tabs.setCurrentWidget(self.coherence_widget)

    def clear_channel_data_info(self):
        """
        清空信道数据信息
        """
        # 清空表格
        self.channel_info_table.setRowCount(0)
        self.frequency_table.setRowCount(0)
        self.array_table.setRowCount(0)

    def clear_arrivals_plot(self):
        """
        清空到达结构图
        """
        self.arrivals_ax.clear()
        self.arrivals_ax.set_title('到达结构')
        self.arrivals_ax.set_xlabel('到达时间 (s)')
        self.arrivals_ax.set_ylabel('幅度')
        self.arrivals_ax.grid(True)
        # 不立即重绘，等待批量重绘

        # 清空表格
        self.arrivals_table.setRowCount(0)

    def clear_received_signal_plot(self):
        """
        清空接收信号时域波形
        """
        self.time_domain_ax.clear()
        self.time_domain_ax.set_title('接收信号时域波形')
        self.time_domain_ax.set_xlabel('时间 (s)')
        self.time_domain_ax.set_ylabel('幅度')
        self.time_domain_ax.grid(True)
        # 不立即重绘，等待批量重绘

    def clear_spectrum_plot(self):
        """
        清空频谱图
        """
        self.freq_domain_ax.clear()
        self.freq_domain_ax.set_title('功率谱密度')
        self.freq_domain_ax.set_xlabel('频率 (Hz)')
        self.freq_domain_ax.set_ylabel('功率谱密度 (dB re 1μPa²/Hz)')
        self.freq_domain_ax.grid(True)
        # 不立即重绘，等待批量重绘

    def clear_directivity_plot(self):
        """
        清空指向性图
        """
        self.directivity_linear_ax.clear()
        self.directivity_linear_ax.set_title('指向性图')
        self.directivity_linear_ax.set_xlabel('角度 (度)')
        self.directivity_linear_ax.set_ylabel('归一化增益 (dB)')
        self.directivity_linear_ax.grid(True)
        # 不立即重绘，等待批量重绘

    def clear_cross_spectrum_plot(self):
        """
        清空互功率谱图
        """
        # 清空互功率谱图
        self.csd_magnitude_ax.clear()
        self.csd_magnitude_ax.set_title('互功率谱密度 - 模值')
        self.csd_magnitude_ax.set_xlabel('频率 (Hz)')
        self.csd_magnitude_ax.set_ylabel('模值 (dB)')
        self.csd_magnitude_ax.grid(True)

        self.csd_phase_ax.clear()
        self.csd_phase_ax.set_title('互功率谱密度 - 相位')
        self.csd_phase_ax.set_xlabel('频率 (Hz)')
        self.csd_phase_ax.set_ylabel('相位 (弧度)')
        self.csd_phase_ax.grid(True)

        # 清空相干函数图
        self.coherence_only_ax.clear()
        self.coherence_only_ax.set_title('相干函数')
        self.coherence_only_ax.set_xlabel('频率 (Hz)')
        self.coherence_only_ax.set_ylabel('相干函数值')
        self.coherence_only_ax.set_ylim([0, 1])
        self.coherence_only_ax.grid(True)
        # 不立即重绘，等待批量重绘

    def clear_all_plots_and_redraw(self):
        """
        清空所有图表并批量重绘
        """
        # 清空所有图表（不重绘）
        self.clear_arrivals_plot()
        self.clear_received_signal_plot()
        self.clear_spectrum_plot()
        self.clear_directivity_plot()
        self.clear_cross_spectrum_plot()

        # 批量重绘所有画布
        self.arrivals_canvas.draw()
        self.time_domain_canvas.draw()
        self.freq_domain_canvas.draw()
        self.directivity_canvas.draw()
        self.cross_spectrum_canvas.draw()
        self.coherence_canvas.draw()
