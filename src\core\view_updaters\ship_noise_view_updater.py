# -*- coding: utf-8 -*-
"""
船舶辐射噪声视图更新器

负责将船舶辐射噪声仿真结果转换为视图更新，将UI更新逻辑从主窗口分离出来。
"""

import numpy as np
from PyQt5.QtCore import QObject


class ShipNoiseViewUpdater(QObject):
    """
    船舶辐射噪声视图更新器

    负责将船舶辐射噪声仿真结果转换为视图更新，将UI更新逻辑从主窗口分离出来。
    """

    def __init__(self, data_manager, view):
        """
        初始化船舶辐射噪声视图更新器

        Args:
            data_manager: 数据管理器实例
            view: 船舶辐射噪声视图实例
        """
        super().__init__()

        # 保存数据管理器和视图引用
        self.data_manager = data_manager
        self.view = view

        # 连接数据管理器的结果变更信号
        self.data_manager.results_changed.connect(self.on_results_changed)

    def on_results_changed(self, module):
        """
        处理数据管理器的结果变更信号

        Args:
            module (str): 变更的模块名称
        """
        # 只处理船舶辐射噪声模块的变更
        if module != 'ship_noise':
            return

        # 更新视图
        self.update_view()

    def reset_continuous_spectrum_charts(self):
        """
        重置连续谱图表
        """
        # 创建空数据
        empty_time = np.array([0, 1])  # 简单的时间范围
        empty_signal = np.zeros_like(empty_time)
        empty_freq = np.array([0, 1])  # 简单的频率范围
        empty_psd = np.zeros_like(empty_freq)

        # 更新图表为空
        self.view.update_continuous_spectrum(
            time_data=empty_time,
            time_signal=empty_signal,
            freq_data=empty_freq,
            psd_data=empty_psd
        )

    def reset_line_spectrum_charts(self):
        """
        重置线谱图表
        """
        # 创建空数据
        empty_time = np.array([0, 1])  # 简单的时间范围
        empty_signal = np.zeros_like(empty_time)
        empty_freq = np.array([0, 1])  # 简单的频率范围
        empty_psd = np.zeros_like(empty_freq)

        # 更新图表为空
        self.view.update_line_spectrum(
            time_data=empty_time,
            time_signal=empty_signal,
            freq_data=empty_freq,
            psd_data=empty_psd
        )

    def reset_radiated_noise_charts(self):
        """
        重置辐射噪声图表
        """
        # 创建空数据
        empty_time = np.array([0, 1])  # 简单的时间范围
        empty_signal = np.zeros_like(empty_time)
        empty_freq = np.array([0, 1])  # 简单的频率范围
        empty_psd = np.zeros_like(empty_freq)

        # 更新图表为空
        self.view.update_radiated_noise(
            time_data=empty_time,
            time_signal=empty_signal,
            freq_data=empty_freq,
            psd_data=empty_psd
        )

    def reset_modulation_spectrum_charts(self):
        """
        重置调制谱图表
        """
        # 创建空数据
        empty_time = np.array([0, 1])  # 简单的时间范围
        empty_signal = np.zeros_like(empty_time)
        empty_freq = np.array([0, 1])  # 简单的频率范围
        empty_psd = np.zeros_like(empty_freq)

        # 更新图表为空
        self.view.update_modulation_spectrum(
            time_data=empty_time,
            time_signal=empty_signal,
            freq_data=empty_freq,
            original_psd_data=empty_psd,
            modulated_psd_data=empty_psd
        )

    def reset_all_charts(self):
        """
        重置所有图表
        """
        self.reset_continuous_spectrum_charts()
        self.reset_line_spectrum_charts()
        self.reset_modulation_spectrum_charts()
        self.reset_radiated_noise_charts()

    def update_view(self):
        """
        更新船舶辐射噪声视图
        """
        # 一次性获取所有结果数据，减少锁操作次数
        results = self.data_manager.get_results('ship_noise')

        # 检查结果是否为空
        if not results:
            # 如果结果为空，重置所有图表
            self.reset_all_charts()
            return

        # 检查必要的数据是否存在
        if 'time_data' not in results:
            # 如果没有时间数据，重置所有图表
            self.reset_all_charts()
            return

        time_data = results['time_data']

        # 更新连续谱视图
        if ('continuous_signal' in results and
                'continuous_freqs' in results and
                'continuous_psd_db' in results):

            self.view.update_continuous_spectrum(
                time_data=time_data,
                time_signal=results['continuous_signal'],
                freq_data=results['continuous_freqs'],
                psd_data=results['continuous_psd_db']
            )
        else:
            # 如果缺少连续谱数据，重置连续谱图表
            self.reset_continuous_spectrum_charts()

        # 更新线谱视图
        if ('line_signal' in results and
                'line_freqs' in results and
                'line_psd_db' in results):

            self.view.update_line_spectrum(
                time_data=time_data,
                time_signal=results['line_signal'],
                freq_data=results['line_freqs'],
                psd_data=results['line_psd_db']
            )
        else:
            # 如果缺少线谱数据，重置线谱图表
            self.reset_line_spectrum_charts()

        # 更新调制谱视图
        if ('modulation_signal' in results and
                'continuous_freqs' in results and
                'continuous_psd_db' in results and
                'modulated_continuous_psd_db' in results):

            self.view.update_modulation_spectrum(
                time_data=time_data,
                time_signal=results['modulation_signal'],
                freq_data=results['continuous_freqs'],
                original_psd_data=results['continuous_psd_db'],
                modulated_psd_data=results['modulated_continuous_psd_db']
            )
        else:
            # 如果缺少调制谱数据，重置调制谱图表
            self.reset_modulation_spectrum_charts()

        # 更新总信号视图
        if ('total_signal' in results and
                'total_freqs' in results and
                'total_psd_db' in results):

            self.view.update_radiated_noise(
                time_data=time_data,
                time_signal=results['total_signal'],
                freq_data=results['total_freqs'],
                psd_data=results['total_psd_db']
            )
        else:
            # 如果缺少总信号数据，重置总信号图表
            self.reset_radiated_noise_charts()
