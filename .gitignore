# 个人文件
/吴家骐_2219012411_任务书.docx

# Python字节码文件
__pycache__/
*.py[cod]
*$py.class

# 分发/打包
dist/
build/
*.egg-info/
*.egg

# 虚拟环境
/venv
env/
ENV/
.env
.venv

# IDE相关文件
.idea/
.vscode/
*.swp
*.swo
.spyderproject
.spyproject
.ropeproject

# 本地配置
.env
.env.local
instance/
.webassets-cache

# 日志文件
*.log
logs/
log/

# 临时文件
tmp/
temp/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Jupyter Notebook
.ipynb_checkpoints

# 缓存文件
.cache/
.pytest_cache/
.coverage
htmlcov/
.tox/
nosetests.xml
coverage.xml
*.cover

# 原始数据文件，data目录中的.csv文件除外
*.csv
!data/*.csv
results/
results1/
results100/

# 文档/ProjectGraph 文件夹及其内容
文档/ProjectGraph/

# channel_data
channel_data/

# 项目文件
*.json
!data/*.json
*.npz