# -*- coding: utf-8 -*-
"""
项目管理器

负责项目的保存、加载、状态跟踪等功能。
将项目管理功能从数据管理器中分离出来，使职责更加清晰。
"""

import os
import json
import numpy as np
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtWidgets import QFileDialog, QMessageBox


class ProjectManager(QObject):
    """
    项目管理器

    负责项目的保存、加载、状态跟踪等功能。
    """

    # 信号定义
    project_loaded = pyqtSignal(str)  # 项目加载信号，参数为项目文件路径
    project_saved = pyqtSignal(str)   # 项目保存信号，参数为项目文件路径
    modification_state_changed = pyqtSignal(bool)  # 修改状态变更信号，参数为是否已修改

    def __init__(self, data_manager):
        """
        初始化项目管理器

        Args:
            data_manager: 数据管理器实例
        """
        super().__init__()

        # 保存数据管理器引用
        self.data_manager = data_manager

        # 项目状态
        self._project_file_path = None  # 项目文件路径
        self._is_modified = False       # 项目是否已修改
        self._saved_state = {}          # 上次保存的状态快照

        # 连接数据管理器的信号
        self.data_manager.parameters_changed.connect(self._on_data_changed)
        self.data_manager.results_changed.connect(self._on_data_changed)
        self.data_manager.global_params_changed.connect(self._on_data_changed)

    def save_project(self, file_path):
        """
        保存项目到JSON和NPZ文件

        使用JSON+NPZ混合格式保存项目：
        - JSON文件存储元数据和参数
        - NPZ文件存储大型数组数据

        Args:
            file_path (str): 项目JSON文件路径
        """
        # 准备要保存的数据
        project_data = {
            'global_params': self.data_manager.get_all_global_params(),
            'parameters': self.data_manager.get_all_parameters(),
            'results_metadata': {},  # 存储结果的元数据，不包含大型数组
            'npz_file': os.path.basename(file_path).replace('.json', '_arrays.npz')  # NPZ文件名
        }

        # 创建数组数据字典
        array_data = {}

        # 处理结果数据 - 只保存船舶辐射噪声和海洋环境噪声的结果
        all_results = self.data_manager.get_all_results()
        for module, results in all_results.items():
            if not results:
                continue

            # 只保存船舶辐射噪声和海洋环境噪声模块的结果
            # 声传播和综合仿真模块的结果不保存，因为它们可以从参数重新计算
            if module in ['ship_noise', 'ambient_noise']:
                # 初始化模块结果元数据
                if module not in project_data['results_metadata']:
                    project_data['results_metadata'][module] = {}

                # 处理模块的结果
                for key, value in results.items():
                    self._process_result_value(project_data, array_data, module, key, value)
            elif module == 'integrated':
                # 对于综合仿真模块，只保存信道数据目录路径
                channel_data_dir = self.data_manager.get_parameters('integrated').get('channel_data_dir', '')
                if channel_data_dir:
                    # 初始化模块结果元数据
                    if module not in project_data['results_metadata']:
                        project_data['results_metadata'][module] = {}

                    # 只保存信道数据目录路径
                    project_data['results_metadata'][module]['channel_data_dir'] = {
                        'is_array': False,
                        'is_channel_data_dir': True,
                        'value': channel_data_dir
                    }
            # 声传播模块的结果完全不保存，因为可以从参数重新计算

        # 保存JSON文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(project_data, f, ensure_ascii=False, indent=2)

        # 保存NPZ文件（如果有数组数据）
        if array_data:
            npz_path = file_path.replace('.json', '_arrays.npz')
            np.savez_compressed(npz_path, **array_data)

        # 更新项目文件路径
        self._project_file_path = file_path

        # 清空保存状态（不再需要状态快照）
        self._saved_state = {}

        # 重置修改状态（这里可以保留，因为保存操作后不需要更新UI）
        self.reset_modified_state()

        # 发送信号
        self.project_saved.emit(file_path)

    def _load_channel_data(self):
        """
        加载信道数据

        如果信道数据目录不存在，提示用户选择新的信道数据目录
        """
        # 获取信道数据目录路径
        params = self.data_manager.get_parameters('integrated')
        channel_data_dir = params.get('channel_data_dir', '')

        # 检查信道数据目录是否存在
        if not channel_data_dir or not os.path.exists(channel_data_dir):
            # 提示用户选择新的信道数据目录
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Warning)
            msg_box.setWindowTitle("信道数据目录不存在")
            msg_box.setText(f"信道数据目录不存在: {channel_data_dir}")
            msg_box.setInformativeText("是否选择新的信道数据目录？")
            msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            msg_box.setDefaultButton(QMessageBox.Yes)

            # 显示对话框并获取用户选择
            reply = msg_box.exec_()

            if reply == QMessageBox.Yes:
                # 打开目录选择对话框
                new_dir = QFileDialog.getExistingDirectory(
                    None,
                    "选择信道数据目录",
                    os.getcwd(),
                    QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
                )

                if new_dir:
                    # 检查新目录是否包含meta.json文件
                    meta_file = os.path.join(new_dir, 'meta.json')
                    if not os.path.exists(meta_file):
                        QMessageBox.warning(None, "目录错误", f"所选目录不包含meta.json文件:\n{new_dir}")
                        return

                    # 更新信道数据目录路径
                    self.data_manager.update_parameter('integrated', 'channel_data_dir', new_dir)
                    channel_data_dir = new_dir
                else:
                    # 用户取消了选择
                    return
            else:
                # 用户选择不选择新的目录
                return

        # 尝试加载信道数据
        try:
            # 使用SimulationController加载信道数据
            from src.core.simulation.simulation_controller import SimulationController

            # 创建临时的SimulationController实例
            simulation_controller = SimulationController(self.data_manager)

            # 加载信道数据
            success = simulation_controller.load_channel_data(channel_data_dir)

            if success:
                print(f"成功加载信道数据: {channel_data_dir}")
            else:
                print(f"加载信道数据失败: {channel_data_dir}")
                QMessageBox.warning(None, "加载错误", f"加载信道数据失败，请检查数据格式:\n{channel_data_dir}")
        except Exception as e:
            print(f"加载信道数据失败: {e}")
            QMessageBox.warning(None, "加载错误", f"加载信道数据失败: {str(e)}\n{channel_data_dir}")

    def _process_result_value(self, project_data, array_data, module, key, value):
        """
        处理单个结果值，决定是存储在JSON中还是NPZ中

        Args:
            project_data: 项目数据字典
            array_data: 数组数据字典
            module: 模块名称
            key: 结果键名
            value: 结果值
        """
        # 检查是否为NumPy数组且大小超过阈值
        if isinstance(value, np.ndarray) and value.size > 1000:
            # 在元数据中记录数组信息
            project_data['results_metadata'][module][key] = {
                'is_array': True,
                'shape': value.shape,
                'dtype': str(value.dtype),
                'array_key': f"{module}_{key}"  # NPZ中的键名
            }

            # 将数组添加到array_data（已经是float32，无需再次转换）
            array_data[f"{module}_{key}"] = value
        elif isinstance(value, dict):
            # 递归处理字典类型，处理嵌套结构中的numpy数组
            processed_dict, nested_arrays = self._process_nested_dict(value, module, key)

            # 将嵌套的数组添加到array_data
            array_data.update(nested_arrays)

            # 存储处理后的字典
            project_data['results_metadata'][module][key] = {
                'is_array': False,
                'value': processed_dict
            }
        else:
            # 小型数据直接存储在JSON中
            if isinstance(value, np.ndarray):
                # 将小型NumPy数组转换为列表
                project_data['results_metadata'][module][key] = {
                    'is_array': False,
                    'value': value.tolist(),
                    'shape': value.shape,
                    'dtype': str(value.dtype)
                }
            else:
                # 其他类型直接存储
                project_data['results_metadata'][module][key] = {
                    'is_array': False,
                    'value': value
                }

    def _process_nested_dict(self, data_dict, module, parent_key):
        """
        递归处理嵌套字典中的numpy数组

        Args:
            data_dict: 要处理的字典
            module: 模块名称
            parent_key: 父级键名

        Returns:
            tuple: (处理后的字典, 提取的数组字典)
        """
        processed_dict = {}
        nested_arrays = {}

        for key, value in data_dict.items():
            if isinstance(value, np.ndarray) and value.size > 1000:
                # 大型数组，存储到NPZ文件
                array_key = f"{module}_{parent_key}_{key}"
                nested_arrays[array_key] = value

                # 在字典中存储数组引用信息
                processed_dict[key] = {
                    '_is_large_array': True,
                    'array_key': array_key,
                    'shape': value.shape,
                    'dtype': str(value.dtype)
                }
            elif isinstance(value, np.ndarray):
                # 小型数组，转换为列表存储在JSON中
                processed_dict[key] = {
                    '_is_small_array': True,
                    'value': value.tolist(),
                    'shape': value.shape,
                    'dtype': str(value.dtype)
                }
            elif isinstance(value, dict):
                # 递归处理嵌套字典
                nested_processed, nested_nested_arrays = self._process_nested_dict(value, module, f"{parent_key}_{key}")
                processed_dict[key] = nested_processed
                nested_arrays.update(nested_nested_arrays)
            else:
                # 其他类型直接存储
                processed_dict[key] = value

        return processed_dict, nested_arrays

    def _restore_nested_dict(self, data_dict, npz_data):
        """
        递归恢复嵌套字典中的numpy数组

        Args:
            data_dict: 要恢复的字典
            npz_data: NPZ数据对象

        Returns:
            dict: 恢复后的字典
        """
        restored_dict = {}

        for key, value in data_dict.items():
            if isinstance(value, dict):
                if value.get('_is_large_array', False):
                    # 从NPZ文件恢复大型数组
                    array_key = value['array_key']
                    if npz_data is not None and array_key in npz_data:
                        restored_dict[key] = npz_data[array_key]
                    else:
                        print(f"警告：无法找到数组 {array_key}")
                        restored_dict[key] = None
                elif value.get('_is_small_array', False):
                    # 恢复小型数组
                    dtype = np.dtype(value['dtype'])
                    shape = tuple(value['shape'])
                    restored_dict[key] = np.array(value['value'], dtype=dtype).reshape(shape)
                else:
                    # 递归处理嵌套字典
                    restored_dict[key] = self._restore_nested_dict(value, npz_data)
            else:
                # 其他类型直接复制
                restored_dict[key] = value

        return restored_dict

    def load_project(self, file_path):
        """
        从JSON和NPZ文件加载项目

        从JSON+NPZ混合格式加载项目：
        - JSON文件包含元数据和参数
        - NPZ文件包含大型数组数据

        Args:
            file_path (str): 项目JSON文件路径
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"项目文件不存在: {file_path}")

        # 加载JSON文件
        with open(file_path, 'r', encoding='utf-8') as f:
            project_data = json.load(f)

        # 加载全局参数
        if 'global_params' in project_data:
            for key, value in project_data['global_params'].items():
                self.data_manager.set_global_param(key, value)

        # 加载参数
        if 'parameters' in project_data:
            self.data_manager.set_all_parameters(project_data['parameters'])

        # 检查是否有NPZ文件
        npz_file = project_data.get('npz_file', os.path.basename(file_path).replace('.json', '_arrays.npz'))
        npz_path = os.path.join(os.path.dirname(file_path), npz_file)

        npz_data = None
        if os.path.exists(npz_path):
            npz_data = np.load(npz_path)

        # 加载结果
        if 'results_metadata' in project_data:
            for module, results_meta in project_data['results_metadata'].items():
                # 初始化模块结果字典
                module_results = {}

                # 处理每个结果
                for key, meta in results_meta.items():
                    # 特殊处理综合仿真模块的信道数据目录
                    if module == 'integrated' and key == 'channel_data_dir' and meta.get('is_channel_data_dir', False):
                        # 获取信道数据目录路径
                        channel_data_dir = meta.get('value', '')

                        # 更新参数中的信道数据目录路径
                        if channel_data_dir:
                            self.data_manager.update_parameter('integrated', 'channel_data_dir', channel_data_dir)

                        # 信道数据将在后续步骤中加载，这里不需要做其他处理
                        continue

                    # 只恢复船舶辐射噪声和海洋环境噪声模块的结果
                    if module in ['ship_noise', 'ambient_noise']:
                        if meta.get('is_array', False) and npz_data is not None:
                            # 从NPZ加载数组
                            array_key = meta['array_key']
                            if array_key in npz_data:
                                module_results[key] = npz_data[array_key]
                        else:
                            # 直接从JSON获取值
                            value = meta.get('value')

                            # 如果元数据中有shape和dtype信息，将列表转回NumPy数组
                            if isinstance(value, list) and 'shape' in meta and 'dtype' in meta:
                                dtype = np.dtype(meta['dtype'])
                                shape = tuple(meta['shape'])
                                module_results[key] = np.array(value, dtype=dtype).reshape(shape)
                            elif isinstance(value, dict):
                                # 递归恢复嵌套字典中的数组
                                module_results[key] = self._restore_nested_dict(value, npz_data)
                            else:
                                module_results[key] = value

                # 设置模块结果（只对船舶辐射噪声和海洋环境噪声模块）
                if module_results and module in ['ship_noise', 'ambient_noise']:
                    self.data_manager.set_results(module, module_results)

                # 如果是综合仿真模块，尝试加载信道数据
                if module == 'integrated':
                    self._load_channel_data()

        # 兼容旧版本项目文件
        elif 'results' in project_data:
            for module, results in project_data['results'].items():
                module_results = {}
                for key, value in results.items():
                    if isinstance(value, dict) and 'type' in value and value['type'] == 'ndarray':
                        # 重建NumPy数组
                        arr = np.array(value['data'], dtype=value['dtype'])
                        if 'shape' in value:
                            arr = arr.reshape(value['shape'])
                        module_results[key] = arr
                    else:
                        module_results[key] = value

                self.data_manager.set_results(module, module_results)

        # 更新项目文件路径
        self._project_file_path = file_path

        # 清空保存状态（不再需要状态快照）
        self._saved_state = {}

        # 注意：不在这里重置修改状态，而是在UI更新后由ProjectUIManager调用reset_modified_state

        # 发送信号
        self.project_loaded.emit(file_path)

    def reset_project(self):
        """
        重置项目

        完全重置项目状态，包括清除所有参数和结果，重置项目文件路径和修改状态
        """
        # 重置项目文件路径
        self._project_file_path = None

        # 清除所有数据
        self.data_manager.clear_all_data()

        # 重新设置默认的全局参数
        self.data_manager.set_global_param('fs', 44100)

        # 重新设置CPU核心数
        import multiprocessing
        self.data_manager.set_global_param('cpu_cores', multiprocessing.cpu_count())

        # 重新设置模块参数中的持续时间
        self.data_manager.update_parameter('ship_noise', 'duration', 5.0)
        self.data_manager.update_parameter('ambient_noise', 'duration', 5.0)

        # 清空保存状态
        self._saved_state = {}

        # 注意：不在这里重置修改状态，而是在UI更新后由ProjectUIManager调用reset_modified_state

    def reset_modified_state(self):
        """
        重置修改状态

        将修改状态设置为未修改，并发送状态变更信号
        在保存、加载或新建项目后调用
        """
        # 重置修改标记
        self._is_modified = False

        # 发送修改状态变更信号
        self.modification_state_changed.emit(False)

    def update_saved_state(self):
        """
        更新保存状态

        清空保存状态，在新建项目设置默认参数后调用
        注意：不再自动重置修改状态，而是由调用者在适当的时机调用reset_modified_state
        """
        # 清空保存状态
        self._saved_state = {}

    def get_project_file_path(self):
        """
        获取当前项目文件路径

        Returns:
            str: 项目文件路径，如果未保存则为None
        """
        return self._project_file_path

    def is_project_saved(self):
        """
        检查项目是否已保存

        Returns:
            bool: 项目是否已保存
        """
        return self._project_file_path is not None

    def is_modified(self):
        """
        检查项目是否已修改

        Returns:
            bool: 项目是否已修改
        """
        # 直接返回修改状态标志
        return self._is_modified



    def _on_data_changed(self, *_):
        """
        数据变更事件处理

        统一处理所有数据变更事件（参数、结果、全局参数）

        Args:
            *_: 变更的相关信息（模块名称或参数名称），这里不使用
        """
        # 直接设置修改状态
        if not self._is_modified:
            self._is_modified = True
            self.modification_state_changed.emit(True)
