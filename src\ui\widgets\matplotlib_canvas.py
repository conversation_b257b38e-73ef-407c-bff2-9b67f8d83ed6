# -*- coding: utf-8 -*-
"""
Matplotlib画布封装

提供Matplotlib与PyQt5集成的画布组件
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QSizePolicy
from matplotlib.figure import Figure
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar


class MatplotlibCanvas(QWidget):
    """
    Matplotlib画布封装
    
    提供Matplotlib与PyQt5集成的画布组件，包括导航工具栏
    """
    
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        """
        初始化Matplotlib画布
        
        Args:
            parent: 父窗口
            width: 图形宽度（英寸）
            height: 图形高度（英寸）
            dpi: 分辨率（每英寸点数）
        """
        super().__init__(parent)
        
        # 创建Figure对象
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        
        # 创建画布
        self.canvas = FigureCanvas(self.fig)
        self.canvas.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.canvas.updateGeometry()
        
        # 创建导航工具栏
        self.toolbar = NavigationToolbar(self.canvas, self)
        
        # 设置布局
        layout = QVBoxLayout(self)
        layout.addWidget(self.toolbar)
        layout.addWidget(self.canvas)
        
        # 创建子图
        self.ax = self.fig.add_subplot(111)
    
    def clear(self):
        """
        清除图形
        """
        self.ax.clear()
        self.canvas.draw()
    
    def plot(self, x, y, *args, **kwargs):
        """
        绘制曲线
        
        Args:
            x: x坐标数组
            y: y坐标数组
            *args, **kwargs: 传递给matplotlib.pyplot.plot的参数
        """
        self.ax.plot(x, y, *args, **kwargs)
        self.canvas.draw()
    
    def scatter(self, x, y, *args, **kwargs):
        """
        绘制散点图
        
        Args:
            x: x坐标数组
            y: y坐标数组
            *args, **kwargs: 传递给matplotlib.pyplot.scatter的参数
        """
        self.ax.scatter(x, y, *args, **kwargs)
        self.canvas.draw()
    
    def imshow(self, data, *args, **kwargs):
        """
        显示图像
        
        Args:
            data: 图像数据
            *args, **kwargs: 传递给matplotlib.pyplot.imshow的参数
        """
        im = self.ax.imshow(data, *args, **kwargs)
        self.fig.colorbar(im, ax=self.ax)
        self.canvas.draw()
    
    def contourf(self, x, y, z, *args, **kwargs):
        """
        绘制填充等值线图
        
        Args:
            x: x坐标数组
            y: y坐标数组
            z: z值二维数组
            *args, **kwargs: 传递给matplotlib.pyplot.contourf的参数
        """
        cs = self.ax.contourf(x, y, z, *args, **kwargs)
        self.fig.colorbar(cs, ax=self.ax)
        self.canvas.draw()
    
    def set_title(self, title):
        """
        设置标题
        
        Args:
            title: 标题文本
        """
        self.ax.set_title(title)
        self.canvas.draw()
    
    def set_xlabel(self, label):
        """
        设置x轴标签
        
        Args:
            label: 标签文本
        """
        self.ax.set_xlabel(label)
        self.canvas.draw()
    
    def set_ylabel(self, label):
        """
        设置y轴标签
        
        Args:
            label: 标签文本
        """
        self.ax.set_ylabel(label)
        self.canvas.draw()
    
    def set_xlim(self, xmin, xmax):
        """
        设置x轴范围
        
        Args:
            xmin: x轴最小值
            xmax: x轴最大值
        """
        self.ax.set_xlim(xmin, xmax)
        self.canvas.draw()
    
    def set_ylim(self, ymin, ymax):
        """
        设置y轴范围
        
        Args:
            ymin: y轴最小值
            ymax: y轴最大值
        """
        self.ax.set_ylim(ymin, ymax)
        self.canvas.draw()
    
    def grid(self, b=True, **kwargs):
        """
        显示网格线
        
        Args:
            b: 是否显示网格线
            **kwargs: 传递给matplotlib.pyplot.grid的参数
        """
        self.ax.grid(b, **kwargs)
        self.canvas.draw()
    
    def legend(self, *args, **kwargs):
        """
        显示图例
        
        Args:
            *args, **kwargs: 传递给matplotlib.pyplot.legend的参数
        """
        self.ax.legend(*args, **kwargs)
        self.canvas.draw()
