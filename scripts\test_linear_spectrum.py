import numpy as np
import matplotlib.pyplot as plt
from test_continuous_spectrum import calculate_target_sl

# --- Constants ---
FS = 1000  # Sampling frequency (Hz). Must be > 2 * max_freq (65 Hz). 1000 Hz is suitable.
T = 1.0    # Duration (seconds)
N_SAMPLES = int(FS * T)
# Reference pressure for underwater sound (Pascals). Used for SL -> Amplitude conversion.
P_REF = 1e-6 # 1 microPascal

# # --- Placeholder Function for Continuous Spectrum SL ---
# # You provide this function's actual implementation.
# # This is just a simple placeholder example.
# def calculate_target_sl(freq, f0=50, SL0=80, a1=10, a2=0):
#     """
#     Calculates the continuous spectrum Sound Level (SL) in dB at a given frequency.

#     Args:
#         freq (float): The frequency (Hz) for which to calculate SL.
#         f0 (float): Reference frequency (Hz).
#         SL0 (float): Sound Level at the reference frequency (dB).
#         a1 (float): Parameter controlling decay below f0 (placeholder).
#         a2 (float): Parameter controlling decay above f0 (placeholder).

#     Returns:
#         float: The continuous spectrum Sound Level (dB re P_REF).
#     """
#     # Simple example: Constant SL up to f0, then logarithmic decay
#     # Replace with your actual model.
#     if freq <= 0:
#         return -np.inf # Invalid frequency
#     if freq <= f0:
#         return SL0 - a1 * np.log10(freq / f0) # Example decay below f0
#     else:
#         return SL0 - a2 * np.log10(freq / f0) # Example decay above f0

# --- Conversion Function: SL (dB) to Amplitude Ak ---
# *** PLACEHOLDER: Fill in your specific formula here if different ***
# The standard formula relating SPL (dB) to pressure amplitude (Pa) is used below.
# SPL = 20 * log10(p_rms / p_ref)
# p_rms = A / sqrt(2) for a sine wave
# So, SL = 20 * log10( (A / sqrt(2)) / p_ref )
# Solving for A: A = sqrt(2) * p_ref * 10^(SL / 20)
def convert_sl_to_amplitude(sl_db, p_ref=P_REF):
    """
    Converts Sound Level (dB re p_ref) to pressure amplitude (Ak).
    *** This is where you should insert your specific conversion formula if needed. ***
    The default uses the standard physical relationship for SPL.

    Args:
        sl_db (float): The sound level in dB re p_ref.
        p_ref (float): The reference pressure (Pascals).

    Returns:
        float: The amplitude (Ak) of the sinusoidal component (Pascals).
    """
    if np.isinf(sl_db) or sl_db < -600: # Avoid issues with extremely low dB values
         print(f"Warning: Very low or infinite dB value ({sl_db}), setting amplitude to 0.")
         return 0.0
    try:
        # Standard formula:
        amplitude = np.sqrt(2) * p_ref * (10**(sl_db / 20.0))
        return amplitude
    except OverflowError:
        print(f"Warning: Overflow calculating amplitude for dB value {sl_db}. Check parameters.")
        return 0.0 # Or some large number / handle appropriately

# --- Simulation Parameters ---
# Frequencies for the line spectrum components (Hz) as specified
line_frequencies = np.array([16, 32, 48, 65])
N_lines = len(line_frequencies)

# --- Calculate Amplitudes (Ak) and Phases (phi_k) ---
amplitudes = np.zeros(N_lines)
phases = np.zeros(N_lines)
line_sls = np.zeros(N_lines) # To store the final line SLs

print("Calculating Line Spectrum Components:")
print("-" * 75)
print(f"{'Freq (Hz)':<12} {'SL_cont (dB)':<15} {'dB Offset':<12} {'SL_line (dB)':<15} {'Amplitude (Pa)':<15}")
print("-" * 75)

# Example parameters for the placeholder calculate_target_sl
# You might need to adjust these based on the expected continuous spectrum level
params = {'f0': 249.0, 'SL0': 130.16, 'a1': 3.0, 'a2': -4.236} # Example parameters

for i, freq in enumerate(line_frequencies):
    # 1. Calculate continuous spectrum SL using the provided function
    sl_continuous = calculate_target_sl(freq, **params)

    # 2. Add random offset (10-25 dB)
    # db_offset = np.random.uniform(10, 25)
    db_offset = 20 # Fixed offset for testing
    sl_line = sl_continuous + db_offset
    line_sls[i] = sl_line # Store for reference

    # 3. Convert final SL_line (dB) to Amplitude Ak using the conversion function
    amplitudes[i] = convert_sl_to_amplitude(sl_line, P_REF)

    # 4. Generate random phase (0 to 2*pi)
    phases[i] = np.random.uniform(0, 2 * np.pi)

    print(f"{freq:<12.1f} {sl_continuous:<15.2f} {db_offset:<12.2f} {sl_line:<15.2f} {amplitudes[i]:<15.2e}")

print("-" * 75)

# --- Generate Time Vector ---
# t = np.arange(0, T, 1/FS) # Alternative way to generate time vector
t = np.linspace(0, T, N_SAMPLES, endpoint=False) # endpoint=False is often preferred for FFT

# --- Generate Time-Domain Signal g_L(t) according to Eq. 2.13 ---
g_L = np.zeros_like(t)
for i in range(N_lines):
    g_L += amplitudes[i] * np.sin(2 * np.pi * line_frequencies[i] * t + phases[i])

# --- Plot Time-Domain Signal ---
plt.figure(figsize=(12, 5))
plt.plot(t, g_L)
plt.title('Simulated Line Spectrum Signal $g_L(t)$ (Time Domain)')
plt.xlabel('Time (s)')
plt.ylabel('Amplitude (Pa)')
plt.xlim(0, T)
plt.grid(True)
plt.tight_layout()
plt.show()

# --- Calculate and Plot Frequency-Domain Power Spectrum ---

# Perform FFT
fft_result = np.fft.fft(g_L)
# Calculate corresponding frequencies
fft_freq = np.fft.fftfreq(N_SAMPLES, 1/FS)

# Calculate Power Spectrum (|FFT|^2 normalized)
# Normalization: Divide by N for power spectral density, or by N^2 for amplitude spectrum?
# For Power Spectrum (units of Pa^2), often use |FFT|^2 / N
power_spectrum = np.abs(fft_result)**2 / N_SAMPLES

# Select positive frequencies only (0 to Nyquist)
positive_freq_mask = fft_freq >= 0
freqs_pos = fft_freq[positive_freq_mask]
power_spectrum_pos = power_spectrum[positive_freq_mask]

# Double the power for non-DC and non-Nyquist frequencies for single-sided spectrum
# This accounts for the energy from the negative frequencies which are discarded
if N_SAMPLES % 2 == 0: # Even number of samples, Nyquist frequency exists at index N/2
     power_spectrum_pos[1:-1] *= 2 # Double all except DC (index 0) and Nyquist (last index)
else: # Odd number of samples, no Nyquist frequency
     power_spectrum_pos[1:] *= 2 # Double all except DC (index 0)


# --- Plot Power Spectrum ---
plt.figure(figsize=(12, 5))
# Using semilogy often makes spectral peaks clearer
plt.semilogy(freqs_pos, power_spectrum_pos)
# Or linear scale: plt.plot(freqs_pos, power_spectrum_pos)

plt.title('Simulated Line Spectrum Signal (Power Spectrum)')
plt.xlabel('Frequency (Hz)')
plt.ylabel('Power ($Pa^2$)')
# Limit x-axis to focus on the area with line spectra
plt.xlim(0, max(line_frequencies) * 1.5 if max(line_frequencies) > 0 else FS/2)
# Set a dynamic bottom limit for y-axis to avoid plotting very small values if using log scale
min_power_display = max(power_spectrum_pos[1:]) / 1e6 if len(power_spectrum_pos) > 1 else 1e-12
plt.ylim(bottom=min_power_display)

plt.grid(True, which='both', linestyle='--', linewidth=0.5) # Add grid for both major and minor ticks on log scale if used
plt.tight_layout()
plt.show()

# Optional: Plot Power Spectrum in dB
# Avoid log10(0) or very small numbers. Add a small epsilon or floor value.
epsilon = 1e-18 # Small value to prevent log10(0)
power_db = 10 * np.log10(np.maximum(power_spectrum_pos, epsilon)) # Use max to avoid log(negative) just in case

plt.figure(figsize=(12, 5))
plt.plot(freqs_pos, power_db)
plt.title('Simulated Line Spectrum Signal (Power Spectrum in dB)')
plt.xlabel('Frequency (Hz)')
plt.ylabel('Power (dB re $Pa^2$)')
plt.xlim(0, max(line_frequencies) * 1.5 if max(line_frequencies) > 0 else FS/2)
# Dynamically set y limits based on max power
max_db = np.max(power_db) if len(power_db)>0 else 0
plt.ylim(max_db - 80, max_db + 10) # Show ~80 dB dynamic range below the peak
plt.grid(True)
plt.tight_layout()
plt.show()