# -*- coding: utf-8 -*-
"""
水深随距离变化编辑对话框

用于编辑水深随距离变化的数据
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QTableWidget, QTableWidgetItem, QHeaderView,
    QPushButton, QFileDialog
)
from PyQt5.QtCore import Qt
import numpy as np


class BathymetryDialog(QDialog):
    """
    水深随距离变化编辑对话框

    用于编辑水深随距离变化的数据
    """

    def __init__(self, parent=None, bathymetry_data=None):
        """
        初始化水深随距离变化编辑对话框

        Args:
            parent: 父窗口
            bathymetry_data: 初始水深数据，格式为[[距离1, 水深1], [距离2, 水深2], ...]
        """
        super().__init__(parent)

        # 保存初始数据
        self.bathymetry_data = bathymetry_data if bathymetry_data is not None else []

        # 设置对话框属性
        self.setWindowTitle("编辑水深随距离变化")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)

        # 创建UI
        self.init_ui()

        # 加载初始数据
        self.load_data()

    def init_ui(self):
        """
        初始化UI
        """
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建表格
        self.bathymetry_table = QTableWidget(0, 2)
        self.bathymetry_table.setHorizontalHeaderLabels(["距离 (m)", "水深 (m)"])
        self.bathymetry_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        main_layout.addWidget(self.bathymetry_table)

        # 按钮组
        buttons_layout = QHBoxLayout()

        # 添加行按钮
        self.add_row_button = QPushButton("添加行")
        self.add_row_button.clicked.connect(self.add_row)
        buttons_layout.addWidget(self.add_row_button)

        # 删除行按钮
        self.remove_row_button = QPushButton("删除行")
        self.remove_row_button.clicked.connect(self.remove_row)
        buttons_layout.addWidget(self.remove_row_button)

        # 导入按钮
        self.import_button = QPushButton("导入")
        self.import_button.clicked.connect(self.import_data)
        buttons_layout.addWidget(self.import_button)

        # 导出按钮
        self.export_button = QPushButton("导出")
        self.export_button.clicked.connect(self.export_data)
        buttons_layout.addWidget(self.export_button)

        main_layout.addLayout(buttons_layout)

        # 对话框按钮
        dialog_buttons_layout = QHBoxLayout()

        # 确定按钮
        self.ok_button = QPushButton("确定")
        self.ok_button.clicked.connect(self.accept)
        dialog_buttons_layout.addWidget(self.ok_button)

        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        dialog_buttons_layout.addWidget(self.cancel_button)

        main_layout.addLayout(dialog_buttons_layout)

    def load_data(self):
        """
        加载初始数据到表格
        """
        # 清空表格
        self.bathymetry_table.setRowCount(0)

        # 添加数据
        for i, (range_val, depth) in enumerate(self.bathymetry_data):
            self.bathymetry_table.insertRow(i)
            self.bathymetry_table.setItem(i, 0, QTableWidgetItem(str(range_val)))
            self.bathymetry_table.setItem(i, 1, QTableWidgetItem(str(depth)))

    def add_row(self):
        """
        添加表格行
        """
        row_count = self.bathymetry_table.rowCount()
        self.bathymetry_table.insertRow(row_count)

        # 如果有前一行，则基于前一行的值设置新行的初始值
        if row_count > 0:
            prev_range = float(self.bathymetry_table.item(row_count - 1, 0).text()) if self.bathymetry_table.item(row_count - 1, 0) else 0
            prev_depth = float(self.bathymetry_table.item(row_count - 1, 1).text()) if self.bathymetry_table.item(row_count - 1, 1) else 100

            # 距离增加1000m，水深保持不变
            new_range = prev_range + 1000
            new_depth = prev_depth

            self.bathymetry_table.setItem(row_count, 0, QTableWidgetItem(str(new_range)))
            self.bathymetry_table.setItem(row_count, 1, QTableWidgetItem(str(new_depth)))
        else:
            # 如果是第一行，设置默认值
            self.bathymetry_table.setItem(row_count, 0, QTableWidgetItem("0"))
            self.bathymetry_table.setItem(row_count, 1, QTableWidgetItem("100"))

    def remove_row(self):
        """
        删除表格行
        """
        row_count = self.bathymetry_table.rowCount()
        if row_count > 0:
            self.bathymetry_table.removeRow(row_count - 1)

    def import_data(self):
        """
        导入数据
        """
        file_path, _ = QFileDialog.getOpenFileName(self, "导入水深数据", "", "文本文件 (*.txt);;CSV文件 (*.csv);;所有文件 (*)")

        if file_path:
            try:
                # 尝试读取文件的前几行来确定文件格式
                with open(file_path, 'r') as f:
                    first_lines = []
                    for _ in range(5):
                        line = f.readline()
                        if line:
                            first_lines.append(line)

                # 检查文件内容，判断使用哪种分隔符
                if any(',' in line for line in first_lines):
                    # 文件包含逗号，可能是CSV格式
                    data = np.loadtxt(file_path, delimiter=',')
                    print(f"使用逗号作为分隔符读取文件")
                elif any('\t' in line for line in first_lines):
                    # 文件包含制表符，使用制表符作为分隔符
                    data = np.loadtxt(file_path, delimiter='\t')
                    print(f"使用制表符作为分隔符读取文件")
                else:
                    # 默认使用空白字符作为分隔符（空格、多个空格等）
                    # np.loadtxt默认会将连续的空白字符视为单个分隔符
                    data = np.loadtxt(file_path)
                    print(f"使用空白字符作为分隔符读取文件")

                # 检查数据维度
                if data.ndim == 1 and len(data) == 2:
                    # 只有一行数据，需要重塑为2D数组
                    data = data.reshape(1, 2)
                elif data.ndim != 2 or data.shape[1] != 2:
                    raise ValueError(f"数据格式错误：期望Nx2数组，实际为{data.shape}")

                # 清空表格
                self.bathymetry_table.setRowCount(0)

                # 添加数据到表格
                for i in range(data.shape[0]):
                    self.bathymetry_table.insertRow(i)
                    self.bathymetry_table.setItem(i, 0, QTableWidgetItem(str(data[i, 0])))
                    self.bathymetry_table.setItem(i, 1, QTableWidgetItem(str(data[i, 1])))

                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "导入成功", f"成功导入水深数据: {data.shape[0]}个点")
                print(f"成功导入水深数据: {data.shape[0]}个点")

            except Exception as e:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "导入失败", f"导入水深数据失败: {e}")
                print(f"导入水深数据失败: {e}")

    def export_data(self):
        """
        导出数据
        """
        file_path, _ = QFileDialog.getSaveFileName(self, "导出水深数据", "", "文本文件 (*.txt);;CSV文件 (*.csv);;所有文件 (*)")

        if file_path:
            try:
                # 收集表格数据
                row_count = self.bathymetry_table.rowCount()
                data = np.zeros((row_count, 2))

                for i in range(row_count):
                    data[i, 0] = float(self.bathymetry_table.item(i, 0).text())
                    data[i, 1] = float(self.bathymetry_table.item(i, 1).text())

                # 根据文件扩展名选择不同的保存方法
                if file_path.lower().endswith('.csv'):
                    # 使用逗号作为分隔符保存CSV文件
                    np.savetxt(file_path, data, fmt='%.6f', delimiter=',', header='range,depth', comments='')
                else:
                    # 对于其他文件，使用默认分隔符（空格）
                    np.savetxt(file_path, data, fmt='%.6f')

                print(f"成功导出水深数据: {row_count}个点")

            except Exception as e:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "导出失败", f"导出水深数据失败: {e}")
                print(f"导出水深数据失败: {e}")

    def get_data(self):
        """
        获取表格数据，并按距离从小到大排序

        Returns:
            list: 水深数据，格式为[[距离1, 水深1], [距离2, 水深2], ...]
        """
        row_count = self.bathymetry_table.rowCount()
        data = []

        for i in range(row_count):
            # 确保单元格不为空
            if self.bathymetry_table.item(i, 0) and self.bathymetry_table.item(i, 1):
                try:
                    range_val = float(self.bathymetry_table.item(i, 0).text())
                    depth = float(self.bathymetry_table.item(i, 1).text())
                    data.append([range_val, depth])
                except ValueError:
                    # 忽略无法转换为浮点数的单元格
                    print(f"警告：第{i+1}行包含无效数据，已忽略")
                    continue

        # 按距离（第一列）从小到大排序
        if data:
            data.sort(key=lambda x: x[0])
            print(f"水深数据已按距离从小到大排序: {len(data)}个点")

        return data
