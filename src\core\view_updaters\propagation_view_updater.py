# -*- coding: utf-8 -*-
"""
声传播视图更新器

负责根据数据管理器中的数据更新声传播视图
"""

from PyQt5.QtCore import QObject, pyqtSlot


class PropagationViewUpdater(QObject):
    """
    声传播视图更新器

    负责根据数据管理器中的数据更新声传播视图
    """

    def __init__(self, data_manager, view):
        """
        初始化声传播视图更新器

        Args:
            data_manager: 数据管理器实例
            view: 声传播视图实例
        """
        super().__init__()

        # 保存引用
        self.data_manager = data_manager
        self.view = view

        # 连接信号
        self.connect_signals()

    def connect_signals(self):
        """连接信号"""
        # 当声传播结果更新时，更新视图
        self.data_manager.results_changed.connect(self.on_results_changed)

        # 当仿真状态变化时，更新视图状态
        self.data_manager.simulation_state_changed.connect(self.on_simulation_state_changed)

    @pyqtSlot(str)
    def on_results_changed(self, module):
        """
        当结果更新时的槽函数

        Args:
            module: 更新的模块名称
        """
        if module != 'propagation':
            return

        # 获取结果
        results = self.data_manager.get_results('propagation')
        if not results:
            # 如果结果为空，重置所有视图
            self.reset_all_views()
            return

        # 根据结果类型更新不同的视图
        if 'environment' in results:
            # 更新环境视图
            self.view.update_environment(results['environment'])
            # 同时更新声速剖面图
            self.view.update_sound_speed_profile(results['environment'])

        if 'rays' in results:
            # 更新声线视图
            self.view.update_rays(results['rays'], results.get('environment'))

        if 'eigenrays' in results:
            # 更新本征声线视图
            self.view.update_rays(results['eigenrays'], results.get('environment'), ray_type='eigenray')

        if 'arrivals' in results:
            # 更新到达结构视图
            self.view.update_arrivals(results['arrivals'])

        if 'transmission_loss' in results:
            # 获取传播损失显示范围参数
            params = self.data_manager.get_parameters('propagation')
            tl_range = params.get('tl_range', {})
            vmin = tl_range.get('vmin')  # 如果未设置，使用None让视图自动计算
            vmax = tl_range.get('vmax')  # 如果未设置，使用None让视图自动计算

            # 更新传播损失视图
            self.view.update_transmission_loss(results['transmission_loss'], results.get('environment'), vmin=vmin, vmax=vmax)

        # 注释掉宽带计算结果更新，暂时不需要
        """
        if 'broadband_arrivals' in results:
            # 更新宽带计算结果
            # 注意：这里需要在PropagationView中添加相应的方法
            if hasattr(self.view, 'update_broadband_arrivals'):
                self.view.update_broadband_arrivals(
                    results['broadband_frequencies'],
                    results['broadband_arrivals']
                )
        """

    @pyqtSlot(str, bool)
    def on_simulation_state_changed(self, module, is_simulating):
        """
        当仿真状态变化时的槽函数

        Args:
            module: 模块名称
            is_simulating: 是否正在仿真
        """
        if module != 'propagation':
            return

        # 更新视图状态（例如显示/隐藏进度条）
        # 这部分可能需要在PropagationView中添加相应的方法
        if hasattr(self.view, 'set_simulating_state'):
            self.view.set_simulating_state(is_simulating)

    def reset_all_views(self):
        """
        重置所有视图到初始状态

        在新建项目或结果被清空时调用
        """
        print("声传播视图更新器: 重置所有视图")

        # 调用视图的重置方法
        if hasattr(self.view, 'reset_all_charts'):
            self.view.reset_all_charts()
        else:
            print("声传播视图更新器: 视图没有reset_all_charts方法")
