# -*- coding: utf-8 -*-
"""
海洋环境噪声控制器

负责执行海洋环境噪声仿真，在后台线程中运行以保持UI响应性。
"""

from PyQt5.QtCore import QThread, pyqtSignal
import numpy as np
from src.models.noise_sources.ocean_ambient import OceanAmbientNoise


class AmbientNoiseController(QThread):
    """
    海洋环境噪声控制器

    负责执行海洋环境噪声仿真，在后台线程中运行以保持UI响应性。
    """

    # 信号定义
    simulation_started = pyqtSignal()  # 仿真开始信号
    simulation_progress = pyqtSignal(int)  # 仿真进度信号，参数为进度百分比
    simulation_completed = pyqtSignal()  # 仿真完成信号
    simulation_error = pyqtSignal(str)  # 仿真错误信号，参数为错误信息

    def __init__(self, data_manager):
        """
        初始化海洋环境噪声控制器

        Args:
            data_manager: 数据管理器实例
        """
        super().__init__()

        # 保存数据管理器引用
        self.data_manager = data_manager

        # 仿真状态
        self.is_running = False
        self.is_cancelled = False

        # 创建海洋环境噪声模型实例，用于仿真和外插
        from src.models.noise_sources.ocean_ambient import OceanAmbientNoise
        self.ambient_model = OceanAmbientNoise()

    def run(self):
        """
        线程执行函数

        执行海洋环境噪声仿真
        """
        # 设置运行状态
        self.is_running = True
        self.is_cancelled = False

        # 设置仿真状态并发送仿真开始信号
        self.data_manager.set_module_simulating('ambient_noise', True)
        self.simulation_started.emit()

        try:
            # 从数据管理器获取参数
            params = self.data_manager.get_parameters('ambient_noise')

            # 检查参数
            if not params:
                self.simulation_error.emit("参数为空")
                self.is_running = False
                return

            # 获取用户定义的点
            user_defined_points = params.get('user_defined_points', [])

            # 获取外插点
            extrapolated_points = params.get('extrapolated_points', [])

            # 获取全局参数
            sampling_rate = self.data_manager.get_global_param('fs')

            # 获取模块参数
            duration = params.get('duration', 5.0)  # 从模块参数获取持续时间
            filter_order = params.get('filter_order', 16385)  # 默认值为16385

            # 发送进度信号
            self.simulation_progress.emit(10)

            # 检查是否取消
            if self.is_cancelled:
                return

            # 重置海洋环境噪声模型
            self.ambient_model.clear_points()
            self.ambient_model.set_simulation_params(sampling_rate, duration, filter_order)

            # 添加用户定义的点
            self.ambient_model.user_defined_points = user_defined_points.copy()  # 直接设置点列表，避免循环添加

            # 发送进度信号
            self.simulation_progress.emit(30)

            # 检查是否取消
            if self.is_cancelled:
                return

            # 如果没有外插点，执行外插
            if not extrapolated_points and user_defined_points:
                # 获取外插设置
                low_freq_method = params.get('low_freq_extrapolation_method', 'auto')
                high_freq_method = params.get('high_freq_extrapolation_method', 'auto')
                low_freq_slope = params.get('low_freq_slope', -9.0)
                high_freq_slope = params.get('high_freq_slope', -5.0)

                # 设置外插方式和斜率
                self.ambient_model.set_extrapolation_method('low_freq', low_freq_method)
                self.ambient_model.set_extrapolation_method('high_freq', high_freq_method)
                self.ambient_model.set_user_slope('low_freq', low_freq_slope)
                self.ambient_model.set_user_slope('high_freq', high_freq_slope)

                # 执行外插
                self.ambient_model.extrapolate_spectrum()
            else:
                # 添加外插点 - 直接设置外插点列表，避免循环添加
                self.ambient_model.extrapolated_points = extrapolated_points.copy()

            # 发送进度信号
            self.simulation_progress.emit(50)

            # 检查是否取消
            if self.is_cancelled:
                return

            # 生成时域信号
            ambient_signal = self.ambient_model.generate_signal()

            # 发送进度信号
            self.simulation_progress.emit(70)

            # 检查是否取消
            if self.is_cancelled:
                return

            # 计算功率谱密度
            freqs, _, psd_db = self.ambient_model.calculate_psd(ambient_signal)  # 使用_忽略未使用的psd变量

            # 发送进度信号
            self.simulation_progress.emit(90)

            # 检查是否取消
            if self.is_cancelled:
                return

            # 创建时间数组
            time_data = np.linspace(0, duration, len(ambient_signal))

            # 获取滤波器系数和缩放因子
            fir_coeffs = self.ambient_model.get_filter_coeffs()
            scaling_factor = self.ambient_model.get_scaling_factor()

            # 创建结果字典
            # 将大型数组转换为float32类型以节省内存
            results = {
                'time_data': np.array(time_data, dtype=np.float32),
                'ambient_signal': np.array(ambient_signal, dtype=np.float32),
                'ambient_freqs': np.array(freqs, dtype=np.float32),
                'ambient_psd_db': np.array(psd_db, dtype=np.float32),

                # 保存滤波器系数和缩放因子，以便在综合仿真中复用
                'fir_coeffs': np.array(fir_coeffs, dtype=np.float32),
                'scaling_factor': float(scaling_factor),

                # 添加元数据，记录生成参数
                'metadata': {
                    'fs': sampling_rate,
                    'duration': duration,
                    'filter_order': filter_order,
                    'generation_time': np.datetime64('now').astype(str)  # 使用NumPy的datetime64，可以序列化
                }
            }

            # 将结果存储到数据管理器
            self.data_manager.set_results('ambient_noise', results)

            # 发送进度信号
            self.simulation_progress.emit(100)

            # 发送仿真完成信号
            self.simulation_completed.emit()

        except Exception as e:
            # 发送错误信号
            self.simulation_error.emit(str(e))
        finally:
            # 重置运行状态和仿真状态
            self.is_running = False
            self.data_manager.set_module_simulating('ambient_noise', False)

    def cancel(self):
        """
        取消仿真
        """
        if self.is_running:
            self.is_cancelled = True

    def is_simulation_running(self):
        """
        检查仿真是否正在运行

        Returns:
            bool: 是否正在运行
        """
        return self.is_running

    def perform_extrapolation(self):
        """
        执行频谱外插

        根据当前参数设置执行外插，并返回外插结果

        Returns:
            list: 外插点列表

        Raises:
            ValueError: 当外插失败时
        """
        # 从数据管理器获取参数
        params = self.data_manager.get_parameters('ambient_noise')

        # 检查参数
        if not params:
            raise ValueError("参数为空")

        # 获取用户定义的点
        user_defined_points = params.get('user_defined_points', [])

        # 如果没有用户点，无法执行外插
        if not user_defined_points:
            raise ValueError("没有用户定义的点，无法执行外插")

        # 如果只有一个点，无法进行自动外插
        if len(user_defined_points) < 2:
            raise ValueError("至少需要两个点才能进行外插")

        # 获取全局参数
        sampling_rate = self.data_manager.get_global_param('fs')

        # 获取外插设置
        low_freq_method = params.get('low_freq_extrapolation_method', 'auto')
        high_freq_method = params.get('high_freq_extrapolation_method', 'auto')
        low_freq_slope = params.get('low_freq_slope', -9.0)
        high_freq_slope = params.get('high_freq_slope', -5.0)

        # 使用预先创建的模型实例，直接调用extrapolate_spectrum方法并传入参数
        # 这样不会修改模型的内部状态，避免与仿真冲突
        # 注意：直接传递引用，避免不必要的数据复制
        extrapolated_points = self.ambient_model.extrapolate_spectrum(
            user_points=user_defined_points,  # 直接传递引用
            sampling_rate=sampling_rate,
            low_freq_method=low_freq_method,
            high_freq_method=high_freq_method,
            low_freq_slope=low_freq_slope,
            high_freq_slope=high_freq_slope
        )

        return extrapolated_points