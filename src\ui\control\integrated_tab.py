# -*- coding: utf-8 -*-
"""
综合仿真标签页

用于设置综合仿真的参数和控制仿真
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLabel, QLineEdit, QPushButton, QGroupBox,
                            QComboBox, QFileDialog, QMessageBox, QRadioButton,
                            QDoubleSpinBox, QCheckBox, QSpinBox, QProgressDialog,
                            QScrollArea)
from PyQt5.QtCore import pyqtSignal, Qt, QThread, QTimer
import os
import numpy as np
import time
import multiprocessing


class DurationCalculator(QThread):
    """
    持续时间建议计算线程

    在后台线程中计算建议时长，避免UI卡顿
    """
    # 计算完成信号
    calculation_done = pyqtSignal(float, float)  # min_duration, recommended_duration

    def __init__(self, data_manager, use_all_arrivals, truncation_time):
        """
        初始化计算线程

        Args:
            data_manager: 数据管理器实例
            use_all_arrivals: 是否使用全部到达声线
            truncation_time: 截断时间
        """
        super().__init__()
        self.data_manager = data_manager
        self.use_all_arrivals = use_all_arrivals
        self.truncation_time = truncation_time

    def run(self):
        """
        线程执行函数
        """
        try:
            # 获取结果
            results = self.data_manager.get_results('integrated')
            if not results or 'channel_data' not in results:
                self.calculation_done.emit(0.0, 0.0)
                return

            # 获取信道数据
            channel_data = results['channel_data']
            t_first_global = channel_data.get('t_first_global', None)
            t_last_global = channel_data.get('t_last_global', None)

            if t_first_global is None or t_last_global is None:
                self.calculation_done.emit(0.0, 0.0)
                return

            # 计算建议时长
            if self.use_all_arrivals:
                # 使用全部到达声线，直接使用全局最晚到达时间
                max_relative_delay = t_last_global - t_first_global
                min_duration = np.ceil(max_relative_delay + 1.0)
                recommended_duration = np.ceil(max_relative_delay + 5.0)
            else:
                # 使用预计算的阈值映射表
                duration_to_truncation_threshold = channel_data.get('duration_to_truncation_threshold', {})

                if duration_to_truncation_threshold:
                    # 找出小于等于截断时间的最大建议时长
                    min_duration = 1  # 默认最小值

                    for duration, threshold in sorted(duration_to_truncation_threshold.items()):
                        if self.truncation_time >= threshold:
                            min_duration = duration

                    # 计算推荐时长（通常是最小时长+4）
                    recommended_duration = min_duration + 4
                else:
                    # 如果没有预计算的映射表，使用原来的方法
                    print("警告: 未找到预计算的建议时长阈值映射表，使用传统计算方法")

                    # 计算有效的全局最晚到达时间
                    if self.truncation_time <= t_first_global:
                        t_last_global_eff = t_last_global
                    else:
                        # 找出所有小于等于截断时间的到达时间中的最大值
                        arrivals_data = channel_data.get('arrivals_data', {})
                        max_time = t_first_global

                        for freq, elements in arrivals_data.items():
                            for element_id, df in elements.items():
                                if 'time_of_arrival' in df.columns:
                                    valid_times = df[df['time_of_arrival'] <= self.truncation_time]['time_of_arrival'].values
                                    if len(valid_times) > 0:
                                        max_time = max(max_time, np.max(valid_times))

                        t_last_global_eff = max_time

                    # 计算最大相对时延
                    max_relative_delay = t_last_global_eff - t_first_global

                    # 计算建议的最小源信号时长（最大相对时延 + 1秒）
                    min_duration = np.ceil(max_relative_delay + 1.0)

                    # 计算推荐的源信号时长（最大相对时延 + 5秒）
                    recommended_duration = np.ceil(max_relative_delay + 5.0)

            # 发送计算完成信号
            self.calculation_done.emit(min_duration, recommended_duration)

        except Exception as e:
            print(f"计算建议时长时出错: {e}")
            self.calculation_done.emit(0.0, 0.0)


class IntegratedTab(QWidget):
    """
    综合仿真标签页

    用于设置综合仿真的参数和控制仿真
    """

    # 自定义信号
    simulation_requested = pyqtSignal(str)  # 仿真请求信号，参数为计算类型

    def __init__(self, data_manager=None, parent=None):
        """
        初始化综合仿真标签页

        Args:
            data_manager: 数据管理器实例
            parent: 父窗口
        """
        super().__init__(parent)

        # 保存数据管理器引用
        self.data_manager = data_manager

        # 计算线程相关
        self.duration_calculator = None
        self.calculation_pending = False
        self.calculation_timer = QTimer(self)
        self.calculation_timer.setSingleShot(True)
        self.calculation_timer.timeout.connect(self._start_duration_calculation)

        # 创建布局
        self.init_ui()

        # 从数据管理器加载参数
        self.load_params_from_data_manager()

        # 连接数据管理器的结果变更信号
        if self.data_manager:
            self.data_manager.results_changed.connect(self.on_results_changed)

    def init_ui(self):
        """
        初始化UI
        """
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        # 创建信道数据组
        channel_data_group = QGroupBox("信道数据")
        channel_data_layout = QVBoxLayout()

        # 创建信道数据目录选择
        dir_layout = QHBoxLayout()
        self.channel_dir_label = QLabel("信道数据目录:")
        dir_layout.addWidget(self.channel_dir_label)

        self.channel_dir_edit = QLineEdit()
        self.channel_dir_edit.setReadOnly(True)
        dir_layout.addWidget(self.channel_dir_edit, 1)

        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self.browse_channel_data_dir)
        dir_layout.addWidget(self.browse_button)

        channel_data_layout.addLayout(dir_layout)

        # 信息显示部分已移到View中
        info_label = QLabel("信息将在视图区域显示")
        info_label.setAlignment(Qt.AlignCenter)
        channel_data_layout.addWidget(info_label)

        # 创建阵元和频率选择
        selection_layout = QFormLayout()

        self.element_combo = QComboBox()
        self.element_combo.currentIndexChanged.connect(self.on_element_selected)
        selection_layout.addRow("选择阵元:", self.element_combo)

        self.frequency_combo = QComboBox()
        self.frequency_combo.currentIndexChanged.connect(self.on_frequency_selected)
        selection_layout.addRow("选择频率 (Hz):", self.frequency_combo)

        channel_data_layout.addLayout(selection_layout)

        # 添加提示信息
        info_label2 = QLabel("选择阵元和频率后，到达结构将自动显示在视图区域")
        info_label2.setAlignment(Qt.AlignCenter)
        channel_data_layout.addWidget(info_label2)

        channel_data_group.setLayout(channel_data_layout)
        scroll_layout.addWidget(channel_data_group)

        # 创建信号处理组
        signal_processing_group = QGroupBox("信号处理")
        signal_processing_layout = QVBoxLayout()

        # 创建截断设置
        truncation_group = QGroupBox("截断设置")
        truncation_layout = QVBoxLayout()

        # 显示全局到达时间信息
        self.arrival_times_info_label = QLabel("全局到达时间: 未知")
        truncation_layout.addWidget(self.arrival_times_info_label)

        # 使用全部到达声线
        self.use_all_arrivals_radio = QRadioButton("使用全部到达声线")
        self.use_all_arrivals_radio.setChecked(True)
        self.use_all_arrivals_radio.toggled.connect(self.on_truncation_option_changed)
        truncation_layout.addWidget(self.use_all_arrivals_radio)

        # 设置截断时间（基于绝对时间轴）
        truncation_time_layout = QHBoxLayout()
        self.use_truncation_radio = QRadioButton("设置截断时间(绝对时间轴):")
        self.use_truncation_radio.setToolTip("截断时间是基于绝对时间轴的，表示只使用到达时间小于等于该值的声线")
        self.use_truncation_radio.toggled.connect(self.on_truncation_option_changed)
        truncation_time_layout.addWidget(self.use_truncation_radio)

        self.truncation_time_spin = QDoubleSpinBox()
        self.truncation_time_spin.setDecimals(4)
        self.truncation_time_spin.setRange(0.0, 10.0)
        self.truncation_time_spin.setSingleStep(0.01)
        self.truncation_time_spin.setSuffix(" s")
        self.truncation_time_spin.setEnabled(False)
        self.truncation_time_spin.valueChanged.connect(self.on_truncation_time_changed)
        truncation_time_layout.addWidget(self.truncation_time_spin)

        truncation_layout.addLayout(truncation_time_layout)

        # 显示最大相对时延信息
        self.max_delay_info_label = QLabel("最大相对时延: 未知")
        truncation_layout.addWidget(self.max_delay_info_label)
        truncation_group.setLayout(truncation_layout)
        signal_processing_layout.addWidget(truncation_group)

        # 创建源信号信息
        source_signal_group = QGroupBox("源信号信息")
        source_signal_layout = QFormLayout()

        # 源信号时长建议
        self.duration_suggestion_label = QLabel("建议源信号时长: 未知")
        source_signal_layout.addRow(self.duration_suggestion_label)

        # 当前源信号信息
        self.source_signal_info_label = QLabel("当前源信号: 未知")
        source_signal_layout.addRow(self.source_signal_info_label)

        source_signal_group.setLayout(source_signal_layout)
        signal_processing_layout.addWidget(source_signal_group)

        # 创建海洋环境噪声设置组
        ambient_noise_group = QGroupBox("海洋环境噪声设置")
        ambient_noise_layout = QVBoxLayout()

        # 添加是否叠加海洋环境噪声的复选框
        self.add_ambient_noise_check = QCheckBox("叠加海洋环境噪声")
        self.add_ambient_noise_check.setChecked(True)
        self.add_ambient_noise_check.setToolTip("选中此项将为各阵元接收信号叠加海洋环境噪声")
        self.add_ambient_noise_check.stateChanged.connect(self.on_add_ambient_noise_changed)
        ambient_noise_layout.addWidget(self.add_ambient_noise_check)

        # 添加海洋环境噪声信息标签
        self.ambient_noise_info_label = QLabel("海洋环境噪声: 未知")
        ambient_noise_layout.addWidget(self.ambient_noise_info_label)

        ambient_noise_group.setLayout(ambient_noise_layout)
        signal_processing_layout.addWidget(ambient_noise_group)

        # 创建接收信号查看组
        signal_view_group = QGroupBox("接收信号查看")
        signal_view_layout = QFormLayout()

        # 创建阵元选择下拉框（用于查看接收信号）
        self.signal_element_combo = QComboBox()
        self.signal_element_combo.currentIndexChanged.connect(self.on_signal_element_selected)
        signal_view_layout.addRow("选择阵元:", self.signal_element_combo)

        signal_view_group.setLayout(signal_view_layout)
        signal_processing_layout.addWidget(signal_view_group)

        # 信号区间选择组已移至阵列处理组

        # 创建处理按钮
        process_layout = QHBoxLayout()

        self.process_signal_button = QPushButton("处理信号")
        self.process_signal_button.clicked.connect(self.on_process_signal_clicked)
        process_layout.addWidget(self.process_signal_button)

        signal_processing_layout.addLayout(process_layout)

        signal_processing_group.setLayout(signal_processing_layout)
        scroll_layout.addWidget(signal_processing_group)

        # 创建阵列处理组（第三阶段）
        array_processing_group = QGroupBox("阵列处理")
        array_processing_layout = QVBoxLayout()

        # 创建信号区间选择组（放在阵列处理组的最上方，作为所有阵列分析处理的基础）
        signal_selection_group = QGroupBox("信号区间选择")
        signal_selection_layout = QFormLayout()

        # 创建起始时间输入框（使用QDoubleSpinBox代替QLineEdit）
        self.start_time_input = QDoubleSpinBox()
        self.start_time_input.setDecimals(4)  # 设置小数位数
        self.start_time_input.setRange(0.0, 1000.0)  # 设置范围
        self.start_time_input.setSingleStep(0.1)  # 设置步长
        self.start_time_input.setValue(0.0)  # 设置默认值
        signal_selection_layout.addRow("起始时间 (s):", self.start_time_input)

        # 创建结束时间输入框（使用QDoubleSpinBox代替QLineEdit）
        self.end_time_input = QDoubleSpinBox()
        self.end_time_input.setDecimals(4)  # 设置小数位数
        self.end_time_input.setRange(0.0, 1000.0)  # 设置范围
        self.end_time_input.setSingleStep(0.1)  # 设置步长
        self.end_time_input.setValue(0.0)  # 设置默认值
        signal_selection_layout.addRow("结束时间 (s):", self.end_time_input)

        signal_selection_group.setLayout(signal_selection_layout)
        array_processing_layout.addWidget(signal_selection_group)

        # 创建频谱分析组
        spectrum_analysis_group = QGroupBox("频谱分析")
        spectrum_analysis_layout = QVBoxLayout()

        # 创建阵元选择组
        element_selection_layout = QFormLayout()

        # 创建阵元选择下拉框
        self.spectrum_element_combo = QComboBox()
        self.spectrum_element_combo.currentIndexChanged.connect(self.on_spectrum_element_selected)
        element_selection_layout.addRow("选择阵元:", self.spectrum_element_combo)

        spectrum_analysis_layout.addLayout(element_selection_layout)

        # 创建频率范围控件（仅用于频谱分析）
        self.freq_range_check = QCheckBox("限制频率范围")
        self.freq_range_check.setChecked(False)
        spectrum_analysis_layout.addWidget(self.freq_range_check)

        freq_range_layout = QHBoxLayout()

        self.freq_min_spin = QSpinBox()
        self.freq_min_spin.setRange(1, 10000)
        self.freq_min_spin.setValue(100)
        self.freq_min_spin.setSingleStep(100)
        self.freq_min_spin.setSuffix(" Hz")
        self.freq_min_spin.setEnabled(False)
        freq_range_layout.addWidget(QLabel("最小频率:"))
        freq_range_layout.addWidget(self.freq_min_spin)

        self.freq_max_spin = QSpinBox()
        self.freq_max_spin.setRange(1, 10000)
        self.freq_max_spin.setValue(1000)
        self.freq_max_spin.setSingleStep(100)
        self.freq_max_spin.setSuffix(" Hz")
        self.freq_max_spin.setEnabled(False)
        freq_range_layout.addWidget(QLabel("最大频率:"))
        freq_range_layout.addWidget(self.freq_max_spin)

        spectrum_analysis_layout.addLayout(freq_range_layout)

        # 连接频率范围复选框的状态变更信号
        self.freq_range_check.stateChanged.connect(self.on_freq_range_changed)

        # 创建计算频谱按钮
        self.calculate_spectrum_button = QPushButton("计算频谱")
        self.calculate_spectrum_button.clicked.connect(self.on_calculate_spectrum_clicked)
        spectrum_analysis_layout.addWidget(self.calculate_spectrum_button)

        spectrum_analysis_group.setLayout(spectrum_analysis_layout)
        array_processing_layout.addWidget(spectrum_analysis_group)

        # 创建指向性计算组
        directivity_group = QGroupBox("指向性计算")
        directivity_layout = QVBoxLayout()

        # 频率范围控件
        freq_range_group = QGroupBox("频率范围")
        freq_range_group_layout = QVBoxLayout()

        self.directivity_freq_range_check = QCheckBox("限制频率范围")
        self.directivity_freq_range_check.setChecked(False)
        freq_range_group_layout.addWidget(self.directivity_freq_range_check)

        freq_range_layout = QHBoxLayout()

        self.directivity_freq_min_spin = QSpinBox()
        self.directivity_freq_min_spin.setRange(1, 10000)
        self.directivity_freq_min_spin.setValue(100)
        self.directivity_freq_min_spin.setSingleStep(100)
        self.directivity_freq_min_spin.setSuffix(" Hz")
        self.directivity_freq_min_spin.setEnabled(False)
        freq_range_layout.addWidget(QLabel("最小频率:"))
        freq_range_layout.addWidget(self.directivity_freq_min_spin)

        self.directivity_freq_max_spin = QSpinBox()
        self.directivity_freq_max_spin.setRange(1, 10000)
        self.directivity_freq_max_spin.setValue(1000)
        self.directivity_freq_max_spin.setSingleStep(100)
        self.directivity_freq_max_spin.setSuffix(" Hz")
        self.directivity_freq_max_spin.setEnabled(False)
        freq_range_layout.addWidget(QLabel("最大频率:"))
        freq_range_layout.addWidget(self.directivity_freq_max_spin)

        freq_range_group_layout.addLayout(freq_range_layout)

        # 连接频率范围复选框的状态变更信号
        self.directivity_freq_range_check.stateChanged.connect(self.on_directivity_freq_range_changed)

        freq_range_group.setLayout(freq_range_group_layout)
        directivity_layout.addWidget(freq_range_group)

        # 创建扫描参数组
        scan_params_group = QGroupBox("扫描参数")
        scan_params_layout = QFormLayout()

        # 角度范围 - 对于距离-深度平面，使用-90到90度的俯仰角范围
        self.angle_start_spin = QSpinBox()
        self.angle_start_spin.setRange(-90, 90)
        self.angle_start_spin.setValue(-90)
        self.angle_start_spin.setSingleStep(5)
        self.angle_start_spin.setSuffix("°")
        scan_params_layout.addRow("起始角度:", self.angle_start_spin)

        self.angle_end_spin = QSpinBox()
        self.angle_end_spin.setRange(-90, 90)
        self.angle_end_spin.setValue(90)
        self.angle_end_spin.setSingleStep(5)
        self.angle_end_spin.setSuffix("°")
        scan_params_layout.addRow("结束角度:", self.angle_end_spin)

        self.angle_step_spin = QDoubleSpinBox()
        self.angle_step_spin.setRange(0.1, 10)
        self.angle_step_spin.setValue(1)
        self.angle_step_spin.setSingleStep(0.1)
        self.angle_step_spin.setDecimals(1)
        self.angle_step_spin.setSuffix("°")
        scan_params_layout.addRow("角度步长:", self.angle_step_spin)

        # 声速
        self.sound_speed_spin = QDoubleSpinBox()
        self.sound_speed_spin.setRange(1400, 1600)
        self.sound_speed_spin.setValue(1500)
        self.sound_speed_spin.setSingleStep(1)
        self.sound_speed_spin.setDecimals(1)
        self.sound_speed_spin.setSuffix(" m/s")
        scan_params_layout.addRow("声速:", self.sound_speed_spin)

        scan_params_group.setLayout(scan_params_layout)
        directivity_layout.addWidget(scan_params_group)

        # 创建波束形成参数组
        beamforming_params_group = QGroupBox("波束形成参数")
        beamforming_params_layout = QFormLayout()

        # 参考位置
        self.reference_position_combo = QComboBox()
        self.reference_position_combo.addItem("阵列中心", "center")
        self.reference_position_combo.addItem("阵列第一个元素", "first")
        self.reference_position_combo.addItem("阵列最后一个元素", "last")
        beamforming_params_layout.addRow("参考位置:", self.reference_position_combo)

        # 加权方式
        self.weighting_combo = QComboBox()
        self.weighting_combo.addItem("均匀加权", "uniform")
        self.weighting_combo.addItem("汉宁窗", "hanning")
        self.weighting_combo.addItem("汉明窗", "hamming")
        self.weighting_combo.addItem("布莱克曼窗", "blackman")
        beamforming_params_layout.addRow("加权方式:", self.weighting_combo)

        # 时延补偿方法
        self.delay_method_combo = QComboBox()
        self.delay_method_combo.addItem("相位补偿（精确频域方法）", "phase")
        self.delay_method_combo.addItem("分数延迟（时域线性插值）", "fractional")
        self.delay_method_combo.addItem("整数延迟（快速时域方法）", "integer")

        beamforming_params_layout.addRow("时延补偿方法:", self.delay_method_combo)

        beamforming_params_group.setLayout(beamforming_params_layout)
        directivity_layout.addWidget(beamforming_params_group)

        # 计算按钮
        self.calculate_directivity_button = QPushButton("计算指向性")
        self.calculate_directivity_button.clicked.connect(self.on_calculate_directivity_clicked)
        directivity_layout.addWidget(self.calculate_directivity_button)

        directivity_group.setLayout(directivity_layout)
        array_processing_layout.addWidget(directivity_group)

        # 创建互功率谱计算组
        cross_spectrum_group = QGroupBox("互功率谱计算")
        cross_spectrum_layout = QVBoxLayout()

        # 创建阵元对选择
        element_pair_layout = QFormLayout()

        # 创建第一个阵元选择下拉框
        self.csd_element1_combo = QComboBox()
        element_pair_layout.addRow("阵元1:", self.csd_element1_combo)

        # 创建第二个阵元选择下拉框
        self.csd_element2_combo = QComboBox()
        element_pair_layout.addRow("阵元2:", self.csd_element2_combo)

        cross_spectrum_layout.addLayout(element_pair_layout)

        # 添加是否显示相干函数的复选框
        self.show_coherence_check = QCheckBox("计算并显示相干函数")
        self.show_coherence_check.setChecked(True)
        self.show_coherence_check.setToolTip("选中此项将计算并显示相干函数")
        cross_spectrum_layout.addWidget(self.show_coherence_check)

        # 创建计算互功率谱按钮
        self.calculate_csd_button = QPushButton("计算互功率谱")
        self.calculate_csd_button.clicked.connect(self.on_calculate_csd_clicked)
        cross_spectrum_layout.addWidget(self.calculate_csd_button)

        cross_spectrum_group.setLayout(cross_spectrum_layout)
        array_processing_layout.addWidget(cross_spectrum_group)

        # 完成阵列处理组的设置
        array_processing_group.setLayout(array_processing_layout)
        scroll_layout.addWidget(array_processing_group)

        # 添加弹性空间
        scroll_layout.addStretch()

        # 设置滚动区域
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)

    def load_params_from_data_manager(self):
        """
        从数据管理器加载参数
        """
        if not self.data_manager:
            return

        try:
            # 获取参数
            params = self.data_manager.get_parameters('integrated')
            if not params:
                return

            print(f"加载综合仿真参数: {params}")

            # 更新UI
            if 'channel_data_dir' in params:
                self.channel_dir_edit.setText(params['channel_data_dir'])
                print(f"设置信道数据目录: {params['channel_data_dir']}")

            # 获取结果数据，用于更新阵元和频率下拉框
            results = self.data_manager.get_results('integrated')
            if results and 'channel_data' in results:
                # 更新阵元和频率下拉框
                if 'selected_element_index' in params:
                    # 注意：需要先加载阵元列表，然后再设置选中的阵元
                    self.update_element_combo(params.get('selected_element_index', 0))
                    print(f"设置选中阵元索引: {params.get('selected_element_index', 0)}")

                if 'selected_frequency' in params:
                    # 注意：需要先加载频率列表，然后再设置选中的频率
                    self.update_frequency_combo(params.get('selected_frequency', 0))
                    print(f"设置选中频率: {params.get('selected_frequency', 0)}")

            # 更新截断设置
            use_all_arrivals = params.get('use_all_arrivals', True)
            truncation_time = params.get('truncation_time', 0.0)

            self.use_all_arrivals_radio.setChecked(use_all_arrivals)
            self.use_truncation_radio.setChecked(not use_all_arrivals)
            self.truncation_time_spin.setValue(truncation_time)
            self.truncation_time_spin.setEnabled(not use_all_arrivals)

            # 更新海洋环境噪声设置
            add_ambient_noise = params.get('add_ambient_noise', True)
            self.add_ambient_noise_check.setChecked(add_ambient_noise)

            # 更新源信号信息
            self.update_source_signal_info()

            # 更新海洋环境噪声信息
            self.update_ambient_noise_info()

            # 更新源信号时长建议
            self.update_duration_suggestion()

        except Exception as e:
            print(f"从数据管理器加载参数失败: {e}")

    def reset_parameters(self):
        """
        重置参数到默认值
        """
        # 重置信道数据目录
        self.channel_dir_edit.setText("")

        # 重置截断设置
        self.use_all_arrivals_radio.setChecked(True)
        self.use_truncation_radio.setChecked(False)
        self.truncation_time_spin.setValue(0.0)
        self.truncation_time_spin.setEnabled(False)

        # 重置海洋环境噪声设置
        self.add_ambient_noise_check.setChecked(True)

        # 重置信号区间选择
        self.start_time_input.setValue(0.0)
        self.end_time_input.setValue(1.0)

        # 重置频谱分析参数
        self.freq_range_check.setChecked(False)
        self.freq_min_spin.setValue(100)
        self.freq_max_spin.setValue(1000)

        # 清空阵元下拉框
        self.element_combo.clear()  # 信道数据分析的阵元选择

        # 清空频率下拉框
        self.frequency_combo.clear()

        # 重置信息标签
        self.source_signal_info_label.setText("未检测到源信号")
        self.ambient_noise_info_label.setText("未检测到海洋环境噪声")
        self.duration_suggestion_label.setText("")

    def update_element_combo(self, selected_index=0):
        """
        更新信道数据分析阵元下拉框

        Args:
            selected_index: 选中的阵元索引
        """
        # 清空下拉框
        self.element_combo.clear()

        # 获取结果
        results = self.data_manager.get_results('integrated')
        if not results or 'channel_data' not in results:
            return

        # 获取阵元列表
        array_elements = results['channel_data'].get('array_elements', [])

        # 添加阵元到下拉框
        for i, element in enumerate(array_elements):
            if isinstance(element, (list, tuple)) and len(element) >= 2:
                self.element_combo.addItem(f"阵元 #{i}: ({element[0]}, {element[1]}) 米")
            else:
                self.element_combo.addItem(f"阵元 #{i}: {element}")

        # 设置选中的阵元
        if 0 <= selected_index < self.element_combo.count():
            self.element_combo.setCurrentIndex(selected_index)

    def update_frequency_combo(self, selected_frequency=0):
        """
        更新频率下拉框

        Args:
            selected_frequency: 选中的频率
        """
        # 清空下拉框
        self.frequency_combo.clear()

        # 获取结果
        results = self.data_manager.get_results('integrated')
        if not results or 'channel_data' not in results:
            return

        # 获取频率列表
        frequencies = results['channel_data'].get('frequencies', [])

        # 添加频率到下拉框
        for freq in frequencies:
            self.frequency_combo.addItem(f"{freq} Hz")

        # 设置选中的频率
        selected_index = 0
        if frequencies:
            # 找到最接近选中频率的索引
            selected_index = min(range(len(frequencies)),
                                key=lambda i: abs(frequencies[i] - selected_frequency))

        if 0 <= selected_index < self.frequency_combo.count():
            self.frequency_combo.setCurrentIndex(selected_index)

    def update_signal_element_combo(self, selected_index=0):
        """
        更新信号处理阵元下拉框

        Args:
            selected_index: 选中的阵元索引
        """
        # 清空下拉框
        self.signal_element_combo.clear()

        # 获取结果
        results = self.data_manager.get_results('integrated')
        if not results or 'signal_processing' not in results:
            return

        # 获取接收信号数据
        signal_processing = results['signal_processing']
        received_signals = signal_processing.get('received_signals', {})

        # 如果没有接收信号数据，返回
        if not received_signals:
            return

        # 获取阵元列表并按数值排序
        element_ids = sorted(list(received_signals.keys()))

        # 获取阵元位置信息
        array_elements = []
        if 'channel_data' in results:
            array_elements = results['channel_data'].get('array_elements', [])

        # 添加阵元到下拉框
        for element_id in element_ids:
            # 确保element_id是整数类型，并且在array_elements范围内
            try:
                element_idx = int(element_id)
                if element_idx < len(array_elements) and isinstance(array_elements[element_idx], (list, tuple)) and len(array_elements[element_idx]) >= 2:
                    self.signal_element_combo.addItem(f"阵元 #{element_id}: ({array_elements[element_idx][0]}, {array_elements[element_idx][1]}) 米")
                else:
                    self.signal_element_combo.addItem(f"阵元 #{element_id}")
            except (ValueError, TypeError):
                self.signal_element_combo.addItem(f"阵元 #{element_id}")

        # 设置选中的阵元
        if element_ids:
            # 找到最接近选中索引的阵元ID
            # 确保selected_index是正确的类型
            try:
                if isinstance(selected_index, str):
                    selected_index = int(selected_index)
            except (ValueError, TypeError):
                selected_index = 0

            if selected_index in element_ids:
                selected_idx = element_ids.index(selected_index)
            else:
                selected_idx = 0

            if 0 <= selected_idx < self.signal_element_combo.count():
                self.signal_element_combo.setCurrentIndex(selected_idx)

    def update_spectrum_element_combo(self, selected_index=0):
        """
        更新频谱分析阵元下拉框

        Args:
            selected_index: 选中的阵元索引
        """
        # 清空下拉框
        self.spectrum_element_combo.clear()

        # 获取结果
        results = self.data_manager.get_results('integrated')
        if not results or 'signal_processing' not in results:
            return

        # 获取接收信号数据
        signal_processing = results['signal_processing']
        received_signals = signal_processing.get('received_signals', {})

        # 如果没有接收信号数据，返回
        if not received_signals:
            return

        # 获取阵元列表并按数值排序
        element_ids = sorted(list(received_signals.keys()))

        # 获取阵元位置信息
        array_elements = []
        if 'channel_data' in results:
            array_elements = results['channel_data'].get('array_elements', [])

        # 添加阵元到下拉框
        for element_id in element_ids:
            # 确保element_id是整数类型，并且在array_elements范围内
            try:
                element_idx = int(element_id)
                if element_idx < len(array_elements) and isinstance(array_elements[element_idx], (list, tuple)) and len(array_elements[element_idx]) >= 2:
                    self.spectrum_element_combo.addItem(f"阵元 #{element_id}: ({array_elements[element_idx][0]}, {array_elements[element_idx][1]}) 米")
                else:
                    self.spectrum_element_combo.addItem(f"阵元 #{element_id}")
            except (ValueError, TypeError):
                self.spectrum_element_combo.addItem(f"阵元 #{element_id}")

        # 设置选中的阵元
        if element_ids:
            # 找到最接近选中索引的阵元ID
            # 确保selected_index是正确的类型
            try:
                if isinstance(selected_index, str):
                    selected_index = int(selected_index)
            except (ValueError, TypeError):
                selected_index = 0

            if selected_index in element_ids:
                selected_idx = element_ids.index(selected_index)
            else:
                selected_idx = 0

            if 0 <= selected_idx < self.spectrum_element_combo.count():
                self.spectrum_element_combo.setCurrentIndex(selected_idx)

    def update_csd_element_combos(self, selected_element1=0, selected_element2=0):
        """
        更新互功率谱阵元下拉框

        Args:
            selected_element1: 选中的第一个阵元索引
            selected_element2: 选中的第二个阵元索引
        """
        # 清空下拉框
        self.csd_element1_combo.clear()
        self.csd_element2_combo.clear()

        # 获取结果
        results = self.data_manager.get_results('integrated')
        if not results or 'signal_processing' not in results:
            return

        # 获取接收信号数据
        signal_processing = results['signal_processing']
        received_signals = signal_processing.get('received_signals', {})

        # 如果没有接收信号数据，返回
        if not received_signals:
            return

        # 获取阵元列表并按数值排序
        element_ids = sorted(list(received_signals.keys()))

        # 获取阵元位置信息
        array_elements = []
        if 'channel_data' in results:
            array_elements = results['channel_data'].get('array_elements', [])

        # 添加阵元到下拉框
        for element_id in element_ids:
            # 确保element_id是整数类型，并且在array_elements范围内
            try:
                element_idx = int(element_id)
                if element_idx < len(array_elements) and isinstance(array_elements[element_idx], (list, tuple)) and len(array_elements[element_idx]) >= 2:
                    element_text = f"阵元 #{element_id}: ({array_elements[element_idx][0]}, {array_elements[element_idx][1]}) 米"
                else:
                    element_text = f"阵元 #{element_id}"
            except (ValueError, TypeError):
                element_text = f"阵元 #{element_id}"

            self.csd_element1_combo.addItem(element_text, element_id)
            self.csd_element2_combo.addItem(element_text, element_id)

        # 设置选中的阵元
        if element_ids:
            # 找到最接近选中索引的阵元ID
            if selected_element1 in element_ids:
                selected_idx1 = element_ids.index(selected_element1)
            else:
                selected_idx1 = 0

            if selected_element2 in element_ids:
                selected_idx2 = element_ids.index(selected_element2)
            else:
                selected_idx2 = min(1, len(element_ids) - 1)  # 默认选择第二个阵元，如果只有一个阵元则选择第一个

            if 0 <= selected_idx1 < self.csd_element1_combo.count():
                self.csd_element1_combo.setCurrentIndex(selected_idx1)

            if 0 <= selected_idx2 < self.csd_element2_combo.count():
                self.csd_element2_combo.setCurrentIndex(selected_idx2)

    def update_channel_data_info(self):
        """
        更新信道数据信息显示
        """
        # 获取结果
        results = self.data_manager.get_results('integrated')
        if not results or 'channel_data' not in results:
            print("未找到信道数据结果")
            return

        # 获取信道数据
        channel_data = results['channel_data']
        print(f"找到信道数据: {channel_data.keys()}")

        # 更新阵元和频率下拉框
        params = self.data_manager.get_parameters('integrated')
        selected_channel_element_index = params.get('selected_channel_element_index', 0) if params else 0
        selected_signal_element_index = params.get('selected_signal_element_index', 0) if params else 0
        selected_spectrum_element_index = params.get('selected_spectrum_element_index', 0) if params else 0
        selected_frequency = params.get('selected_frequency', 0) if params else 0

        # 获取互功率谱阵元对
        selected_csd_element1 = params.get('selected_csd_element1', 0) if params else 0
        selected_csd_element2 = params.get('selected_csd_element2', 1) if params else 1

        self.update_element_combo(selected_channel_element_index)
        self.update_frequency_combo(selected_frequency)

        # 更新信号处理阵元下拉框
        self.update_signal_element_combo(selected_signal_element_index)

        # 更新频谱分析阵元下拉框
        self.update_spectrum_element_combo(selected_spectrum_element_index)

        # 更新互功率谱阵元下拉框
        self.update_csd_element_combos(selected_csd_element1, selected_csd_element2)

        # 获取全局到达时间信息
        t_first_global = channel_data.get('t_first_global', None)
        t_last_global = channel_data.get('t_last_global', None)

        # 更新全局到达时间信息
        if t_first_global is not None and t_last_global is not None:
            self.arrival_times_info_label.setText(
                f"全局到达时间: 最早 {t_first_global:.4f} s, 最晚 {t_last_global:.4f} s")

            # 动态设置截断时间范围
            self.truncation_time_spin.setRange(t_first_global, t_last_global)

            # 如果当前值不在范围内，设置为最小值
            current_value = self.truncation_time_spin.value()
            if current_value < t_first_global or current_value > t_last_global:
                self.truncation_time_spin.setValue(t_first_global)

            # 如果使用截断时间选项被选中，更新数据管理器中的截断时间
            if self.use_truncation_radio.isChecked():
                self.data_manager.update_parameter('integrated', 'truncation_time', t_first_global)
        else:
            self.arrival_times_info_label.setText("全局到达时间: 未知")

        # 更新源信号信息和时长建议
        self.update_source_signal_info()
        self.update_duration_suggestion()

    def browse_channel_data_dir(self):
        """
        浏览信道数据目录
        """
        # 获取当前目录
        current_dir = self.channel_dir_edit.text()
        if not current_dir:
            current_dir = os.getcwd()

        # 打开目录选择对话框
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "选择信道数据目录",
            current_dir,
            QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
        )

        if not dir_path:
            return

        # 检查目录是否包含meta.json文件
        meta_file = os.path.join(dir_path, 'meta.json')
        if not os.path.exists(meta_file):
            QMessageBox.warning(self, "目录错误", f"所选目录不包含meta.json文件:\n{dir_path}")
            return

        # 更新目录路径
        self.channel_dir_edit.setText(dir_path)

        # 更新数据管理器
        if self.data_manager:
            self.data_manager.update_parameter('integrated', 'channel_data_dir', dir_path)

        # 直接调用加载方法，而不是发送信号
        from src.ui.main.main_window import MainWindow
        main_window = self.find_parent(MainWindow)
        if main_window:
            success = main_window.simulation_controller.load_channel_data(dir_path)
            if not success:
                QMessageBox.warning(self, "加载错误", "加载信道数据失败，请检查数据格式")
                return

            # 更新UI
            self.update_channel_data_info()
        else:
            # 如果找不到主窗口，则发送信号
            self.data_manager.update_parameter('integrated', 'computation_type', 'channel_data_analysis')
            self.simulation_requested.emit('channel_data_analysis')

    def findParent(self, parent_class):
        """
        查找指定类型的父窗口

        Args:
            parent_class: 父窗口类型

        Returns:
            找到的父窗口，如果未找到则返回None
        """
        parent = self.parent()
        while parent:
            if isinstance(parent, parent_class):
                return parent
            parent = parent.parent()
        return None

    def on_element_selected(self, index):
        """
        信道数据分析阵元选择事件处理

        Args:
            index: 选中的索引
        """
        if index < 0 or not self.data_manager:
            return

        # 更新数据管理器，使用专门的参数
        self.data_manager.update_parameter('integrated', 'selected_channel_element_index', index)

        # 通知数据管理器参数已更新，触发视图更新
        self.data_manager.parameters_changed.emit('integrated')

    def on_frequency_selected(self, index):
        """
        频率选择事件处理

        Args:
            index: 选中的索引
        """
        if index < 0 or not self.data_manager:
            return

        # 获取选中的频率
        results = self.data_manager.get_results('integrated')
        if not results or 'channel_data' not in results:
            return

        frequencies = results['channel_data'].get('frequencies', [])
        if index >= len(frequencies):
            return

        # 更新数据管理器
        self.data_manager.update_parameter('integrated', 'selected_frequency', frequencies[index])

        # 通知数据管理器参数已更新，触发视图更新
        self.data_manager.parameters_changed.emit('integrated')



    def on_truncation_option_changed(self, _):
        """
        截断选项变更事件处理

        Args:
            _: 是否选中（未使用）
        """
        if not self.data_manager:
            return

        # 更新截断时间输入框状态
        use_truncation = self.use_truncation_radio.isChecked()
        self.truncation_time_spin.setEnabled(use_truncation)

        # 更新数据管理器中的参数
        self.data_manager.update_parameter('integrated', 'use_all_arrivals', not use_truncation)

        # 如果选择了截断时间，更新截断时间参数
        if use_truncation:
            truncation_time = self.truncation_time_spin.value()
            self.data_manager.update_parameter('integrated', 'truncation_time', truncation_time)

        # 请求计算建议时长
        self._request_duration_calculation()

    def on_directivity_freq_range_changed(self, state):
        """
        指向性计算频率范围复选框状态变更事件处理

        Args:
            state: 复选框状态
        """
        # 更新频率范围输入框状态
        enabled = (state == Qt.Checked)
        self.directivity_freq_min_spin.setEnabled(enabled)
        self.directivity_freq_max_spin.setEnabled(enabled)

    def on_freq_range_changed(self, state):
        """
        频谱分析频率范围复选框状态变更事件处理

        Args:
            state: 复选框状态
        """
        # 更新频率范围输入框状态
        enabled = (state == Qt.Checked)
        self.freq_min_spin.setEnabled(enabled)
        self.freq_max_spin.setEnabled(enabled)

        # 更新源信号时长建议标签，显示计算中
        self.duration_suggestion_label.setText("建议源信号时长: 计算中...")

        # 请求计算建议时长（延迟执行，避免频繁计算）
        self._request_duration_calculation()

    def on_truncation_time_changed(self, value):
        """
        截断时间变更事件处理

        Args:
            value: 新的截断时间
        """
        if not self.data_manager:
            return

        # 更新数据管理器
        self.data_manager.update_parameter('integrated', 'truncation_time', value)

        # 更新源信号时长建议标签，显示计算中
        self.duration_suggestion_label.setText("建议源信号时长: 计算中...")

        # 请求计算建议时长（延迟执行，避免频繁计算）
        self._request_duration_calculation()

    def on_add_ambient_noise_changed(self, state):
        """
        叠加海洋环境噪声选项变更事件处理

        Args:
            state: 复选框状态
        """
        if not self.data_manager:
            return

        # 检查是否选中
        is_checked = state == Qt.Checked

        # 更新数据管理器
        self.data_manager.update_parameter('integrated', 'add_ambient_noise', is_checked)

        # 同时更新use_original_signal参数（与add_ambient_noise相反）
        self.data_manager.update_parameter('integrated', 'use_original_signal', not is_checked)

        print(f"叠加海洋环境噪声: {is_checked}, 使用原始信号: {not is_checked}")

        # 更新海洋环境噪声信息
        self.update_ambient_noise_info()

    def on_spectrum_element_selected(self, index):
        """
        频谱分析阵元选择事件处理

        Args:
            index: 选中的索引
        """
        if index < 0 or not self.data_manager:
            return

        # 获取当前选中的阵元ID
        results = self.data_manager.get_results('integrated')
        if not results or 'signal_processing' not in results:
            return

        signal_processing = results['signal_processing']
        received_signals = signal_processing.get('received_signals', {})

        # 获取排序后的阵元ID列表
        element_ids = sorted(list(received_signals.keys()))

        # 确保索引有效
        if index >= len(element_ids):
            return

        # 获取实际的阵元ID
        element_id = element_ids[index]

        # 更新数据管理器中的选中阵元，使用专门的参数
        self.data_manager.update_parameter('integrated', 'selected_spectrum_element_index', element_id)

        # 更新信号区间选择的默认值
        self.update_signal_time_range_defaults(element_id)

        # 切换到频谱图标签页 - 无论是否已计算过频谱，都切换到频谱图标签页
        from src.ui.main.main_window import MainWindow
        main_window = self.find_parent(MainWindow)
        if main_window and hasattr(main_window, 'integrated_view'):
            main_window.integrated_view.tabs.setCurrentWidget(
                main_window.integrated_view.spectrum_widget
            )

            # 如果已经计算过该阵元的频谱，直接更新频谱图
            if 'spectrum' in signal_processing and element_id in signal_processing['spectrum']:
                # 更新频谱图
                spectrum_result = signal_processing['spectrum'][element_id]
                main_window.integrated_view.update_spectrum_plot(
                    freqs=spectrum_result['freqs'],
                    psd_db=spectrum_result['psd_db'],
                    element_index=element_id,
                    signal_type=spectrum_result['signal_type']
                )
            else:
                # 如果没有计算过该阵元的频谱，只在控制台打印信息，不弹出提示框
                print(f"阵元 #{element_id} 的频谱尚未计算，请点击'计算频谱'按钮计算")

        # 通知数据管理器参数已更新，触发视图更新
        self.data_manager.parameters_changed.emit('integrated')

    def on_calculate_directivity_clicked(self):
        """
        计算指向性按钮点击事件处理
        """
        # 检查是否已处理信号
        results = self.data_manager.get_results('integrated')
        if not results or 'signal_processing' not in results:
            QMessageBox.warning(self, "错误", "请先处理信号")
            return

        # 获取信号处理结果
        signal_processing = results['signal_processing']
        received_signals = signal_processing.get('received_signals', {})

        if not received_signals:
            QMessageBox.warning(self, "错误", "未找到接收信号数据")
            return

        # 检查频率范围是否有效
        if self.directivity_freq_range_check.isChecked():
            freq_min = self.directivity_freq_min_spin.value()
            freq_max = self.directivity_freq_max_spin.value()
            if freq_min >= freq_max:
                QMessageBox.warning(self, "输入错误", "最小频率必须小于最大频率")
                return

        # 检查角度范围是否有效
        angle_start = self.angle_start_spin.value()
        angle_end = self.angle_end_spin.value()
        if angle_start >= angle_end:
            QMessageBox.warning(self, "输入错误", "起始角度必须小于结束角度")
            return

        # 禁用计算按钮，避免重复点击
        self.calculate_directivity_button.setEnabled(False)
        self.calculate_directivity_button.setText("计算中...")

        # 创建进度对话框
        progress_dialog = QProgressDialog("计算指向性...", "取消", 0, 100, self)
        progress_dialog.setWindowTitle("进度")
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)  # 立即显示
        progress_dialog.setValue(0)

        # 连接取消按钮
        progress_dialog.canceled.connect(self.on_directivity_calculation_canceled)

        # 获取当前选中的阵元索引（从频谱分析的阵元选择下拉框）
        element_index = self.spectrum_element_combo.currentIndex()
        if element_index < 0:
            QMessageBox.warning(self, "错误", "请先选择阵元")
            return

        # 获取信号区间选择参数
        start_time = self.start_time_input.value()
        end_time = self.end_time_input.value()
        duration = end_time - start_time

        # 收集参数
        params = {
            # 信号选择
            'start_time': start_time,
            'duration': duration,
            'freq_range_enabled': self.directivity_freq_range_check.isChecked(),
            'freq_min': self.directivity_freq_min_spin.value(),
            'freq_max': self.directivity_freq_max_spin.value(),
            'element_index': element_index,

            # 扫描参数
            'angle_start': angle_start,
            'angle_end': angle_end,
            'angle_step': self.angle_step_spin.value(),
            'sound_speed': self.sound_speed_spin.value(),

            # 波束形成参数
            'reference_position': self.reference_position_combo.currentData(),
            'weighting': self.weighting_combo.currentData(),
            'delay_method': self.delay_method_combo.currentData(),  # 使用用户选择的时延补偿方法
            'display_mode': 'linear',  # 固定使用线性图

            # 系统内部默认使用并行计算

            # 计算类型
            'computation_type': 'directivity_calculation'
        }

        # 更新数据管理器
        for key, value in params.items():
            self.data_manager.update_parameter('integrated', key, value)

        # 连接进度信号
        from src.ui.main.main_window import MainWindow
        main_window = self.find_parent(MainWindow)
        if main_window and hasattr(main_window, 'simulation_controller'):
            controller = main_window.simulation_controller
            # 连接进度信号
            controller.simulation_progress.connect(self.on_directivity_calculation_progress)
            # 连接完成信号
            controller.simulation_completed.connect(self.on_directivity_calculation_completed)
            # 连接错误信号
            controller.simulation_error.connect(self.on_directivity_calculation_error)

            # 保存进度对话框引用
            self.directivity_progress_dialog = progress_dialog

        # 发送仿真请求
        self.simulation_requested.emit('directivity_calculation')

    def on_directivity_calculation_progress(self, module, progress):
        """
        指向性计算进度更新事件处理

        Args:
            module: 模块名称
            progress: 进度值（0-100）
        """
        if module != 'integrated':
            return

        # 更新进度对话框
        if hasattr(self, 'directivity_progress_dialog') and self.directivity_progress_dialog:
            self.directivity_progress_dialog.setValue(progress)

    def on_directivity_calculation_completed(self, module):
        """
        指向性计算完成事件处理

        Args:
            module: 模块名称
        """
        if module != 'integrated':
            return

        print("指向性计算完成事件处理")

        # 关闭进度对话框
        if hasattr(self, 'directivity_progress_dialog') and self.directivity_progress_dialog:
            self.directivity_progress_dialog.close()
            self.directivity_progress_dialog = None

        # 断开信号连接
        from src.ui.main.main_window import MainWindow
        main_window = self.find_parent(MainWindow)
        if main_window and hasattr(main_window, 'simulation_controller'):
            controller = main_window.simulation_controller
            controller.simulation_progress.disconnect(self.on_directivity_calculation_progress)
            controller.simulation_completed.disconnect(self.on_directivity_calculation_completed)
            controller.simulation_error.disconnect(self.on_directivity_calculation_error)

        # 恢复计算按钮
        self.calculate_directivity_button.setEnabled(True)
        self.calculate_directivity_button.setText("计算指向性")

        # 获取计算结果
        results = self.data_manager.get_results('integrated')
        if 'array_processing' in results and 'directivity' in results['array_processing']:
            directivity_result = results['array_processing']['directivity']
            print(f"找到指向性结果，角度点数: {len(directivity_result.get('angles', []))}")

            # 手动触发结果更新
            self.data_manager.results_changed.emit('integrated')
            print("已手动触发结果更新信号")

            # 直接调用视图更新方法
            from src.ui.main.main_window import MainWindow
            main_window = self.find_parent(MainWindow)
            if main_window and hasattr(main_window, 'integrated_view'):
                # 获取指向性数据
                angles = np.array(directivity_result.get('angles', []))
                pattern = np.array(directivity_result.get('pattern', []))  # 原始功率数据
                params = directivity_result.get('params', {})

                # 将原始的平均功率数据转换为归一化的dB指向性图
                if len(pattern) > 0:
                    # 确保所有值都是正的
                    pattern_safe = np.where(pattern > 0, pattern, 1e-10)

                    # 转换为dB
                    pattern_db = 10 * np.log10(pattern_safe)

                    # 归一化
                    if np.any(np.isfinite(pattern_db)):
                        max_db = np.max(pattern_db[np.isfinite(pattern_db)])
                        pattern_db = pattern_db - max_db

                    # 替换任何NaN或无穷大值
                    pattern_db = np.where(np.isfinite(pattern_db), pattern_db, -100)

                    print(f"将原始功率数据转换为dB并归一化，范围: [{np.min(pattern_db)}, {np.max(pattern_db)}] dB")

                    # 直接调用视图更新方法
                    print("直接调用指向性视图更新方法")
                    main_window.integrated_view.update_directivity_plot(angles, pattern_db, params)
                else:
                    print("指向性数据为空，无法转换为dB")
        else:
            print("未找到指向性结果")

    def on_directivity_calculation_error(self, module, error_message):
        """
        指向性计算错误事件处理

        Args:
            module: 模块名称
            error_message: 错误信息
        """
        if module != 'integrated':
            return

        # 关闭进度对话框
        if hasattr(self, 'directivity_progress_dialog') and self.directivity_progress_dialog:
            self.directivity_progress_dialog.close()
            self.directivity_progress_dialog = None

        # 断开信号连接
        from src.ui.main.main_window import MainWindow
        main_window = self.find_parent(MainWindow)
        if main_window and hasattr(main_window, 'simulation_controller'):
            controller = main_window.simulation_controller
            controller.simulation_progress.disconnect(self.on_directivity_calculation_progress)
            controller.simulation_completed.disconnect(self.on_directivity_calculation_completed)
            controller.simulation_error.disconnect(self.on_directivity_calculation_error)

        # 恢复计算按钮
        self.calculate_directivity_button.setEnabled(True)
        self.calculate_directivity_button.setText("计算指向性")

        # 显示错误信息
        QMessageBox.warning(self, "计算错误", f"指向性计算失败: {error_message}")

    def on_directivity_calculation_canceled(self):
        """
        指向性计算取消事件处理
        """
        # 取消计算
        from src.ui.main.main_window import MainWindow
        main_window = self.find_parent(MainWindow)
        if main_window and hasattr(main_window, 'simulation_controller'):
            controller = main_window.simulation_controller
            controller.cancel_integrated_simulation()

        # 恢复计算按钮
        self.calculate_directivity_button.setEnabled(True)
        self.calculate_directivity_button.setText("计算指向性")

    def find_parent(self, parent_class):
        """
        查找指定类型的父窗口

        Args:
            parent_class: 父窗口类型

        Returns:
            找到的父窗口，如果未找到则返回None
        """
        parent = self.parent()
        while parent:
            if isinstance(parent, parent_class):
                return parent
            parent = parent.parent()
        return None

    def on_calculate_csd_clicked(self):
        """
        计算互功率谱按钮点击事件处理
        """
        # 获取当前选中的阵元对
        element1_idx = self.csd_element1_combo.currentIndex()
        element2_idx = self.csd_element2_combo.currentIndex()

        if element1_idx < 0 or element2_idx < 0:
            QMessageBox.warning(self, "错误", "请先选择阵元对")
            return

        # 获取阵元ID
        element1_id = self.csd_element1_combo.itemData(element1_idx)
        element2_id = self.csd_element2_combo.itemData(element2_idx)

        # 获取结果
        results = self.data_manager.get_results('integrated')
        if not results or 'signal_processing' not in results:
            QMessageBox.warning(self, "错误", "请先处理信号")
            return

        # 获取信号处理结果
        signal_processing = results['signal_processing']
        received_signals = signal_processing.get('received_signals', {})

        # 检查阵元是否存在
        if element1_id not in received_signals or element2_id not in received_signals:
            QMessageBox.warning(self, "错误", f"阵元 {element1_id} 或 {element2_id} 不存在")
            return

        # 获取信号时间范围
        time_data1 = received_signals[element1_id].get('time_data', [])
        time_data2 = received_signals[element2_id].get('time_data', [])

        if len(time_data1) == 0 or len(time_data2) == 0:
            QMessageBox.warning(self, "错误", "接收信号数据为空")
            return

        # 获取用户输入的起始时间和结束时间
        start_time = self.start_time_input.value()
        end_time = self.end_time_input.value()

        # 检查时间范围是否有效
        if start_time >= end_time:
            QMessageBox.warning(self, "输入错误", "起始时间必须小于结束时间")
            return

        # 检查时间范围是否在信号范围内
        default_start_time = max(time_data1[0], time_data2[0])
        default_end_time = min(time_data1[-1], time_data2[-1])

        if start_time < default_start_time:
            print(f"警告: 起始时间 {start_time:.4f} 小于信号的最小时间 {default_start_time:.4f}，将自动调整为信号最小时间")
            start_time = default_start_time
            self.start_time_input.setValue(start_time)

        if end_time > default_end_time:
            print(f"警告: 结束时间 {end_time:.4f} 大于信号的最大时间 {default_end_time:.4f}，将自动调整为信号最大时间")
            end_time = default_end_time
            self.end_time_input.setValue(end_time)

        # 获取是否显示相干函数
        show_coherence = self.show_coherence_check.isChecked()

        # 更新数据管理器
        self.data_manager.update_parameter('integrated', 'computation_type', 'cross_spectral_density')
        self.data_manager.update_parameter('integrated', 'start_time', start_time)
        self.data_manager.update_parameter('integrated', 'end_time', end_time)
        self.data_manager.update_parameter('integrated', 'element_pair', [element1_id, element2_id])
        self.data_manager.update_parameter('integrated', 'selected_csd_element1', element1_id)
        self.data_manager.update_parameter('integrated', 'selected_csd_element2', element2_id)
        self.data_manager.update_parameter('integrated', 'show_coherence', show_coherence)

        # 找到主窗口
        from src.ui.main.main_window import MainWindow
        main_window = self.find_parent(MainWindow)
        if not main_window:
            QMessageBox.warning(self, "错误", "无法找到主窗口")
            return

        # 显示状态信息
        main_window.statusBar().showMessage("正在计算互功率谱...")

        # 禁用计算按钮，避免重复点击
        self.calculate_csd_button.setEnabled(False)
        self.calculate_csd_button.setText("计算中...")

        # 创建进度对话框
        progress_dialog = QProgressDialog("计算互功率谱...", "取消", 0, 100, self)
        progress_dialog.setWindowTitle("进度")
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)  # 立即显示
        progress_dialog.setValue(0)

        # 连接取消按钮
        progress_dialog.canceled.connect(self.on_csd_calculation_canceled)

        # 连接进度信号
        if hasattr(main_window, 'simulation_controller'):
            controller = main_window.simulation_controller
            # 连接进度信号
            controller.simulation_progress.connect(self.on_csd_calculation_progress)
            # 连接完成信号
            controller.simulation_completed.connect(self.on_csd_calculation_completed)
            # 连接错误信号
            controller.simulation_error.connect(self.on_csd_calculation_error)

            # 保存进度对话框引用
            self.csd_progress_dialog = progress_dialog

        # 发送仿真请求信号
        self.simulation_requested.emit('cross_spectral_density')

    def on_csd_calculation_progress(self, module, progress):
        """
        互功率谱计算进度更新事件处理

        Args:
            module: 模块名称
            progress: 进度值（0-100）
        """
        if module != 'integrated':
            return

        # 更新进度对话框
        if hasattr(self, 'csd_progress_dialog') and self.csd_progress_dialog:
            self.csd_progress_dialog.setValue(progress)

    def on_csd_calculation_completed(self, module):
        """
        互功率谱计算完成事件处理

        Args:
            module: 模块名称
        """
        if module != 'integrated':
            return

        print("互功率谱计算完成事件处理")

        # 关闭进度对话框
        if hasattr(self, 'csd_progress_dialog') and self.csd_progress_dialog:
            self.csd_progress_dialog.close()
            self.csd_progress_dialog = None

        # 断开信号连接
        from src.ui.main.main_window import MainWindow
        main_window = self.find_parent(MainWindow)
        if main_window and hasattr(main_window, 'simulation_controller'):
            controller = main_window.simulation_controller
            controller.simulation_progress.disconnect(self.on_csd_calculation_progress)
            controller.simulation_completed.disconnect(self.on_csd_calculation_completed)
            controller.simulation_error.disconnect(self.on_csd_calculation_error)

        # 恢复计算按钮
        self.calculate_csd_button.setEnabled(True)
        self.calculate_csd_button.setText("计算互功率谱")

        # 获取计算结果
        results = self.data_manager.get_results('integrated')
        if 'array_processing' in results and 'cross_spectral_density' in results['array_processing']:
            csd_result = results['array_processing']['cross_spectral_density']
            print(f"找到互功率谱结果，频率点数: {len(csd_result.get('freqs', []))}")

            # 手动触发结果更新
            self.data_manager.results_changed.emit('integrated')
            print("已手动触发结果更新信号")

            # 切换到互功率谱标签页
            from src.ui.main.main_window import MainWindow
            main_window = self.find_parent(MainWindow)
            if main_window and hasattr(main_window, 'integrated_view'):
                # 根据是否计算相干函数选择不同的标签页
                params = self.data_manager.get_parameters('integrated')
                show_coherence = params.get('show_coherence', False)

                # 直接调用视图更新方法
                freqs = np.array(csd_result.get('freqs', []))
                csd_magnitude = np.array(csd_result.get('csd_magnitude', []))
                csd_phase_unwrapped = np.array(csd_result.get('csd_phase_unwrapped', []))
                coherence = np.array(csd_result.get('coherence', [])) if 'coherence' in csd_result else None
                element_pair = csd_result.get('element_pair', [0, 0])
                params = csd_result.get('params', {})

                print("直接调用视图更新方法")
                main_window.integrated_view.update_cross_spectrum_plot(
                    freqs=freqs,
                    csd_magnitude=csd_magnitude,
                    csd_phase_unwrapped=csd_phase_unwrapped,
                    coherence=coherence if coherence is not None and len(coherence) > 0 else None,
                    element_pair=element_pair,
                    params=params
                )

                # 如果计算了相干函数，也更新相干函数图表
                if coherence is not None and len(coherence) > 0:
                    print("直接调用相干函数视图更新方法")
                    main_window.integrated_view.update_coherence_plot(
                        freqs=freqs,
                        coherence=coherence,
                        element_pair=element_pair,
                        params=params
                    )
        else:
            print("未找到互功率谱结果")

    def on_csd_calculation_error(self, module, error_message):
        """
        互功率谱计算错误事件处理

        Args:
            module: 模块名称
            error_message: 错误信息
        """
        if module != 'integrated':
            return

        # 关闭进度对话框
        if hasattr(self, 'csd_progress_dialog') and self.csd_progress_dialog:
            self.csd_progress_dialog.close()
            self.csd_progress_dialog = None

        # 断开信号连接
        from src.ui.main.main_window import MainWindow
        main_window = self.find_parent(MainWindow)
        if main_window and hasattr(main_window, 'simulation_controller'):
            controller = main_window.simulation_controller
            controller.simulation_progress.disconnect(self.on_csd_calculation_progress)
            controller.simulation_completed.disconnect(self.on_csd_calculation_completed)
            controller.simulation_error.disconnect(self.on_csd_calculation_error)

        # 恢复计算按钮
        self.calculate_csd_button.setEnabled(True)
        self.calculate_csd_button.setText("计算互功率谱")

        # 显示错误信息
        QMessageBox.warning(self, "计算错误", f"互功率谱计算失败: {error_message}")

    def on_csd_calculation_canceled(self):
        """
        互功率谱计算取消事件处理
        """
        # 取消计算
        from src.ui.main.main_window import MainWindow
        main_window = self.find_parent(MainWindow)
        if main_window and hasattr(main_window, 'simulation_controller'):
            controller = main_window.simulation_controller
            controller.cancel_integrated_simulation()

        # 恢复计算按钮
        self.calculate_csd_button.setEnabled(True)
        self.calculate_csd_button.setText("计算互功率谱")

    def on_calculate_spectrum_clicked(self):
        """
        计算频谱按钮点击事件处理
        """
        # 获取当前选中的阵元索引（从频谱分析的阵元选择下拉框）
        element_index = self.spectrum_element_combo.currentIndex()
        if element_index < 0:
            QMessageBox.warning(self, "错误", "请先选择阵元")
            return

        # 获取结果
        results = self.data_manager.get_results('integrated')
        if not results or 'signal_processing' not in results:
            QMessageBox.warning(self, "错误", "请先处理信号")
            return

        # 获取信号处理结果
        signal_processing = results['signal_processing']
        received_signals = signal_processing.get('received_signals', {})

        # 获取排序后的阵元ID列表
        element_ids = sorted(list(received_signals.keys()))

        # 确保索引有效
        if element_index >= len(element_ids):
            QMessageBox.warning(self, "错误", "无效的阵元索引")
            return

        # 获取实际的阵元ID
        element_id = element_ids[element_index]

        # 获取接收信号数据
        received_signal = received_signals[element_id]
        time_data = received_signal.get('time_data', [])

        if len(time_data) == 0:
            QMessageBox.warning(self, "错误", "接收信号数据为空")
            return

        # 这些变量在使用QDoubleSpinBox后不再需要
        # 因为我们直接从控件获取值，而不是根据信道数据计算推荐值

        # 设置默认的起始时间和结束时间
        default_start_time = time_data[0]
        default_end_time = time_data[-1]

        # 获取用户输入的起始时间和结束时间（从QDoubleSpinBox控件）
        start_time = self.start_time_input.value()
        end_time = self.end_time_input.value()

        # 检查时间范围是否有效
        if start_time >= end_time:
            QMessageBox.warning(self, "输入错误", "起始时间必须小于结束时间")
            return

        # 检查时间范围是否在信号范围内，如果超出范围则自动调整
        if start_time < default_start_time:
            print(f"警告: 起始时间 {start_time:.4f} 小于信号的最小时间 {default_start_time:.4f}，将自动调整为信号最小时间")
            start_time = default_start_time
            self.start_time_input.setValue(start_time)

        if end_time > default_end_time:
            print(f"警告: 结束时间 {end_time:.4f} 大于信号的最大时间 {default_end_time:.4f}，将自动调整为信号最大时间")
            end_time = default_end_time
            self.end_time_input.setValue(end_time)

        # 获取频率范围（如果启用）
        freq_range = None
        if self.freq_range_check.isChecked():
            freq_min = self.freq_min_spin.value()
            freq_max = self.freq_max_spin.value()
            if freq_min >= freq_max:
                QMessageBox.warning(self, "输入错误", "最小频率必须小于最大频率")
                return
            freq_range = (freq_min, freq_max)

        # 找到主窗口
        from src.ui.main.main_window import MainWindow
        main_window = self.find_parent(MainWindow)
        if not main_window:
            QMessageBox.warning(self, "错误", "无法找到主窗口")
            return

        # 直接调用控制器的方法，计算所有阵元的频谱
        if hasattr(main_window, 'simulation_controller') and hasattr(main_window.simulation_controller, 'integrated_controller'):
            controller = main_window.simulation_controller.integrated_controller

            # 显示状态信息
            main_window.statusBar().showMessage("正在计算所有阵元的频谱...")

            # 计算所有阵元的频谱
            all_spectrums = controller.calculate_all_spectrums(start_time, end_time, freq_range)

            if all_spectrums:
                # 更新数据管理器
                results = self.data_manager.get_results('integrated')
                if 'signal_processing' not in results:
                    results['signal_processing'] = {}
                if 'spectrum' not in results['signal_processing']:
                    results['signal_processing']['spectrum'] = {}

                # 保存所有阵元的频谱结果
                results['signal_processing']['spectrum'].update(all_spectrums)
                self.data_manager.set_results('integrated', results)

                # 通知视图更新
                self.data_manager.results_changed.emit('integrated')

                # 显示完成信息
                main_window.statusBar().showMessage(f"频谱计算完成，共计算了 {len(all_spectrums)} 个阵元的频谱")

            # 切换到频谱图标签页
            if hasattr(main_window, 'integrated_view'):
                main_window.integrated_view.tabs.setCurrentWidget(
                    main_window.integrated_view.spectrum_widget
                )

                # 获取当前选中的阵元ID
                params = self.data_manager.get_parameters('integrated')
                selected_spectrum_element_index = params.get('selected_spectrum_element_index', 0)

                # 如果已经计算过该阵元的频谱，直接更新频谱图
                if 'spectrum' in results['signal_processing'] and selected_spectrum_element_index in results['signal_processing']['spectrum']:
                    # 更新频谱图
                    spectrum_result = results['signal_processing']['spectrum'][selected_spectrum_element_index]
                    main_window.integrated_view.update_spectrum_plot(
                        freqs=spectrum_result['freqs'],
                        psd_db=spectrum_result['psd_db'],
                        element_index=selected_spectrum_element_index,
                        signal_type=spectrum_result['signal_type']
                    )

    def _request_duration_calculation(self):
        """
        请求计算建议时长

        使用计时器延迟执行，避免用户快速调整截断时间时频繁计算
        """
        # 标记有待处理的计算请求
        self.calculation_pending = True

        # 重置计时器，300毫秒后执行计算
        self.calculation_timer.start(300)

    def _start_duration_calculation(self):
        """
        启动建议时长计算线程
        """
        # 如果没有待处理的计算请求，直接返回
        if not self.calculation_pending:
            return

        # 重置待处理标记
        self.calculation_pending = False

        # 获取当前参数
        params = self.data_manager.get_parameters('integrated')
        if not params:
            return

        use_all_arrivals = params.get('use_all_arrivals', True)
        truncation_time = params.get('truncation_time', 0.0)

        # 如果已有计算线程在运行，停止它
        if self.duration_calculator is not None and self.duration_calculator.isRunning():
            self.duration_calculator.terminate()
            self.duration_calculator.wait()

        # 创建并启动新的计算线程
        self.duration_calculator = DurationCalculator(self.data_manager, use_all_arrivals, truncation_time)
        self.duration_calculator.calculation_done.connect(self._on_duration_calculation_done)
        self.duration_calculator.start()

    def _on_duration_calculation_done(self, min_duration, recommended_duration):
        """
        建议时长计算完成的回调函数

        Args:
            min_duration: 最小建议时长
            recommended_duration: 推荐建议时长
        """
        # 更新数据管理器
        self.data_manager.update_parameter('integrated', 'min_duration_suggestion', min_duration)
        self.data_manager.update_parameter('integrated', 'recommended_duration', recommended_duration)

        # 更新源信号时长建议显示
        self.update_duration_suggestion()

    def _recalculate_duration_suggestion(self):
        """
        重新计算建议时长

        根据当前的截断设置重新计算建议时长，并更新数据管理器
        使用预计算的阈值映射表提高性能
        """
        if not self.data_manager:
            return

        # 获取结果
        results = self.data_manager.get_results('integrated')
        if not results or 'channel_data' not in results:
            return

        # 获取信道数据
        channel_data = results['channel_data']
        t_first_global = channel_data.get('t_first_global', None)
        t_last_global = channel_data.get('t_last_global', None)

        if t_first_global is None or t_last_global is None:
            return

        # 获取当前截断设置
        params = self.data_manager.get_parameters('integrated')
        use_all_arrivals = params.get('use_all_arrivals', True)
        truncation_time = params.get('truncation_time', 0.0)

        # 计算建议时长
        if use_all_arrivals:
            # 使用全部到达声线，直接使用全局最晚到达时间
            max_relative_delay = t_last_global - t_first_global
            min_duration = np.ceil(max_relative_delay + 1.0)
            recommended_duration = np.ceil(max_relative_delay + 5.0)
        else:
            # 使用预计算的阈值映射表
            duration_to_truncation_threshold = channel_data.get('duration_to_truncation_threshold', {})

            if duration_to_truncation_threshold:
                # 找出小于等于截断时间的最大建议时长
                min_duration = 1  # 默认最小值

                for duration, threshold in sorted(duration_to_truncation_threshold.items()):
                    if truncation_time >= threshold:
                        min_duration = duration

                # 计算推荐时长（通常是最小时长+4）
                recommended_duration = min_duration + 4
            else:
                # 如果没有预计算的映射表，使用原来的方法
                print("警告: 未找到预计算的建议时长阈值映射表，使用传统计算方法")

                # 计算有效的全局最晚到达时间
                if truncation_time <= t_first_global:
                    t_last_global_eff = t_last_global
                else:
                    # 找出所有小于等于截断时间的到达时间中的最大值
                    arrivals_data = channel_data.get('arrivals_data', {})
                    max_time = t_first_global

                    for freq, elements in arrivals_data.items():
                        for element_id, df in elements.items():
                            if 'time_of_arrival' in df.columns:
                                valid_times = df[df['time_of_arrival'] <= truncation_time]['time_of_arrival'].values
                                if len(valid_times) > 0:
                                    max_time = max(max_time, np.max(valid_times))

                    t_last_global_eff = max_time

                # 计算最大相对时延
                max_relative_delay = t_last_global_eff - t_first_global

                # 计算建议的最小源信号时长（最大相对时延 + 1秒）
                min_duration = np.ceil(max_relative_delay + 1.0)

                # 计算推荐的源信号时长（最大相对时延 + 5秒）
                recommended_duration = np.ceil(max_relative_delay + 5.0)

        # 更新数据管理器
        self.data_manager.update_parameter('integrated', 'min_duration_suggestion', min_duration)
        self.data_manager.update_parameter('integrated', 'recommended_duration', recommended_duration)



    def find_parent(self, parent_class):
        """
        查找指定类型的父窗口

        Args:
            parent_class: 父窗口类型

        Returns:
            找到的父窗口实例，如果未找到则返回None
        """
        parent = self.parent()
        while parent:
            if isinstance(parent, parent_class):
                return parent
            parent = parent.parent()
        return None

    def update_source_signal_info(self):
        """
        更新源信号信息
        """
        # 获取船舶辐射噪声结果
        try:
            ship_noise_results = self.data_manager.get_results('ship_noise')
            if ship_noise_results and 'total_signal' in ship_noise_results:
                # 检查是否有元数据
                if 'metadata' in ship_noise_results:
                    metadata = ship_noise_results['metadata']
                    # 获取持续时间
                    duration = metadata.get('duration', 0.0)
                    # 获取生成时间
                    generation_time = metadata.get('generation_time', "")

                    # 更新源信号信息标签
                    if generation_time:
                        display_name = f"船舶辐射噪声 ({duration:.1f}s, {generation_time})"
                    else:
                        display_name = f"船舶辐射噪声 ({duration:.1f}s)"

                    self.source_signal_info_label.setText(display_name)

                    # 更新建议源信号时长标签
                    self.update_source_duration_label(duration)
                else:
                    # 如果没有元数据，使用默认名称
                    self.source_signal_info_label.setText("船舶辐射噪声")
            else:
                # 如果没有船舶辐射噪声结果，添加提示
                self.source_signal_info_label.setText("请先生成船舶辐射噪声")
                self.duration_suggestion_label.setText("建议源信号时长: 未知")
        except Exception as e:
            print(f"获取船舶辐射噪声结果失败: {e}")

    def update_directivity_time_range_defaults(self, element_id):
        """
        更新指向性计算的时间范围默认值

        Args:
            element_id: 阵元ID
        """
        # 获取结果
        results = self.data_manager.get_results('integrated')
        if not results or 'signal_processing' not in results:
            return

        # 获取信号处理结果
        signal_processing = results['signal_processing']
        received_signals = signal_processing.get('received_signals', {})

        # 检查阵元ID是否在数据中
        if element_id not in received_signals:
            return

        # 获取接收信号数据
        received_signal = received_signals[element_id]
        time_data = received_signal.get('time_data', [])

        if not len(time_data):
            return

        # 获取信道数据
        channel_data = results.get('channel_data', {})

        # 获取全局到达时间信息
        t_first_global = channel_data.get('t_first_global', None)

        # 获取源信号信息
        source_signal = signal_processing.get('source_signal', {})
        source_duration = source_signal.get('metadata', {}).get('duration', 0.0)

        # 获取最后一个有效的到达声线的到达时间
        params = self.data_manager.get_parameters('integrated')
        use_all_arrivals = params.get('use_all_arrivals', True)

        if use_all_arrivals:
            # 使用全部到达声线，最后到达时间为全局最后到达时间
            t_last_valid = channel_data.get('t_last_global', None)
        else:
            # 使用截断时间，最后到达时间为截断时间
            t_last_valid = params.get('truncation_time', None)

        # 如果没有有效的时间信息，使用信号的时间范围
        if t_first_global is None or t_last_valid is None:
            start_time = time_data[0]
        else:
            # 设置起始时间为最后一个有效的到达声线的到达时间
            start_time = t_last_valid

        # 设置持续时间为1秒或源信号时长（如果可用）
        if source_duration > 0:
            duration = min(source_duration, 1.0)
        else:
            duration = 1.0

        # 确保时间范围在信号范围内
        start_time = max(start_time, time_data[0])
        if start_time + duration > time_data[-1]:
            duration = time_data[-1] - start_time

        # 指向性计算使用与频谱分析相同的信号区间选择控件，不需要单独更新

    def update_source_duration_label(self, source_duration):
        """
        更新源信号时长与建议时长的比较信息

        Args:
            source_duration: 源信号时长
        """
        # 获取建议的最小时长
        params = self.data_manager.get_parameters('integrated')
        if not params:
            return

        min_duration = params.get('min_duration_suggestion', 0.0)
        recommended_duration = params.get('recommended_duration', 0.0)

        if min_duration > 0.0 and recommended_duration > 0.0:
            if source_duration < min_duration:
                self.duration_suggestion_label.setText(
                    f"<span style='color:red'>警告: 源信号时长 ({source_duration:.2f}s) 小于建议的最小时长 ({min_duration:.2f}s)</span>")
            else:
                self.duration_suggestion_label.setText(
                    f"源信号时长 ({source_duration:.2f}s) 满足建议的最小时长 ({min_duration:.2f}s)")
        else:
            self.duration_suggestion_label.setText("建议源信号时长: 未知")

    def update_ambient_noise_info(self):
        """
        更新海洋环境噪声信息
        """
        # 获取海洋环境噪声结果
        try:
            ambient_noise_results = self.data_manager.get_results('ambient_noise')
            if ambient_noise_results and 'ambient_signal' in ambient_noise_results:
                # 检查是否有元数据
                if 'metadata' in ambient_noise_results:
                    metadata = ambient_noise_results['metadata']
                    # 获取持续时间
                    duration = metadata.get('duration', 0.0)
                    # 获取生成时间
                    generation_time = metadata.get('generation_time', "")

                    # 更新海洋环境噪声信息标签
                    if generation_time:
                        display_name = f"海洋环境噪声 ({duration:.1f}s, {generation_time})"
                    else:
                        display_name = f"海洋环境噪声 ({duration:.1f}s)"

                    self.ambient_noise_info_label.setText(display_name)
                else:
                    # 如果没有元数据，使用默认名称
                    self.ambient_noise_info_label.setText("海洋环境噪声: 已生成")
            else:
                # 如果没有海洋环境噪声结果，添加提示
                self.ambient_noise_info_label.setText("海洋环境噪声: 未生成")
        except Exception as e:
            print(f"获取海洋环境噪声结果失败: {e}")
            self.ambient_noise_info_label.setText("海洋环境噪声: 未知")

    def on_results_changed(self, module):
        """
        结果变更事件处理

        Args:
            module: 变更的模块名称
        """
        # 如果是船舶辐射噪声模块的结果变更，更新源信号信息
        if module == 'ship_noise':
            self.update_source_signal_info()
            self.update_duration_suggestion()

        # 如果是海洋环境噪声模块的结果变更，更新海洋环境噪声信息
        elif module == 'ambient_noise':
            self.update_ambient_noise_info()

        # 如果是综合仿真模块的结果变更，更新信道数据信息和信号处理阵元下拉框
        elif module == 'integrated':
            self.update_channel_data_info()

            # 检查是否有信号处理结果
            results = self.data_manager.get_results('integrated')
            if results and 'signal_processing' in results:
                # 获取参数中选中的阵元
                params = self.data_manager.get_parameters('integrated')
                selected_channel_element_index = params.get('selected_channel_element_index', 0) if params else 0
                selected_signal_element_index = params.get('selected_signal_element_index', 0) if params else 0
                selected_spectrum_element_index = params.get('selected_spectrum_element_index', 0) if params else 0

                # 更新各个阵元下拉框
                self.update_element_combo(selected_channel_element_index)
                self.update_signal_element_combo(selected_signal_element_index)
                self.update_spectrum_element_combo(selected_spectrum_element_index)

                # 更新信号区间选择的默认值
                self.update_signal_time_range_defaults(selected_signal_element_index)

    def update_duration_suggestion(self):
        """
        更新源信号时长建议和到达时间信息
        """
        # 获取参数
        params = self.data_manager.get_parameters('integrated')
        if not params:
            return

        # 获取结果数据
        results = self.data_manager.get_results('integrated')
        if not results:
            return

        # 获取全局到达时间信息
        channel_data = results.get('channel_data', {})
        t_first_global = channel_data.get('t_first_global', None)
        t_last_global = channel_data.get('t_last_global', None)

        # 更新全局到达时间信息
        if t_first_global is not None and t_last_global is not None:
            self.arrival_times_info_label.setText(
                f"全局到达时间: 最早 {t_first_global:.4f} s, 最晚 {t_last_global:.4f} s")

            # 动态设置截断时间范围，确保最小值略大于t_first_global
            min_truncation_time = t_first_global + 0.0001  # 设置一个略大于t_first_global的值
            self.truncation_time_spin.setRange(min_truncation_time, t_last_global)

            # 如果当前值不在范围内，设置为最小值
            current_value = self.truncation_time_spin.value()
            if current_value < min_truncation_time or current_value > t_last_global:
                self.truncation_time_spin.setValue(min_truncation_time)
        else:
            self.arrival_times_info_label.setText("全局到达时间: 未知")

        # 获取最大相对时延信息
        # 首先尝试从信道数据中获取
        max_relative_delay = channel_data.get('max_relative_delay', None)

        # 如果信道数据中没有，尝试从信号处理结果中获取
        if max_relative_delay is None:
            signal_processing = results.get('signal_processing', {})
            max_relative_delay = signal_processing.get('max_relative_delay', None)

        # 如果仍然没有，但有全局最早和最晚到达时间，则计算
        if max_relative_delay is None and t_first_global is not None and t_last_global is not None:
            max_relative_delay = t_last_global - t_first_global

        # 更新最大相对时延信息
        if max_relative_delay is not None:
            self.max_delay_info_label.setText(f"最大相对时延: {max_relative_delay:.4f} s")
        else:
            self.max_delay_info_label.setText("最大相对时延: 未知")

        # 更新源信号时长建议
        min_duration = params.get('min_duration_suggestion', 0.0)
        recommended_duration = params.get('recommended_duration', 0.0)

        # 检查是否有船舶辐射噪声结果
        try:
            ship_noise_results = self.data_manager.get_results('ship_noise')
            if ship_noise_results and 'metadata' in ship_noise_results:
                # 获取源信号时长
                source_duration = ship_noise_results['metadata'].get('duration', 0.0)

                # 更新源信号时长与建议时长的比较信息
                self.update_source_duration_label(source_duration)
            else:
                # 如果没有源信号，只显示建议时长
                if min_duration > 0.0 and recommended_duration > 0.0:
                    self.duration_suggestion_label.setText(
                        f"建议源信号时长: 最小 {min_duration:.2f} s, 推荐 {recommended_duration:.2f} s")
                else:
                    self.duration_suggestion_label.setText("建议源信号时长: 未知")
        except Exception as e:
            print(f"获取船舶辐射噪声结果失败: {e}")
            # 如果出错，只显示建议时长
            if min_duration > 0.0 and recommended_duration > 0.0:
                self.duration_suggestion_label.setText(
                    f"建议源信号时长: 最小 {min_duration:.2f} s, 推荐 {recommended_duration:.2f} s")
            else:
                self.duration_suggestion_label.setText("建议源信号时长: 未知")

    def on_signal_element_selected(self, index):
        """
        信号处理阵元选择事件处理

        Args:
            index: 选中的索引
        """
        if index < 0 or not self.data_manager:
            return

        # 获取当前选中的阵元ID
        results = self.data_manager.get_results('integrated')
        if not results or 'signal_processing' not in results:
            return

        signal_processing = results['signal_processing']
        received_signals = signal_processing.get('received_signals', {})

        # 获取排序后的阵元ID列表
        element_ids = sorted(list(received_signals.keys()))

        # 确保索引有效
        if index >= len(element_ids):
            return

        # 获取实际的阵元ID
        element_id = element_ids[index]

        # 更新数据管理器，使用专门的参数
        self.data_manager.update_parameter('integrated', 'selected_signal_element_index', element_id)

        # 更新信号区间选择的默认值
        self.update_signal_time_range_defaults(element_id)

        # 切换到时域波形标签页
        from src.ui.main.main_window import MainWindow
        main_window = self.find_parent(MainWindow)
        if main_window and hasattr(main_window, 'integrated_view'):
            main_window.integrated_view.tabs.setCurrentWidget(
                main_window.integrated_view.time_domain_widget
            )

        # 通知数据管理器参数已更新，触发视图更新
        self.data_manager.parameters_changed.emit('integrated')

    def update_signal_time_range_defaults(self, element_id=None):
        """
        更新信号区间选择的默认值

        Args:
            element_id: 阵元ID，如果为None则使用当前选中的阵元
        """
        # 获取结果
        results = self.data_manager.get_results('integrated')
        if not results or 'signal_processing' not in results:
            return

        # 获取信号处理结果
        signal_processing = results['signal_processing']
        received_signals = signal_processing.get('received_signals', {})

        # 如果没有指定阵元ID，获取当前选中的阵元
        if element_id is None:
            # 获取当前选中的阵元索引
            index = self.signal_element_combo.currentIndex()
            if index < 0:
                return

            # 获取排序后的阵元ID列表
            element_ids = sorted(list(received_signals.keys()))

            # 确保索引有效
            if index >= len(element_ids):
                return

            # 获取实际的阵元ID
            element_id = element_ids[index]

        # 检查阵元ID是否在数据中
        if element_id not in received_signals:
            return

        # 同时更新指向性计算的时间范围默认值
        self.update_directivity_time_range_defaults(element_id)

        # 获取接收信号数据
        received_signal = received_signals[element_id]
        time_data = received_signal.get('time_data', [])

        if not len(time_data):
            return

        # 获取信道数据
        channel_data = results.get('channel_data', {})

        # 获取全局到达时间信息
        t_first_global = channel_data.get('t_first_global', None)

        # 获取源信号信息
        source_signal = signal_processing.get('source_signal', {})
        source_duration = source_signal.get('metadata', {}).get('duration', 0.0)

        # 获取最后一个有效的到达声线的到达时间
        # 如果使用截断时间，则为截断时间；否则为全局最后到达时间
        params = self.data_manager.get_parameters('integrated')
        use_all_arrivals = params.get('use_all_arrivals', True)

        if use_all_arrivals:
            # 使用全部到达声线，最后到达时间为全局最后到达时间
            t_last_valid = channel_data.get('t_last_global', None)
        else:
            # 使用截断时间，最后到达时间为截断时间
            t_last_valid = params.get('truncation_time', None)

        # 如果没有有效的时间信息，使用信号的时间范围
        if t_first_global is None or t_last_valid is None:
            start_time = time_data[0]
            end_time = time_data[-1]
        else:
            # 设置起始时间为最后一个有效的到达声线的到达时间
            start_time = t_last_valid

            # 设置结束时间为t_global_first + 源信号时长
            if t_first_global is not None and source_duration > 0:
                end_time = t_first_global + source_duration
            else:
                end_time = time_data[-1]

        # 确保时间范围有效
        if start_time >= end_time:
            # 如果起始时间大于等于结束时间，使用信号的时间范围
            start_time = time_data[0]
            end_time = time_data[-1]

        # 确保时间范围在信号范围内
        start_time = max(start_time, time_data[0])
        end_time = min(end_time, time_data[-1])

        # 更新起始时间和结束时间输入框的范围和值
        self.start_time_input.setRange(time_data[0], time_data[-1])
        self.end_time_input.setRange(time_data[0], time_data[-1])

        # 设置默认值
        self.start_time_input.setValue(start_time)
        self.end_time_input.setValue(end_time)

    def on_process_signal_clicked(self):
        """
        处理信号按钮点击事件处理
        """
        # 检查是否已加载信道数据
        channel_data_dir = self.channel_dir_edit.text()
        if not channel_data_dir or not os.path.exists(channel_data_dir):
            QMessageBox.warning(self, "数据错误", "请先加载信道数据")
            return

        # 检查是否有船舶辐射噪声结果
        try:
            ship_noise_results = self.data_manager.get_results('ship_noise')
            if not ship_noise_results or 'total_signal' not in ship_noise_results:
                QMessageBox.warning(self, "数据错误", "请先生成船舶辐射噪声")
                return
        except Exception as e:
            QMessageBox.warning(self, "数据错误", f"获取船舶辐射噪声结果失败: {e}")
            return

        # 检查源信号时长是否满足建议的最小时长
        try:
            # 获取船舶辐射噪声结果
            ship_noise_results = self.data_manager.get_results('ship_noise')
            if not ship_noise_results or 'metadata' not in ship_noise_results:
                QMessageBox.warning(self, "数据错误", "无法获取源信号元数据")
                return

            # 获取源信号时长
            source_duration = ship_noise_results['metadata'].get('duration', 0.0)

            # 获取建议的最小时长
            params = self.data_manager.get_parameters('integrated')
            if not params:
                QMessageBox.warning(self, "数据错误", "无法获取参数")
                return

            min_duration = params.get('min_duration_suggestion', 0.0)

            # 如果源信号时长小于建议的最小时长，显示警告并询问是否继续
            if min_duration > 0.0 and source_duration < min_duration:
                reply = QMessageBox.question(
                    self,
                    "源信号时长不足",
                    f"当前源信号时长 ({source_duration:.2f}s) 小于建议的最小时长 ({min_duration:.2f}s)。\n\n"
                    f"这可能导致接收信号的统计稳定性不足，建议重新生成更长的源信号。\n\n"
                    f"是否仍要继续处理？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.No:
                    return
        except Exception as e:
            print(f"检查源信号时长失败: {e}")

        # 获取参数
        use_all_arrivals = self.use_all_arrivals_radio.isChecked()
        truncation_time = self.truncation_time_spin.value() if not use_all_arrivals else 0.0
        add_ambient_noise = self.add_ambient_noise_check.isChecked()

        # 如果选择叠加海洋环境噪声，检查是否已生成海洋环境噪声
        if add_ambient_noise:
            try:
                ambient_noise_results = self.data_manager.get_results('ambient_noise')
                if not ambient_noise_results or 'ambient_signal' not in ambient_noise_results:
                    reply = QMessageBox.question(
                        self,
                        "海洋环境噪声未生成",
                        "您选择了叠加海洋环境噪声，但尚未生成海洋环境噪声。\n\n"
                        "是否继续处理？（将不会叠加海洋环境噪声）",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )

                    if reply == QMessageBox.No:
                        return

                    # 如果用户选择继续，则不叠加海洋环境噪声
                    add_ambient_noise = False
                    self.add_ambient_noise_check.setChecked(False)
            except Exception as e:
                print(f"检查海洋环境噪声结果失败: {e}")

        # 更新数据管理器
        self.data_manager.update_parameter('integrated', 'use_all_arrivals', use_all_arrivals)
        self.data_manager.update_parameter('integrated', 'truncation_time', truncation_time)
        self.data_manager.update_parameter('integrated', 'add_ambient_noise', add_ambient_noise)

        # 同时更新use_original_signal参数（与add_ambient_noise相反）
        self.data_manager.update_parameter('integrated', 'use_original_signal', not add_ambient_noise)
        print(f"处理信号 - 叠加海洋环境噪声: {add_ambient_noise}, 使用原始信号: {not add_ambient_noise}")

        # 设置计算类型并发送仿真请求信号
        computation_type = 'signal_processing'
        self.data_manager.update_parameter('integrated', 'computation_type', computation_type)
        self.simulation_requested.emit(computation_type)
