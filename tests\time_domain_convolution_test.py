#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
时域卷积测试脚本

使用给定的到达结构数据，构建冲击响应，然后与高斯白噪声卷积，
验证不同到达时间的声线是否能在接收信号中体现出来。
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import scipy.signal
import pandas as pd

# 设置随机种子，确保结果可重现
np.random.seed(42)

# matplotlib中文支持
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
matplotlib.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 采样率
FS = 22050  # Hz

# matplotlib中文支持
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
matplotlib.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def create_impulse_response(arrivals_data, fs):
    """
    根据到达结构数据创建冲击响应，使用相对时间轴

    Args:
        arrivals_data: 到达结构数据，包含复数幅度和到达时间
        fs: 采样率 (Hz)

    Returns:
        tuple: (冲击响应, 时间轴, 最早到达时间, 最晚到达时间)
    """
    # 找出最早和最晚的到达时间
    arrival_times = [data[1] for data in arrivals_data]
    t_first = min(arrival_times)
    t_last = max(arrival_times)

    # 计算相对时延
    relative_delays = [t - t_first for t in arrival_times]
    max_delay = max(relative_delays)

    # 创建足够长的冲击响应
    ir_length = int(np.ceil(max_delay * fs)) + 1
    ir = np.zeros(ir_length, dtype=np.complex128)

    # 对每个到达路径，在对应的相对时间点添加一个脉冲
    for i, relative_delay in enumerate(relative_delays):
        # 获取振幅
        amplitude = arrivals_data[i][0]
        # 计算对应的样本索引
        idx = int(relative_delay * fs)
        if 0 <= idx < len(ir):
            ir[idx] = amplitude

    # 创建相对时间轴
    t = np.arange(0, len(ir)/fs, 1/fs)

    return ir, t, t_first, t_last

def convolve_with_noise(ir, noise, fs, t_first):
    """
    将冲击响应与噪声信号卷积

    Args:
        ir: 冲击响应
        noise: 噪声信号
        fs: 采样率 (Hz)
        t_first: 最早到达时间，用于时间轴对齐

    Returns:
        tuple: (卷积结果, 时间轴)
    """
    # 执行卷积
    result = scipy.signal.fftconvolve(noise, ir, mode='full')

    # 创建时间轴，从t_first开始
    t = np.linspace(t_first, t_first + len(result)/fs, len(result))

    return result, t

def plot_results(ir, ir_t, noise, noise_t, received, received_t, arrivals_data, t_first, t_last):
    """
    绘制结果

    Args:
        ir: 冲击响应
        ir_t: 冲击响应时间轴
        noise: 噪声信号
        noise_t: 噪声时间轴
        received: 接收信号
        received_t: 接收信号时间轴
        arrivals_data: 到达结构数据
        t_first: 最早到达时间
        t_last: 最晚到达时间
    """
    plt.figure(figsize=(15, 12))

    # 绘制冲击响应
    plt.subplot(4, 1, 1)
    plt.plot(ir_t, np.abs(ir))
    plt.title('冲击响应幅度 (相对时间轴)')
    plt.xlabel('相对时间 (s)')
    plt.ylabel('幅度')
    plt.grid(True)

    # 标记相对到达时间
    arrival_times = [data[1] for data in arrivals_data]
    relative_delays = [t - t_first for t in arrival_times]
    arrival_amplitudes = [np.abs(data[0]) for data in arrivals_data]
    plt.stem(relative_delays, arrival_amplitudes, linefmt='r-', markerfmt='ro', basefmt='')

    # 绘制源信号
    plt.subplot(4, 1, 2)
    plt.plot(noise_t, noise)
    plt.title('源信号 (高斯白噪声)')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.grid(True)

    # 绘制接收信号
    plt.subplot(4, 1, 3)
    plt.plot(received_t, np.real(received))
    plt.title('接收信号 (实部)')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.grid(True)

    # 绘制接收信号包络
    plt.subplot(4, 1, 4)
    # 计算包络
    envelope = np.abs(signal.hilbert(np.real(received)))
    plt.plot(received_t, envelope)
    plt.title('接收信号包络')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.grid(True)

    # 调整布局
    plt.tight_layout()
    plt.savefig('time_domain_convolution_results.png', dpi=300)
    plt.show()

    # 绘制放大后的接收信号，分段显示
    plt.figure(figsize=(15, 15))

    # 定义要放大的时间段，基于实际到达时间
    time_ranges = [
        (t_first - 0.5, t_first + 1.5),  # 14秒附近
        (17.0 - 0.5, 17.0 + 1.5),        # 17秒附近
        (20.5 - 0.5, 20.5 + 1.5)         # 20秒附近
    ]

    for i, (t_start, t_end) in enumerate(time_ranges):
        # 找到时间范围对应的索引
        idx_start = np.where(received_t >= t_start)[0][0] if t_start <= received_t[-1] else 0
        idx_end = np.where(received_t >= t_end)[0][0] if t_end <= received_t[-1] else len(received_t)

        # 绘制实部
        plt.subplot(3, 2, 2*i+1)
        plt.plot(received_t[idx_start:idx_end], np.real(received[idx_start:idx_end]))
        plt.title(f'接收信号实部 ({t_start:.1f}-{t_end:.1f}s)')
        plt.xlabel('时间 (s)')
        plt.ylabel('幅度')
        plt.grid(True)

        # 绘制包络
        plt.subplot(3, 2, 2*i+2)
        plt.plot(received_t[idx_start:idx_end], envelope[idx_start:idx_end])
        plt.title(f'接收信号包络 ({t_start:.1f}-{t_end:.1f}s)')
        plt.xlabel('时间 (s)')
        plt.ylabel('幅度')
        plt.grid(True)

    # 调整布局
    plt.tight_layout()
    plt.savefig('time_domain_convolution_zoomed.png', dpi=300)
    plt.show()

    # 绘制对数尺度的接收信号包络
    plt.figure(figsize=(15, 10))

    # 全局视图
    plt.subplot(2, 1, 1)
    # 避免log(0)
    epsilon = 1e-10
    log_envelope = 20 * np.log10(envelope + epsilon)
    plt.plot(received_t, log_envelope)
    plt.title('接收信号包络 (dB尺度)')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度 (dB)')
    plt.grid(True)

    # 放大视图
    plt.subplot(2, 1, 2)
    # 设置y轴范围，只显示较高的dB值
    max_db = np.max(log_envelope)
    plt.plot(received_t, log_envelope)
    plt.ylim([max_db - 60, max_db + 5])  # 显示最大值以下60dB的范围
    plt.title('接收信号包络 (dB尺度，放大视图)')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度 (dB)')
    plt.grid(True)

    # 调整布局
    plt.tight_layout()
    plt.savefig('time_domain_convolution_log_scale.png', dpi=300)
    plt.show()

def main():
    """
    主函数
    """
    # 到达结构数据
    arrivals_data = [
        (complex(-4.432325619305166e-09, 2.9379206443763108e-09), 20.7623615),
        (complex(2.8766759172278563e-07, 5.03960878616461e-07), 20.763319),
        (complex(-4.721590941741726e-07, -4.660568361174445e-08), 20.7039261),
        (complex(-1.454368600997917e-06, -5.456174234665298e-07), 17.0197601),
        (complex(-1.5439565491719136e-06, -3.8093373679990854e-07), 17.0198345),
        (complex(2.34975775132506e-06, -1.3487050084114022e-07), 16.9720249),
        (complex(-9.74841001107253e-06, -3.9777018705420065e-06), 14.2817326),
        (complex(-1.1400914618561324e-05, -3.3931826892846545e-06), 14.2817945),
        (complex(-1.0385797299189416e-05, -3.4911535458438804e-06), 14.2557716),
        (complex(-1.1949324282384501e-05, -2.820902666565566e-06), 14.2558298),
        (complex(-3.4297573221246817e-06, 5.322697608169281e-06), 14.1706066),
        (complex(-1.452756390575749e-05, 1.4437543264234374e-05), 14.1704702),
        (complex(7.181350694451082e-06, -2.372166039597837e-05), 14.1947861),
        (complex(1.931519054533225e-06, -2.227271498516916e-06), 16.8145332),
        (complex(-1.5353865624304392e-06, -1.3769649962143823e-06), 16.8615227),
        (complex(-7.398746859003999e-07, -9.079313277058061e-07), 16.8614235),
        (complex(-2.7091012059888334e-08, 3.218975845952804e-07), 20.5069351),
        (complex(4.182350739445713e-07, 5.805932360773806e-08), 20.5659008),
        (complex(1.613734451743229e-07, 4.9888110031460715e-08), 20.5657978)
    ]

    # 创建1秒的高斯白噪声作为源信号
    duration = 1.0  # 秒
    num_samples = int(duration * FS)
    noise = np.random.normal(0, 1, num_samples)
    noise_t = np.arange(0, duration, 1/FS)

    # 创建冲击响应
    ir, ir_t, t_first, t_last = create_impulse_response(arrivals_data, FS)

    # 执行卷积
    received, received_t = convolve_with_noise(ir, noise, FS, t_first)

    # 绘制结果
    plot_results(ir, ir_t, noise, noise_t, received, received_t, arrivals_data, t_first, t_last)

    print("处理完成，结果已保存为图片文件。")
    print(f"最早到达时间: {t_first:.4f}s, 最晚到达时间: {t_last:.4f}s")
    print(f"最大相对时延: {t_last - t_first:.4f}s")

if __name__ == "__main__":
    main()
