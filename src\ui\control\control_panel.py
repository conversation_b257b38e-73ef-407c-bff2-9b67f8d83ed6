# -*- coding: utf-8 -*-
"""
控制面板基类

提供控制面板的基本功能和接口
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QTabWidget
from PyQt5.QtCore import pyqtSignal


class ControlPanel(QWidget):
    """
    控制面板基类
    
    提供控制面板的基本功能和接口，包括标签页切换等
    """
    
    # 自定义信号
    tab_changed = pyqtSignal(int)
    
    def __init__(self, parent=None):
        """
        初始化控制面板
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        
        # 创建标签页控件
        self.tabs = QTabWidget()
        
        # 设置布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.tabs)
        
        # 连接标签页切换信号
        self.tabs.currentChanged.connect(self.on_tab_changed)
    
    def on_tab_changed(self, index):
        """
        标签页切换事件处理
        
        Args:
            index: 标签页索引
        """
        # 发射自定义信号
        self.tab_changed.emit(index)
