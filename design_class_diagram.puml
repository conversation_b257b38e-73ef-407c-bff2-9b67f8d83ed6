@startuml
!theme plain
hide empty members
'skinparam linetype ortho

' ===== UI Layer =====
class MainWindow {
  main_menu
  view_panel
  control_panel
  --
  initializeUI()
  handleMenuActions()
  updateDisplay()
  connectSignals()
}

class SimulationPanel {
  input_controls
  result_display
  --
  setupControls()
  validateInput()
  displayResults()
  handleUserEvents()
  updateFromDataManager()
}

class ShipNoisePanel {
  ship_param_inputs
  spectrum_displays
  propeller_config_widget
  --
  setupShipParameterInputs()
  displaySpectrumResults()
}

class OceanNoisePanel {
  curve_editor
  wenz_display
  point_selection_widget
  --
  setupCurveEditor()
  handlePointSelection()
  displayWenzCurve()
}

class PropagationPanel {
  environment_inputs
  ray_display
  sound_speed_widget
  --
  setupEnvironmentInputs()
  displayRayPaths()
  showTransmissionLoss()
}

class IntegratedSimPanel {
  array_config
  beamforming_display
  element_selection_widget
  --
  setupArrayConfiguration()
  displayDirectivityPattern()
  showCrossSpectralDensity()
}

' ===== Controller Layer =====
class SimulationController {
  data_manager
  calculation_service
  is_running
  --
  validateParameters()
  executeSimulation()
  processResults()
  updateDataManager()
  handleSimulationProgress()
}

class ShipNoiseController {
  --
  calculateContinuousSpectrum()
  calculateLineSpectrum()
  calculateModulationSpectrum()
}

class OceanNoiseController {
  --
  processUserCurvePoints()
  designFIRFilter()
  generateFilteredNoise()
}

class PropagationController {
  --
  computeEnvironmentModel()
  calculateRayPaths()
  computeTransmissionLoss()
  prepareChannelData()
}

class IntegratedController {
  --
  processArraySignals()
  calculateDirectivity()
  computeCrossSpectralDensity()
  performBeamforming()
}

class ProjectController {
  current_project_path
  modification_state
  --
  createNewProject()
  saveProject()
  loadProject()
  trackModifications()
}

class ExportController {
  export_formats
  data_manager
  --
  validateExportParams()
  exportWAVFiles()
  exportNPZFiles()
  exportNPZFiles()
  generateMetadata()
}

' ===== Data Management Layer =====
class DataManager {
  global_parameters
  module_parameters
  simulation_results
  simulation_states
  --
  setParameters(module, params)
  getParameters(module)
  setResults(module, results)
  getResults(module)
  setGlobalParam(key, value)
  getGlobalParam(key)
  notifyParametersChanged(module)
  notifyResultsChanged(module)
}

' ===== Data Transfer Object Layer =====
class SimulationData {
  module_name
  timestamp
  parameters
  results
  --
  getParameters()
  setParameters()
  getResults()
  setResults()
}

class ChannelData {
  array_geometry
  frequency_list
  arrival_structure
  impulse_responses
  --
  getChannelResponse(frequency)
  interpolateResponse(new_frequency)
}

' ===== Calculation Service Layer =====
class CalculationService {
  --
  executeCalculation(parameters)
  validateInputs(parameters)
  processResults(raw_results)
}

class ShipNoiseCalculationService {
  --
  generateSignal(ship_params, continuous_params, line_params, modulation_params)
  calculateSpectrum(signal_data)
}

class OceanNoiseCalculationService {
  --
  generateNoise(curve_points, filter_params)
  designFIRFilter(frequency_response)
}

class PropagationCalculationService {
  --
  computeEnvironment(sound_speed_profile, bathymetry_data)
  generateChannelData(array_geometry, source_position)
  calculateTransmissionLoss(frequency, range_data)
}

class IntegratedCalculationService {
  --
  simulateArraySignals(source_signal, channel_data, ambient_noise)
  calculateDirectivity(array_signals, scan_angles)
  computeCrossSpectralDensity(element_pair, signal_data)
}

' ===== Inheritance Relationships =====
SimulationPanel <|-- ShipNoisePanel
SimulationPanel <|-- OceanNoisePanel
SimulationPanel <|-- PropagationPanel
SimulationPanel <|-- IntegratedSimPanel

SimulationController <|-- ShipNoiseController
SimulationController <|-- OceanNoiseController
SimulationController <|-- PropagationController
SimulationController <|-- IntegratedController

CalculationService <|-- ShipNoiseCalculationService
CalculationService <|-- OceanNoiseCalculationService
CalculationService <|-- PropagationCalculationService
CalculationService <|-- IntegratedCalculationService
SimulationData <|-- ChannelData

' ===== Core Architecture Relationships =====
MainWindow *-- SimulationPanel
MainWindow *-- DataManager
MainWindow --> ProjectController : project operations
MainWindow --> ExportController : export operations

SimulationPanel --> SimulationController : delegates to
SimulationController --> DataManager : gets/stores data
SimulationController --> CalculationService : calls methods

ProjectController --> DataManager : accesses
ExportController --> DataManager : accesses

' ===== Data Flow Relationships =====
DataManager --> SimulationPanel : notifies changes
SimulationController --> SimulationData : creates/uses

@enduml
