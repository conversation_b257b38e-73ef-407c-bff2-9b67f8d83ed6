# 综合仿真模块信号处理技术文档

## 1. 概述

本文档详细介绍了综合仿真模块中的信号处理方法，包括多阵元接收信号的模拟实现、频域处理技术、以及性能优化策略。

## 2. 多阵元接收信号模拟原理

### 2.1 基本原理

在水声信道中，声波从声源传播到接收阵元会经历多条路径，每条路径有不同的时延和衰减。我们的模拟基于以下物理模型：

1. **多径传播**：声波通过多条路径到达接收阵元，每条路径有特定的时延和复振幅
2. **频率依赖性**：声波在不同频率下的传播特性不同
3. **阵列接收**：多个阵元在不同位置接收信号，形成空间采样

### 2.2 数学模型

对于源信号 $s(t)$ 和信道冲击响应 $h(t)$，接收信号 $r(t)$ 可表示为：

$$r(t) = s(t) * h(t) = \int_{-\infty}^{\infty} s(\tau) h(t-\tau) d\tau$$

其中 $h(t)$ 可以表示为多条路径的叠加：

$$h(t) = \sum_{m=1}^{M} A_m \delta(t - t_m)$$

$A_m$ 是第 $m$ 条路径的复振幅，$t_m$ 是其时延。

在频域中，这等价于：

$$R(f) = S(f) \cdot H(f)$$

其中传递函数 $H(f)$ 为：

$$H(f) = \sum_{m=1}^{M} A_m e^{-j2\pi f t_m}$$

## 3. 频域处理方法

### 3.1 传统方法与频域方法对比

#### 传统方法（时域卷积）
1. 对源信号进行子带滤波，得到各子带信号
2. 对每个子带信号与对应的冲击响应进行时域卷积
3. 将各子带卷积结果叠加得到最终接收信号

#### 频域方法
1. 计算源信号的FFT，得到 $S(f)$
2. 构建传递函数 $H(f)$
3. 频域相乘：$R(f) = S(f) \cdot H(f)$
4. 对结果进行IFFT，得到时域接收信号 $r(t)$

### 3.2 子带近似

由于信道特性随频率变化，我们采用子带近似方法：

1. 将整个频率范围划分为多个子带
2. 对每个子带，使用其中心频率的信道特性代表整个子带
3. 构建分段传递函数 $H(f)$

### 3.3 优缺点分析

**频域处理的优点**：
- 计算效率高，尤其对于长信号和复杂信道
- 易于实现并行计算
- 子带处理更灵活，可以针对不同频率使用不同的信道模型

**频域处理的缺点**：
- 子带近似可能引入误差
- 需要更多的内存来存储FFT结果
- 实现复杂度较高

## 4. 实现细节

### 4.1 传递函数构建

传递函数构建是整个处理流程的核心，我们的实现如下：

```python
# 为每个频率创建相位项矩阵
phase_matrix = np.exp(-1j * 2 * np.pi * np.outer(subband_freqs, t_prime_m))

# 对每个频率，计算与振幅的乘积并求和
H_subband = np.matmul(phase_matrix, A_m)
```

其中：
- `subband_freqs` 是子带内的频率点
- `t_prime_m` 是相对时延（到达时间减去全局最早到达时间）
- `A_m` 是各路径的复振幅

### 4.2 频域卷积

频域卷积的实现如下：

```python
# 对源信号进行补零
s_padded = np.zeros(self._N_FFT)
s_padded[:len(self.source_signal)] = self.source_signal

# 计算源信号的FFT
S_fft = scipy.fft.fft(s_padded)

# 频域乘法
R_fft = S_fft * H_fft

# 逆FFT
r_padded = scipy.fft.ifft(R_fft).real

# 取有效部分
r_prime = r_padded[:self._N_conv_len]
```

### 4.3 时间轴对齐

为了正确表示接收信号的物理时间，我们进行了时间轴对齐：

```python
# 创建物理时间轴，从t_first_global开始
duration = len(r_prime) / self.fs
time_data = np.linspace(self.t_first_global, self.t_first_global + duration, len(r_prime))
```

## 5. 性能优化

### 5.1 向量化计算

我们使用NumPy的向量化操作代替循环，大幅提高计算效率：

```python
# 向量化计算所有频率点的H值
phase_matrix = np.exp(-1j * 2 * np.pi * np.outer(subband_freqs, t_prime_m))
H_subband = np.matmul(phase_matrix, A_m)
```

### 5.2 并行计算

我们采用两级并行策略：

1. **阵元级并行**：使用线程池并行处理不同阵元
   ```python
   with concurrent.futures.ThreadPoolExecutor() as executor:
       results = list(executor.map(process_func, element_ids))
   ```

2. **子带内向量化**：对每个子带内的频率点使用向量化计算

### 5.3 批处理优化

我们按子带组织计算，而不是按单个频率点：

```python
# 对每个子带分别处理其覆盖的频率点
for (f_start, f_end), (t_prime_m, A_m, _) in subband_to_arrivals.items():
    # 找出该子带覆盖的所有频率点
    subband_mask = ...
    subband_freqs = freqs[positive_indices[subband_mask]]

    # 批量处理该子带内的所有频率点
    ...
```

### 5.4 内存优化

为了减少内存使用，我们采用了以下策略：

1. **只存储必要的中间结果**：计算完成后立即释放不再需要的大型数组
2. **使用适当的数据类型**：当精度允许时，使用 `np.complex64` 而非 `np.complex128`，可减少一半的内存使用
3. **重用计算缓存**：对于多次使用的结果进行缓存，避免重复计算
4. **分块处理大型数据**：当处理大量频率点时（如单频率情况下的全频带），采用分块处理策略：
   ```python
   # 分块处理大量频率点
   chunk_size = 10000  # 每块的频率点数
   num_chunks = (len(all_freqs) + chunk_size - 1) // chunk_size

   for chunk_idx in range(num_chunks):
       start_idx = chunk_idx * chunk_size
       end_idx = min((chunk_idx + 1) * chunk_size, len(all_freqs))
       chunk_freqs = all_freqs[start_idx:end_idx]

       # 对当前块应用向量化计算
       # ...
   ```
5. **避免不必要的数组复制**：使用视图（view）而不是复制，例如使用切片而不是创建新数组
6. **优化FFT长度**：使用 `scipy.fft.next_fast_len()` 选择计算效率高的FFT长度，减少内存和计算开销

## 6. 结论

我们的综合仿真模块采用了频域处理方法模拟多阵元接收信号，通过子带近似处理频率依赖性，并使用向量化计算和并行处理提高性能。这种方法相比传统的时域卷积有显著的性能优势，特别是对于长信号和复杂信道。

通过优化计算策略，我们成功地将计算时间从原来的数分钟减少到几十秒，同时保持了模拟的准确性。这使得我们能够更高效地进行水声信道仿真，为后续的信号处理和分析提供了坚实的基础。
