# -*- coding: utf-8 -*-
"""
船舶辐射噪声参数计算模块

提供根据船舶工况参数计算连续谱参数的功能。
"""

import math


def calculate_spectrum_params(D, J_P, omega, sigma_i_input, H, v_op_kn, N_op_rpm):
    """
    根据船舶工况参数计算连续谱参数

    Args:
        D (float): 螺旋桨直径 (m)
        J_P (float): 螺旋桨进速系数 (-)
        omega (float): 伴流分数 (-)
        sigma_i_input (float): 用户输入的空泡评价数 (-)
        H (float): 航行深度 (m)
        v_op_kn (float): 航速 (knots)
        N_op_rpm (float): 螺旋桨转速 (r/min)

    Returns:
        dict: 连续谱参数字典，包含以下键值:
            - f_0: 峰值频率 (Hz)
            - SL_0: 峰值频率处的声源级 (dB re 1μPa @ 1m)
            - q_rise: 上升段斜率系数 (-)
            - q_fall1: 第一段下降斜率系数 (-)
            - q_fall2: 第二段下降斜率系数 (-)
            - cavitation_state: 空化状态 ("Non-cavitating", "Developing", "Saturated")
            - SL_1kHz: 1kHz处的声源级 (dB re 1μPa @ 1m)
    """
    # 常量定义
    A_const = 2300.0
    PI = math.pi
    KNOTS_TO_MPS = 0.514444

    # 参数有效性检查
    if D <= 0 or J_P <= 0 or H <= 0 or v_op_kn <= 0 or N_op_rpm <= 0:
        raise ValueError("所有输入参数必须为正数")

    # 计算螺旋桨叶尖线速度
    U_tip = PI * D * (N_op_rpm / 60.0)

    # 步骤1: 计算初始关键速度和频率
    # 峰值频率 f_0
    f_0 = (A_const/D) * ((1.98 / v_op_kn**2) * (10+H) * J_P**2)**(1.5)

    # 空泡初生航速 v_SI_kn
    v_SI_kn = 2.7 * math.sqrt((10+H)/((1+(J_P/PI)**2)*sigma_i_input)) * (J_P / (1-omega))

    # 空泡饱和航速 v_SY_kn
    v_SY_kn = 2.82 * J_P * math.sqrt((10+H) / (1 + (J_P/PI)**2))

    # 步骤2: 判断空化状态
    if v_op_kn < v_SI_kn:
        cavitation_state = "Non-cavitating"  # 无空泡
    elif v_SI_kn <= v_op_kn < v_SY_kn:
        cavitation_state = "Developing"  # 空泡演变区
    else:  # v_op_kn >= v_SY_kn
        cavitation_state = "Saturated"  # 空泡饱和

    # 步骤3: 根据空化状态确定斜率和声源级参数
    if cavitation_state == "Non-cavitating":
        # 非空化状态
        q_nc_slope_coeff = 10.0 / (10.0 * math.log10(2.0))  # 约 3.3219
        q_rise = q_nc_slope_coeff
        q_fall1 = q_nc_slope_coeff
        q_fall2 = q_nc_slope_coeff

        SL_1_common = 72.7 + 1.55*D - 0.1*D**2
        SL_5kHz = SL_1_common + 40*math.log10(U_tip/10.0)
        SL_1kHz = SL_5kHz + 10 * q_fall2 * math.log10(5000.0/1000.0)
        SL_0 = SL_1kHz + 10 * q_fall1 * math.log10(1000.0/f_0)

    elif cavitation_state == "Developing":
        # 发展空化状态
        q_fall2 = 0.7
        v_SI_mps = v_SI_kn * KNOTS_TO_MPS
        V_a_at_SI_mps = v_SI_mps * (1-omega)
        N_si_rpm = (V_a_at_SI_mps * 60.0) / (J_P * D)
        N_ratio = N_op_rpm / N_si_rpm

        # 可选约束，根据delta_SL公式适用范围
        # N_ratio = max(1.2, min(N_ratio, 2.0))

        delta_SL = 13.75 * N_ratio - 16.5
        SL_1_common = 72.7 + 1.55*D - 0.1*D**2
        SL_5kHz = SL_1_common + 40*math.log10(U_tip/10.0) + 25.0 + delta_SL
        q_lt_1kHz = 1.2 * N_ratio - 0.4
        q_rise = q_lt_1kHz
        q_fall1 = q_lt_1kHz
        SL_1kHz = SL_5kHz + 10 * q_fall2 * math.log10(5000.0/1000.0)
        SL_0 = SL_1kHz + 10 * q_fall1 * math.log10(1000.0/f_0)

    else:  # cavitation_state == "Saturated"
        # 饱和空化状态
        q_fall2 = 6.0 / (10.0 * math.log10(2.0))  # 约 1.9931
        v_SI_mps = v_SI_kn * KNOTS_TO_MPS
        V_a_at_SI_mps = v_SI_mps * (1-omega)
        N_si_rpm = (V_a_at_SI_mps * 60.0) / (J_P * D)
        N_ratio_sat_raw = N_op_rpm / N_si_rpm
        N_ratio_sat_eff = min(N_ratio_sat_raw, 2.0)  # 限制delta_SL计算中的N_ratio不超过2.0

        delta_SL_sat = 13.75 * N_ratio_sat_eff - 16.5
        SL_1_common = 72.7 + 1.55*D - 0.1*D**2
        SL_5kHz = SL_1_common + 40*math.log10(U_tip/10.0) + 25.0 + delta_SL_sat
        q_lt_1kHz_sat = 1.2 * N_ratio_sat_eff - 0.4
        q_rise = q_lt_1kHz_sat
        q_fall1 = q_lt_1kHz_sat
        SL_1kHz = SL_5kHz + 10 * q_fall2 * math.log10(5000.0/1000.0)
        SL_0 = SL_1kHz + 10 * q_fall1 * math.log10(1000.0/f_0)

    # 将q值转换为dB/Octave
    # 在计算中q是衰减因子，用于10*q*log10(f/f0)公式
    # 而dB/Octave是10*log10(2)*q，因为一个倍频程是频率翻倍
    q_rise_dB_oct = 10 * math.log10(2) * q_rise
    q_fall1_dB_oct = -10 * math.log10(2) * q_fall1  # 注意下降斜率为负
    q_fall2_dB_oct = -10 * math.log10(2) * q_fall2  # 注意下降斜率为负

    # 步骤4: 组装并返回结果字典
    return {
        'f_0': f_0,
        'SL_0': SL_0,
        'q_rise': q_rise,
        'q_fall1': q_fall1,
        'q_fall2': q_fall2,
        'q_rise_dB_oct': q_rise_dB_oct,
        'q_fall1_dB_oct': q_fall1_dB_oct,
        'q_fall2_dB_oct': q_fall2_dB_oct,
        'cavitation_state': cavitation_state,
        'SL_1kHz': SL_1kHz,
        'v_SI_kn': v_SI_kn,
        'v_SY_kn': v_SY_kn
    }


def convert_spectrum_params_to_ui_params(spectrum_params):
    """
    将计算得到的连续谱参数转换为UI参数格式

    Args:
        spectrum_params (dict): 计算得到的连续谱参数字典

    Returns:
        dict: UI参数字典，包含以下键值:
            - f0: 峰值频率 (Hz)
            - sl0: 峰值谱级 (dB)
            - a1: 峰值频率前的上升斜率 (dB/Oct)
            - a2: 峰值频率后的下降斜率 (dB/Oct)
    """
    return {
        'f0': spectrum_params['f_0'],
        'sl0': spectrum_params['SL_0'],
        'a1': spectrum_params['q_rise_dB_oct'],
        'a2': spectrum_params['q_fall2_dB_oct']  # 注意这里使用q_fall2对应的斜率
    }
