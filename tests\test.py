import numpy as np
import scipy
import scipy.fft
import scipy.signal


# N_h = int(np.ceil(7 * 22050)) + 1
# print(N_h)

# L = 8192
# N_FFT = scipy.fft.next_fast_len(286649, True)
# print(N_FFT)

# print(scipy.fft.fftfreq(scipy.fft.next_fast_len(100), d = 1))

# scipy.signal.fftconvolve()

# print(np.dot([0,1],[0.866,0.5]))

# signal_j = [1,2,3,4,5]
# compensated_delay_samples = -2
# signal_length = 5

# print(np.pad(signal_j,  (0, -compensated_delay_samples), 'constant')[-signal_length:])


A = 2300
D = 1.6
v = 7.5
H = 30
J_p = 0.47
sigma_i = 3.6
omega = 0.21


f_0 = (A/D) * ((1.98/v**2) * (10+H) * J_p**2)**(3/2)
v_SI = 2.7 * np.sqrt((10+H)/((1+(J_p/np.pi)**2)*sigma_i)) * (J_p/ (1-omega))

print(f_0)
print(v_SI)