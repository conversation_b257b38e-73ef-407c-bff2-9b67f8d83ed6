#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
处理Bellhop计算得到的arrivals数据，计算信道传递函数。
"""

import os
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from pathlib import Path
import glob

# 设置中文字体
def set_chinese_font():
    """
    设置matplotlib使用中文字体
    """
    try:
        # 查找系统中的中文字体
        chinese_fonts = [f.name for f in fm.fontManager.ttflist
                        if 'SimHei' in f.name or 'Microsoft YaHei' in f.name
                        or 'SimSun' in f.name or 'FangSong' in f.name]

        if chinese_fonts:
            # 使用找到的第一个中文字体
            plt.rcParams['font.sans-serif'] = [chinese_fonts[0]] + plt.rcParams['font.sans-serif']
            print(f"使用中文字体: {chinese_fonts[0]}")
        else:
            # 如果没有找到中文字体，尝试使用一些常见的中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'FangSong'] + plt.rcParams['font.sans-serif']
            print("尝试使用默认中文字体")

        # 解决负号显示问题
        plt.rcParams['axes.unicode_minus'] = False
    except Exception as e:
        print(f"设置中文字体时出错: {e}")
        print("将使用默认字体，中文可能无法正确显示")

def load_arrivals_data(input_dir, pattern="bellhop_arrivals_*.csv"):
    """
    加载arrivals数据
    
    参数:
        input_dir: 输入目录
        pattern: 文件名匹配模式
    
    返回:
        字典，键为频率，值为arrivals DataFrame
    """
    results = {}
    
    # 获取所有匹配的文件
    input_path = Path(input_dir)
    files = list(input_path.glob(pattern))
    
    if not files:
        print(f"在目录 {input_dir} 中未找到匹配 {pattern} 的文件")
        return results
    
    # 加载每个文件
    for file in files:
        # 从文件名中提取频率
        try:
            # 假设文件名格式为 bellhop_arrivals_XXXHz.csv
            freq_str = file.stem.split('_')[-1].replace('Hz', '')
            freq = float(freq_str)
            
            # 加载数据
            arrivals = pd.read_csv(file)
            
            # 确保arrival_amplitude是复数
            if 'arrival_amplitude' in arrivals.columns:
                # 如果已经是字符串形式的复数，需要转换
                if arrivals['arrival_amplitude'].dtype == object:
                    arrivals['arrival_amplitude'] = arrivals['arrival_amplitude'].apply(lambda x: complex(x.replace('j', 'j')))
            
            results[freq] = arrivals
            print(f"已加载频率 {freq} Hz 的数据，共 {len(arrivals)} 条记录")
        except Exception as e:
            print(f"处理文件 {file} 时出错: {str(e)}")
    
    return results

def process_channel_transfer_function(results):
    """
    处理arrivals信息，计算信道传递函数及其幅度和相位
    
    参数:
        results: 计算结果字典，键为频率，值为arrivals DataFrame
    
    返回:
        字典，包含以下键值对：
        - 'frequencies': 频率数组
        - 'H_all': 所有到达声线的信道传递函数
        - 'H_filtered': 只考虑海底和海面反弹次数都小于等于1的到达声线的信道传递函数
        - 'H_all_magnitude': H_all的幅度
        - 'H_all_phase': H_all的相位（弧度）
        - 'H_filtered_magnitude': H_filtered的幅度
        - 'H_filtered_phase': H_filtered的相位（弧度）
    """
    if not results:
        return None
    
    # 提取频率并排序
    frequencies = np.array(sorted(results.keys()))
    
    # 初始化信道传递函数数组
    H_all = np.zeros(len(frequencies), dtype=complex)
    H_filtered = np.zeros(len(frequencies), dtype=complex)
    
    # 计算每个频率的信道传递函数
    for i, freq in enumerate(frequencies):
        arrivals = results[freq]
        
        # 1. 所有到达声线的信道传递函数
        H_all[i] = np.sum(arrivals['arrival_amplitude'])
        
        # 2. 只考虑海底和海面反弹次数都小于等于1的到达声线的信道传递函数
        filtered_arrivals = arrivals[(arrivals['surface_bounces'] <= 1) & (arrivals['bottom_bounces'] <= 1)]
        H_filtered[i] = np.sum(filtered_arrivals['arrival_amplitude'])
    
    # 3. 计算幅度和相位
    H_all_magnitude = np.abs(H_all)
    H_all_phase = np.angle(H_all)
    H_filtered_magnitude = np.abs(H_filtered)
    H_filtered_phase = np.angle(H_filtered)
    
    return {
        'frequencies': frequencies,
        'H_all': H_all,
        'H_filtered': H_filtered,
        'H_all_magnitude': H_all_magnitude,
        'H_all_phase': H_all_phase,
        'H_filtered_magnitude': H_filtered_magnitude,
        'H_filtered_phase': H_filtered_phase
    }

def plot_channel_transfer_function(ctf_results, output_dir="results", base_filename="channel_transfer_function"):
    """
    绘制信道传递函数的幅度和相位
    
    参数:
        ctf_results: 信道传递函数结果字典
        output_dir: 输出目录
        base_filename: 基础文件名
    """
    if not ctf_results:
        return
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    frequencies = ctf_results['frequencies']
    
    # 绘制幅度
    plt.figure(figsize=(10, 6))
    plt.subplot(2, 1, 1)
    plt.plot(frequencies, ctf_results['H_all_magnitude'], 'b-', label='所有到达声线')
    plt.plot(frequencies, ctf_results['H_filtered_magnitude'], 'r--', label='反弹次数≤1的声线')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('幅度')
    plt.title('信道传递函数幅度')
    plt.grid(True)
    plt.legend()
    
    # 绘制相位
    plt.subplot(2, 1, 2)
    plt.plot(frequencies, ctf_results['H_all_phase'], 'b-', label='所有到达声线')
    plt.plot(frequencies, ctf_results['H_filtered_phase'], 'r--', label='反弹次数≤1的声线')
    plt.xlabel('频率 (Hz)')
    plt.ylabel('相位 (弧度)')
    plt.title('信道传递函数相位')
    plt.grid(True)
    plt.legend()
    
    plt.tight_layout()
    
    # 保存图表
    plot_filepath = output_path / f"{base_filename}.png"
    plt.savefig(plot_filepath, dpi=300)
    plt.close()
    print(f"已保存信道传递函数图表到 {plot_filepath}")

def save_channel_transfer_function(ctf_results, output_dir="results", base_filename="channel_transfer_function"):
    """
    保存信道传递函数结果
    
    参数:
        ctf_results: 信道传递函数结果字典
        output_dir: 输出目录
        base_filename: 基础文件名
    """
    if not ctf_results:
        return
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 保存信道传递函数结果
    ctf_data = pd.DataFrame({
        'frequency': ctf_results['frequencies'],
        'H_all_real': np.real(ctf_results['H_all']),
        'H_all_imag': np.imag(ctf_results['H_all']),
        'H_filtered_real': np.real(ctf_results['H_filtered']),
        'H_filtered_imag': np.imag(ctf_results['H_filtered']),
        'H_all_magnitude': ctf_results['H_all_magnitude'],
        'H_all_phase': ctf_results['H_all_phase'],
        'H_filtered_magnitude': ctf_results['H_filtered_magnitude'],
        'H_filtered_phase': ctf_results['H_filtered_phase']
    })
    ctf_filepath = output_path / f"{base_filename}.csv"
    ctf_data.to_csv(ctf_filepath, index=False)
    print(f"已保存信道传递函数结果到 {ctf_filepath}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='处理Bellhop计算得到的arrivals数据，计算信道传递函数')
    parser.add_argument('--input_dir', type=str, default='results', help='输入目录')
    parser.add_argument('--output_dir', type=str, default='results', help='输出目录')
    parser.add_argument('--pattern', type=str, default='bellhop_arrivals_*.csv', help='文件名匹配模式')
    parser.add_argument('--base_filename', type=str, default='channel_transfer_function', help='输出文件基础名')
    parser.add_argument('--no_plot', action='store_true', help='不绘制图表')
    args = parser.parse_args()
    
    set_chinese_font()

    # 加载arrivals数据
    results = load_arrivals_data(args.input_dir, args.pattern)
    
    if not results:
        print("没有找到有效的arrivals数据")
        return
    
    # 处理信道传递函数
    ctf_results = process_channel_transfer_function(results)
    
    if not ctf_results:
        print("处理信道传递函数失败")
        return
    
    # 保存信道传递函数结果
    save_channel_transfer_function(ctf_results, args.output_dir, args.base_filename)
    
    # 绘制信道传递函数图表
    if not args.no_plot:
        plot_channel_transfer_function(ctf_results, args.output_dir, args.base_filename)

if __name__ == "__main__":
    main()
