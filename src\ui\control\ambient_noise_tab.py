# -*- coding: utf-8 -*-
"""
海洋环境噪声标签页

用于设置海洋环境噪声的参数和控制仿真
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLabel, QDoubleSpinBox, QPushButton, QGroupBox,
                            QRadioButton, QButtonGroup, QSpacerItem, QSizePolicy,
                            QFileDialog, QMessageBox, QFrame)
from PyQt5.QtCore import pyqtSignal
import numpy as np

from src.ui.control.point_data_dialog import PointDataDialog


class AmbientNoiseTab(QWidget):
    """
    海洋环境噪声标签页

    用于设置海洋环境噪声的参数和控制仿真
    """

    # 自定义信号
    simulation_requested = pyqtSignal()  # 仿真请求信号

    def __init__(self, main_window=None, data_manager=None, parent=None):
        """
        初始化海洋环境噪声标签页

        Args:
            main_window: 主窗口引用
            data_manager: 数据管理器实例
            parent: 父窗口
        """
        super().__init__(parent)

        # 保存主窗口引用和数据管理器引用
        self.main_window = main_window
        self.data_manager = data_manager

        # 初始化属性
        self.point_data_dialog = None

        # 创建UI
        self.init_ui()

    def init_ui(self):
        """
        初始化UI
        """
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建模式选择组
        mode_group = QGroupBox("操作模式")
        mode_layout = QVBoxLayout()  # 改为垂直布局以容纳说明标签

        # 创建单选按钮水平布局
        radio_layout = QHBoxLayout()

        # 创建模式按钮组
        self.mode_button_group = QButtonGroup(self)

        # 添加模式
        self.add_mode_button = QRadioButton("添加点")
        self.add_mode_button.setChecked(True)  # 默认选中
        self.add_mode_button.setToolTip("点击画布添加新点")
        self.mode_button_group.addButton(self.add_mode_button, 0)
        radio_layout.addWidget(self.add_mode_button)

        # 调整模式
        self.adjust_mode_button = QRadioButton("调整点")
        self.adjust_mode_button.setToolTip("选中点后使用方向键微调位置\n按住Shift+方向键可以进行更大幅度调整")
        self.mode_button_group.addButton(self.adjust_mode_button, 1)
        radio_layout.addWidget(self.adjust_mode_button)

        # 删除模式
        self.delete_mode_button = QRadioButton("删除点")
        self.delete_mode_button.setToolTip("点击要删除的点")
        self.mode_button_group.addButton(self.delete_mode_button, 2)
        radio_layout.addWidget(self.delete_mode_button)

        # 只读模式
        self.readonly_mode_button = QRadioButton("只读模式")
        self.readonly_mode_button.setToolTip("锁定画布，防止意外修改点")
        self.mode_button_group.addButton(self.readonly_mode_button, 3)
        radio_layout.addWidget(self.readonly_mode_button)

        # 将单选按钮布局添加到主模式布局
        mode_layout.addLayout(radio_layout)

        # 添加模式说明标签
        mode_help_label = QLabel("提示: 在调整模式下，选中点后可使用键盘方向键微调位置，按住Shift键可进行更大幅度调整")
        mode_help_label.setWordWrap(True)
        mode_help_label.setStyleSheet("color: #666666; font-style: italic;")
        mode_layout.addWidget(mode_help_label)

        # 设置模式组布局
        mode_group.setLayout(mode_layout)
        main_layout.addWidget(mode_group)

        # 创建精确添加点功能组
        add_point_group = QGroupBox("精确添加点")
        add_point_layout = QFormLayout()

        # 频率输入框
        self.add_freq_input = QDoubleSpinBox()
        self.add_freq_input.setRange(0.01, 1000000)  # 允许更广泛的频率范围，只要是正数即可
        self.add_freq_input.setDecimals(2)
        self.add_freq_input.setSuffix(" Hz")
        self.add_freq_input.setValue(10)  # 默认值设为10Hz，更接近低频要求
        add_point_layout.addRow("频率:", self.add_freq_input)

        # 谱级输入框
        self.add_level_input = QDoubleSpinBox()
        self.add_level_input.setRange(0, 200)
        self.add_level_input.setDecimals(2)
        self.add_level_input.setSuffix(" dB")
        self.add_level_input.setValue(60)  # 默认值
        add_point_layout.addRow("谱级:", self.add_level_input)

        # 添加按钮
        self.add_point_button = QPushButton("添加")
        add_point_layout.addRow("", self.add_point_button)

        # 添加频率范围提示标签
        self.freq_range_label = QLabel("<font color='blue'>提示: 至少需要两个点才能进行外插，建议添加覆盖关注频率范围的多个点</font>")
        self.freq_range_label.setWordWrap(True)
        add_point_layout.addRow("", self.freq_range_label)

        # 设置精确添加点功能组布局
        add_point_group.setLayout(add_point_layout)
        main_layout.addWidget(add_point_group)

        # 创建外插设置组
        extrapolation_group = QGroupBox("外插设置")
        extrapolation_layout = QVBoxLayout()

        # 低频外插设置
        low_freq_layout = QFormLayout()

        # 低频外插方式选择
        self.low_freq_method_group = QButtonGroup(self)
        low_freq_method_layout = QHBoxLayout()

        self.low_freq_auto_radio = QRadioButton("自动计算斜率")
        self.low_freq_auto_radio.setChecked(True)
        self.low_freq_method_group.addButton(self.low_freq_auto_radio, 0)
        low_freq_method_layout.addWidget(self.low_freq_auto_radio)

        self.low_freq_manual_radio = QRadioButton("指定斜率")
        self.low_freq_method_group.addButton(self.low_freq_manual_radio, 1)
        low_freq_method_layout.addWidget(self.low_freq_manual_radio)

        low_freq_layout.addRow("低频外插方式:", low_freq_method_layout)

        # 低频斜率输入框
        self.low_freq_slope_input = QDoubleSpinBox()
        self.low_freq_slope_input.setRange(-20, 0)
        self.low_freq_slope_input.setDecimals(1)
        self.low_freq_slope_input.setSuffix(" dB/倍频程")
        self.low_freq_slope_input.setValue(-9.0)
        self.low_freq_slope_input.setEnabled(False)  # 初始禁用
        low_freq_layout.addRow("低频斜率:", self.low_freq_slope_input)

        # 添加低频设置到主布局
        extrapolation_layout.addLayout(low_freq_layout)

        # 添加分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        extrapolation_layout.addWidget(line)

        # 高频外插设置
        high_freq_layout = QFormLayout()

        # 高频外插方式选择
        self.high_freq_method_group = QButtonGroup(self)
        high_freq_method_layout = QHBoxLayout()

        self.high_freq_auto_radio = QRadioButton("自动计算斜率")
        self.high_freq_auto_radio.setChecked(True)
        self.high_freq_method_group.addButton(self.high_freq_auto_radio, 0)
        high_freq_method_layout.addWidget(self.high_freq_auto_radio)

        self.high_freq_manual_radio = QRadioButton("指定斜率")
        self.high_freq_method_group.addButton(self.high_freq_manual_radio, 1)
        high_freq_method_layout.addWidget(self.high_freq_manual_radio)

        high_freq_layout.addRow("高频外插方式:", high_freq_method_layout)

        # 高频斜率输入框
        self.high_freq_slope_input = QDoubleSpinBox()
        self.high_freq_slope_input.setRange(-20, 0)
        self.high_freq_slope_input.setDecimals(1)
        self.high_freq_slope_input.setSuffix(" dB/倍频程")
        self.high_freq_slope_input.setValue(-5.0)
        self.high_freq_slope_input.setEnabled(False)  # 初始禁用
        high_freq_layout.addRow("高频斜率:", self.high_freq_slope_input)

        # 添加高频设置到主布局
        extrapolation_layout.addLayout(high_freq_layout)

        # 外插按钮
        self.extrapolate_button = QPushButton("执行外插")
        extrapolation_layout.addWidget(self.extrapolate_button)

        # 设置外插设置组布局
        extrapolation_group.setLayout(extrapolation_layout)
        main_layout.addWidget(extrapolation_group)

        # 创建操作按钮组
        operation_group = QGroupBox("操作")
        operation_layout = QVBoxLayout()  # 改为垂直布局以容纳更多按钮

        # 第一行按钮
        button_row1 = QHBoxLayout()

        # 查看数据按钮
        self.view_data_button = QPushButton("查看数据")
        button_row1.addWidget(self.view_data_button)

        # 清除按钮
        self.clear_button = QPushButton("清除所有点")
        button_row1.addWidget(self.clear_button)

        # 添加第一行按钮到主布局
        operation_layout.addLayout(button_row1)

        # 第二行按钮 - 导入/导出
        button_row2 = QHBoxLayout()

        # 导入数据点按钮
        self.import_points_button = QPushButton("导入数据点")
        self.import_points_button.setToolTip("从文件导入频率-谱级数据点")
        button_row2.addWidget(self.import_points_button)

        # 导出数据点按钮
        self.export_points_button = QPushButton("导出数据点")
        self.export_points_button.setToolTip("将当前频率-谱级数据点导出到文件")
        button_row2.addWidget(self.export_points_button)

        # 导入格式帮助按钮
        self.import_help_button = QPushButton("格式帮助")
        self.import_help_button.setToolTip("查看导入文件格式说明")
        button_row2.addWidget(self.import_help_button)

        # 添加第二行按钮到主布局
        operation_layout.addLayout(button_row2)

        # 设置操作按钮组布局
        operation_group.setLayout(operation_layout)
        main_layout.addWidget(operation_group)

        # 创建高级设置组
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QFormLayout()

        # 持续时间输入框
        self.duration_input = QDoubleSpinBox()
        self.duration_input.setRange(0.1, 60.0)  # 设置合理的范围
        self.duration_input.setSingleStep(0.1)  # 步长为0.1
        self.duration_input.setValue(5.0)  # 默认值
        self.duration_input.setSuffix(" s")
        advanced_layout.addRow("信号持续时间:", self.duration_input)

        # 添加持续时间说明
        duration_label = QLabel("信号持续时间决定了生成信号的长度。系统计算功率谱密度时默认采用1Hz的频率分辨率。")
        duration_label.setWordWrap(True)
        advanced_layout.addRow("", duration_label)

        # 滤波器阶数输入框
        self.filter_order_input = QDoubleSpinBox()
        self.filter_order_input.setRange(257, 32769)  # 设置合理的范围
        self.filter_order_input.setDecimals(0)  # 不需要小数
        self.filter_order_input.setSingleStep(256)  # 步长为256
        self.filter_order_input.setValue(16385)  # 默认值
        advanced_layout.addRow("滤波器阶数:", self.filter_order_input)

        # 添加滤波器阶数说明
        filter_order_label = QLabel("较大的滤波器阶数可以提高仿真精度，但会增加计算时间。阶数必须为奇数，系统会自动调整。")
        filter_order_label.setWordWrap(True)
        advanced_layout.addRow("", filter_order_label)

        # 设置高级设置组布局
        advanced_group.setLayout(advanced_layout)
        main_layout.addWidget(advanced_group)

        # 创建仿真按钮
        self.simulate_button = QPushButton("开始仿真")
        self.simulate_button.setMinimumHeight(40)
        main_layout.addWidget(self.simulate_button)

        # 添加弹性空间
        main_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # 连接信号
        self.connect_signals()

    def get_main_window(self):
        """
        获取主窗口引用

        Returns:
            MainWindow: 主窗口引用
        """
        return self.main_window

    def connect_signals(self):
        """
        连接信号和槽
        """
        # 模式按钮组
        self.mode_button_group.buttonClicked.connect(self.on_mode_changed)

        # 精确添加点
        self.add_point_button.clicked.connect(self.on_add_point_clicked)

        # 外插设置
        self.low_freq_method_group.buttonClicked.connect(self.on_low_freq_method_changed)
        self.high_freq_method_group.buttonClicked.connect(self.on_high_freq_method_changed)
        self.low_freq_slope_input.valueChanged.connect(self.on_low_freq_slope_changed)
        self.high_freq_slope_input.valueChanged.connect(self.on_high_freq_slope_changed)

        # 操作按钮
        self.view_data_button.clicked.connect(self.on_view_data_clicked)
        self.extrapolate_button.clicked.connect(self.on_extrapolate_clicked)
        self.clear_button.clicked.connect(self.on_clear_clicked)

        # 导入/导出按钮
        self.import_points_button.clicked.connect(self.on_import_points_clicked)
        self.export_points_button.clicked.connect(self.on_export_points_clicked)
        self.import_help_button.clicked.connect(self.show_import_help)

        # 仿真按钮
        self.simulate_button.clicked.connect(self.on_simulate_clicked)

        # 高级设置控件
        self.duration_input.valueChanged.connect(self.on_duration_changed)
        self.filter_order_input.valueChanged.connect(self.on_filter_order_changed)

    def on_mode_changed(self, button):
        """
        模式变更事件处理

        Args:
            button: 被点击的按钮
        """
        # 获取当前模式
        mode_id = self.mode_button_group.id(button)

        # 根据模式ID设置模式
        if mode_id == 0:
            mode = 'add'
        elif mode_id == 1:
            mode = 'adjust'
        elif mode_id == 2:
            mode = 'delete'
        else:
            mode = 'locked'  # 只读模式

        # 通知视图更新模式
        self.get_main_window().ambient_noise_view.set_mode(mode)

    def on_low_freq_method_changed(self, button):
        """
        低频外插方式变更事件处理

        Args:
            button: 被点击的按钮
        """
        # 获取当前选择
        method_id = self.low_freq_method_group.id(button)

        # 根据选择启用或禁用斜率输入框
        if method_id == 0:  # 自动计算斜率
            self.low_freq_slope_input.setEnabled(False)
        else:  # 指定斜率
            self.low_freq_slope_input.setEnabled(True)

        # 注意：不再立即更新数据管理器，而是在执行外插或仿真前更新

    def on_high_freq_method_changed(self, button):
        """
        高频外插方式变更事件处理

        Args:
            button: 被点击的按钮
        """
        # 获取当前选择
        method_id = self.high_freq_method_group.id(button)

        # 根据选择启用或禁用斜率输入框
        if method_id == 0:  # 自动计算斜率
            self.high_freq_slope_input.setEnabled(False)
        else:  # 指定斜率
            self.high_freq_slope_input.setEnabled(True)

        # 注意：不再立即更新数据管理器，而是在执行外插或仿真前更新

    def on_low_freq_slope_changed(self, value):
        """
        低频斜率变更事件处理

        Args:
            value (float): 新斜率值
        """
        # 注意：不再立即更新数据管理器，而是在执行外插或仿真前更新
        pass

    def on_high_freq_slope_changed(self, value):
        """
        高频斜率变更事件处理

        Args:
            value (float): 新斜率值
        """
        # 注意：不再立即更新数据管理器，而是在执行外插或仿真前更新
        pass

    def _update_extrapolation_settings_to_data_manager(self):
        """
        将外插设置更新到数据管理器

        在执行外插或仿真前调用此方法，一次性更新所有外插设置
        """
        if not self.data_manager:
            return

        # 创建一个包含所有外插设置的字典
        extrapolation_settings = {
            'low_freq_extrapolation_method': 'auto' if self.low_freq_auto_radio.isChecked() else 'manual',
            'high_freq_extrapolation_method': 'auto' if self.high_freq_auto_radio.isChecked() else 'manual'
        }

        # 如果是手动模式，添加斜率值
        if not self.low_freq_auto_radio.isChecked():
            extrapolation_settings['low_freq_slope'] = self.low_freq_slope_input.value()

        if not self.high_freq_auto_radio.isChecked():
            extrapolation_settings['high_freq_slope'] = self.high_freq_slope_input.value()

        # 逐个更新参数到数据管理器
        for key, value in extrapolation_settings.items():
            self.data_manager.update_parameter('ambient_noise', key, value)

        # 发送参数变更信号
        self.data_manager.parameters_changed.emit('ambient_noise')



    def on_add_point_clicked(self):
        """
        添加点按钮点击事件处理
        """
        # 获取频率和谱级
        freq = self.add_freq_input.value()
        level = self.add_level_input.value()

        # 添加点
        self.get_main_window().ambient_noise_view.add_point(freq, level)

        # 直接更新数据管理器
        if self.data_manager:
            # 获取用户定义的点
            view = self.get_main_window().ambient_noise_view
            user_points = view.get_user_points()

            # 更新数据管理器
            self.data_manager.update_parameter('ambient_noise', 'user_defined_points', user_points)

    def on_view_data_clicked(self):
        """
        查看数据按钮点击事件处理
        """
        # 创建点数据对话框
        if not self.point_data_dialog:
            self.point_data_dialog = PointDataDialog(self.get_main_window())
            self.point_data_dialog.data_changed.connect(self.on_point_data_changed)

        # 获取用户点和外插点
        view = self.get_main_window().ambient_noise_view
        user_points = view.get_user_points()
        extrapolated_points = [(freq, level) for freq, level, _ in view.extrapolated_points]

        # 更新对话框数据
        self.point_data_dialog.update_data(user_points, extrapolated_points)

        # 显示对话框
        self.point_data_dialog.show()

    def on_point_data_changed(self, user_points, extrapolated_points):
        """
        点数据变更事件处理

        Args:
            user_points (list): 用户定义的点列表
            extrapolated_points (list): 外插的点列表
        """
        # 更新视图
        view = self.get_main_window().ambient_noise_view
        view.set_user_points(user_points)
        view.update_extrapolated_curve(extrapolated_points)

    def on_extrapolate_clicked(self):
        """
        执行外插按钮点击事件处理
        """
        # 获取用户点
        view = self.get_main_window().ambient_noise_view
        user_points = view.get_user_points()

        # 校验用户点
        if not self._validate_user_points(user_points):
            return

        # 更新外插设置和用户点到数据管理器
        if self.data_manager:
            # 一次性更新所有外插设置
            self._update_extrapolation_settings_to_data_manager()

            # 更新用户点
            self.data_manager.update_parameter('ambient_noise', 'user_defined_points', user_points)

        # 获取控制器
        controller = self.get_main_window().simulation_controller.ambient_noise_controller

        try:
            # 执行外插
            extrapolated_points = controller.perform_extrapolation()

            # 更新视图
            view.update_extrapolated_curve(extrapolated_points)

            # 更新数据管理器
            if self.data_manager:
                self.data_manager.update_parameter('ambient_noise', 'extrapolated_points', extrapolated_points)

            # 更新状态栏
            self.get_main_window().statusBar().showMessage(f"外插完成，添加了{len(extrapolated_points)}个外插点")
        except ValueError as e:
            # 显示错误信息
            error_message = str(e)
            self.get_main_window().statusBar().showMessage(f"外插失败: {error_message}")

            # 显示错误对话框
            QMessageBox.warning(self, "外插失败", error_message)

    def _validate_user_points(self, user_points):
        """
        校验用户点

        Args:
            user_points (list): 用户点列表

        Returns:
            bool: 校验是否通过
        """
        # 如果没有用户点，无法执行外插
        if not user_points:
            self.get_main_window().statusBar().showMessage("没有用户定义的点，无法执行外插")
            QMessageBox.warning(self, "外插失败", "没有用户定义的点，无法执行外插")
            return False

        # 如果只有一个点，无法进行自动外插
        if len(user_points) < 2:
            self.get_main_window().statusBar().showMessage("至少需要两个点才能进行外插")
            QMessageBox.warning(self, "外插失败", "至少需要两个点才能进行外插")
            return False

        # 检查频率是否都为正数
        for freq, _ in user_points:
            if freq <= 0:
                error_message = f"频率必须为正数，但发现频率为{freq}Hz"
                self.get_main_window().statusBar().showMessage(f"外插失败: {error_message}")
                QMessageBox.warning(self, "外插失败", error_message)
                return False

        # 检查是否有重复频率
        freqs = [freq for freq, _ in user_points]
        if len(freqs) != len(set(freqs)):
            error_message = "存在重复的频率点，请确保每个频率只有一个数据点"
            self.get_main_window().statusBar().showMessage(f"外插失败: {error_message}")
            QMessageBox.warning(self, "外插失败", error_message)
            return False

        return True

    def on_clear_clicked(self):
        """
        清除所有点按钮点击事件处理
        """
        # 清除视图中的点
        self.get_main_window().ambient_noise_view.clear_points()

        # 重置提示标签样式
        self.freq_range_label.setStyleSheet("")

    def on_simulate_clicked(self):
        """
        开始仿真按钮点击事件处理
        """
        # 获取用户点
        view = self.get_main_window().ambient_noise_view
        user_points = view.get_user_points()

        # 如果没有用户点，无法进行仿真
        if not user_points:
            self.get_main_window().statusBar().showMessage("没有用户定义的点，无法进行仿真")
            return

        # 校验用户点
        if not self._validate_user_points(user_points):
            return

        # 确保用户点已保存到数据管理器
        if self.data_manager:
            self.data_manager.update_parameter('ambient_noise', 'user_defined_points', user_points)

        # 每次仿真前都重新执行外插，确保使用最新的用户数据点和外插设置
        try:
            # 更新外插设置到数据管理器
            if self.data_manager:
                # 一次性更新所有外插设置
                self._update_extrapolation_settings_to_data_manager()

            # 获取控制器
            controller = self.get_main_window().simulation_controller.ambient_noise_controller

            # 执行外插
            extrapolated_points = controller.perform_extrapolation()

            # 更新视图
            view.update_extrapolated_curve(extrapolated_points)

            # 更新数据管理器
            if self.data_manager:
                self.data_manager.update_parameter('ambient_noise', 'extrapolated_points', extrapolated_points)

            self.get_main_window().statusBar().showMessage("执行外插完成，准备开始仿真")
        except ValueError as e:
            # 显示错误信息
            error_message = str(e)
            self.get_main_window().statusBar().showMessage(f"外插失败: {error_message}")
            QMessageBox.warning(self, "外插失败", error_message)
            return

        # 获取高级参数
        duration = self.duration_input.value()
        filter_order = int(self.filter_order_input.value())

        # 确保滤波器阶数为奇数
        if filter_order % 2 == 0:
            filter_order += 1
            self.filter_order_input.setValue(filter_order)

        # 直接更新数据管理器
        if self.data_manager:
            self.data_manager.update_parameter('ambient_noise', 'user_defined_points', user_points)
            self.data_manager.update_parameter('ambient_noise', 'duration', duration)
            self.data_manager.update_parameter('ambient_noise', 'filter_order', filter_order)

        # 发送仿真请求信号（不再需要携带参数）
        self.simulation_requested.emit()

    def on_duration_changed(self, value):
        """
        持续时间变更事件处理

        Args:
            value (float): 新持续时间值
        """
        # 更新数据管理器
        if self.data_manager:
            self.data_manager.update_parameter('ambient_noise', 'duration', value)

    def on_filter_order_changed(self, value):
        """
        滤波器阶数变更事件处理

        Args:
            value (float): 新滤波器阶数值
        """
        # 确保滤波器阶数为奇数
        filter_order = int(value)
        if filter_order % 2 == 0:
            filter_order += 1
            self.filter_order_input.setValue(filter_order)
            return

        # 更新数据管理器
        if self.data_manager:
            self.data_manager.update_parameter('ambient_noise', 'filter_order', filter_order)

    def on_point_added(self, freq, level):
        """
        点添加事件处理

        Args:
            freq (float): 频率
            level (float): 谱级
        """
        # 更新状态栏
        self.get_main_window().statusBar().showMessage(f"添加点: 频率={freq:.1f}Hz, 谱级={level:.1f}dB")

    def on_point_selected(self, index):
        """
        点选中事件处理

        Args:
            index (int): 点的索引
        """
        # 获取选中点的频率和谱级
        view = self.get_main_window().ambient_noise_view
        if 0 <= index < len(view.user_points):
            freq, level, _ = view.user_points[index]

            # 更新状态栏
            self.get_main_window().statusBar().showMessage(f"选中点: 频率={freq:.1f}Hz, 谱级={level:.1f}dB")

    def on_point_updated(self, index, freq, level):
        """
        点更新事件处理

        Args:
            index (int): 点的索引
            freq (float): 新频率
            level (float): 新谱级
        """
        # 更新状态栏
        self.get_main_window().statusBar().showMessage(f"更新点 #{index}: 频率={freq:.1f}Hz, 谱级={level:.1f}dB")

        # 直接更新数据管理器
        if self.data_manager:
            # 获取用户定义的点
            view = self.get_main_window().ambient_noise_view
            user_points = view.get_user_points()

            # 更新数据管理器
            self.data_manager.update_parameter('ambient_noise', 'user_defined_points', user_points)

    def on_point_removed(self, index):
        """
        点删除事件处理

        Args:
            index (int): 点的索引
        """
        # 直接更新数据管理器
        if self.data_manager:
            # 获取用户定义的点
            view = self.get_main_window().ambient_noise_view
            user_points = view.get_user_points()

            # 更新数据管理器
            self.data_manager.update_parameter('ambient_noise', 'user_defined_points', user_points)

        # 更新状态栏
        self.get_main_window().statusBar().showMessage(f"删除点 #{index}")

    def on_import_points_clicked(self):
        """
        导入数据点按钮点击事件处理
        """
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入频率-谱级数据点", "",
            "CSV文件 (*.csv);;文本文件 (*.txt);;所有文件 (*)"
        )

        if not file_path:
            return

        try:
            # 尝试读取文件
            try:
                # 首先尝试以CSV格式读取
                if file_path.lower().endswith('.csv'):
                    data = np.genfromtxt(file_path, delimiter=',', dtype=float)
                else:
                    # 尝试以空格分隔的格式读取
                    data = np.genfromtxt(file_path, dtype=float)
            except Exception as e:
                self.get_main_window().statusBar().showMessage(f"文件读取失败: {str(e)}")
                QMessageBox.critical(self, "导入错误", f"无法读取文件，请检查文件格式。\n错误详情: {str(e)}")
                return

            # 检查数据结构
            if len(data.shape) < 2:
                # 如果只有一行数据，尝试重塑数组
                if len(data.shape) == 1 and data.size >= 2:
                    if data.size % 2 == 0:  # 确保数据点数是偶数
                        data = data.reshape(-1, 2)
                    else:
                        raise ValueError("数据点数不是偶数，无法形成有效的频率-谱级对")
                else:
                    raise ValueError("数据格式不正确，无法解析为频率-谱级对")

            if data.shape[1] != 2:
                raise ValueError(f"数据列数不正确，应为2列（频率和谱级），实际为{data.shape[1]}列")

            # 检查数据是否有缺失值
            if np.isnan(data).any():
                # 找出有缺失值的行
                nan_rows = np.where(np.isnan(data).any(axis=1))[0]
                raise ValueError(f"数据中存在缺失值，在第 {', '.join(map(str, nan_rows + 1))} 行")

            # 检查频率值是否为正数
            if (data[:, 0] <= 0).any():
                invalid_rows = np.where(data[:, 0] <= 0)[0]
                raise ValueError(f"频率必须为正数，在第 {', '.join(map(str, invalid_rows + 1))} 行存在非正频率值")

            # 检查频率是否唯一
            unique_freqs = np.unique(data[:, 0])
            if len(unique_freqs) < len(data):
                # 找出重复的频率
                freq_counts = {}
                for freq in data[:, 0]:
                    freq_counts[freq] = freq_counts.get(freq, 0) + 1

                duplicate_freqs = [freq for freq, count in freq_counts.items() if count > 1]
                warning_msg = f"警告：存在重复的频率值: {', '.join(map(str, duplicate_freqs))}\n将只保留每个频率的最后一个数据点"
                QMessageBox.warning(self, "导入警告", warning_msg)

                # 创建一个字典来存储每个频率的最后一个谱级
                freq_level_dict = {}
                for freq, level in data:
                    freq_level_dict[freq] = level

                # 转换回数组
                data = np.array(list(freq_level_dict.items()))

            # 按频率排序
            data = data[data[:, 0].argsort()]

            # 清除现有点
            view = self.get_main_window().ambient_noise_view
            view.clear_points()

            # 添加导入的点
            for i in range(data.shape[0]):
                freq = data[i, 0]
                level = data[i, 1]
                view.add_point(freq, level)

            # 更新数据管理器
            if self.data_manager:
                user_points = view.get_user_points()
                self.data_manager.update_parameter('ambient_noise', 'user_defined_points', user_points)

            # 更新状态栏
            self.get_main_window().statusBar().showMessage(f"成功导入 {data.shape[0]} 个数据点")

        except Exception as e:
            self.get_main_window().statusBar().showMessage(f"导入数据点失败: {str(e)}")
            QMessageBox.critical(self, "导入错误", f"导入数据点失败:\n{str(e)}")

    def on_export_points_clicked(self):
        """
        导出数据点按钮点击事件处理
        """
        # 获取用户点
        view = self.get_main_window().ambient_noise_view
        user_points = view.get_user_points()

        # 如果没有用户点，无法导出
        if not user_points:
            self.get_main_window().statusBar().showMessage("没有用户定义的点，无法导出")
            QMessageBox.warning(self, "导出警告", "没有用户定义的点，无法导出")
            return

        file_path, selected_filter = QFileDialog.getSaveFileName(
            self, "导出频率-谱级数据点", "",
            "CSV文件 (*.csv);;文本文件 (*.txt);;所有文件 (*)"
        )

        if not file_path:
            return

        try:
            # 按频率排序
            user_points.sort(key=lambda x: x[0])

            # 转换为NumPy数组
            data = np.array(user_points)

            # 根据选择的文件类型决定分隔符
            if selected_filter == "CSV文件 (*.csv)":
                np.savetxt(file_path, data, delimiter=',', fmt='%.6f')
            else:
                np.savetxt(file_path, data, delimiter=' ', fmt='%.6f')

            # 更新状态栏
            self.get_main_window().statusBar().showMessage(f"成功导出 {len(user_points)} 个数据点到 {file_path}")

        except Exception as e:
            self.get_main_window().statusBar().showMessage(f"导出数据点失败: {str(e)}")
            QMessageBox.critical(self, "导出错误", f"导出数据点失败:\n{str(e)}")

    def show_import_help(self):
        """
        显示导入文件格式帮助
        """
        help_text = """
        <h3>导入文件格式说明</h3>

        <p>支持的文件格式：</p>
        <ul>
            <li><b>CSV文件</b>：两列数据，以逗号分隔，第一列为频率(Hz)，第二列为谱级(dB re 1μPa²/Hz)</li>
            <li><b>TXT文件</b>：两列数据，以空格或制表符分隔，第一列为频率(Hz)，第二列为谱级(dB re 1μPa²/Hz)</li>
        </ul>

        <p>数据要求：</p>
        <ul>
            <li>频率必须为正数</li>
            <li>每个频率只能有一个谱级值（如有重复，将保留最后一个）</li>
            <li>为了外插算法正确工作，建议包含<20Hz的低频点和>500Hz的高频点</li>
        </ul>

        <p>示例数据（CSV格式）：</p>
        <pre>
        10,80
        100,70
        1000,60
        10000,50
        </pre>

        <p>示例数据（TXT格式）：</p>
        <pre>
        10 80
        100 70
        1000 60
        10000 50
        </pre>
        """

        QMessageBox.information(self, "导入文件格式帮助", help_text)