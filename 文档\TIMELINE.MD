**第一阶段：核心突破与中期检查准备 (中期检查前，约 1.5 周)**

* **目标：** 实现 Python 对 BELLHOP 的基本调用与结果解析，并构建能驱动这次调用的最简化模型。准备中期汇报材料。
* **任务 1.1: (最高优先级) BELLHOP 基础攻关 (3-4 天)**
  * **手动运行：** 先脱离 Python，直接学习 BELLHOP 的文档，理解最简单的环境文件（`.env`，如等声速、平坦海底）、射线文件（`.ray`，可选）、声源文件（`.sgy`，可选）和输出文件（如传播损失 `.shd`、声线轨迹 `.plt`）的格式。**手动编写**一个最简单的输入文件组，并在**命令行成功运行** BELLHOP，得到预期输出。
  * **Python 调用原型 (`src/models/propagation/bellhop_wrapper.py` - 初版):**
    * 编写 Python 代码**生成**上述最简输入文件。
    * 使用 `subprocess` 模块**执行** BELLHOP 可执行程序。
    * 编写 Python 代码**解析**最关键的输出文件（例如 `.shd` 文件中的传播损失 Transmission Loss），能将其读入为 NumPy 数组即可。
  * *产出：* 成功的命令行运行记录，一个能运行 BELLHOP 并读回最基本结果的 Python 脚本/函数，对 BELLHOP 的 I/O 有了初步但确切的认识。
* **任务 1.2: 最简化模型搭建 (2-3 天)**
  * **环境模型 (`src/models/environment.py` - v0.1):** 实现一个类，仅包含运行任务 1.1 中 BELLHOP 场景所需的参数（如频率、深度、简单声速）。
  * **声源表示 (`src/utils/data_structures.py`):** 仅定义声源位置等 BELLHOP 输入所需的信息。暂时不需要完整声源模型。
  * *产出：* 能够为 `bellhop_wrapper.py` 提供最基本输入参数的 Python 类。
* **任务 1.3: 命令行驱动脚本 (`scripts/run_minimal_sim.py`) (1 天)**
  * 创建一个简单的 Python 脚本：
    * 初始化上述最简环境模型。
    * 调用 `bellhop_wrapper.py` 中的函数，传入参数，执行仿真。
    * 打印或简单记录解析出的传播损失结果。
  * *产出：* 一个无 UI 的、能完整跑通“参数 -> BELLHOP -> 结果”流程的脚本。
* **任务 1.4: 中期检查材料准备 (1-2 天)**
  * 整理 PPT：项目背景、需求、 **已完成工作（重点展示任务 1.1-1.3 的成果，强调 BELLHOP 集成已打通）** 、遇到的问题、 **下一步详细计划（后续模型、UI、可视化）** 。
  * 准备演示：运行 `run_minimal_sim.py` 脚本，展示 Python 成功调用 BELLHOP 并获取结果的过程。
  * *产出：* 中期答辩 PPT 和演示脚本。

**第二阶段：模型实现与核心功能集成 (中期后 - 约 3 周)**

* **目标：** 实现主要的噪声模型（船舶、环境），将其与 BELLHOP 集成，完成核心的噪声场特性（频谱）计算。
* **任务 2.1: 模型实现 (并行)**
  * 实现船舶辐射噪声模型 (`src/models/noise_sources/ship_radiated.py` - v1)：根据你调研的公式或数据，实现频谱生成。接口要清晰（输入参数，输出 NumPy 频谱数组）。
  * 实现海洋环境噪声模型 (`src/models/noise_sources/ocean_ambient.py` - v1)：如 Wenz 曲线模型。
  * 完善环境模型 (`src/models/environment.py` - v1)：支持更复杂的声速剖面、海底参数等（根据模型需要和 BELLHOP 支持度）。
* **任务 2.2: 集成与计算**
  * 完善 `bellhop_wrapper.py`：使其能接受更复杂的环境/声源参数，并可能解析更多类型的输出。
  * 实现结果计算 (`src/models/field_calculator.py` - v1)：计算接收点的噪声频谱（结合声源谱和传播损失）。
  * 修改 `scripts/run_minimal_sim.py` (或新建 `run_full_sim.py`)：使用 v1 版本的模型驱动仿真，并调用 `field_calculator` 计算频谱。
* **任务 2.3: 单元测试**
  * 为新实现的模型和计算逻辑编写单元测试。

**第三阶段：UI 开发与可视化集成 (约 2-3 周)**

* **目标：** 快速构建用户界面，实现参数输入和结果（频谱图）的可视化展示。
* **任务 3.1: UI 框架与参数输入**
  * 基于第一阶段的 UI 骨架，实现参数输入面板，并与第二阶段定义的模型参数数据结构绑定。
* **任务 3.2: 仿真控制与后台执行**
  * 实现 `SimulationManager` 和 `QThread`，将第二阶段的仿真逻辑（脚本中的流程）移植到后台线程中，由 UI 按钮触发。
* **任务 3.3: 可视化实现与集成**
  * 实现 `Plotter` 类和 Matplotlib 嵌入控件，用于显示 `field_calculator` 计算出的频谱图。
  * 将仿真结果信号传递给 UI 并更新绘图。

**第四阶段：功能完善、测试、文档与论文冲刺 (剩余时间 约 1-2 周 + 论文时间)**

* **目标：** 完成剩余功能（如保存/加载、更多可视化特性如指向性等），进行全面测试，撰写论文。
* **任务 4.1: (优先级可选) 附加功能**
  * 实现项目/场景的保存与加载。
  * 根据时间和 BELLHOP 输出支持情况，尝试实现指向性、相关性等计算与可视化。
* **任务 4.2: (必须) 全面测试与优化**
  * 系统测试，边界条件测试，UI 交互测试。
* **任务 4.3: (必须) 文档与论文**
  * 整理代码注释、技术文档。
  * **集中精力冲刺论文写作** ，根据之前积累的素材和最终的软件功能、结果进行撰写和完善。
  * 准备最终答辩。
