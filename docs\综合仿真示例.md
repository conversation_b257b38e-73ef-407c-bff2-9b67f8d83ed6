好的，考虑到你的具体需求（CSV 输入、多阵元、叠加噪声、类实现），我们来设计这个仿真类的结构和关键实现点。

```python
import numpy as np
import scipy.fft
import scipy.signal
import pandas as pd  # 用于读取 CSV
import os

class UnderwaterChannelSimulator:
    """
    通过 Bellhop 到达数据模拟宽带信号在水声信道中的传播，
    并可选择叠加环境噪声。支持多阵元接收。
    采用频域子带处理和 Overlap-Save 分块卷积。
    """
    def __init__(self, fs, L=4096):
        """
        初始化模拟器。
        :param fs: 源信号和模拟的采样率 (Hz)
        :param L: 分块卷积的块长 (samples), 默认 4096
        """
        self.fs = fs
        self.L = L
        self._subband_defs = None      # 子带定义 [(fc, f_start, f_end), ...]
        self._csv_dir = None           # 包含 CSV 文件的目录
        self._element_locations = None # 阵元位置信息 (可能需要，取决于 CSV 格式)
        self._source_signal_path = None # 源信号文件路径
        self._output_dir = None        # 输出文件目录 (可选)
        self._noise_psd_level = None   # 环境噪声功率谱密度级 (dB re 1uPa^2/Hz) (可选)
        self._t_h_user_max = None      # 用户指定的最大相对延迟 (秒) (可选)

        # --- 内部计算状态 ---
        self._t_first_global = None
        self._max_t_prime_eff = None
        self._N_h = None
        self._N_FFT = None
        self._H_fft_cache = {}         # 缓存 {element_id: H_fft_array}

    # --- Setter 方法 ---
    def set_subbands(self, subband_definitions):
        """设置子带定义。格式: [(f_center, f_start, f_end), ...]"""
        # 可以添加校验逻辑
        self._subband_defs = sorted(subband_definitions, key=lambda x: x[0]) # 按中心频率排序

    def set_arrival_data_dir(self, csv_directory, element_locations=None):
        """设置包含到达数据 CSV 文件的目录和阵元位置信息。"""
        if not os.path.isdir(csv_directory):
            raise ValueError(f"Directory not found: {csv_directory}")
        self._csv_dir = csv_directory
        self._element_locations = element_locations # 例如: {element_id: (x,y,z)}

    def set_source_signal(self, file_path):
        """设置源信号文件路径 (例如 WAV 文件)。"""
        if not os.path.isfile(file_path):
             raise ValueError(f"Source signal file not found: {file_path}")
        self._source_signal_path = file_path

    def set_output_directory(self, dir_path):
        """设置保存接收信号的目录 (可选)。"""
        self._output_dir = dir_path
        os.makedirs(self._output_dir, exist_ok=True)

    def set_ambient_noise(self, psd_level_db):
        """设置环境噪声级 (dB re 1uPa^2/Hz)。"""
        self._noise_psd_level = psd_level_db

    def set_truncation_time(self, max_relative_delay_sec):
        """设置冲击响应截断的最大相对延迟时间 (秒)。"""
        self._t_h_user_max = max_relative_delay_sec

    # --- 核心计算方法 ---
    def _load_and_process_arrival_data(self):
        """
        加载所有子带频率的 CSV 文件，处理所有阵元的到达数据，
        计算 t_first_global, max_t_prime_eff, 和每个阵元/子带的 Hk_val。
        缓存每个阵元的路径数据 {(Am, tm), ...}。
        """
        if self._subband_defs is None or self._csv_dir is None:
            raise RuntimeError("Subbands and arrival data directory must be set.")

        print("Loading and processing arrival data...")
        all_t_m_global = []
        element_path_data = {} # {element_id: {f_center: [(Am, tm), ...]}}

        for f_center, f_start, f_end in self._subband_defs:
            csv_filename = os.path.join(self._csv_dir, f"arrivals_f{int(f_center)}.csv") # 假设文件名格式
            if not os.path.isfile(csv_filename):
                print(f"Warning: CSV file not found for f={f_center}, skipping.")
                continue

            try:
                # *** 读取 CSV ***
                # df = pd.read_csv(csv_filename)
                # 假设 CSV 列: 'element_id', 'frequency', 'arrival_index',
                #               'amplitude_real', 'amplitude_imag', 'travel_time'
                # df['Am'] = df['amplitude_real'] + 1j * df['amplitude_imag']
                # df['tm'] = df['travel_time']

                # --- 假设的 CSV 读取和处理逻辑 ---
                df = pd.read_csv(csv_filename) # 你需要根据你的 CSV 结构调整
                required_cols = ['element_id', 'amplitude_real', 'amplitude_imag', 'travel_time']
                if not all(col in df.columns for col in required_cols):
                   raise ValueError(f"CSV {csv_filename} missing required columns.")

                df['Am'] = df['amplitude_real'] + 1j * df['amplitude_imag']
                df['tm'] = df['travel_time']
                all_t_m_global.extend(df['tm'].tolist())
                # --- 结束假设逻辑 ---


                # 按阵元分组存储路径数据
                for element_id, group in df.groupby('element_id'):
                    if element_id not in element_path_data:
                        element_path_data[element_id] = {}
                    if f_center not in element_path_data[element_id]:
                       element_path_data[element_id][f_center] = []
                    element_path_data[element_id][f_center].extend(list(zip(group['Am'], group['tm'])))

            except Exception as e:
                print(f"Error processing {csv_filename}: {e}")
                # Decide how to handle errors: skip file, raise exception, etc.

        if not all_t_m_global:
            raise RuntimeError("No valid arrival times found in any CSV file.")

        self._t_first_global = np.min(all_t_m_global)
        print(f"Global t_first: {self._t_first_global:.4f} s")

        # --- 计算每个阵元的 max_t_prime_eff 和 Hk_val ---
        max_t_prime_eff_global = 0
        self._Hk_vals_cache = {} # {element_id: {f_center: Hk_val}}

        for element_id, freq_data in element_path_data.items():
            self._Hk_vals_cache[element_id] = {}
            max_t_prime_eff_element = 0
            valid_paths_element = False

            for f_center, paths in freq_data.items():
                omega_k = 2 * np.pi * f_center
                H_k_sum = 0j
                current_max_t_prime = 0
                valid_paths_freq = False
                for Am, tm in paths:
                    t_prime_m = tm - self._t_first_global
                    if self._t_h_user_max is None or t_prime_m <= self._t_h_user_max:
                        H_k_sum += Am * np.exp(-1j * omega_k * t_prime_m) # 用相对时间
                        current_max_t_prime = max(current_max_t_prime, t_prime_m)
                        valid_paths_freq = True
                        valid_paths_element = True # 标记该阵元有有效路径

                if valid_paths_freq:
                   self._Hk_vals_cache[element_id][f_center] = H_k_sum
                   max_t_prime_eff_element = max(max_t_prime_eff_element, current_max_t_prime)

            if valid_paths_element: # 只有当阵元有有效路径时才更新全局最大值
               max_t_prime_eff_global = max(max_t_prime_eff_global, max_t_prime_eff_element)

        self._max_t_prime_eff = max_t_prime_eff_global
        self._N_h = int(np.ceil(self._max_t_prime_eff * self.fs)) + 1
        # 确保 N_h 至少为 1 (即使没有延迟)
        self._N_h = max(1, self._N_h)

        # 重新计算 N_FFT，现在 N_h 可能已经改变
        self._N_FFT = scipy.fft.next_fast_len(self.L + self._N_h - 1)

        print(f"Effective max relative delay: {self._max_t_prime_eff:.4f} s")
        print(f"Effective impulse response length Nh: {self._N_h} samples")
        print(f"Using FFT length N_FFT: {self._N_FFT} samples")

        # --- 预计算并缓存每个阵元的 H_fft ---
        self._precompute_H_fft_all_elements()


    def _precompute_H_fft_all_elements(self):
        """
        为所有具有有效到达数据的阵元预计算并缓存 H_fft。
        """
        if self._N_FFT is None or self._subband_defs is None or not hasattr(self, '_Hk_vals_cache'):
             raise RuntimeError("Cannot precompute H_fft. Run _load_and_process_arrival_data first.")

        print("Precomputing transfer functions (H_fft) for all elements...")
        freqs = scipy.fft.fftfreq(self._N_FFT, d=1/self.fs)
        self._H_fft_cache = {} # 清空旧缓存

        # 优化：预先计算频率点到子带的映射
        band_indices = np.zeros(self._N_FFT, dtype=int) - 1 # -1表示未分配
        f_center_map = {} # index -> f_center
        for idx, (fc, f_start, f_end) in enumerate(self._subband_defs):
            # 找到属于 [f_start, f_end) 的频率索引
            mask = (freqs >= f_start) & (freqs < f_end)
            band_indices[mask] = idx
            f_center_map[idx] = fc
        # 处理 Nyquist 频率 (如果存在且需要)
        if freqs[-1] == self.fs / 2 and band_indices[-1] == -1:
             # 将 Nyquist 分配给最后一个频带（或根据策略）
             if self._subband_defs:
                 last_idx = len(self._subband_defs) - 1
                 band_indices[-1] = last_idx
                 f_center_map[last_idx] = self._subband_defs[-1][0]


        for element_id, Hk_vals_element in self._Hk_vals_cache.items():
            if not Hk_vals_element: # 跳过没有有效数据的阵元
                print(f"Skipping H_fft computation for element {element_id} (no valid Hk_vals).")
                continue

            H_fft = np.zeros(self._N_FFT, dtype=np.complex128)
            for i in range(self._N_FFT):
                band_idx = band_indices[i]
                if band_idx != -1:
                    f_center = f_center_map[band_idx]
                    if f_center in Hk_vals_element:
                        H_fft[i] = Hk_vals_element[f_center]
                    # else: H_fft[i] = 0 (如果该子带没有Hk_val, 默认为0)
                # else: H_fft[i] = 0 (如果频率不属于任何定义子带)

            self._H_fft_cache[element_id] = H_fft # 缓存结果

        print(f"Precomputed H_fft for {len(self._H_fft_cache)} elements.")


    def _generate_noise(self, duration_samples):
        """生成指定长度的环境噪声信号。"""
        if self._noise_psd_level is None:
            return np.zeros(duration_samples)

        # 将 dB/Hz 转换为线性功率谱密度 (uPa^2/Hz)
        psd_linear = 10**(self._noise_psd_level / 10.0)

        # 计算每个频率 bin 的噪声方差 (PSD * bin宽度)
        # 注意：对于实信号，功率分布在正负频率上
        noise_variance_per_bin = psd_linear * (self.fs / duration_samples) # 近似值，更准确需要考虑窗函数

        # 生成频域噪声 (满足厄米对称性)
        noise_fft = np.zeros(duration_samples, dtype=np.complex128)
        # 只需生成正频率部分 (不含 DC 和 Nyquist)
        num_positive_freqs = (duration_samples - 1) // 2
        # 幅度服从瑞利分布，相位服从均匀分布；或直接生成高斯实部虚部
        # * 0.5 是因为功率分布在正负频率
        noise_std_dev = np.sqrt(0.5 * noise_variance_per_bin)

        # 生成独立的复高斯噪声
        real_part = np.random.normal(0, noise_std_dev, num_positive_freqs)
        imag_part = np.random.normal(0, noise_std_dev, num_positive_freqs)
        noise_fft[1 : num_positive_freqs + 1] = real_part + 1j * imag_part

        # 添加厄米共轭部分
        noise_fft[duration_samples - num_positive_freqs : duration_samples] = np.conj(noise_fft[1 : num_positive_freqs + 1][::-1])

        # 处理 DC (f=0) 和 Nyquist (f=Fs/2) - 通常假设为实数且方差不同
        noise_fft[0] = np.random.normal(0, np.sqrt(noise_variance_per_bin)) # DC component variance
        if duration_samples % 2 == 0: # Nyquist frequency exists
             noise_fft[duration_samples // 2] = np.random.normal(0, np.sqrt(noise_variance_per_bin)) # Nyquist variance

        # IFFT 回到时域
        noise_signal = np.real(scipy.fft.ifft(noise_fft))
        return noise_signal * np.sqrt(duration_samples) # 能量校正因子? 需要验证

    def run_simulation(self):
        """
        执行整个仿真流程：加载数据、分块卷积、添加噪声、保存结果。
        """
        if self._source_signal_path is None:
            raise RuntimeError("Source signal must be set.")

        # --- 1. 加载并预处理数据 ---
        self._load_and_process_arrival_data() # 计算 Nh, NFFT, 并缓存 H_fft

        if not self._H_fft_cache:
             print("Warning: No valid H_fft computed for any element. Simulation cannot proceed.")
             return {} # 返回空字典

        # --- 2. 循环处理每个阵元 ---
        received_signals = {} # {element_id: r_full_signal}

        # 获取源信号信息 (长度等) - 需要 soundfile 或类似库
        import soundfile as sf
        source_info = sf.info(self._source_signal_path)
        source_duration_samples = source_info.frames
        print(f"Source signal: {self._source_signal_path}, Samples: {source_duration_samples}")


        for element_id, H_fft_element in self._H_fft_cache.items():
            print(f"Simulating for element {element_id}...")
            overlap_buffer = np.zeros(self._N_h - 1, dtype=np.float64)
            r_element_blocks = []
            processed_samples = 0

            # 使用 soundfile 逐块读取
            with sf.SoundFile(self._source_signal_path, 'r') as infile:
                while processed_samples < source_duration_samples:
                    # 读取 L 个样本，或文件剩余样本数
                    samples_to_read = min(self.L, source_duration_samples - processed_samples)
                    s_chunk = infile.read(samples_to_read, dtype='float64')

                    # --- Overlap-Save 核心 ---
                    input_frame = np.concatenate((overlap_buffer, s_chunk))

                    # 补零到 N_FFT (如果需要)
                    if len(input_frame) < self._N_FFT:
                        input_frame = np.pad(input_frame, (0, self._N_FFT - len(input_frame)))
                    elif len(input_frame) > self._N_FFT: # 防御性检查
                         input_frame = input_frame[:self._N_FFT]

                    S_frame_fft = scipy.fft.fft(input_frame, n=self._N_FFT)
                    R_frame_fft = S_frame_fft * H_fft_element
                    r_frame = scipy.fft.ifft(R_frame_fft, n=self._N_FFT)
                    r_frame_real = np.real(r_frame)

                    # 保存有效部分
                    r_valid = r_frame_real[self._N_h - 1 : self._N_h - 1 + len(s_chunk)]
                    r_element_blocks.append(r_valid)

                    # 更新重叠缓存
                    # 从 input_frame 取最后 Nh-1 个样本
                    overlap_buffer = input_frame[len(input_frame) - (self._N_h - 1) : ]

                    processed_samples += len(s_chunk)
                    if len(s_chunk) < self.L: # 文件读取完毕
                         break

            # 拼接该阵元的接收信号 (无噪声)
            r_element_no_noise = np.concatenate(r_element_blocks)

            # --- 3. 添加环境噪声 (可选) ---
            if self._noise_psd_level is not None:
                print(f"Generating and adding noise for element {element_id}...")
                # 注意：生成与接收信号等长的噪声
                noise = self._generate_noise(len(r_element_no_noise))
                r_element_final = r_element_no_noise + noise
            else:
                r_element_final = r_element_no_noise

            received_signals[element_id] = r_element_final
            print(f"Finished simulation for element {element_id}.")

            # --- 4. 保存结果 (可选) ---
            if self._output_dir:
                output_filename = os.path.join(self._output_dir, f"received_element_{element_id}.wav")
                try:
                    # 使用 soundfile 保存
                    sf.write(output_filename, r_element_final, self.fs)
                    print(f"Saved received signal to {output_filename}")
                except Exception as e:
                    print(f"Error saving file {output_filename}: {e}")

        print("Simulation complete.")
        return received_signals # 返回包含所有阵元接收信号的字典

    # --- 辅助和分析方法 (可以后续添加) ---
    def get_steady_state_signal(self, received_signals, element_id):
        """获取指定阵元的稳态接收信号部分。"""
        if self._N_h is None:
             raise RuntimeError("Run simulation first to determine Nh.")
        if element_id not in received_signals:
             raise ValueError(f"No simulated signal found for element {element_id}")

        n_steady_start = self._N_h # 稳态起始点 (相对索引)
        signal = received_signals[element_id]
        if len(signal) > n_steady_start:
            return signal[n_steady_start:]
        else:
            print(f"Warning: Signal for element {element_id} is shorter than impulse response. No steady state.")
            return np.array([]) # 返回空数组

    def get_time_vector(self, signal_length):
         """获取与接收信号对应的物理时间向量。"""
         if self._t_first_global is None:
              raise RuntimeError("Run simulation first to determine t_first_global.")
         return self._t_first_global + np.arange(signal_length) / self.fs

    def plot_psd(self, signal, element_id=""):
        """绘制信号的功率谱密度图。"""
        import matplotlib.pyplot as plt
        if len(signal) == 0:
             print("Cannot plot PSD for empty signal.")
             return
        freqs, psd = scipy.signal.welch(signal, fs=self.fs, nperseg=min(len(signal), 1024))
        plt.figure()
        plt.semilogy(freqs, psd)
        plt.xlabel('Frequency [Hz]')
        plt.ylabel('PSD [SignalUnit^2/Hz]') # 单位取决于信号单位
        plt.title(f'Power Spectral Density (Element {element_id})')
        plt.grid(True)
        plt.show()

# --- 使用示例 ---
if __name__ == "__main__":
    # 1. 定义参数
    FS = 22050
    BLOCK_LENGTH = 8192
    SOURCE_FILE = 'path/to/your/source_noise.wav' # 替换为你的源文件路径
    CSV_DATA_DIR = 'path/to/your/csv_files/'     # 替换为你的CSV目录
    OUTPUT_DIR = 'simulation_output/'

    # 定义子带: (中心频率, 起始频率, 结束频率)
    subbands = [
        (250, 0, 500),
        (750, 500, 1000),
        (1250, 1000, 1500),
        # ... 添加更多子带直到覆盖你的频率范围
        (10750, 10500, 11025) # 最后一个包含 Nyquist
    ]

    # (可选) 环境噪声级
    NOISE_DB = 60 # dB re 1uPa^2/Hz

    # (可选) 截断时间
    MAX_REL_DELAY = 5.0 # 秒

    # 2. 创建和配置模拟器实例
    simulator = UnderwaterChannelSimulator(fs=FS, L=BLOCK_LENGTH)
    simulator.set_subbands(subbands)
    simulator.set_arrival_data_dir(CSV_DATA_DIR) # 可以传入 element_locations (如果需要)
    simulator.set_source_signal(SOURCE_FILE)
    simulator.set_output_directory(OUTPUT_DIR)
    # simulator.set_ambient_noise(NOISE_DB) # 取消注释以添加噪声
    # simulator.set_truncation_time(MAX_REL_DELAY) # 取消注释以截断

    # 3. 运行仿真
    try:
        all_received_signals = simulator.run_simulation()

        # 4. 后续分析 (示例)
        if all_received_signals:
            first_element_id = list(all_received_signals.keys())[0]
            steady_signal = simulator.get_steady_state_signal(all_received_signals, first_element_id)
            time_vector = simulator.get_time_vector(len(all_received_signals[first_element_id]))
            time_vector_steady = time_vector[-len(steady_signal):] # 对应稳态部分的时间

            print(f"Steady state signal length for element {first_element_id}: {len(steady_signal)} samples")

            # 绘制稳态信号PSD
            simulator.plot_psd(steady_signal, element_id=first_element_id)

            # 对于指向性分析，你需要获取所有阵元的 steady_signal
            # beamforming_input = np.array([simulator.get_steady_state_signal(all_received_signals, eid)
            #                              for eid in all_received_signals.keys()])
            # ... 进行波束形成处理 ...

    except Exception as e:
        print(f"An error occurred during simulation: {e}")
        import traceback
        traceback.print_exc()

```

**类结构和关键点解释：**

1.  **`__init__`：** 初始化基本参数 (fs, L) 和所有配置属性为 `None`。同时初始化内部状态变量。
2.  **Setter 方法 (`set_*`)：** 用于配置模拟器的各种参数（子带、数据目录、源信号、输出、噪声、截断）。提供了基本的检查（如目录/文件是否存在）。
3.  **`_load_and_process_arrival_data` (核心准备步骤)：**
    *   读取所有子带的 CSV 文件。**你需要根据你的 CSV 格式修改 pandas 读取和列提取部分。**
    *   按 `element_id` 分组存储所有路径的 `(Am, tm)`。
    *   计算全局 `t_first_global`。
    *   计算每个阵元在**考虑用户截断 (`_t_h_user_max`)** 后的有效相对延迟 `t'_m`。
    *   计算每个阵元、每个子带的 `Hk_val`（使用相对延迟 `t'_m`）。
    *   确定全局的最大有效相对延迟 `_max_t_prime_eff` 和对应的 `_N_h`。
    *   计算最终的 `_N_FFT`。
    *   **调用 `_precompute_H_fft_all_elements`**。
4.  **`_precompute_H_fft_all_elements` (优化)：**
    *   **为每个阵元**构建并缓存长度为 `_N_FFT` 的传递函数 `H_fft`。
    *   它利用 `_Hk_vals_cache` 和子带定义，将对应的 `Hk_val` 填充到 `H_fft` 数组的正确频率位置。
    *   **优化：** 预先计算 `freqs` 到子带索引的映射，避免在循环中重复查找。
    *   这个缓存避免了在处理每个数据块时重复构建 `H_fft`，尤其是在多阵元场景下非常重要。
5.  **`_generate_noise` (可选)：**
    *   根据设定的 PSD 级别生成频域噪声。
    *   **注意：** 准确生成具有特定 PSD 的高斯噪声涉及一些细节（厄米对称性、DC/Nyquist 处理、功率校正），这里的实现是一个基本示例，可能需要根据更严格的信号处理文献进行验证或调整。
6.  **`run_simulation` (核心执行步骤)：**
    *   先调用 `_load_and_process_arrival_data`。
    *   **循环遍历 `self._H_fft_cache` 中的每个 `element_id`。**
    *   为每个阵元执行 **Overlap-Save** 分块卷积：
        *   使用 `soundfile` (或其他库) 逐块读取源信号。
        *   管理 `overlap_buffer`。
        *   执行 FFT, 频域乘法 (使用缓存的 `H_fft_element`), IFFT。
        *   丢弃无效样本，保存有效输出块。
        *   更新 `overlap_buffer`。
    *   拼接该阵元的完整接收信号 `r_element_no_noise`。
    *   (可选) 调用 `_generate_noise` 生成噪声并叠加，得到 `r_element_final`。
    *   将 `r_element_final` 存入 `received_signals` 字典。
    *   (可选) 将结果保存到 WAV 文件。
    *   返回包含所有阵元结果的字典。
7.  **辅助方法 (`get_steady_state_signal`, `get_time_vector`, `plot_psd`)：** 用于后续分析，提取稳态部分、计算正确的时间轴、绘制 PSD 等。

**多阵元优化：**

*   关键在于 **`_load_and_process_arrival_data`** 中按 `element_id` 分组处理数据，以及 **`_precompute_H_fft_all_elements`** 中为每个阵元缓存 `H_fft`。
*   `run_simulation` 只需要遍历缓存的 `H_fft`，对每个阵元执行相同的分块卷积流程，避免了重复的数据加载和 H 函数构建。

这个类结构提供了一个清晰、模块化的方式来组织你的仿真代码，并通过 setter 方法方便配置。你需要重点完善 CSV 读取部分以匹配你的文件格式。