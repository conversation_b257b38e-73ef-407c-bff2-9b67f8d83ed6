import numpy as np
from scipy import signal
import matplotlib.pyplot as plt

def calculate_sl_log2(f, f0, sl0, a1_oct, a2_oct):
    """
    Calculates the Sound Level (SL) in dB based on the provided piecewise model,
    interpreting the formula's log as log base 2.

    Args:
        f (np.ndarray): Array of frequencies.
        f0 (float): Peak frequency.
        sl0 (float): Peak sound level (dB).
        a1_oct (float): Power spectrum rising slope (dB/Octave). Directly used
                        in the formula. Must be non-negative.
        a2_oct (float): Power spectrum falling slope (dB/Octave). Directly used
                        in the formula. Expected to be negative for attenuation
                        (e.g., -6).

    Returns:
        np.ndarray: Array of sound levels (dB) corresponding to input frequencies.
    """
    f = np.asarray(f)
    sl = np.zeros_like(f, dtype=float)  # Initialize with zeros

    # Ensure f0 is positive to avoid log issues
    if f0 <= 0:
        raise ValueError("Peak frequency f0 must be positive.")

    # Avoid log2(0) issues by setting a minimum frequency value
    f = np.maximum(f, 1e-9)  # Prevent log2(0)

    # Rising slope part (f < f0)
    mask_low = (f < f0)
    # Formula: SL0 + a1 * log2(f/f0)
    sl[mask_low] = sl0 + a1_oct * np.log2(f[mask_low] / f0)

    # Peak (f == f0) - explicitly set
    sl[f == f0] = sl0

    # Falling slope part (f > f0)
    mask_high = (f > f0)
    # Formula: SL0 + a2 * log2(f/f0)
    sl[mask_high] = sl0 + a2_oct * np.log2(f[mask_high] / f0)

    return sl

def simulate_continuous_spectrum_fft(f0, sl0, a1_oct, a2_oct, fs=10000, duration=1.0, filter_order=1025):
    """
    Simulates a continuous spectrum signal based on the log2 model using FIR filter,
    and provides FFT for verification.

    Args:
        f0 (float): Peak frequency (Hz). Must be positive.
        sl0 (float): Peak sound level (dB).
        a1_oct (float): Power spectrum rising slope (dB/Octave). Must be non-negative.
        a2_oct (float): Power spectrum falling slope (dB/Octave). Expected to be
                        negative for attenuation (e.g., -6).
        fs (int): Sampling frequency (Hz). Default is 10000 Hz.
        duration (float): Duration of the output signal (seconds). Default is 1.0 s.
        filter_order (int): Order of the FIR filter. Should be odd for Type I
                            linear phase filter. If even, it's increased by 1.
                            Default is 1001.

    Returns:
        tuple: (filtered_signal, b, target_freqs, target_amps_linear,
                fft_freqs, fft_magnitudes_db)
            - filtered_signal (np.ndarray): The generated time-domain signal.
            - b (np.ndarray): The FIR filter coefficients.
            - target_freqs (np.ndarray): Frequency points used for filter design (Hz).
            - target_amps_linear (np.ndarray): Target linear amplitudes at target_freqs.
            - fft_freqs (np.ndarray): Frequencies corresponding to the FFT result.
            - fft_magnitudes_db (np.ndarray): Magnitude of the FFT of the output
                                              signal in dB (normalized).
            - scaling_factor (float): Scaling factor could be used for scaling
                                            the FFT output to match the desired
                                            signal's amplitude.
    """
    # --- Input Validation ---
    if not (isinstance(f0, (int, float)) and f0 > 0):
        raise ValueError("Peak frequency f0 must be a positive number.")
    if not isinstance(sl0, (int, float)):
        raise ValueError("Peak sound level sl0 must be a number.")
    if not (isinstance(a1_oct, (int, float)) and a1_oct >= 0):
        raise ValueError("Rising slope a1_oct must be a non-negative number.")
    if not isinstance(a2_oct, (int, float)):
        print("Warning: Falling slope a2_oct is typically negative (e.g., -6).") # Inform user
    if not (isinstance(fs, int) and fs > 0):
        raise ValueError("Sampling frequency fs must be a positive integer.")
    if not (isinstance(duration, (int, float)) and duration > 0):
        raise ValueError("Duration must be a positive number.")
    if not (isinstance(filter_order, int) and filter_order > 0):
        raise ValueError("Filter order must be a positive integer.")

    if filter_order % 2 == 0:
        print(f"Warning: Even filter order ({filter_order}) requested. Using order+1 ({filter_order+1}) for potentially better symmetry (Type I FIR).")
        filter_order += 1
    numtaps = filter_order

    # --- Parameters and Constants ---
    f_nyq = fs / 2.0

    # --- Determine Frequency Points for Filter Design ---
    # (No slope conversion needed as log2 is used in calculate_sl_log2)
    freq_pts = {0.0, 1.0, f0, f_nyq}
    f_curr = f0 / 2.0
    while f_curr >= 1.0:
        freq_pts.add(f_curr)
        f_curr /= 2.0
    f_curr = f0 * 2.0
    while f_curr <= f_nyq:
        freq_pts.add(f_curr)
        f_curr *= 2.0
    freq_pts = sorted([f for f in freq_pts if 0 <= f <= f_nyq])
    freq_pts = np.array(freq_pts)

    # --- Calculate Target Amplitudes at Frequency Points ---
    # Use the modified function with direct slopes and log2
    sl_values = calculate_sl_log2(freq_pts, f0, sl0, a1_oct, a2_oct)

    # 打印设计FIR所使用的频率点和对应的SL值（未归一化）
    print("\n设计FIR所使用的频率点和对应的SL值（未归一化）:")
    print("频率点 (Hz)\t声级 (dB)")
    for i in range(len(freq_pts)):
        print(f"{freq_pts[i]:.2f}\t\t{sl_values[i]:.2f}")
    print()

    amps_linear = 10**(sl_values / 20.0)
    if freq_pts[0] == 0:
        amps_linear[0] = 0.0

    # --- Normalize Frequencies for FIR Filter Design ---
    norm_freqs = freq_pts / f_nyq

    # --- Design the FIR Filter using firwin2 ---
    try:
        # Ensure endpoints 0 and 1, and handle uniqueness/monotonicity
        if not np.isclose(norm_freqs[0], 0.0):
             norm_freqs = np.insert(norm_freqs, 0, 0.0)
             amps_linear = np.insert(amps_linear, 0, 0.0)
        if not np.isclose(norm_freqs[-1], 1.0):
             sl_nyq = calculate_sl_log2(f_nyq, f0, sl0, a1_oct, a2_oct)
             amp_nyq = 10**(sl_nyq / 20.0)
             norm_freqs = np.append(norm_freqs, 1.0)
             amps_linear = np.append(amps_linear, amp_nyq)

        unique_freqs, unique_indices = np.unique(norm_freqs, return_index=True)
        if len(unique_freqs) < len(norm_freqs):
            norm_freqs = unique_freqs
            amps_linear = amps_linear[unique_indices]

        if not np.all(np.diff(norm_freqs) > 0):
             unique_freqs, unique_indices = np.unique(norm_freqs, return_index=True)
             if len(unique_freqs) < len(norm_freqs):
                  norm_freqs = unique_freqs
                  amps_linear = amps_linear[unique_indices]
             if not np.all(np.diff(norm_freqs) > 0):
                  raise ValueError("Frequency points are not strictly increasing after processing.")

        # Normalize amps_linear to [0,1] range
        norm_amps = amps_linear / np.max(amps_linear)

        # 打印归一化后的频率和幅度
        print("\n归一化后的频率和幅度:")
        print("归一化频率\t归一化幅度")
        for i in range(len(norm_freqs)):
            print(f"{norm_freqs[i]:.6f}\t{norm_amps[i]:.6f}")
        print()

        b = signal.firwin2(numtaps, norm_freqs, norm_amps, window=None)

    except ValueError as e:
        print(f"Error during firwin2 design: {e}")
        print("Normalized Frequencies:", norm_freqs)
        print("Linear Amplitudes:", amps_linear)
        raise

    # --- Generate White Noise ---
    n_samples = int(duration * fs)
    noise = np.random.randn(n_samples)

    # --- Filter the Noise ---
    filtered_signal = signal.lfilter(b, 1.0, noise)

    # --- Calculate FFT of the Output Signal ---
    # Use rfft for real signal efficiency (returns only positive frequencies)
    fft_result = np.fft.rfft(filtered_signal)
    fft_freqs = np.fft.rfftfreq(n_samples, d=1.0/fs)

    # Calculate Amplitude Spectrum (single-sided)
    # Scale by 2/N, except for DC (0 Hz) and Nyquist if N is even
    fft_magnitude = np.abs(fft_result) / n_samples
    if n_samples % 2 == 0: # Nyquist present
        fft_magnitude[1:-1] *= 2
    else: # Nyquist not present
        fft_magnitude[1:] *= 2

    # Convert amplitude magnitude to dB
    # Add small epsilon to avoid log10(0)
    epsilon_db = 1e-20
    fft_magnitudes_db = 20 * np.log10(fft_magnitude + epsilon_db)

    # 计算目标频谱在f0处的线性幅度
    target_amp_at_f0 = 10**(sl0 / 20.0)

    # 找到FFT频率数组中最接近f0的点的索引
    # np.abs(fft_freqs - f0)计算每个频率点与f0的差的绝对值
    # argmin()返回最小值的索引，即最接近f0的频率点的索引
    f0_idx = np.abs(fft_freqs - f0).argmin()
    fft_amp_at_f0 = fft_magnitude[f0_idx]

    # 计算缩放因子
    scaling_factor = target_amp_at_f0 / fft_amp_at_f0
    print(f"\n缩放因子计算结果:")
    print(f"目标频谱在f0处的线性幅度: {target_amp_at_f0:.6e}")
    print(f"FFT频率数组中最接近f0的频率为: {fft_freqs[f0_idx]:.6f}")
    print(f"FFT在f0处的线性幅度: {fft_amp_at_f0:.6e}")
    print(f"缩放因子: {scaling_factor:.6e}")

    # 对滤波后的信号进行缩放
    # filtered_signal *= scaling_factor

    # Return results including FFT
    return filtered_signal, b, freq_pts, amps_linear, fft_freqs, fft_magnitudes_db, scaling_factor

def simulate_line_spectrum(frequencies, amplitudes=None, total_harmonics=4, blade_count=None, rotation_speed=None,
                         fs=10000, duration=1.0, random_phase=True):
    """
    Simulates a line spectrum signal with specified frequencies and amplitudes.

    Args:
        frequencies (np.ndarray or list): Array of frequencies (Hz) for the line spectrum components.
                                         If None, frequencies will be calculated based on harmonics,
                                         blade count and rotation speed.
        amplitudes (np.ndarray or list): Array of amplitudes for each frequency component.
                                        If None, amplitudes will be randomly generated from uniform
                                        distribution in [0,1].
        total_harmonics (int): Number of harmonics to generate if frequencies is None. Default is 4.
        blade_count (int): Number of propeller blades. Required if frequencies is None.
        rotation_speed (float): Propeller rotation speed in revolutions per second (r/s).
                              Required if frequencies is None.
        fs (int): Sampling frequency (Hz). Default is 10000 Hz.
        duration (float): Duration of the output signal (seconds). Default is 1.0 s.
        random_phase (bool): Whether to use random phase for each component. Default is True.
                           If False, all phases will be set to 0.

    Returns:
        tuple: (signal, frequencies, amplitudes, phases)
            - signal (np.ndarray): The generated time-domain signal.
            - frequencies (np.ndarray): Frequencies of the line spectrum components (Hz).
            - amplitudes (np.ndarray): Amplitudes of each frequency component.
            - phases (np.ndarray): Phase of each frequency component (radians).
    """
    # --- Input Validation ---
    if frequencies is None:
        if blade_count is None or rotation_speed is None:
            raise ValueError("If frequencies is None, both blade_count and rotation_speed must be provided.")

        # Calculate frequencies based on harmonics, blade count and rotation speed
        # Formula: f_k = m × n × s (where m is harmonic number, n is blade count, s is rotation speed)
        base_freq = blade_count * rotation_speed
        frequencies = [m * base_freq for m in range(1, total_harmonics + 1)]

        # Add two random frequencies in the range [100, 1000] Hz
        random_freqs = np.random.uniform(100, 1000, 2)
        frequencies = np.concatenate([frequencies, random_freqs])
    else:
        frequencies = np.asarray(frequencies)

    # Ensure frequencies is a numpy array
    frequencies = np.asarray(frequencies)
    # Round frequencies to 1 decimal place for better readability
    frequencies = np.round(frequencies, 1)

    # Generate random amplitudes if not provided
    if amplitudes is None:
        amplitudes = np.random.uniform(0.5, 1, len(frequencies))
    else:
        amplitudes = np.asarray(amplitudes)
        if len(amplitudes) != len(frequencies):
            raise ValueError("The length of amplitudes must match the length of frequencies.")

    # Generate random phases if requested
    if random_phase:
        phases = np.random.uniform(0, 2*np.pi, len(frequencies))
    else:
        phases = np.zeros(len(frequencies))

    # Generate time array
    n_samples = int(duration * fs)
    t = np.linspace(0, duration, n_samples, endpoint=False)

    # Initialize signal array
    signal = np.zeros(n_samples)

    # Generate line spectrum signal
    for i in range(len(frequencies)):
        signal += amplitudes[i] * np.sin(2 * np.pi * frequencies[i] * t + phases[i])

    # Print information about the generated signal
    print("\n线谱信号生成信息:")
    print("频率 (Hz)\t幅值\t相位 (rad)")
    for i in range(len(frequencies)):
        print(f"{frequencies[i]:.2f}\t\t{amplitudes[i]:.4f}\t{phases[i]:.4f}")

    return signal, frequencies, amplitudes, phases

# --- Example Usage ---
if __name__ == "__main__":
    # Parameters for continuous spectrum
    peak_freq = 250.0   # Hz
    peak_level_db = 130.0 # dB
    rise_slope_db_oct = 3   # dB/octave (positive)
    fall_slope_db_oct = -4.2  # dB/octave (NEGATIVE, as requested)
    sampling_freq = 10000 # Hz
    signal_duration = 5   # seconds
    fir_filter_order = 2049 # Filter taps

    # Uncomment to simulate continuous spectrum
    '''
    print("Simulating continuous spectrum signal (using log2 and FFT)...")
    print(f"Parameters: f0={peak_freq} Hz, SL0={peak_level_db} dB, a1={rise_slope_db_oct} dB/Oct, a2={fall_slope_db_oct} dB/Oct")
    print(f"            fs={sampling_freq} Hz, Duration={signal_duration} s, Filter Order={fir_filter_order}")

    sim_signal, fir_coeffs, target_freqs_hz, target_amps_lin, \
        fft_freqs_hz, fft_mags_db, scaling_factor = simulate_continuous_spectrum_fft(
            f0=peak_freq,
            sl0=peak_level_db,
            a1_oct=rise_slope_db_oct,
            a2_oct=fall_slope_db_oct, # Pass the negative value directly
            fs=sampling_freq,
            duration=signal_duration,
            filter_order=fir_filter_order
        )

    print(f"Generated signal with {len(sim_signal)} samples.")
    print(f"Filter designed with {len(fir_coeffs)} coefficients.")

    # 打印生成的时域信号
    # sim_signal *= scaling_factor
    print("\n生成的时域信号前10个采样点:")
    print(sim_signal[:10])
    print(f"\n时域信号总长度: {len(sim_signal)}")
    print(f"时域信号均值: {np.mean(sim_signal):.6f}")
    print(f"时域信号标准差: {np.std(sim_signal):.6f}")
    print(f"时域信号最大值: {np.max(sim_signal):.6f}")
    print(f"时域信号最小值: {np.min(sim_signal):.6f}")
    '''

    # Parameters for line spectrum
    print("\n" + "=" * 50)
    print("Simulating line spectrum signal...")

    # Example 1: Using default parameters with blade count and rotation speed
    blade_count = 4  # Number of propeller blades
    rotation_speed = 2.0  # Propeller rotation speed in r/s
    total_harmonics = 5  # Number of harmonics

    print(f"\nExample 1: Using default parameters with blade count and rotation speed")
    print(f"Parameters: blade_count={blade_count}, rotation_speed={rotation_speed} r/s, total_harmonics={total_harmonics}")
    print(f"            fs={sampling_freq} Hz, Duration={signal_duration} s")

    line_signal1, freqs1, amps1, phases1 = simulate_line_spectrum(
        frequencies=None,
        amplitudes=None,
        total_harmonics=total_harmonics,
        blade_count=blade_count,
        rotation_speed=rotation_speed,
        fs=sampling_freq,
        duration=signal_duration,
        random_phase=True
    )

    print(f"Generated line spectrum signal with {len(line_signal1)} samples.")
    print(f"\n生成的时域信号前10个采样点:")
    print(line_signal1[:10])
    print(f"\n时域信号总长度: {len(line_signal1)}")
    print(f"时域信号均值: {np.mean(line_signal1):.6f}")
    print(f"时域信号标准差: {np.std(line_signal1):.6f}")
    print(f"时域信号最大值: {np.max(line_signal1):.6f}")
    print(f"时域信号最小值: {np.min(line_signal1):.6f}")

    # Example 2: Using custom frequencies and amplitudes
    print("\n" + "=" * 50)
    print(f"\nExample 2: Using custom frequencies and amplitudes")

    custom_freqs = np.array([16.0, 32.0, 48.0, 65.0, 350.0, 800.0])  # Custom frequencies in Hz
    custom_amps = np.array([31622776.6, 22387211.39, 17782794.1, 14125375.45, 3162277.66, 1778279.41])  # Custom amplitudes

    print(f"Parameters: Custom frequencies and amplitudes")
    print(f"            fs={sampling_freq} Hz, Duration={signal_duration} s")

    line_signal2, freqs2, amps2, phases2 = simulate_line_spectrum(
        frequencies=custom_freqs,
        amplitudes=custom_amps,
        fs=sampling_freq,
        duration=signal_duration,
        random_phase=True
    )

    print(f"Generated line spectrum signal with {len(line_signal2)} samples.")
    print(f"\n生成的时域信号前10个采样点:")
    print(line_signal2[:10])
    print(f"\n时域信号总长度: {len(line_signal2)}")
    print(f"时域信号均值: {np.mean(line_signal2):.6f}")
    print(f"时域信号标准差: {np.std(line_signal2):.6f}")
    print(f"时域信号最大值: {np.max(line_signal2):.6f}")
    print(f"时域信号最小值: {np.min(line_signal2):.6f}")

    # Plot the line spectrum signals
    plt.figure(figsize=(12, 10))

    # Plot Example 1: Time domain signal
    plt.subplot(2, 2, 1)
    t = np.linspace(0, signal_duration, len(line_signal1), endpoint=False)
    plt.plot(t[:10000], line_signal1[:10000])  # Plot first 10000 samples
    plt.title('Example 1: Line Spectrum Signal (Time Domain)')
    plt.xlabel('Time (s)')
    plt.ylabel('Amplitude')
    plt.grid(True)

    # Plot Example 1: Frequency domain
    plt.subplot(2, 2, 2)
    fft_result1 = np.fft.rfft(line_signal1)
    fft_freqs1 = np.fft.rfftfreq(len(line_signal1), d=1.0/sampling_freq)
    fft_magnitude1 = np.abs(fft_result1) / len(line_signal1)
    if len(line_signal1) % 2 == 0:  # Nyquist present
        fft_magnitude1[1:-1] *= 2
    else:  # Nyquist not present
        fft_magnitude1[1:] *= 2

    # 找出幅值超过阈值的频率点
    threshold = np.max(fft_magnitude1) * 0.01  # 使用最大幅值的1%作为阈值
    significant_mask = fft_magnitude1 > threshold

    # 打印信息查看筛选结果
    print("\n筛选出的频率点数量:", np.sum(significant_mask))
    print("\n最大幅值:", np.max(fft_magnitude1))
    print("阈值:", threshold)

    # 只使用有意义的频率点和幅值
    sig_freqs = fft_freqs1[significant_mask]
    sig_magnitudes = fft_magnitude1[significant_mask]

    # 打印筛选出的频率点和幅值
    print("\n筛选出的频率点:")
    for i in range(len(sig_freqs)):
        print(f"{sig_freqs[i]:.2f} Hz: {sig_magnitudes[i]:.6f}")

    # 计算dB值
    sig_magnitudes_db = 20 * np.log10(sig_magnitudes)

    # 只绘制有意义的点
    plt.stem(sig_freqs, sig_magnitudes_db, markerfmt='ro', basefmt='b-')
    plt.title('Example 1: Line Spectrum (Frequency Domain)')
    plt.xlabel('Frequency (Hz)')
    plt.ylabel('Magnitude (dB)')
    plt.grid(True)

    # Plot Example 2: Time domain signal
    plt.subplot(2, 2, 3)
    t = np.linspace(0, signal_duration, len(line_signal2), endpoint=False)
    plt.plot(t[:10000], line_signal2[:10000])  # Plot first 10000 samples
    plt.title('Example 2: Line Spectrum Signal (Time Domain)')
    plt.xlabel('Time (s)')
    plt.ylabel('Amplitude')
    plt.grid(True)

    # Plot Example 2: Frequency domain
    plt.subplot(2, 2, 4)
    fft_result2 = np.fft.rfft(line_signal2)
    fft_freqs2 = np.fft.rfftfreq(len(line_signal2), d=1.0/sampling_freq)
    fft_magnitude2 = np.abs(fft_result2) / len(line_signal2)
    if len(line_signal2) % 2 == 0:  # Nyquist present
        fft_magnitude2[1:-1] *= 2
    else:  # Nyquist not present
        fft_magnitude2[1:] *= 2

    # 找出幅值超过阈值的频率点
    threshold = np.max(fft_magnitude2) * 0.01  # 使用最大幅值的1%作为阈值
    significant_mask = fft_magnitude2 > threshold

    # 打印信息查看筛选结果
    print("\n筛选出的频率点数量:", np.sum(significant_mask))
    print("\n最大幅值:", np.max(fft_magnitude2))
    print("阈值:", threshold)

    # 只使用有意义的频率点和幅值
    sig_freqs = fft_freqs2[significant_mask]
    sig_magnitudes = fft_magnitude2[significant_mask]

    # 打印筛选出的频率点和幅值
    print("\n筛选出的频率点:")
    for i in range(len(sig_freqs)):
        print(f"{sig_freqs[i]:.2f} Hz: {sig_magnitudes[i]:.6f}")


    # 计算dB值
    sig_magnitudes_db = 20 * np.log10(sig_magnitudes)

    # 只绘制有意义的点
    plt.stem(sig_freqs, sig_magnitudes_db, markerfmt='ro', basefmt='b-')
    plt.title('Example 2: Line Spectrum (Frequency Domain)')
    plt.xlabel('Frequency (Hz)')
    plt.ylabel('Magnitude (dB)')
    plt.grid(True)

    plt.tight_layout()
    plt.show()


    print("\nSimulation complete.")

    # # --- Verification Plotting ---
    # print("Plotting results for verification...")

    # # 1. Plot Target Spectrum vs Filter Frequency Response
    # w_filt, h_filt = signal.freqz(fir_coeffs, worN=8000)
    # filter_freqs_hz_resp = (w_filt / np.pi) * (sampling_freq / 2.0)
    # filter_response_db = 20 * np.log10(np.abs(h_filt))

    # target_amps_db = 20 * np.log10(target_amps_lin + 1e-20) # Add epsilon for log10(0)
    # target_amps_db[np.isneginf(target_amps_db)] = np.nanmin(target_amps_db[np.isfinite(target_amps_db)]) - 40 if np.any(np.isfinite(target_amps_db)) else -200

    # plt.figure(figsize=(12, 10)) # Adjusted height for three plots

    # plt.subplot(3, 1, 1)
    # plt.plot(target_freqs_hz, target_amps_db, 'o-', label='Target Spectrum Points (dB)')
    # plt.plot(filter_freqs_hz_resp, filter_response_db, label=f'FIR Filter Response ({len(fir_coeffs)} taps)', alpha=0.7)
    # plt.title('Target Spectrum and FIR Filter Frequency Response')
    # plt.xlabel('Frequency (Hz)')
    # plt.ylabel('Gain (dB)')
    # plt.grid(True, which='both')
    # plt.legend()
    # # Adjust ylim bottom dynamically based on plotted data
    # min_db_plot1 = np.nanmin([np.nanmin(target_amps_db), np.nanmin(filter_response_db)])
    # plt.ylim(bottom=min_db_plot1 - 10 if np.isfinite(min_db_plot1) else -200)


    # # 2. Plot Target Spectrum Shape vs. Output Signal FFT Magnitude
    # # Normalize FFT dB level to match target sl0 at f0 for visual comparison
    # fft_interp_val_at_f0 = np.interp(peak_freq, fft_freqs_hz, fft_mags_db)
    # fft_offset = peak_level_db - fft_interp_val_at_f0

    # plt.subplot(3, 1, 2)
    # plt.semilogx(target_freqs_hz, target_amps_db, 'o-', label='Target Spectrum Shape (dB)')
    # # Plot FFT results - skip DC (index 0) if it's extremely low due to filtering/noise floor for better scaling
    # start_index = 1 if fft_freqs_hz[0] == 0 else 0
    # plt.semilogx(fft_freqs_hz[start_index:], fft_mags_db[start_index:] + fft_offset, label='Simulated Signal FFT Mag (Normalized)', alpha=0.7)
    # plt.title('Target Spectrum Shape vs. Simulated Signal FFT Magnitude')
    # plt.xlabel('Frequency (Hz)')
    # plt.ylabel('Level (dB / Normalized FFT Mag)')
    # plt.grid(True, which='both')
    # plt.legend()
    # # Adjust ylim bottom dynamically based on plotted data
    # min_db_plot2 = np.nanmin([np.nanmin(target_amps_db), np.nanmin(fft_mags_db[start_index:] + fft_offset)])
    # plt.ylim(bottom=min_db_plot2 - 10 if np.isfinite(min_db_plot2) else -200)


    # # 3. Plot Simulated Signal and FFT Magnitude
    # plt.subplot(3, 1, 3)
    # # Plot FFT magnitude with linear axes (not using semilogx)
    # plt.plot(fft_freqs_hz, fft_mags_db, label='FFT Magnitude (dB)', alpha=0.7)
    # plt.title('Simulated Signal FFT Magnitude (Original Values)')
    # plt.xlabel('Frequency (Hz)')
    # plt.ylabel('Magnitude (dB)')
    # plt.grid(True, which='both')
    # plt.legend()
    # # Adjust ylim bottom dynamically based on plotted data
    # min_db_plot3 = np.nanmin(fft_mags_db)
    # plt.ylim(bottom=min_db_plot3 - 10 if np.isfinite(min_db_plot3) else -200)

    # plt.tight_layout()
    # plt.show()

    # # 4. Plot Scaled Signal FFT Magnitude as a separate figure
    # # 对sim_signal应用scaling_factor缩放
    # scaled_signal = sim_signal * scaling_factor

    # # 计算缩放后信号的rfft
    # scaled_fft_result = np.fft.rfft(scaled_signal)
    # # 频率轴与原始信号相同
    # scaled_fft_freqs = fft_freqs_hz

    # # 计算幅度谱（单边谱）
    # n_samples = len(scaled_signal)
    # scaled_fft_magnitude = np.abs(scaled_fft_result) / n_samples
    # if n_samples % 2 == 0: # Nyquist present
    #     scaled_fft_magnitude[1:-1] *= 2
    # else: # Nyquist not present
    #     scaled_fft_magnitude[1:] *= 2

    # # 转换为dB
    # epsilon_db = 1e-20
    # scaled_fft_mags_db = 20 * np.log10(scaled_fft_magnitude + epsilon_db)

    # # 创建新的图表用于第四个子图
    # plt.figure(figsize=(12, 6))

    # # 绘制缩放后的FFT幅度
    # plt.plot(scaled_fft_freqs, scaled_fft_mags_db, label='Scaled FFT Magnitude (dB)', alpha=0.7, color='green')

    # # 过滤掉target_amps_db在0Hz处的值
    # non_zero_mask = target_freqs_hz > 0
    # filtered_target_freqs = target_freqs_hz[non_zero_mask]
    # filtered_target_amps_db = target_amps_db[non_zero_mask]

    # # 绘制过滤后的目标频谱点
    # plt.plot(filtered_target_freqs, filtered_target_amps_db, 'o-', label='Target Spectrum Points (dB)', color='blue')

    # plt.title('Scaled Signal FFT Magnitude (After Applying Scaling Factor)')
    # plt.xlabel('Frequency (Hz)')
    # plt.ylabel('Magnitude (dB)')
    # plt.grid(True, which='both')
    # plt.legend()

    # # 调整y轴下限，使其与目标频谱相似
    # min_db_plot4 = np.nanmin([np.nanmin(scaled_fft_mags_db), np.nanmin(filtered_target_amps_db)])
    # plt.ylim(bottom=min_db_plot4 - 10 if np.isfinite(min_db_plot4) else -200)

    # plt.tight_layout()
    # plt.show()

    # print("Simulation and plotting complete.")
    # print(f"已应用缩放因子 {scaling_factor:.6e} 到原始信号并绘制频谱图。")

