# -*- coding: utf-8 -*-
"""
信道数据制备性能测试脚本

测试_prepare_channel_data函数在不同参数配置下的性能表现
"""

import os
import sys
import time
import json
import numpy as np
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.core.controllers.propagation_controller import PropagationController
from src.core.data.simulation_data_manager import SimulationDataManager


class ChannelDataPerformanceTester:
    """信道数据制备性能测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.data_manager = SimulationDataManager()

        # 设置全局参数
        self.data_manager.set_global_param('fs', 44100)
        self.data_manager.set_global_param('cpu_cores', 4)

        self.controller = PropagationController(self.data_manager)
        self.test_results = []

        # 创建测试输出目录
        self.test_output_dir = os.path.join(project_root, "test_output", "channel_data_performance")
        os.makedirs(self.test_output_dir, exist_ok=True)
        
    def create_base_environment_params(self):
        """
        创建基础环境参数
        
        这些参数在所有测试中保持一致，确保测试的公平性
        
        Returns:
            dict: 基础环境参数字典
        """
        return {
            # 基本参数
            'name': '性能测试',
            'type': '2D',
            'frequency': 1000,  # 探测频率，用于环境创建
            
            # 水体设置
            'depth_type': 'flat',
            'depth': 5000,  # 水深100米
            
            # 声速设置
            'ssp_type': 'constant',
            'soundspeed': 1500,  # 声速1500 m/s
            
            # 海底参数
            'bottom_soundspeed': 1600,
            'bottom_density': 1800,
            'bottom_absorption': 0.5,
            'bottom_roughness': 0,
            
            # 声源和接收器参数
            'tx_depth': 50,  # 声源深度50米
            'rx_depth': 60,  # 接收器深度60米（用于环境创建）
            'rx_range': 1000,  # 接收器距离1000米（用于环境创建）
            
            # 射线参数
            'min_angle': -80,
            'max_angle': 80,
            'nbeams': 0,  # 自动设置声束数量
            
            # 高级选项
            'debug_mode': False,
            
            # 计算类型
            'computation_type': 'channel_preparation'
        }
    
    def create_test_configurations(self):
        """
        创建四个不同的测试配置
        
        Returns:
            list: 测试配置列表
        """
        base_params = self.create_base_environment_params()
        
        configurations = []
        
        # 测试1: 小规模测试 - 少频率点，少阵元
        config1 = base_params.copy()
        config1.update({
            'test_name': '测试1_小规模',
            'description': '5个频率点，4个阵元',
            'freq_mode': 'range',
            'freq_start': 500,
            'freq_end': 2500,
            'freq_step': 500,  # 5个频率点: 500, 1000, 1500, 2000, 2500
            'array_elements_data': [
                (1000, 49),
                (1000, 50),
                (1000, 51),
                (1000, 52)
            ],
            'max_workers': 2,
            'output_dir': os.path.join(self.test_output_dir, 'test1_small')
        })
        configurations.append(config1)
        
        # 测试2: 中等规模测试 - 中等频率点，中等阵元
        config2 = base_params.copy()
        config2.update({
            'test_name': '测试2_中等规模',
            'description': '10个频率点，8个阵元',
            'freq_mode': 'range',
            'freq_start': 250,
            'freq_end': 2750,
            'freq_step': 250,  # 11个频率点: 250, 500, ..., 2750
            'array_elements_data': [
                (1000, 46),
                (1000, 47),
                (1000, 48),
                (1000, 49),
                (1000, 50),
                (1000, 51),
                (1000, 52),
                (1000, 53)
            ],
            'max_workers': 4,
            'output_dir': os.path.join(self.test_output_dir, 'test2_medium')
        })
        configurations.append(config2)
        
        # 测试3: 大规模测试 - 多频率点，多阵元
        config3 = base_params.copy()
        config3.update({
            'test_name': '测试3_大规模',
            'description': '20个频率点，16个阵元',
            'freq_mode': 'range',
            'freq_start': 100,
            'freq_end': 2000,
            'freq_step': 100,  # 20个频率点: 100, 200, ..., 2000
            'array_elements_data': [
                (1000, 42 + i * 0.5) for i in range(16)  # 16个阵元，间距0.5米
            ],
            'max_workers': 6,
            'output_dir': os.path.join(self.test_output_dir, 'test3_large')
        })
        configurations.append(config3)
        
        # 测试4: 自定义频率列表测试
        config4 = base_params.copy()
        config4.update({
            'test_name': '测试4_自定义频率',
            'description': '自定义15个频率点，12个阵元',
            'freq_mode': 'list',
            'freq_list': [50, 100, 200, 300, 500, 750, 1000, 1250, 1500, 1750, 2000, 2250, 2500, 3000, 4000],
            'array_elements_data': [
                (1000, 44 + i * 0.5) for i in range(12)  # 12个阵元，间距0.5米
            ],
            'max_workers': 4,
            'output_dir': os.path.join(self.test_output_dir, 'test4_custom_freq')
        })
        configurations.append(config4)
        
        return configurations
    
    def run_single_test(self, config):
        """
        运行单个测试
        
        Args:
            config (dict): 测试配置
            
        Returns:
            dict: 测试结果
        """
        print(f"\n开始运行 {config['test_name']}: {config['description']}")
        
        # 创建输出目录
        os.makedirs(config['output_dir'], exist_ok=True)
        
        # 记录开始时间
        start_time = time.time()
        start_datetime = datetime.now()
        
        try:
            # 重置controller状态
            self.controller.is_cancelled = False
            self.controller.is_running = False
            
            # 调用_prepare_channel_data函数
            self.controller._prepare_channel_data(config)
            
            # 记录结束时间
            end_time = time.time()
            end_datetime = datetime.now()
            
            # 计算耗时
            duration = end_time - start_time
            
            # 计算频率点数量
            if config['freq_mode'] == 'range':
                freq_count = len(np.arange(config['freq_start'], 
                                         config['freq_end'] + config['freq_step']/2, 
                                         config['freq_step']))
            else:
                freq_count = len(config['freq_list'])
            
            # 计算阵元数量
            array_count = len(config['array_elements_data'])
            
            # 计算总计算量（频率点数 × 阵元数）
            total_calculations = freq_count * array_count
            
            result = {
                'test_name': config['test_name'],
                'description': config['description'],
                'status': 'success',
                'start_time': start_datetime.isoformat(),
                'end_time': end_datetime.isoformat(),
                'duration_seconds': duration,
                'duration_formatted': f"{duration:.2f}秒",
                'frequency_count': freq_count,
                'array_element_count': array_count,
                'total_calculations': total_calculations,
                'calculations_per_second': total_calculations / duration if duration > 0 else 0,
                'max_workers': config['max_workers'],
                'output_dir': config['output_dir'],
                'config': config
            }
            
            print(f"✓ {config['test_name']} 完成")
            print(f"  耗时: {duration:.2f}秒")
            print(f"  频率点数: {freq_count}")
            print(f"  阵元数: {array_count}")
            print(f"  总计算量: {total_calculations}")
            print(f"  计算速度: {total_calculations/duration:.2f} 计算/秒")
            
            return result
            
        except Exception as e:
            end_time = time.time()
            end_datetime = datetime.now()
            duration = end_time - start_time

            result = {
                'test_name': config['test_name'],
                'description': config['description'],
                'status': 'error',
                'start_time': start_datetime.isoformat(),
                'end_time': end_datetime.isoformat(),
                'duration_seconds': duration,
                'error_message': str(e),
                'output_dir': config.get('output_dir', 'N/A'),
                'config': config
            }

            print(f"✗ {config['test_name']} 失败: {str(e)}")

            return result
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("信道数据制备性能测试")
        print("=" * 60)
        
        # 获取测试配置
        configurations = self.create_test_configurations()
        
        print(f"准备运行 {len(configurations)} 个测试...")
        
        # 运行所有测试
        for i, config in enumerate(configurations, 1):
            print(f"\n[{i}/{len(configurations)}]", end="")
            result = self.run_single_test(config)
            self.test_results.append(result)
        
        # 生成测试报告
        self.generate_report()
        
        print("\n" + "=" * 60)
        print("所有测试完成！")
        print("=" * 60)
    
    def generate_report(self):
        """生成测试报告"""
        # 创建报告数据
        report_data = {
            'test_summary': {
                'total_tests': len(self.test_results),
                'successful_tests': len([r for r in self.test_results if r['status'] == 'success']),
                'failed_tests': len([r for r in self.test_results if r['status'] == 'error']),
                'total_duration': sum(r['duration_seconds'] for r in self.test_results),
                'report_generated_at': datetime.now().isoformat()
            },
            'test_results': self.test_results
        }
        
        # 保存JSON报告
        report_file = os.path.join(self.test_output_dir, 'performance_test_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=4, ensure_ascii=False)
        
        # 生成文本报告
        self.generate_text_report(report_data)
        
        print(f"\n测试报告已保存到: {report_file}")
    
    def generate_text_report(self, report_data):
        """生成文本格式的测试报告"""
        report_file = os.path.join(self.test_output_dir, 'performance_test_report.txt')
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("信道数据制备性能测试报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 测试摘要
            summary = report_data['test_summary']
            f.write("测试摘要:\n")
            f.write(f"  总测试数: {summary['total_tests']}\n")
            f.write(f"  成功测试: {summary['successful_tests']}\n")
            f.write(f"  失败测试: {summary['failed_tests']}\n")
            f.write(f"  总耗时: {summary['total_duration']:.2f}秒\n")
            f.write(f"  报告生成时间: {summary['report_generated_at']}\n\n")
            
            # 详细结果
            f.write("详细测试结果:\n")
            f.write("-" * 50 + "\n")
            
            for result in self.test_results:
                f.write(f"\n{result['test_name']}: {result['description']}\n")
                f.write(f"  状态: {result['status']}\n")
                
                if result['status'] == 'success':
                    f.write(f"  耗时: {result['duration_formatted']}\n")
                    f.write(f"  频率点数: {result['frequency_count']}\n")
                    f.write(f"  阵元数: {result['array_element_count']}\n")
                    f.write(f"  总计算量: {result['total_calculations']}\n")
                    f.write(f"  计算速度: {result['calculations_per_second']:.2f} 计算/秒\n")
                    f.write(f"  并行进程数: {result['max_workers']}\n")
                else:
                    f.write(f"  错误信息: {result['error_message']}\n")
                
                f.write(f"  输出目录: {result['output_dir']}\n")


def main():
    """主函数"""
    try:
        # 创建测试器
        tester = ChannelDataPerformanceTester()
        
        # 运行所有测试
        tester.run_all_tests()
        
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
