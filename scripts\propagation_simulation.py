#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
声传播模拟脚本

该脚本实现了基本的声传播模拟方法：
1. 生成白噪声作为源信号
2. 使用arlpy.uwapm计算特定频率(2500Hz)的冲击响应
3. 将源信号与冲击响应进行卷积
4. 计算卷积前后的功率比得到传输损失(TL)
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from scipy import signal
import arlpy.uwapm as pm
import time
import os
import tempfile

# 设置中文字体
def set_chinese_font():
    """
    设置matplotlib使用中文字体
    """
    try:
        # 查找系统中的中文字体
        chinese_fonts = [f.name for f in fm.fontManager.ttflist
                        if 'SimHei' in f.name or 'Microsoft YaHei' in f.name
                        or 'SimSun' in f.name or 'FangSong' in f.name]

        if chinese_fonts:
            # 使用找到的第一个中文字体
            plt.rcParams['font.sans-serif'] = [chinese_fonts[0]] + plt.rcParams['font.sans-serif']
            print(f"使用中文字体: {chinese_fonts[0]}")
        else:
            # 如果没有找到中文字体，尝试使用一些常见的中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'FangSong'] + plt.rcParams['font.sans-serif']
            print("尝试使用默认中文字体")

        # 解决负号显示问题
        plt.rcParams['axes.unicode_minus'] = False
    except Exception as e:
        print(f"设置中文字体时出错: {e}")
        print("将使用默认字体，中文可能无法正确显示")

def create_white_noise(fs, duration):
    """
    创建白噪声信号

    参数:
        fs: 采样率 (Hz)
        duration: 信号持续时间 (s)

    返回:
        t: 时间向量
        x: 信号向量
    """
    t = np.arange(0, duration, 1/fs)
    x = np.random.randn(len(t))
    # 归一化
    x = x / np.sqrt(np.mean(x**2))
    return t, x

def create_sine_signal(freq, fs, duration):
    """
    创建单频正弦信号

    参数:
        freq: 频率 (Hz)
        fs: 采样率 (Hz)
        duration: 信号持续时间 (s)

    返回:
        t: 时间向量
        x: 信号向量
    """
    t = np.arange(0, duration, 1/fs)
    x = np.sin(2 * np.pi * freq * t)
    return t, x

def create_acoustic_environment(freq, tx_depth=10, rx_depth=20, rx_range=1000):
    """
    创建声学环境

    参数:
        freq: 频率 (Hz)
        tx_depth: 发射深度 (m)
        rx_depth: 接收深度 (m)
        rx_range: 接收距离 (m)

    返回:
        env: 声学环境
    """
    # 创建一个简单的声学环境
    env = pm.create_env2d(
        depth=4000,                  # 水深100米
        soundspeed=1500,            # 声速1500 m/s
        bottom_soundspeed=1600,     # 海底声速
        bottom_density=1800,        # 海底密度
        bottom_absorption=0.5,      # 海底吸收系数
        tx_depth=tx_depth,          # 发射深度
        rx_depth=rx_depth,          # 接收深度
        rx_range=rx_range,          # 接收距离
        frequency=freq              # 频率
    )
    return env

def compute_impulse_response(env, fs, debug=False, abs_time=False):
    """
    计算冲击响应

    参数:
        env: 声学环境
        fs: 采样率 (Hz)
        debug: 是否输出调试信息
        abs_time: 是否使用绝对时间(True)或相对时间(False)

    返回:
        ir: 冲击响应
        arrivals: 到达信息
        t0: 冲击响应的起始时间(秒)
    """
    # 创建临时文件名
    temp_dir = tempfile.gettempdir()
    fname_base = os.path.join(temp_dir, f"bellhop_temp_{int(time.time())}")

    try:
        # 计算到达信息
        arrivals = pm.compute_arrivals(env, debug=debug, fname_base=fname_base)

        # 记录最早到达时间，用于后续时间轴计算
        t0 = 0 if abs_time else min(arrivals.time_of_arrival)

        # 转换为冲击响应
        ir = pm.arrivals_to_impulse_response(arrivals, fs, abs_time=abs_time)

        return ir, arrivals, t0
    except Exception as e:
        print(f"计算冲击响应时出错: {str(e)}")
        return np.array([1.0]), None, 0  # 返回单位冲击
    finally:
        # 清理临时文件
        if not debug:
            for ext in ['.env', '.bty', '.ssp', '.ati', '.sbp', '.prt', '.log', '.arr', '.ray', '.shd']:
                try:
                    os.unlink(fname_base + ext)
                except:
                    pass

def calculate_power(signal_data):
    """
    计算信号功率

    参数:
        signal_data: 信号数据

    返回:
        power: 信号功率
    """
    return np.mean(np.abs(signal_data)**2)

def calculate_transmission_loss(input_power, output_power):
    """
    计算传输损失

    参数:
        input_power: 输入信号功率
        output_power: 输出信号功率

    返回:
        tl_db: 传输损失 (dB)
    """
    if output_power > 0:
        return 10 * np.log10(input_power / output_power)
    else:
        return float('inf')  # 无限大损失

def set_chinese_font():
    """设置中文字体"""
    try:
        import matplotlib.font_manager as fm
        # 尝试设置中文字体，如果失败则使用默认字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
    except:
        print("警告: 设置中文字体失败，图表中的中文可能无法正确显示")

def main():
    # 设置中文字体
    set_chinese_font()

    # 参数设置
    fs = 44100                # 采样率 (Hz)
    duration = 5.0            # 信号持续时间 (s)
    center_freq = 2500        # 中心频率 (Hz)
    tx_depth = 60             # 发射深度 (m)
    rx_depth = 200            # 接收深度 (m)
    rx_range = 20000          # 接收距离 (m)

    # 创建白噪声信号
    print("生成白噪声信号...")
    t_source, noise_signal = create_white_noise(fs, duration)

    # 创建单频正弦信号（用于比较）
    print("生成单频正弦信号...")
    _, sine_signal = create_sine_signal(center_freq, fs, duration)

    # 创建声学环境
    print(f"创建声学环境，频率: {center_freq} Hz...")
    # 声速剖面
    ssp = np.array([
        [0.00, 1548.52],
        [200.00, 1530.29],
        [250.00, 1526.69],
        [400.00, 1517.78],
        [600.00, 1509.49],
        [800.00, 1504.30],
        [1000.00, 1501.38],
        [1200.00, 1500.14],
        [1400.00, 1500.12],
        [1600.00, 1501.02],
        [1800.00, 1502.57],
        [2000.00, 1504.62],
        [2200.00, 1507.02],
        [2400.00, 1509.69],
        [2600.00, 1512.55],
        [2800.00, 1515.56],
        [3000.00, 1518.67],
        [3200.00, 1521.85],
        [3400.00, 1525.10],
        [3600.00, 1528.38],
        [3800.00, 1531.70],
        [4000.00, 1535.04]
    ])
    env = pm.create_env2d(
        depth = 4000,
        max_angle = 90,
        min_angle = -90,
        rx_range = rx_range,
        rx_depth = rx_depth,
        tx_depth = tx_depth,
        soundspeed = ssp,
        frequency = center_freq
    )

    print("输出环境信息...")
    pm.print_env(env)

    # 计算冲击响应，使用相对时间模式
    print("计算冲击响应...")
    ir, arrivals, t0 = compute_impulse_response(env, fs, debug=True, abs_time=False)

    # 打印到达信息
    if arrivals is not None:
        print(f"到达路径数量: {len(arrivals)}")
        print(f"最早到达时间: {min(arrivals.time_of_arrival):.4f} 秒")
        print(f"最晚到达时间: {max(arrivals.time_of_arrival):.4f} 秒")
        print(f"冲击响应长度: {len(ir)} 样本 ({len(ir)/fs:.4f} 秒)")

        print("\n前5条到达路径信息:")
        print(arrivals.head(5)[['time_of_arrival', 'arrival_amplitude', 'angle_of_departure', 'angle_of_arrival', 'surface_bounces', 'bottom_bounces']])

    # 卷积处理 - 使用完整卷积模式
    print("进行卷积处理...")
    if len(ir) > 1:
        # 对白噪声进行卷积 - 使用'full'模式
        noise_output_full = signal.fftconvolve(noise_signal, ir, mode='full')

        # 对正弦信号进行卷积 - 使用'full'模式
        sine_output_full = signal.fftconvolve(sine_signal, ir, mode='full')
    else:
        # 如果冲击响应只有一个点，直接缩放信号
        noise_output_full = noise_signal * ir[0]
        sine_output_full = sine_signal * ir[0]

    # 创建输出信号的时间轴
    # 源信号时间轴: 0 到 duration
    # 输出信号时间轴: t0 到 t0 + len(output_full)/fs
    t_output = np.arange(len(noise_output_full)) / fs + t0

    # 计算功率和传输损失
    noise_input_power = calculate_power(noise_signal)
    noise_output_power = calculate_power(noise_output_full)
    noise_tl_db = calculate_transmission_loss(noise_input_power, noise_output_power)

    sine_input_power = calculate_power(sine_signal)
    sine_output_power = calculate_power(sine_output_full)
    sine_tl_db = calculate_transmission_loss(sine_input_power, sine_output_power)

    print(f"\n白噪声传输损失: {noise_tl_db:.2f} dB")
    print(f"正弦信号传输损失: {sine_tl_db:.2f} dB")

    # 计算冲击响应的传输损失
    ir_power = np.sum(np.abs(ir)**2)
    if ir_power > 0:
        ir_tl_db = -10 * np.log10(ir_power)
        print(f"冲击响应传输损失: {ir_tl_db:.2f} dB")

    # 绘制结果
    plt.figure(figsize=(12, 12))

    # 冲击响应
    plt.subplot(3, 2, 1)
    t_ir = np.arange(len(ir)) / fs + t0  # 使用正确的时间轴
    plt.plot(t_ir, np.real(ir))
    plt.title('冲击响应 (实部)')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.grid(True)

    plt.subplot(3, 2, 2)
    plt.plot(t_ir, np.abs(ir))
    plt.title('冲击响应 (幅度)')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.grid(True)

    # 白噪声信号
    plt.subplot(3, 2, 3)
    plt.plot(t_source, noise_signal)
    plt.title('输入白噪声信号')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.grid(True)

    plt.subplot(3, 2, 4)
    plt.plot(t_output, noise_output_full)
    plt.title(f'输出白噪声信号 (TL: {noise_tl_db:.2f} dB)')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.grid(True)

    # 正弦信号
    plt.subplot(3, 2, 5)
    plt.plot(t_source, sine_signal)
    plt.title(f'输入正弦信号 ({center_freq} Hz)')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.grid(True)

    plt.subplot(3, 2, 6)
    plt.plot(t_output, sine_output_full)
    plt.title(f'输出正弦信号 (TL: {sine_tl_db:.2f} dB)')
    plt.xlabel('时间 (s)')
    plt.ylabel('幅度')
    plt.grid(True)

    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    main()