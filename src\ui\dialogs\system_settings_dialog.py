# -*- coding: utf-8 -*-
"""
系统设置对话框

用于设置全局参数，如采样率
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QFormLayout, QLabel,
                            QSpinBox, QDialogButtonBox,
                            QGroupBox)


class SystemSettingsDialog(QDialog):
    """
    系统设置对话框

    用于设置全局参数，如采样率
    """

    def __init__(self, parent=None):
        """
        初始化系统设置对话框

        Args:
            parent: 父窗口
        """
        super().__init__(parent)

        # 设置窗口标题
        self.setWindowTitle("系统参数设置")

        # 获取数据管理器
        self.data_manager = parent.data_manager

        # 创建布局
        layout = QVBoxLayout(self)

        # 创建采样率设置组
        fs_group = QGroupBox("采样率设置")
        fs_layout = QFormLayout()

        # 采样率输入框
        self.fs_spin = QSpinBox()
        self.fs_spin.setRange(8000, 192000)
        self.fs_spin.setSingleStep(1000)
        self.fs_spin.setValue(self.data_manager.get_global_param('fs'))
        self.fs_spin.setSuffix(" Hz")
        fs_layout.addRow("采样率:", self.fs_spin)

        # 设置采样率组布局
        fs_group.setLayout(fs_layout)
        layout.addWidget(fs_group)

        # 添加说明文本
        info_label = QLabel("注意: 采样率设置将统一应用于所有仿真模块。")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 创建按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def accept(self):
        """
        确定按钮点击事件处理
        """
        # 保存设置
        self.data_manager.set_global_param('fs', self.fs_spin.value())

        # 调用父类的accept方法
        super().accept()
