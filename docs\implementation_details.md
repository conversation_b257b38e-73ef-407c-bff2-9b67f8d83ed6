# 模块实现细节文档

本文档详细记录了水声噪声仿真系统中各模块的实现细节，特别是最近完成的海洋环境噪声模块和更新的船舶辐射噪声模块。

## 目录

1. [海洋环境噪声模块](#1-海洋环境噪声模块)
   1. [模块概述](#11-模块概述)
   2. [核心功能实现](#12-核心功能实现)
   3. [交互设计与实现](#13-交互设计与实现)
   4. [性能优化](#14-性能优化)
   5. [已知问题与解决方案](#15-已知问题与解决方案)

2. [船舶辐射噪声模块更新](#2-船舶辐射噪声模块更新)
   1. [模块重构](#21-模块重构)
   2. [频率点密度优化](#22-频率点密度优化)
   3. [缩放因子计算改进](#23-缩放因子计算改进)

3. [元数据记录机制](#3-元数据记录机制)
   1. [设计目的](#31-设计目的)
   2. [实现方式](#32-实现方式)
   3. [应用场景](#33-应用场景)

## 1. 海洋环境噪声模块

### 1.1 模块概述

海洋环境噪声模块是水声噪声仿真系统的重要组成部分，用于模拟和生成海洋环境中的背景噪声。该模块基于Wenz曲线模型，通过直观的图形界面允许用户自定义海洋环境噪声的功率谱密度曲线，并生成相应的时域噪声信号。

模块的核心功能包括：

- 基于Wenz曲线的海洋环境噪声模拟
- 交互式噪声谱定义界面
- 智能频谱外插
- 高质量噪声信号生成
- 结果可视化与验证

### 1.2 核心功能实现

#### 1.2.1 Wenz曲线背景实现

不同于使用图片背景的方式，本模块直接使用Matplotlib绘制Wenz曲线作为背景。实现步骤：

1. **数据准备**：
   - 使用数字化工具从标准Wenz曲线图中提取关键曲线的坐标数据点
   - 将数据保存到`data/wenz_curves.json`文件中，包含以下曲线：
     - 不同海况/风速的风生噪声曲线
     - 交通噪声上下边界
     - 湍流压力波动范围
     - 热噪声曲线
     - 噪声总体上下限

2. **绘制实现**：
   - 使用Matplotlib的对数X轴和线性Y轴
   - 使用`plot()`函数绘制各条曲线
   - 使用`fill_between()`函数绘制带状区域
   - 添加适当的图例和标签

3. **样式配置**：
   - 使用`data/wenz_style_config.json`存储曲线样式配置
   - 环境噪声上下限用黑色实线
   - 浅海交通噪声带用红色
   - 深海交通噪声带用浅粉色
   - 极浅海域风生噪声带用#C7AD7D（透明化后）

#### 1.2.2 频谱外插功能

为确保噪声谱覆盖完整的关注频率范围（1-10000Hz），系统提供自动外插功能：

1. **外插触发条件**：
   - 用户完成初步选点后，系统检查是否覆盖1-10000Hz范围
   - 如果用户选择的最小频率点不等于1.0Hz，则需要向低频外插
   - 如果用户选择的最大频率点不等于10000Hz，则需要向高频外插

2. **外插算法**：
   - 支持两种外插方式：自动和手动指定斜率
   - 自动模式：从用户曲线自动延伸，保持用户曲线在边界处的斜率
   - 手动模式：使用用户指定的斜率值（dB/倍频程）进行外插
   - 两种模式可以混合使用：低频使用一种方式，高频使用另一种方式

3. **外插实现**：
   - 低频外插默认斜率：-9.0 dB/倍频程
   - 高频外插默认斜率：-5.0 dB/倍频程
   - 以倍频程为间隔进行外插，确保覆盖1-10000Hz范围
   - 外插点使用不同颜色（蓝色）与用户选择的点（红色）区分

#### 1.2.3 噪声信号生成

基于用户定义的噪声谱（包括外插后的完整频谱），生成时域噪声信号的步骤：

1. **频谱处理**：
   - 将用户定义的点（包括外插点）按频率排序
   - 将dB值转换为线性幅度值
   - 确保频率点覆盖完整的1-10000Hz范围
   - 添加0Hz和Nyquist频率点以满足滤波器设计要求

2. **信号生成**：
   - 生成高斯白噪声
   - 使用`firwin2`函数设计FIR滤波器，传入频率点和线性幅度值
   - 对白噪声进行滤波
   - 进行谱级校正，确保生成信号的PSD符合目标谱

3. **滤波器设计优化**：
   - 使用高阶FIR滤波器（16385阶）提高频谱精度
   - 允许用户自定义滤波器阶数，在精度和计算效率之间取得平衡
   - 在0Hz处设置幅度为0，避免直流分量

4. **谱级校正**：
   - 计算生成信号的PSD
   - 计算目标PSD与生成信号PSD的比值
   - 使用几何平均计算缩放因子：K_power = (r₁×r₂×...×rₙ)^(1/n)
   - 对信号应用缩放因子的平方根，确保功率谱密度符合目标值

### 1.3 交互设计与实现

#### 1.3.1 用户交互模式

系统提供三种交互模式，用户可以通过控制面板上的单选按钮切换：

1. **添加模式**：
   - 用户点击画布添加新的频率-噪声级点
   - 点击后立即在该位置添加一个红色点
   - 实时更新用户曲线

2. **调整模式**：
   - 用户点击选择已有点进行调整
   - 被选中的点变为绿色，并显示其频率和噪声级值
   - 用户可以通过以下方式调整点的位置：
     - 拖动点到新位置
     - 使用键盘方向键微调位置
     - 在控制面板的输入框中直接输入新的频率和噪声级值

3. **删除模式**：
   - 用户点击已有点进行删除
   - 点击后立即删除该点
   - 实时更新用户曲线

#### 1.3.2 点操作实现

1. **点的选择机制**：
   - 在调整或删除模式下，基于屏幕像素距离计算点击位置与所有可见点的距离
   - 当最近点的距离小于预设阈值（10像素）时，该点被视为选中
   - 选中点后，更新`selected_point_index`属性，记录当前选中点的索引

2. **点的视觉反馈**：
   - 普通点：红色，标记大小为6
   - 选中点：绿色，标记大小为8
   - 外插点：蓝色，标记大小为6
   - 选中外插点：青色，标记大小为8

3. **键盘方向键微调**：
   - 在调整模式下，选中点后可使用键盘方向键微调位置
   - 左右键：调整频率（对数刻度，每次调整约1%）
   - 上下键：调整噪声级（线性刻度，每次调整0.5dB）
   - 微调后保持点的选中状态，允许连续调整

#### 1.3.3 坐标轴尺度变化处理

在使用Matplotlib的缩放工具或切换坐标轴尺度（如从对数尺度切换到线性尺度）时，需要特殊处理点的显示和选择：

1. **尺度变化检测**：
   - 在初始化时保存当前的坐标轴尺度：`self.current_xscale`和`self.current_yscale`
   - 监听`xlim_changed`和`ylim_changed`事件，在事件处理函数中检查尺度是否发生变化
   - 只在尺度实际变化时（如从对数变为线性）才刷新点的显示状态

2. **点的刷新机制**：
   - 实现`refresh_points_display`方法，保存当前点的数据和选中状态
   - 移除所有现有点并重新创建它们，确保点的颜色状态正确
   - 使用标志变量`self.is_refreshing`避免重复刷新

3. **选中状态保持**：
   - 在刷新点显示时，记录当前选中点的索引
   - 刷新完成后，恢复选中状态
   - 确保坐标轴尺度变化不会导致选中状态丢失

### 1.4 性能优化

#### 1.4.1 绘图性能优化

1. **Matplotlib Blitting技术**：
   - 缓存静态背景（Wenz曲线、坐标轴等）
   - 在交互时仅重绘动态元素（用户曲线、外插曲线、选中点等）
   - 显著提高交互响应速度

2. **事件处理优化**：
   - 对高频事件（如鼠标移动时的坐标显示）使用防抖或节流技术
   - 限制更新频率，避免不必要的计算开销
   - 使用`draw_idle()`代替`draw()`，允许Matplotlib合并多次重绘请求

3. **数据结构优化**：
   - 使用NumPy数组存储和处理频率和噪声级数据
   - 避免在循环中进行昂贵的操作（如排序）
   - 使用向量化操作代替循环，提高计算效率

#### 1.4.2 外插操作优化

1. **延迟执行**：
   - 不在每次点操作后自动执行外插，而是在用户点击"应用"按钮或"开始仿真"按钮时执行
   - 避免频繁的外插计算，特别是在用户快速添加多个点时

2. **缓存结果**：
   - 缓存外插结果，只在必要时重新计算
   - 在用户修改外插设置（如斜率值或外插方式）时清除缓存

3. **算法优化**：
   - 优化外插点的生成算法，减少不必要的计算
   - 使用对数间隔生成外插点，确保在对数坐标系下的均匀分布

#### 1.4.3 信号生成优化

1. **滤波器设计优化**：
   - 允许用户选择滤波器阶数，在精度和计算速度之间取得平衡
   - 默认使用16385阶，提供良好的频谱精度
   - 对于快速原型设计，可以使用较低阶数（如4097或8193）

2. **内存使用优化**：
   - 使用float32数据类型存储大型数组，减少内存占用
   - 在处理完成后释放临时数组，避免内存泄漏
   - 使用内存映射技术处理超大信号

### 1.5 已知问题与解决方案

#### 1.5.1 方向键移动选中点失效问题

**问题描述**：
在海洋环境噪声视图中，当用户选中一个点后，只能通过方向键移动一次，然后就不能继续控制了。具体表现为，方向键移动选中点后，选中状态丢失，点的颜色从绿色变回红色。

**根本原因**：
在使用方向键移动选中点的过程中，选中点的索引（`selected_point_index`）被丢失或重置。

**解决方案**：
在`on_key_press`函数中添加代码，保存当前选中点的索引，并在更新点位置后确保点仍然处于选中状态：

```python
# 保存当前选中的点索引
current_selected_index = self.selected_point_index

# 更新点的位置
self.update_point(self.selected_point_index, new_freq, new_level)

# 确保点仍然处于选中状态
self.selected_point_index = current_selected_index
self.highlight_selected_point()
```

#### 1.5.2 坐标轴尺度变化后点颜色不更新问题

**问题描述**：
当用户通过工具栏将x轴从对数尺度改为线性尺度后，虽然系统能够正确识别选中的点，但选中的点没有变绿色，导致用户体验不佳。

**根本原因**：
当坐标轴尺度变化时，Matplotlib会进行深度重绘，但在`user_points`列表中存储的`(freq, level, artist)`三元组中，`artist`对象与实际显示的点对象之间的关联可能会变得不一致。

**解决方案**：
实现坐标轴尺度变化检测机制，在尺度变化时重新创建所有点：

```python
def on_xlim_changed(self, event):
    """当X轴范围变化时调用"""
    # 检查尺度是否变化
    current_xscale = self.ax.get_xscale()
    if current_xscale != self.current_xscale:
        self.current_xscale = current_xscale
        self.refresh_points_display()

def refresh_points_display(self):
    """刷新所有点的显示"""
    if self.is_refreshing:
        return

    self.is_refreshing = True

    # 保存当前点的数据和选中状态
    user_points_data = [(freq, level) for freq, level, _ in self.user_points]
    extrapolated_points_data = [(freq, level) for freq, level, _ in self.extrapolated_points]
    selected_index = self.selected_point_index

    # 移除所有现有点
    for _, _, artist in self.user_points + self.extrapolated_points:
        if artist:
            artist.remove()

    # 重新创建点
    self.user_points = []
    self.extrapolated_points = []

    # 重新添加用户点
    for freq, level in user_points_data:
        self.add_point_internal(freq, level, is_user_point=True)

    # 重新添加外插点
    for freq, level in extrapolated_points_data:
        self.add_point_internal(freq, level, is_user_point=False)

    # 恢复选中状态
    if selected_index is not None and 0 <= selected_index < len(self.user_points):
        self.selected_point_index = selected_index
        self.highlight_selected_point()

    self.update_user_curve()
    self.update_extrapolated_curve()
    self.canvas.draw_idle()

    self.is_refreshing = False
```

## 2. 船舶辐射噪声模块更新

### 2.1 模块重构

船舶辐射噪声模块进行了重构，将原来的单一模块分解为三个独立的功能模块：连续谱、线谱和调制谱。这种重构提高了代码的模块化程度，使各部分功能更加清晰，便于维护和扩展。

#### 2.1.1 连续谱模块

连续谱模块负责生成船舶辐射噪声的连续谱部分，主要特点包括：

1. **参数设计**：
   - 峰值频率(f0)：连续谱峰值所在的频率，默认为250Hz
   - 峰值电平(sl0)：连续谱峰值处的声压级，默认为130.0dB
   - 上升斜率(a1)：峰值频率以下的斜率，单位为dB/倍频程，默认为3.0
   - 下降斜率(a2)：峰值频率以上的斜率，单位为dB/倍频程，默认为-4.2

2. **频谱生成**：
   - 使用分段函数描述连续谱形状，以峰值频率为分界点
   - 低于峰值频率的部分：SL(f) = SL0 + a1 * log2(f/f0)
   - 高于峰值频率的部分：SL(f) = SL0 + a2 * log2(f/f0)
   - 在频率点生成过程中应用倍频程采样优化

3. **信号生成**：
   - 使用FIR滤波器对白噪声进行滤波，生成具有目标频谱特性的时域信号
   - 应用谱级校正，确保生成信号的PSD与目标谱一致

#### 2.1.2 线谱模块

线谱模块负责生成船舶辐射噪声的线谱部分，主要特点包括：

1. **参数设计**：
   - 线谱频率列表：用户可以指定多个离散频率点，默认包含[16, 32, 48, 65, 350, 800]Hz
   - 线谱电平差值列表：相对于连续谱峰值的电平差，默认为[25.0, 23.0, 21.0, 19.0, 18.0, 18.0]dB

2. **信号生成**：
   - 对每个线谱频率，生成相应的正弦信号
   - 根据电平差值设置每个正弦信号的幅度
   - 将所有正弦信号叠加，形成完整的线谱信号

3. **频谱计算**：
   - 计算线谱信号的PSD
   - 在频谱图上显示为离散的峰值

#### 2.1.3 调制谱模块

调制谱模块负责生成船舶辐射噪声的调制特性，主要特点包括：

1. **参数设计**：
   - 轴频(mod_f1)：与轴旋转相关的基频，默认为8.0Hz
   - 叶频(mod_f2)：与螺旋桨叶片相关的频率，默认为32.0Hz
   - 轴频分量振幅(mod_A1)：轴频调制的深度，默认为0.05
   - 叶频分量振幅(mod_A2)：叶频调制的深度，默认为0.3
   - 轴频衰减系数(mod_p)：控制轴频谐波衰减速度，默认为0.1
   - 叶频衰减系数(mod_q)：控制叶频谐波衰减速度，默认为0.1
   - 轴频谐波数量(mod_N)：考虑的轴频谐波数，默认为5
   - 叶频谐波数量(mod_M)：考虑的叶频谐波数，默认为5

2. **调制实现**：
   - 生成调制函数a(t)，包含轴频和叶频的谐波成分
   - 调制通过时域乘法实现：[1+a(t)]*g_x(t)，而非频域卷积
   - 调制后的信号保持原始信号的总体频谱特性，但在各频率成分周围产生调制边带

### 2.2 频率点密度优化

为了提高船舶辐射噪声频谱的描述精度，对频率点的生成方式进行了优化：

1. **倍频程采样优化**：
   - 采用每倍频程取4个点的方法，增加频率点密度
   - 保持从峰值频率向两侧按倍频程采样的原始方法
   - 实现方式：在每个倍频程区间内均匀插入额外的频率点

2. **实现细节**：
   - 定义倍频程区间：[f0/2^n, f0/2^(n-1)]和[f0*2^(n-1), f0*2^n]
   - 在每个区间内生成4个等比分布的频率点
   - 计算公式：f_i = f_start * (f_end/f_start)^(i/4)，其中i=0,1,2,3

3. **优势**：
   - 提高了频谱描述的精度，特别是在频谱变化较大的区域
   - 保持了对数频率轴上的均匀分布特性
   - 在不显著增加计算量的情况下，提高了频谱细节的表现

### 2.3 缩放因子计算改进

为了确保生成信号的功率谱密度与目标谱一致，改进了缩放因子的计算方法：

1. **几何平均计算**：
   - 使用几何平均计算功率比值：K_power = (r₁×r₂×...×rₙ)^(1/n)
   - 其中r_i是目标PSD与生成信号PSD在第i个频率点的比值
   - 几何平均能更好地处理跨越多个数量级的比值

2. **信号缩放**：
   - 使用缩放因子的平方根对信号进行缩放：signal_scaled = signal * sqrt(K_power)
   - 这确保了功率谱密度的正确缩放，因为功率与信号幅度的平方成正比

3. **优势**：
   - 相比算术平均，几何平均对异常值不太敏感
   - 在对数刻度上，几何平均能更均匀地分布误差
   - 提高了生成信号与目标谱的一致性，特别是在频谱变化较大的区域

## 3. 元数据记录机制

### 3.1 设计目的

元数据记录机制的设计目的是在仿真结果中记录生成参数，为后续的信号处理和叠加操作提供必要的信息。主要目标包括：

1. **结果可重现性**：
   - 记录生成参数，确保仿真结果的可重现性
   - 便于用户了解结果是在什么条件下生成的
   - 支持结果的验证和比较

2. **信号叠加支持**：
   - 在将来实现信号叠加功能时，可以通过检查元数据中的参数确保信号兼容性
   - 例如，只有采样率和持续时间相同的信号才能直接叠加
   - 如果参数不一致，系统可以提示用户或自动进行转换

3. **结果验证和调试**：
   - 当结果出现问题时，可以通过检查元数据来定位问题
   - 便于比较不同参数设置下的结果差异
   - 提供完整的结果溯源能力

### 3.2 实现方式

元数据记录机制的实现方式如下：

1. **元数据结构**：
   - 在结果字典中添加`metadata`字段，记录生成参数
   - 元数据包含以下关键信息：
     - `fs`：采样率（Hz）
     - `duration`：信号持续时间（s）
     - `filter_order`：滤波器阶数
     - `generation_time`：生成时间（ISO格式的时间戳）

2. **控制器实现**：
   - 在`ShipNoiseController`和`AmbientNoiseController`的`run`方法中，将生成参数添加到结果字典中
   - 使用NumPy的`datetime64`类型记录生成时间，并转换为字符串以便序列化
   - 示例代码：
     ```python
     # 添加元数据，记录生成参数
     results['metadata'] = {
         'fs': fs,
         'duration': duration,
         'filter_order': filter_order,
         'generation_time': np.datetime64('now').astype(str)
     }
     ```

3. **项目保存和加载**：
   - 元数据作为结果字典的一部分，在项目保存时被序列化到JSON文件中
   - 小型数据（如元数据字典）直接存储在JSON文件中，而不是NPZ文件中
   - 在项目加载时，元数据与其他结果数据一起被恢复

### 3.3 应用场景

元数据记录机制的主要应用场景包括：

1. **信号叠加**：
   - 在将来实现信号叠加功能时，系统可以检查元数据中的`fs`和`duration`参数
   - 只有这些参数相同的信号才能直接叠加
   - 如果参数不同，系统可以提供自动转换选项（如重采样或截断/补零）

2. **结果验证**：
   - 用户可以查看元数据，了解结果是在什么条件下生成的
   - 在比较不同结果时，可以确认参数差异
   - 在调试问题时，可以检查元数据以确定可能的原因

3. **批处理和自动化**：
   - 在批处理或自动化脚本中，可以使用元数据来跟踪和管理多个仿真结果
   - 基于元数据进行结果筛选和分组
   - 自动生成结果报告，包含关键参数信息

4. **结果共享和协作**：
   - 在团队协作环境中，元数据提供了结果的完整上下文
   - 当共享结果文件时，接收方可以了解结果的生成条件
   - 便于不同用户之间的结果比较和讨论
