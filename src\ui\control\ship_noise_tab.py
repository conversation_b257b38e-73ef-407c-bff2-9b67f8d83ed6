# -*- coding: utf-8 -*-
"""
船舶辐射噪声标签页

用于设置船舶辐射噪声的参数和控制仿真
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLabel, QLineEdit, QSpinBox, QDoubleSpinBox,
                            QPushButton, QGroupBox, QCheckBox, QComboBox,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QMessageBox)
from PyQt5.QtCore import pyqtSignal

from src.ui.dialogs.ship_condition_dialog import ShipConditionDialog


class ShipNoiseTab(QWidget):
    """
    船舶辐射噪声标签页

    用于设置船舶辐射噪声的参数和控制仿真
    """

    # 自定义信号
    simulation_requested = pyqtSignal()

    def __init__(self, data_manager=None, parent=None):
        """
        初始化船舶辐射噪声标签页

        Args:
            data_manager: 数据管理器实例
            parent: 父窗口
        """
        super().__init__(parent)

        # 保存数据管理器引用
        self.data_manager = data_manager

        # 创建布局
        main_layout = QVBoxLayout(self)

        # 创建连续谱参数组
        continuous_group = QGroupBox("连续谱参数")
        continuous_layout = QFormLayout()

        self.peak_freq_spin = QDoubleSpinBox()
        self.peak_freq_spin.setRange(1, 2000)
        self.peak_freq_spin.setValue(250)
        self.peak_freq_spin.setSuffix(" Hz")
        continuous_layout.addRow("峰值频率:", self.peak_freq_spin)

        self.peak_level_spin = QDoubleSpinBox()
        self.peak_level_spin.setRange(0, 300)
        self.peak_level_spin.setValue(130.0)
        self.peak_level_spin.setSuffix(" dB")
        continuous_layout.addRow("峰值谱级:", self.peak_level_spin)

        self.rise_slope_spin = QDoubleSpinBox()
        self.rise_slope_spin.setRange(0, 20)
        self.rise_slope_spin.setValue(3.0)
        self.rise_slope_spin.setSuffix(" dB/Oct")
        continuous_layout.addRow("峰值频率前的上升斜率:", self.rise_slope_spin)

        self.fall_slope_spin = QDoubleSpinBox()
        self.fall_slope_spin.setRange(-20, 0)
        self.fall_slope_spin.setValue(-4.2)
        self.fall_slope_spin.setSuffix(" dB/Oct")
        continuous_layout.addRow("峰值频率后的下降斜率:", self.fall_slope_spin)

        # 添加工况参数计算按钮
        self.estimate_params_button = QPushButton("根据目标工况估计连续谱参数")
        self.estimate_params_button.clicked.connect(self.on_estimate_params_clicked)
        continuous_layout.addRow("", self.estimate_params_button)

        continuous_group.setLayout(continuous_layout)
        main_layout.addWidget(continuous_group)

        # 创建线谱参数组
        line_group = QGroupBox("线谱参数")
        line_layout = QVBoxLayout()

        # 创建线谱参数表格
        self.line_table = QTableWidget()
        self.line_table.setColumnCount(2)
        self.line_table.setHorizontalHeaderLabels(["频率 (Hz)", "相对连续谱的dB差值"])
        self.line_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # 预设线谱参数
        default_freqs = [16, 32, 48, 65, 350, 800]
        default_levels = [25.0, 23.0, 21.0, 19.0, 18.0, 18.0]

        # 设置初始行数
        self.line_table.setRowCount(len(default_freqs))

        # 填充默认数据
        for i, (freq, level) in enumerate(zip(default_freqs, default_levels)):
            freq_item = QTableWidgetItem(str(freq))
            level_item = QTableWidgetItem(str(level))
            self.line_table.setItem(i, 0, freq_item)
            self.line_table.setItem(i, 1, level_item)

        line_layout.addWidget(self.line_table)

        # 添加和删除行的按钮
        button_layout = QHBoxLayout()

        self.add_line_button = QPushButton("添加行")
        self.add_line_button.clicked.connect(self.add_line_row)
        button_layout.addWidget(self.add_line_button)

        self.remove_line_button = QPushButton("删除选中行")
        self.remove_line_button.clicked.connect(self.remove_line_row)
        button_layout.addWidget(self.remove_line_button)

        line_layout.addLayout(button_layout)

        # 包含线谱复选框
        self.include_line_check = QCheckBox("包含线谱")
        self.include_line_check.setChecked(True)
        line_layout.addWidget(self.include_line_check)

        line_group.setLayout(line_layout)
        main_layout.addWidget(line_group)

        # 创建调制谱参数组
        modulation_group = QGroupBox("调制谱参数")
        modulation_layout = QFormLayout()

        # 轴频参数
        self.mod_f1_spin = QDoubleSpinBox()
        self.mod_f1_spin.setRange(0.1, 100)
        self.mod_f1_spin.setValue(8.0)
        self.mod_f1_spin.setSuffix(" Hz")
        modulation_layout.addRow("轴频:", self.mod_f1_spin)

        # 叶频参数
        self.mod_f2_spin = QDoubleSpinBox()
        self.mod_f2_spin.setRange(0.1, 500)
        self.mod_f2_spin.setValue(32.0)
        self.mod_f2_spin.setSuffix(" Hz")
        modulation_layout.addRow("叶频:", self.mod_f2_spin)

        # 轴频分量振幅
        self.mod_a1_spin = QDoubleSpinBox()
        self.mod_a1_spin.setRange(0.0, 1.0)
        self.mod_a1_spin.setValue(0.05)
        self.mod_a1_spin.setSingleStep(0.01)
        modulation_layout.addRow("轴频分量振幅:", self.mod_a1_spin)

        # 叶频分量振幅
        self.mod_a2_spin = QDoubleSpinBox()
        self.mod_a2_spin.setRange(0.0, 1.0)
        self.mod_a2_spin.setValue(0.3)
        self.mod_a2_spin.setSingleStep(0.01)
        modulation_layout.addRow("叶频分量振幅:", self.mod_a2_spin)

        # 轴频衰减系数
        self.mod_p_spin = QDoubleSpinBox()
        self.mod_p_spin.setRange(0.0, 1.0)
        self.mod_p_spin.setValue(0.1)
        self.mod_p_spin.setSingleStep(0.01)
        modulation_layout.addRow("轴频衰减系数:", self.mod_p_spin)

        # 叶频衰减系数
        self.mod_q_spin = QDoubleSpinBox()
        self.mod_q_spin.setRange(0.0, 1.0)
        self.mod_q_spin.setValue(0.1)
        self.mod_q_spin.setSingleStep(0.01)
        modulation_layout.addRow("叶频衰减系数:", self.mod_q_spin)

        # 轴频谐波数量
        self.mod_n_spin = QSpinBox()
        self.mod_n_spin.setRange(1, 20)
        self.mod_n_spin.setValue(5)
        modulation_layout.addRow("轴频谐波数量:", self.mod_n_spin)

        # 叶频谐波数量
        self.mod_m_spin = QSpinBox()
        self.mod_m_spin.setRange(1, 20)
        self.mod_m_spin.setValue(5)
        modulation_layout.addRow("叶频谐波数量:", self.mod_m_spin)

        # 包含调制谱复选框
        self.include_mod_check = QCheckBox("包含调制谱")
        self.include_mod_check.setChecked(True)
        modulation_layout.addRow("", self.include_mod_check)

        modulation_group.setLayout(modulation_layout)
        main_layout.addWidget(modulation_group)

        # 创建高级设置组
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QFormLayout()

        # 持续时间输入框
        self.duration_input = QDoubleSpinBox()
        self.duration_input.setRange(0.1, 60.0)  # 设置合理的范围
        self.duration_input.setSingleStep(0.1)  # 步长为0.1
        self.duration_input.setValue(5.0)  # 默认值
        self.duration_input.setSuffix(" s")
        advanced_layout.addRow("信号持续时间:", self.duration_input)

        # 添加持续时间说明
        duration_label = QLabel("信号持续时间决定了生成信号的长度。系统计算功率谱密度时默认采用1Hz的频率分辨率。")
        duration_label.setWordWrap(True)
        advanced_layout.addRow("", duration_label)

        # 滤波器阶数输入框
        self.filter_order_input = QDoubleSpinBox()
        self.filter_order_input.setRange(257, 32769)  # 设置合理的范围
        self.filter_order_input.setDecimals(0)  # 不需要小数
        self.filter_order_input.setSingleStep(256)  # 步长为256
        self.filter_order_input.setValue(1025)  # 默认值
        advanced_layout.addRow("滤波器阶数:", self.filter_order_input)

        # 添加滤波器阶数说明
        filter_order_label = QLabel("较大的滤波器阶数可以提高仿真精度，但会增加计算时间。阶数必须为奇数，系统会自动调整。")
        filter_order_label.setWordWrap(True)
        advanced_layout.addRow("", filter_order_label)

        # 设置高级设置组布局
        advanced_group.setLayout(advanced_layout)
        main_layout.addWidget(advanced_group)


        # 创建控制按钮
        control_layout = QHBoxLayout()

        self.simulate_button = QPushButton("开始仿真")
        self.simulate_button.clicked.connect(self.on_simulate_clicked)
        control_layout.addWidget(self.simulate_button)

        self.reset_button = QPushButton("重置参数")
        self.reset_button.clicked.connect(self.reset_parameters)
        control_layout.addWidget(self.reset_button)

        main_layout.addLayout(control_layout)

        # 添加弹性空间
        main_layout.addStretch()

        # 连接控件值变化信号
        self.connect_value_changed_signals()

    def validate_line_spectrum_table(self, show_warning=True):
        """
        验证线谱表格数据

        Args:
            show_warning (bool): 是否显示警告对话框

        Returns:
            tuple: (is_valid, error_rows, valid_frequencies, valid_levels)
                is_valid: 是否有效
                error_rows: 错误行信息列表
                valid_frequencies: 有效的频率列表
                valid_levels: 有效的电平差值列表
        """
        error_rows = []
        valid_frequencies = []
        valid_levels = []

        # 验证表格数据
        for row in range(self.line_table.rowCount()):
            # 获取频率单元格
            freq_item = self.line_table.item(row, 0)
            level_item = self.line_table.item(row, 1)

            # 跳过空行
            if (not freq_item or not freq_item.text().strip()) and \
               (not level_item or not level_item.text().strip()):
                continue

            # 检查是否缺失数据
            if not freq_item or not freq_item.text().strip():
                error_rows.append(f"第{row+1}行缺失频率数据")
                continue

            if not level_item or not level_item.text().strip():
                error_rows.append(f"第{row+1}行缺失dB差值数据")
                continue

            # 检查数据格式
            try:
                freq = float(freq_item.text().strip())
                if freq <= 0:
                    error_rows.append(f"第{row+1}行频率必须为正数")
                    continue
            except ValueError:
                error_rows.append(f"第{row+1}行频率格式不正确")
                continue

            try:
                level = float(level_item.text().strip())
                if level <= 0:
                    error_rows.append(f"第{row+1}行dB差值必须为正数")
                    continue
            except ValueError:
                error_rows.append(f"第{row+1}行dB差值格式不正确")
                continue

            # 添加有效数据
            valid_frequencies.append(freq)
            valid_levels.append(level)

        # 如果有错误且需要显示警告，显示错误信息
        if error_rows and show_warning:
            error_message = "\n".join(error_rows)
            QMessageBox.warning(self, "参数错误", f"线谱参数存在以下问题:\n{error_message}")

        is_valid = len(error_rows) == 0
        return is_valid, error_rows, valid_frequencies, valid_levels

    def on_estimate_params_clicked(self):
        """
        根据目标工况估计连续谱参数按钮点击事件处理
        """
        # 创建并显示工况参数输入对话框
        dialog = ShipConditionDialog(self)
        dialog.params_calculated.connect(self.on_params_calculated)
        dialog.exec_()

    def on_params_calculated(self, params):
        """
        工况参数计算完成事件处理

        Args:
            params (dict): 计算得到的连续谱参数
        """
        # 更新UI控件
        self.peak_freq_spin.setValue(params['f0'])
        self.peak_level_spin.setValue(params['sl0'])
        self.rise_slope_spin.setValue(params['a1'])
        self.fall_slope_spin.setValue(params['a2'])

        # 显示成功消息
        QMessageBox.information(self, "参数计算成功", "已根据工况参数更新连续谱参数")

    def on_simulate_clicked(self):
        """
        开始仿真按钮点击事件处理
        """
        # 验证线谱参数
        is_valid, _, _, _ = self.validate_line_spectrum_table(show_warning=True)
        if not is_valid:
            return

        # 获取当前参数（用于验证）
        params = self.get_current_parameters()

        # 检查是否有线谱参数
        if params['include_line_spectrum'] and not params['line_frequencies']:
            QMessageBox.warning(self, "参数错误", "启用了线谱但没有有效的线谱参数")
            return

        # 发射信号（不再需要携带参数）
        self.simulation_requested.emit()

    def add_line_row(self):
        """
        添加线谱参数行
        """
        current_row_count = self.line_table.rowCount()
        self.line_table.setRowCount(current_row_count + 1)

        # 添加空白单元格
        self.line_table.setItem(current_row_count, 0, QTableWidgetItem(""))
        self.line_table.setItem(current_row_count, 1, QTableWidgetItem(""))

    def remove_line_row(self):
        """
        删除选中的线谱参数行
        """
        selected_rows = set()
        for item in self.line_table.selectedItems():
            selected_rows.add(item.row())

        # 从后向前删除行，避免索引变化
        for row in sorted(selected_rows, reverse=True):
            self.line_table.removeRow(row)

        # 确保至少有一行
        if self.line_table.rowCount() == 0:
            self.line_table.setRowCount(1)
            self.line_table.setItem(0, 0, QTableWidgetItem(""))
            self.line_table.setItem(0, 1, QTableWidgetItem(""))

    def get_current_parameters(self):
        """
        获取当前UI中的参数

        Returns:
            dict: 参数字典
        """
        params = {
            # 连续谱参数
            'f0': self.peak_freq_spin.value(),
            'sl0': self.peak_level_spin.value(),
            'a1': self.rise_slope_spin.value(),
            'a2': self.fall_slope_spin.value(),

            # 线谱参数
            'line_frequencies': self.get_line_frequencies(),
            'line_levels_diff': self.get_line_levels_diff(),
            'include_line_spectrum': self.include_line_check.isChecked(),

            # 调制谱参数
            'mod_f1': self.mod_f1_spin.value(),
            'mod_f2': self.mod_f2_spin.value(),
            'mod_A1': self.mod_a1_spin.value(),
            'mod_A2': self.mod_a2_spin.value(),
            'mod_p': self.mod_p_spin.value(),
            'mod_q': self.mod_q_spin.value(),
            'mod_N': self.mod_n_spin.value(),
            'mod_M': self.mod_m_spin.value(),
            'include_modulation': self.include_mod_check.isChecked(),

            # 高级设置参数
            'duration': self.duration_input.value(),
            'filter_order': int(self.filter_order_input.value())
        }
        return params

    def get_line_frequencies(self):
        """
        从表格中获取线谱频率

        Returns:
            list: 频率列表（只包含有效的频率）
        """
        _, _, valid_frequencies, _ = self.validate_line_spectrum_table(show_warning=False)
        return valid_frequencies

    def get_line_levels_diff(self):
        """
        从表格中获取线谱电平差值

        Returns:
            list: 电平差值列表（只包含有效的电平差值）
        """
        _, _, _, valid_levels = self.validate_line_spectrum_table(show_warning=False)
        return valid_levels

    def set_parameters(self, params):
        """
        设置参数到UI

        Args:
            params (dict): 参数字典
        """
        # 设置连续谱参数
        if 'f0' in params:
            self.peak_freq_spin.setValue(params['f0'])
        if 'sl0' in params:
            self.peak_level_spin.setValue(params['sl0'])
        if 'a1' in params:
            self.rise_slope_spin.setValue(params['a1'])
        if 'a2' in params:
            self.fall_slope_spin.setValue(params['a2'])

        # 设置线谱参数
        if 'include_line_spectrum' in params:
            self.include_line_check.setChecked(params['include_line_spectrum'])

        # 设置线谱频率和电平
        if 'line_frequencies' in params and 'line_levels_diff' in params:
            frequencies = params['line_frequencies']
            levels_diff = params['line_levels_diff']

            # 确保两个列表长度相同
            min_len = min(len(frequencies), len(levels_diff))

            # 清空表格
            self.line_table.setRowCount(0)

            # 设置行数
            self.line_table.setRowCount(min_len)

            # 填充数据
            for i in range(min_len):
                freq_item = QTableWidgetItem(str(frequencies[i]))
                level_item = QTableWidgetItem(str(levels_diff[i]))
                self.line_table.setItem(i, 0, freq_item)
                self.line_table.setItem(i, 1, level_item)

        # 设置调制谱参数
        if 'mod_f1' in params:
            self.mod_f1_spin.setValue(params['mod_f1'])
        if 'mod_f2' in params:
            self.mod_f2_spin.setValue(params['mod_f2'])
        if 'mod_A1' in params:
            self.mod_a1_spin.setValue(params['mod_A1'])
        if 'mod_A2' in params:
            self.mod_a2_spin.setValue(params['mod_A2'])
        if 'mod_p' in params:
            self.mod_p_spin.setValue(params['mod_p'])
        if 'mod_q' in params:
            self.mod_q_spin.setValue(params['mod_q'])
        if 'mod_N' in params:
            self.mod_n_spin.setValue(params['mod_N'])
        if 'mod_M' in params:
            self.mod_m_spin.setValue(params['mod_M'])
        if 'include_modulation' in params:
            self.include_mod_check.setChecked(params['include_modulation'])

        # 设置高级参数
        if 'duration' in params:
            self.duration_input.setValue(params['duration'])
        if 'filter_order' in params:
            self.filter_order_input.setValue(params['filter_order'])

    def reset_parameters(self):
        """
        重置参数
        """
        # 连续谱参数
        self.peak_freq_spin.setValue(250)
        self.peak_level_spin.setValue(130.0)
        self.rise_slope_spin.setValue(3.0)
        self.fall_slope_spin.setValue(-4.2)

        # 线谱参数
        # 清空表格
        self.line_table.setRowCount(0)

        # 预设线谱参数
        default_freqs = [16, 32, 48, 65, 350, 800]
        default_levels = [25.0, 23.0, 21.0, 19.0, 18.0, 18.0]

        # 设置初始行数
        self.line_table.setRowCount(len(default_freqs))

        # 填充默认数据
        for i, (freq, level) in enumerate(zip(default_freqs, default_levels)):
            freq_item = QTableWidgetItem(str(freq))
            level_item = QTableWidgetItem(str(level))
            self.line_table.setItem(i, 0, freq_item)
            self.line_table.setItem(i, 1, level_item)

        self.include_line_check.setChecked(True)

        # 调制谱参数
        self.mod_f1_spin.setValue(8.0)
        self.mod_f2_spin.setValue(32.0)
        self.mod_a1_spin.setValue(0.05)
        self.mod_a2_spin.setValue(0.3)
        self.mod_p_spin.setValue(0.1)
        self.mod_q_spin.setValue(0.1)
        self.mod_n_spin.setValue(5)
        self.mod_m_spin.setValue(5)
        self.include_mod_check.setChecked(True)

        # 重置高级参数
        self.duration_input.setValue(5.0)
        self.filter_order_input.setValue(1025)

    def connect_value_changed_signals(self):
        """
        连接控件值变化信号
        """
        # 连接连续谱参数控件
        self.peak_freq_spin.valueChanged.connect(self.on_parameter_changed)
        self.peak_level_spin.valueChanged.connect(self.on_parameter_changed)
        self.rise_slope_spin.valueChanged.connect(self.on_parameter_changed)
        self.fall_slope_spin.valueChanged.connect(self.on_parameter_changed)

        # 连接线谱参数控件
        self.include_line_check.stateChanged.connect(self.on_parameter_changed)
        self.line_table.itemChanged.connect(self.on_parameter_changed)

        # 连接调制谱参数控件
        self.mod_f1_spin.valueChanged.connect(self.on_parameter_changed)
        self.mod_f2_spin.valueChanged.connect(self.on_parameter_changed)
        self.mod_a1_spin.valueChanged.connect(self.on_parameter_changed)
        self.mod_a2_spin.valueChanged.connect(self.on_parameter_changed)
        self.mod_p_spin.valueChanged.connect(self.on_parameter_changed)
        self.mod_q_spin.valueChanged.connect(self.on_parameter_changed)
        self.mod_n_spin.valueChanged.connect(self.on_parameter_changed)
        self.mod_m_spin.valueChanged.connect(self.on_parameter_changed)
        self.include_mod_check.stateChanged.connect(self.on_parameter_changed)

        # 连接高级设置控件
        self.duration_input.valueChanged.connect(self.on_parameter_changed)
        self.filter_order_input.valueChanged.connect(self.on_parameter_changed)

    def on_parameter_changed(self, value=None):
        """
        参数变化事件处理

        当任何参数控件的值发生变化时触发

        Args:
            value: 新的参数值（由信号提供）
        """
        # 如果没有数据管理器，直接返回
        if not self.data_manager:
            return

        # 确定是哪个控件发送的信号
        sender = self.sender()

        # 根据发送者更新相应的参数
        if sender == self.peak_freq_spin:
            self.data_manager.update_parameter('ship_noise', 'f0', value)
        elif sender == self.peak_level_spin:
            self.data_manager.update_parameter('ship_noise', 'sl0', value)
        elif sender == self.rise_slope_spin:
            self.data_manager.update_parameter('ship_noise', 'a1', value)
        elif sender == self.fall_slope_spin:
            self.data_manager.update_parameter('ship_noise', 'a2', value)
        elif sender == self.include_line_check:
            self.data_manager.update_parameter('ship_noise', 'include_line_spectrum', sender.isChecked())
        elif sender == self.line_table:
            # 表格变化需要更新整个线谱参数
            self.data_manager.update_parameter('ship_noise', 'line_frequencies', self.get_line_frequencies())
            self.data_manager.update_parameter('ship_noise', 'line_levels_diff', self.get_line_levels_diff())
        elif sender == self.mod_f1_spin:
            self.data_manager.update_parameter('ship_noise', 'mod_f1', value)
        elif sender == self.mod_f2_spin:
            self.data_manager.update_parameter('ship_noise', 'mod_f2', value)
        elif sender == self.mod_a1_spin:
            self.data_manager.update_parameter('ship_noise', 'mod_A1', value)
        elif sender == self.mod_a2_spin:
            self.data_manager.update_parameter('ship_noise', 'mod_A2', value)
        elif sender == self.mod_p_spin:
            self.data_manager.update_parameter('ship_noise', 'mod_p', value)
        elif sender == self.mod_q_spin:
            self.data_manager.update_parameter('ship_noise', 'mod_q', value)
        elif sender == self.mod_n_spin:
            self.data_manager.update_parameter('ship_noise', 'mod_N', value)
        elif sender == self.mod_m_spin:
            self.data_manager.update_parameter('ship_noise', 'mod_M', value)
        elif sender == self.include_mod_check:
            self.data_manager.update_parameter('ship_noise', 'include_modulation', sender.isChecked())
        elif sender == self.duration_input:
            self.data_manager.update_parameter('ship_noise', 'duration', value)
        elif sender == self.filter_order_input:
            # 确保滤波器阶数为奇数
            filter_order = int(value)
            if filter_order % 2 == 0:
                filter_order += 1
                self.filter_order_input.setValue(filter_order)
                return  # 避免重复更新
            self.data_manager.update_parameter('ship_noise', 'filter_order', filter_order)
